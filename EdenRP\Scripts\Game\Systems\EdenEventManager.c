//! Event Manager for Eden Reforger
//! Converted from original Eden Altis Life event systems
//! Manages conquest events, federal reserve heists, blackwater operations, airdrops, and other special events

[ComponentEditorProps(category: "Eden Systems", description: "Manages special events and activities")]
class EdenEventManagerComponent : ScriptComponent
{
    [Attribute("300", UIWidgets.EditBox, "Conquest event duration in seconds")]
    protected int m_iConquestDuration;
    
    [Attribute("1800", UIWidgets.EditBox, "Federal event cooldown in seconds")]
    protected int m_iFederalCooldown;
    
    [Attribute("3600", UIWidgets.EditBox, "Airdrop event cooldown in seconds")]
    protected int m_iAirdropCooldown;
    
    [Attribute("7200", UIWidgets.EditBox, "Conquest event cooldown in seconds")]
    protected int m_iConquestCooldown;
    
    [RplProp()]
    protected bool m_bConquestActive;
    
    [RplProp()]
    protected bool m_bFederalEventActive;
    
    [Rpl<PERSON>rop()]
    protected bool m_bAirdropActive;
    
    [RplProp()]
    protected int m_iLastFederalEvent;
    
    [RplProp()]
    protected int m_iLastAirdropEvent;
    
    [RplProp()]
    protected int m_iLastConquestEvent;
    
    protected ref array<ref EdenConquestZone> m_aConquestZones;
    protected ref array<ref EdenEventData> m_aActiveEvents;
    protected ref EdenConquestZone m_CurrentConquestZone;
    protected int m_iConquestStartTime;
    protected int m_iConquestPot;
    
    //! Constructor
    void EdenEventManagerComponent(IEntityComponentSource src, IEntity ent, IEntity parent)
    {
        m_bConquestActive = false;
        m_bFederalEventActive = false;
        m_bAirdropActive = false;
        m_iLastFederalEvent = 0;
        m_iLastAirdropEvent = 0;
        m_iLastConquestEvent = 0;
        m_aConquestZones = new array<ref EdenConquestZone>();
        m_aActiveEvents = new array<ref EdenEventData>();
        m_CurrentConquestZone = null;
        m_iConquestStartTime = 0;
        m_iConquestPot = 10000000; // Starting pot of $10M
        
        InitializeConquestZones();
    }
    
    //! Initialize conquest zones
    protected void InitializeConquestZones()
    {
        // Ghost Hotel
        ref array<vector> ghostPoints = {
            "21980.107 0 21035.193",
            "23620.52 0 21078.857", 
            "22422.44 0 20011.785"
        };
        ref array<vector> ghostPoly = {
            "23050.629 0 21847.943",
            "24186.055 0 21492.576",
            "23838.908 0 20403.652",
            "22453.787 0 19368.78",
            "21684.2 0 19945.623",
            "21397.473 0 21163.564"
        };
        m_aConquestZones.Insert(new EdenConquestZone("Ghost Hotel", ghostPoints, ghostPoly));
        
        // Nifi
        ref array<vector> nifiPoints = {
            "18366.957 0 15525.622",
            "19409.207 0 15389.239",
            "18551.406 0 14661.3"
        };
        ref array<vector> nifiPoly = {
            "19545.887 0 16041.155",
            "20156.535 0 15469.738",
            "20029.672 0 14402.67",
            "18232.605 0 14422.520",
            "17650.369 0 15792.173",
            "18446.352 0 16273.845"
        };
        m_aConquestZones.Insert(new EdenConquestZone("Nifi", nifiPoints, nifiPoly));
        
        // Kavala
        ref array<vector> kavalaPoints = {
            "6460.088 0 13773.676",
            "7701.869 0 14354.458",
            "7524.256 0 12829.32"
        };
        ref array<vector> kavalaPoly = {
            "7273.354 0 15584.284",
            "8528.051 0 14909.702",
            "8552.593 0 13074.955",
            "7350.592 0 12173.107",
            "5680.194 0 13794.09"
        };
        m_aConquestZones.Insert(new EdenConquestZone("Kavala", kavalaPoints, kavalaPoly));
        
        // Additional zones can be added here
    }
    
    //! Start conquest event
    bool StartConquestEvent(int zoneIndex = -1)
    {
        if (m_bConquestActive)
            return false;
            
        // Check cooldown
        int currentTime = GetGame().GetWorld().GetWorldTime();
        if (currentTime - m_iLastConquestEvent < m_iConquestCooldown)
            return false;
            
        // Select zone
        if (zoneIndex < 0 || zoneIndex >= m_aConquestZones.Count())
            zoneIndex = Math.RandomInt(0, m_aConquestZones.Count());
            
        m_CurrentConquestZone = m_aConquestZones[zoneIndex];
        if (!m_CurrentConquestZone)
            return false;
            
        m_bConquestActive = true;
        m_iConquestStartTime = currentTime;
        
        // Create event data
        EdenEventData eventData = new EdenEventData();
        eventData.SetEventType("conquest");
        eventData.SetEventName("Conquest Event");
        eventData.SetLocation(m_CurrentConquestZone.GetZoneName());
        eventData.SetStartTime(currentTime);
        eventData.SetDuration(m_iConquestDuration);
        eventData.SetReward(m_iConquestPot);
        eventData.SetIsActive(true);
        m_aActiveEvents.Insert(eventData);
        
        // Broadcast event start
        BroadcastEventMessage(string.Format("Conquest event started at %1! Prize pot: $%2", 
            m_CurrentConquestZone.GetZoneName(), 
            FormatMoney(m_iConquestPot)));
            
        Print(string.Format("EdenEventManager: Conquest event started at %1", m_CurrentConquestZone.GetZoneName()));
        
        // Start conquest timer
        GetGame().GetCallqueue().CallLater(EndConquestEvent, m_iConquestDuration * 1000, false);
        
        return true;
    }
    
    //! End conquest event
    void EndConquestEvent()
    {
        if (!m_bConquestActive)
            return;
            
        m_bConquestActive = false;
        m_iLastConquestEvent = GetGame().GetWorld().GetWorldTime();
        
        // Calculate rewards and distribute
        DistributeConquestRewards();
        
        // Clean up
        m_CurrentConquestZone = null;
        
        // Remove from active events
        for (int i = m_aActiveEvents.Count() - 1; i >= 0; i--)
        {
            EdenEventData eventData = m_aActiveEvents[i];
            if (eventData && eventData.GetEventType() == "conquest")
            {
                eventData.SetIsActive(false);
                m_aActiveEvents.RemoveOrdered(i);
                break;
            }
        }
        
        BroadcastEventMessage("Conquest event has ended!");
        Print("EdenEventManager: Conquest event ended");
    }
    
    //! Distribute conquest rewards
    protected void DistributeConquestRewards()
    {
        // TODO: Implement gang scoring and reward distribution
        // This would involve tracking gang participation and distributing rewards based on performance
        Print("EdenEventManager: Distributing conquest rewards (not yet implemented)");
    }
    
    //! Start federal reserve event
    bool StartFederalEvent()
    {
        if (m_bFederalEventActive)
            return false;
            
        // Check cooldown
        int currentTime = GetGame().GetWorld().GetWorldTime();
        if (currentTime - m_iLastFederalEvent < m_iFederalCooldown)
            return false;
            
        m_bFederalEventActive = true;
        
        // Create event data
        EdenEventData eventData = new EdenEventData();
        eventData.SetEventType("federal");
        eventData.SetEventName("Federal Reserve Heist");
        eventData.SetLocation("Federal Reserve");
        eventData.SetStartTime(currentTime);
        eventData.SetDuration(1200); // 20 minutes
        eventData.SetReward(5000000); // $5M base reward
        eventData.SetIsActive(true);
        m_aActiveEvents.Insert(eventData);
        
        BroadcastEventMessage("Federal Reserve is under attack! Respond immediately!");
        Print("EdenEventManager: Federal Reserve event started");
        
        return true;
    }
    
    //! End federal event
    void EndFederalEvent(bool success = false)
    {
        if (!m_bFederalEventActive)
            return;
            
        m_bFederalEventActive = false;
        m_iLastFederalEvent = GetGame().GetWorld().GetWorldTime();
        
        // Remove from active events
        for (int i = m_aActiveEvents.Count() - 1; i >= 0; i--)
        {
            EdenEventData eventData = m_aActiveEvents[i];
            if (eventData && eventData.GetEventType() == "federal")
            {
                eventData.SetIsActive(false);
                m_aActiveEvents.RemoveOrdered(i);
                break;
            }
        }
        
        if (success)
            BroadcastEventMessage("Federal Reserve heist successful!");
        else
            BroadcastEventMessage("Federal Reserve heist was stopped by police!");
            
        Print(string.Format("EdenEventManager: Federal event ended (success: %1)", success));
    }
    
    //! Start airdrop event
    bool StartAirdropEvent(vector location = "0 0 0")
    {
        if (m_bAirdropActive)
            return false;
            
        // Check cooldown
        int currentTime = GetGame().GetWorld().GetWorldTime();
        if (currentTime - m_iLastAirdropEvent < m_iAirdropCooldown)
            return false;
            
        // Generate random location if not specified
        if (location == "0 0 0")
        {
            location = GenerateRandomAirdropLocation();
        }
        
        m_bAirdropActive = true;
        
        // Create event data
        EdenEventData eventData = new EdenEventData();
        eventData.SetEventType("airdrop");
        eventData.SetEventName("Supply Airdrop");
        eventData.SetLocation(string.Format("Grid %1", VectorToGrid(location)));
        eventData.SetStartTime(currentTime);
        eventData.SetDuration(1500); // 25 minutes
        eventData.SetReward(0); // Reward is the loot itself
        eventData.SetIsActive(true);
        m_aActiveEvents.Insert(eventData);
        
        // Spawn airdrop
        SpawnAirdrop(location);
        
        BroadcastEventMessage(string.Format("Supply airdrop incoming at %1!", VectorToGrid(location)));
        Print(string.Format("EdenEventManager: Airdrop event started at %1", location));
        
        // Auto-end after duration
        GetGame().GetCallqueue().CallLater(EndAirdropEvent, 1500 * 1000, false);
        
        return true;
    }
    
    //! End airdrop event
    void EndAirdropEvent()
    {
        if (!m_bAirdropActive)
            return;
            
        m_bAirdropActive = false;
        m_iLastAirdropEvent = GetGame().GetWorld().GetWorldTime();
        
        // Remove from active events
        for (int i = m_aActiveEvents.Count() - 1; i >= 0; i--)
        {
            EdenEventData eventData = m_aActiveEvents[i];
            if (eventData && eventData.GetEventType() == "airdrop")
            {
                eventData.SetIsActive(false);
                m_aActiveEvents.RemoveOrdered(i);
                break;
            }
        }
        
        BroadcastEventMessage("Supply airdrop event has ended!");
        Print("EdenEventManager: Airdrop event ended");
    }
    
    //! Generate random airdrop location
    protected vector GenerateRandomAirdropLocation()
    {
        // Generate random coordinates within map bounds
        float x = Math.RandomFloat(1000, 25000);
        float z = Math.RandomFloat(1000, 25000);
        float y = GetGame().GetWorld().GetSurfaceY(x, z);
        
        return Vector(x, y, z);
    }
    
    //! Spawn airdrop crate
    protected void SpawnAirdrop(vector location)
    {
        // TODO: Implement actual airdrop spawning with parachute and loot crate
        Print(string.Format("EdenEventManager: Spawning airdrop at %1 (not yet implemented)", location));
    }
    
    //! Convert vector to grid reference
    protected string VectorToGrid(vector pos)
    {
        // Simple grid conversion (can be improved)
        int gridX = Math.Floor(pos[0] / 1000);
        int gridZ = Math.Floor(pos[2] / 1000);
        return string.Format("%1-%2", gridX, gridZ);
    }
    
    //! Format money display
    protected string FormatMoney(int amount)
    {
        if (amount >= 1000000)
            return string.Format("%.1fM", amount / 1000000.0);
        else if (amount >= 1000)
            return string.Format("%.1fK", amount / 1000.0);
        else
            return amount.ToString();
    }
    
    //! Broadcast event message
    protected void BroadcastEventMessage(string message)
    {
        // TODO: Implement proper broadcasting to all players
        Print(string.Format("EVENT BROADCAST: %1", message));
    }
    
    //! Get active events
    array<ref EdenEventData> GetActiveEvents()
    {
        return m_aActiveEvents;
    }
    
    //! Check if conquest is active
    bool IsConquestActive() { return m_bConquestActive; }
    
    //! Check if federal event is active
    bool IsFederalEventActive() { return m_bFederalEventActive; }
    
    //! Check if airdrop is active
    bool IsAirdropActive() { return m_bAirdropActive; }
    
    //! Get current conquest zone
    EdenConquestZone GetCurrentConquestZone() { return m_CurrentConquestZone; }
    
    //! Get conquest zones
    array<ref EdenConquestZone> GetConquestZones() { return m_aConquestZones; }
}

//! Event Manager class for easy access
class EdenEventManager
{
    protected static EdenEventManager s_Instance;
    protected EdenEventManagerComponent m_Component;
    
    //! Get singleton instance
    static EdenEventManager GetInstance()
    {
        if (!s_Instance)
            s_Instance = new EdenEventManager();
        return s_Instance;
    }
    
    //! Initialize with component
    void Initialize(EdenEventManagerComponent component)
    {
        m_Component = component;
    }
    
    //! Get component
    EdenEventManagerComponent GetComponent()
    {
        return m_Component;
    }
}
