//! Eden House Dialog System - Handles house interaction UI
//! Converted from original house dialog systems

class EdenHouseDialog
{
    protected int m_CurrentHouseId;
    protected string m_PlayerId;
    protected bool m_IsDialogOpen;
    protected EdenHousingManager m_HousingManager;
    protected EdenHouseInventorySystem m_InventorySystem;
    protected EdenHouseInteractionSystem m_InteractionSystem;
    
    void EdenHouseDialog()
    {
        m_CurrentHouseId = -1;
        m_PlayerId = "";
        m_IsDialogOpen = false;
        
        // Get manager references
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            m_HousingManager = gameMode.GetHousingManager();
            m_InventorySystem = gameMode.GetHouseInventorySystem();
            m_InteractionSystem = gameMode.GetHouseInteractionSystem();
        }
    }
    
    //! Open house purchase dialog
    bool OpenPurchaseDialog(string playerId, int houseId)
    {
        if (m_IsDialogOpen || !m_HousingManager)
            return false;
            
        m_PlayerId = playerId;
        m_CurrentHouseId = houseId;
        m_IsDialogOpen = true;
        
        // Get house configuration
        EdenDataManager dataManager = EdenDataManager.GetInstance();
        EdenHouseConfig houseConfig = dataManager.GetHouseConfig(houseId);
        if (!houseConfig)
            return false;
            
        // Display purchase dialog
        ShowPurchaseDialog(houseConfig);
        
        Print(string.Format("[EdenHouseDialog] Opened purchase dialog for house %1", houseId));
        return true;
    }
    
    //! Open house management dialog
    bool OpenManagementDialog(string playerId, int houseId)
    {
        if (m_IsDialogOpen || !m_HousingManager)
            return false;
            
        // Check if player can access house
        if (!m_HousingManager.CanAccessHouse(playerId, houseId))
            return false;
            
        m_PlayerId = playerId;
        m_CurrentHouseId = houseId;
        m_IsDialogOpen = true;
        
        // Get house data
        EdenDataManager dataManager = EdenDataManager.GetInstance();
        EdenHouseData houseData = dataManager.GetHouseData(houseId);
        if (!houseData)
            return false;
            
        // Display management dialog
        ShowManagementDialog(houseData);
        
        Print(string.Format("[EdenHouseDialog] Opened management dialog for house %1", houseId));
        return true;
    }
    
    //! Open house inventory dialog
    bool OpenInventoryDialog(string playerId, int houseId)
    {
        if (m_IsDialogOpen || !m_InventorySystem)
            return false;
            
        // Check if player can access house
        if (!m_HousingManager.CanAccessHouse(playerId, houseId))
            return false;
            
        m_PlayerId = playerId;
        m_CurrentHouseId = houseId;
        m_IsDialogOpen = true;
        
        // Open house inventory
        bool success = m_InventorySystem.OpenHouseInventory(playerId, houseId);
        if (success)
        {
            ShowInventoryDialog();
        }
        else
        {
            m_IsDialogOpen = false;
        }
        
        Print(string.Format("[EdenHouseDialog] Opened inventory dialog for house %1", houseId));
        return success;
    }
    
    //! Open house upgrade dialog
    bool OpenUpgradeDialog(string playerId, int houseId)
    {
        if (m_IsDialogOpen || !m_HousingManager)
            return false;
            
        // Check if player owns house
        EdenDataManager dataManager = EdenDataManager.GetInstance();
        EdenHouseData houseData = dataManager.GetHouseData(houseId);
        if (!houseData || houseData.GetOwnerId() != playerId)
            return false;
            
        m_PlayerId = playerId;
        m_CurrentHouseId = houseId;
        m_IsDialogOpen = true;
        
        // Display upgrade dialog
        ShowUpgradeDialog(houseData);
        
        Print(string.Format("[EdenHouseDialog] Opened upgrade dialog for house %1", houseId));
        return true;
    }
    
    //! Open house key management dialog
    bool OpenKeyManagementDialog(string playerId, int houseId)
    {
        if (m_IsDialogOpen || !m_HousingManager)
            return false;
            
        // Check if player owns house
        EdenDataManager dataManager = EdenDataManager.GetInstance();
        EdenHouseData houseData = dataManager.GetHouseData(houseId);
        if (!houseData || houseData.GetOwnerId() != playerId)
            return false;
            
        m_PlayerId = playerId;
        m_CurrentHouseId = houseId;
        m_IsDialogOpen = true;
        
        // Display key management dialog
        ShowKeyManagementDialog(houseData);
        
        Print(string.Format("[EdenHouseDialog] Opened key management dialog for house %1", houseId));
        return true;
    }
    
    //! Handle purchase confirmation
    void OnPurchaseConfirmed()
    {
        if (!m_IsDialogOpen || m_CurrentHouseId == -1 || !m_HousingManager)
            return;
            
        bool success = m_HousingManager.PurchaseHouse(m_PlayerId, m_CurrentHouseId);
        
        if (success)
        {
            ShowMessage("House purchased successfully!");
        }
        else
        {
            ShowMessage("Failed to purchase house. Check your funds and try again.");
        }
        
        CloseDialog();
    }
    
    //! Handle house sale
    void OnSellHouse()
    {
        if (!m_IsDialogOpen || m_CurrentHouseId == -1 || !m_HousingManager)
            return;
            
        bool success = m_HousingManager.SellHouse(m_PlayerId, m_CurrentHouseId);
        
        if (success)
        {
            ShowMessage("House sold successfully!");
        }
        else
        {
            ShowMessage("Failed to sell house.");
        }
        
        CloseDialog();
    }
    
    //! Handle storage upgrade
    void OnUpgradeStorage(string upgradeType)
    {
        if (!m_IsDialogOpen || m_CurrentHouseId == -1 || !m_HousingManager)
            return;
            
        bool success = m_HousingManager.UpgradeHouseStorage(m_PlayerId, m_CurrentHouseId, upgradeType);
        
        if (success)
        {
            ShowMessage("Storage upgraded successfully!");
        }
        else
        {
            ShowMessage("Failed to upgrade storage. Check your funds and try again.");
        }
        
        // Refresh dialog
        RefreshUpgradeDialog();
    }
    
    //! Handle key giving
    void OnGiveKeys(string targetPlayerId)
    {
        if (!m_IsDialogOpen || m_CurrentHouseId == -1 || !m_HousingManager)
            return;
            
        bool success = m_HousingManager.GiveHouseKeys(m_PlayerId, m_CurrentHouseId, targetPlayerId);
        
        if (success)
        {
            ShowMessage("Keys given successfully!");
        }
        else
        {
            ShowMessage("Failed to give keys. Check key limit and try again.");
        }
        
        // Refresh dialog
        RefreshKeyManagementDialog();
    }
    
    //! Handle key removal
    void OnRemoveKeys(string targetPlayerId)
    {
        if (!m_IsDialogOpen || m_CurrentHouseId == -1 || !m_HousingManager)
            return;
            
        bool success = m_HousingManager.RemoveHouseKeys(m_PlayerId, m_CurrentHouseId, targetPlayerId);
        
        if (success)
        {
            ShowMessage("Keys removed successfully!");
        }
        else
        {
            ShowMessage("Failed to remove keys.");
        }
        
        // Refresh dialog
        RefreshKeyManagementDialog();
    }
    
    //! Handle tax payment
    void OnPayTax()
    {
        if (!m_IsDialogOpen || m_CurrentHouseId == -1 || !m_HousingManager)
            return;
            
        bool success = m_HousingManager.PayHouseTax(m_PlayerId, m_CurrentHouseId);
        
        if (success)
        {
            ShowMessage("Tax paid successfully!");
        }
        else
        {
            ShowMessage("Failed to pay tax. Check your funds and try again.");
        }
        
        // Refresh dialog
        RefreshManagementDialog();
    }
    
    //! Handle house listing for sale
    void OnListForSale(int salePrice)
    {
        if (!m_IsDialogOpen || m_CurrentHouseId == -1 || !m_HousingManager)
            return;
            
        bool success = m_HousingManager.ListHouseForSale(m_PlayerId, m_CurrentHouseId, salePrice);
        
        if (success)
        {
            ShowMessage("House listed for sale successfully!");
        }
        else
        {
            ShowMessage("Failed to list house for sale.");
        }
        
        CloseDialog();
    }
    
    //! Close dialog
    void CloseDialog()
    {
        if (!m_IsDialogOpen)
            return;
            
        m_IsDialogOpen = false;
        m_CurrentHouseId = -1;
        m_PlayerId = "";
        
        // Close any open inventory sessions
        if (m_InventorySystem)
        {
            m_InventorySystem.CloseHouseInventory(m_PlayerId);
        }
        
        HideDialog();
        
        Print("[EdenHouseDialog] Dialog closed");
    }
    
    //! UI Display methods (placeholder implementations)
    protected void ShowPurchaseDialog(EdenHouseConfig houseConfig)
    {
        // Implementation would show purchase dialog UI
        Print(string.Format("[EdenHouseDialog] Showing purchase dialog for house class %1, price %2", 
            houseConfig.GetHouseClass(), houseConfig.GetPrice()));
    }
    
    protected void ShowManagementDialog(EdenHouseData houseData)
    {
        // Implementation would show management dialog UI
        Print(string.Format("[EdenHouseDialog] Showing management dialog for house %1", houseData.GetHouseId()));
    }
    
    protected void ShowInventoryDialog()
    {
        // Implementation would show inventory dialog UI
        Print("[EdenHouseDialog] Showing inventory dialog");
    }
    
    protected void ShowUpgradeDialog(EdenHouseData houseData)
    {
        // Implementation would show upgrade dialog UI
        Print(string.Format("[EdenHouseDialog] Showing upgrade dialog for house %1", houseData.GetHouseId()));
    }
    
    protected void ShowKeyManagementDialog(EdenHouseData houseData)
    {
        // Implementation would show key management dialog UI
        Print(string.Format("[EdenHouseDialog] Showing key management dialog for house %1", houseData.GetHouseId()));
    }
    
    protected void ShowMessage(string message)
    {
        // Implementation would show message to player
        Print(string.Format("[EdenHouseDialog] Message: %1", message));
    }
    
    protected void HideDialog()
    {
        // Implementation would hide dialog UI
        Print("[EdenHouseDialog] Hiding dialog");
    }
    
    protected void RefreshUpgradeDialog()
    {
        // Implementation would refresh upgrade dialog
        Print("[EdenHouseDialog] Refreshing upgrade dialog");
    }
    
    protected void RefreshKeyManagementDialog()
    {
        // Implementation would refresh key management dialog
        Print("[EdenHouseDialog] Refreshing key management dialog");
    }
    
    protected void RefreshManagementDialog()
    {
        // Implementation would refresh management dialog
        Print("[EdenHouseDialog] Refreshing management dialog");
    }
    
    //! Getters
    bool IsDialogOpen() { return m_IsDialogOpen; }
    int GetCurrentHouseId() { return m_CurrentHouseId; }
    string GetCurrentPlayerId() { return m_PlayerId; }
}
