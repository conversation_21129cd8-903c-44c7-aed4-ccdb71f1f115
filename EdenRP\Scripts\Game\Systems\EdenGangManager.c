//! Eden Gang Manager - Handles all gang systems including territories, wars, and management
//! Converted from original gang systems

class EdenGangManager
{
    protected ref map<int, ref EdenGangData> m_Gangs;
    protected ref map<string, ref EdenGangMemberData> m_GangMembers;
    protected ref map<string, ref EdenTerritoryData> m_Territories;
    protected ref map<string, ref EdenGangBuildingData> m_GangBuildings;
    protected ref map<string, ref EdenGangWarData> m_ActiveWars;
    
    // Gang configuration
    protected int m_GangCreationCost;
    protected int m_MaxGangMembers;
    protected int m_MinMembersForBuilding;
    protected int m_BuildingCost;
    protected int m_TerritoryUpdateInterval;
    
    // Territory system
    protected ref array<string> m_AvailableTerritories;
    protected ref map<string, vector> m_TerritoryPositions;
    protected ref map<string, string> m_TerritoryNames;
    
    void EdenGangManager()
    {
        m_Gangs = new map<int, ref EdenGangData>();
        m_GangMembers = new map<string, ref EdenGangMemberData>();
        m_Territories = new map<string, ref EdenTerritoryData>();
        m_GangBuildings = new map<string, ref EdenGangBuildingData>();
        m_ActiveWars = new map<string, ref EdenGangWarData>();
        
        m_GangCreationCost = 75000;
        m_MaxGangMembers = 15;
        m_MinMembersForBuilding = 8;
        m_BuildingCost = 20000000;
        m_TerritoryUpdateInterval = 300; // 5 minutes
        
        m_AvailableTerritories = new array<string>();
        m_TerritoryPositions = new map<string, vector>();
        m_TerritoryNames = new map<string, string>();
        
        InitializeTerritories();
    }
    
    //! Initialize gang systems
    void Initialize()
    {
        Print("[EdenGangManager] Initializing gang systems...");
        
        LoadGangsFromDatabase();
        LoadTerritoriesFromDatabase();
        LoadGangBuildingsFromDatabase();
        
        // Set up periodic updates
        GetGame().GetCallqueue().CallLater(UpdateTerritories, m_TerritoryUpdateInterval * 1000, true);
        GetGame().GetCallqueue().CallLater(UpdateGangWars, 60000, true); // Every minute
        GetGame().GetCallqueue().CallLater(ProcessGangPayments, 86400000, true); // Daily
        
        Print("[EdenGangManager] Gang systems initialized");
    }
    
    //! Initialize territory system
    protected void InitializeTerritories()
    {
        // Cartel territories
        m_AvailableTerritories.Insert("Meth");
        m_AvailableTerritories.Insert("Moonshine");
        m_AvailableTerritories.Insert("Mushroom");
        m_AvailableTerritories.Insert("Arms");
        
        // Territory display names
        m_TerritoryNames.Set("Meth", "Meth and Weed");
        m_TerritoryNames.Set("Moonshine", "Moonshine and Heroin");
        m_TerritoryNames.Set("Mushroom", "Mushroom and Cocaine");
        m_TerritoryNames.Set("Arms", "Arms Dealer");
        
        // Territory positions (placeholder - should be configured per map)
        m_TerritoryPositions.Set("Meth", "0 0 0");
        m_TerritoryPositions.Set("Moonshine", "100 0 100");
        m_TerritoryPositions.Set("Mushroom", "200 0 200");
        m_TerritoryPositions.Set("Arms", "300 0 300");
        
        Print(string.Format("[EdenGangManager] Initialized %1 territories", m_AvailableTerritories.Count()));
    }
    
    //! Create a new gang
    int CreateGang(string playerId, string gangName)
    {
        if (playerId == "" || gangName == "")
            return -1;
            
        // Check if player is already in a gang
        if (IsPlayerInGang(playerId))
            return -2;
            
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return -3;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return -4;
            
        // Check funds
        if (playerComp.GetBankAccount() < m_GangCreationCost)
            return -5;
            
        // Check gang name availability
        if (IsGangNameTaken(gangName))
            return -6;
            
        // Create gang
        int gangId = GenerateGangId();
        EdenGangData gangData = new EdenGangData();
        gangData.SetGangId(gangId);
        gangData.SetGangName(gangName);
        gangData.SetOwnerId(playerId);
        gangData.SetCreationTime(GetGame().GetWorld().GetWorldTime());
        gangData.SetBankBalance(0);
        
        m_Gangs.Set(gangId, gangData);
        
        // Add creator as leader
        EdenGangMemberData memberData = new EdenGangMemberData();
        memberData.SetPlayerId(playerId);
        memberData.SetGangId(gangId);
        memberData.SetRank(5); // Leader rank
        memberData.SetJoinTime(GetGame().GetWorld().GetWorldTime());
        
        m_GangMembers.Set(playerId, memberData);
        
        // Deduct money
        playerComp.RemoveBankMoney(m_GangCreationCost);
        
        // Save to database
        SaveGangToDatabase(gangData);
        SaveGangMemberToDatabase(memberData);
        
        Print(string.Format("[EdenGangManager] Gang '%1' created by %2 (ID: %3)", gangName, playerId, gangId));
        return gangId;
    }
    
    //! Invite player to gang
    bool InvitePlayerToGang(string inviterId, string targetId, int gangId)
    {
        if (inviterId == "" || targetId == "" || gangId <= 0)
            return false;
            
        // Check if inviter has permission
        if (!CanManageGang(inviterId, gangId))
            return false;
            
        // Check if target is already in a gang
        if (IsPlayerInGang(targetId))
            return false;
            
        // Check gang member limit
        if (GetGangMemberCount(gangId) >= m_MaxGangMembers)
            return false;
            
        // Send invitation (implementation would handle UI)
        SendGangInvitation(targetId, gangId);
        
        Print(string.Format("[EdenGangManager] Player %1 invited %2 to gang %3", inviterId, targetId, gangId));
        return true;
    }
    
    //! Accept gang invitation
    bool AcceptGangInvitation(string playerId, int gangId)
    {
        if (playerId == "" || gangId <= 0)
            return false;
            
        // Check if gang exists
        if (!m_Gangs.Contains(gangId))
            return false;
            
        // Check if player is already in a gang
        if (IsPlayerInGang(playerId))
            return false;
            
        // Add player to gang
        EdenGangMemberData memberData = new EdenGangMemberData();
        memberData.SetPlayerId(playerId);
        memberData.SetGangId(gangId);
        memberData.SetRank(1); // Basic member rank
        memberData.SetJoinTime(GetGame().GetWorld().GetWorldTime());
        
        m_GangMembers.Set(playerId, memberData);
        
        // Save to database
        SaveGangMemberToDatabase(memberData);
        
        Print(string.Format("[EdenGangManager] Player %1 joined gang %2", playerId, gangId));
        return true;
    }
    
    //! Kick player from gang
    bool KickPlayerFromGang(string kickerId, string targetId, int gangId)
    {
        if (kickerId == "" || targetId == "" || gangId <= 0)
            return false;
            
        // Check permissions
        if (!CanManageGang(kickerId, gangId))
            return false;
            
        // Check if target is in the gang
        if (!IsPlayerInGang(targetId) || GetPlayerGangId(targetId) != gangId)
            return false;
            
        // Cannot kick the gang leader
        EdenGangMemberData targetMember = m_GangMembers.Get(targetId);
        if (targetMember.GetRank() == 5)
            return false;
            
        // Remove from gang
        m_GangMembers.Remove(targetId);
        
        // Remove from database
        RemoveGangMemberFromDatabase(targetId);
        
        Print(string.Format("[EdenGangManager] Player %1 kicked %2 from gang %3", kickerId, targetId, gangId));
        return true;
    }
    
    //! Leave gang
    bool LeaveGang(string playerId)
    {
        if (playerId == "")
            return false;
            
        if (!IsPlayerInGang(playerId))
            return false;
            
        EdenGangMemberData memberData = m_GangMembers.Get(playerId);
        int gangId = memberData.GetGangId();
        
        // If leader is leaving, disband gang or transfer leadership
        if (memberData.GetRank() == 5)
        {
            // Try to transfer leadership to highest ranking member
            string newLeader = FindNextLeader(gangId, playerId);
            if (newLeader != "")
            {
                PromoteToLeader(newLeader, gangId);
            }
            else
            {
                // No suitable replacement, disband gang
                DisbandGang(gangId);
                return true;
            }
        }
        
        // Remove from gang
        m_GangMembers.Remove(playerId);
        RemoveGangMemberFromDatabase(playerId);
        
        Print(string.Format("[EdenGangManager] Player %1 left gang %2", playerId, gangId));
        return true;
    }
    
    //! Disband gang
    bool DisbandGang(int gangId)
    {
        if (gangId <= 0 || !m_Gangs.Contains(gangId))
            return false;
            
        EdenGangData gangData = m_Gangs.Get(gangId);
        
        // Remove all members
        array<string> membersToRemove = {};
        foreach (string playerId, EdenGangMemberData memberData : m_GangMembers)
        {
            if (memberData.GetGangId() == gangId)
            {
                membersToRemove.Insert(playerId);
            }
        }
        
        foreach (string playerId : membersToRemove)
        {
            m_GangMembers.Remove(playerId);
            RemoveGangMemberFromDatabase(playerId);
        }
        
        // Remove gang buildings
        RemoveGangBuildings(gangId);
        
        // Remove from territories
        RemoveGangFromTerritories(gangId);
        
        // Remove gang
        m_Gangs.Remove(gangId);
        RemoveGangFromDatabase(gangId);
        
        Print(string.Format("[EdenGangManager] Gang %1 (%2) disbanded", gangData.GetGangName(), gangId));
        return true;
    }
    
    //! Start territory capture
    bool StartTerritoryCapture(string playerId, string territoryName)
    {
        if (playerId == "" || territoryName == "")
            return false;
            
        if (!IsPlayerInGang(playerId))
            return false;
            
        if (!m_Territories.Contains(territoryName))
            return false;
            
        int gangId = GetPlayerGangId(playerId);
        EdenTerritoryData territory = m_Territories.Get(territoryName);
        
        // Check if territory is already being captured
        if (territory.IsBeingCaptured())
            return false;
            
        // Start capture process
        territory.StartCapture(gangId, GetGame().GetWorld().GetWorldTime());
        
        Print(string.Format("[EdenGangManager] Gang %1 started capturing territory %2", gangId, territoryName));
        return true;
    }
    
    //! Update territory capture progress
    void UpdateTerritoryCapture(string territoryName, int gangId, float progress)
    {
        if (!m_Territories.Contains(territoryName))
            return;
            
        EdenTerritoryData territory = m_Territories.Get(territoryName);
        territory.SetCaptureProgress(progress);
        
        // Check if capture is complete
        if (progress >= 1.0)
        {
            territory.SetOwnerGangId(gangId);
            territory.SetCaptureProgress(1.0);
            territory.SetBeingCaptured(false);
            
            Print(string.Format("[EdenGangManager] Gang %1 captured territory %2", gangId, territoryName));
        }
    }
    
    //! Start gang war
    bool StartGangWar(int attackerGangId, int defenderGangId)
    {
        if (attackerGangId <= 0 || defenderGangId <= 0 || attackerGangId == defenderGangId)
            return false;
            
        if (!m_Gangs.Contains(attackerGangId) || !m_Gangs.Contains(defenderGangId))
            return false;
            
        // Check if war already exists
        string warKey = string.Format("%1_%2", attackerGangId, defenderGangId);
        string reverseWarKey = string.Format("%1_%2", defenderGangId, attackerGangId);
        
        if (m_ActiveWars.Contains(warKey) || m_ActiveWars.Contains(reverseWarKey))
            return false;
            
        // Create war data
        EdenGangWarData warData = new EdenGangWarData();
        warData.SetAttackerGangId(attackerGangId);
        warData.SetDefenderGangId(defenderGangId);
        warData.SetStartTime(GetGame().GetWorld().GetWorldTime());
        warData.SetAttackerPoints(0);
        warData.SetDefenderPoints(0);
        
        m_ActiveWars.Set(warKey, warData);
        
        Print(string.Format("[EdenGangManager] Gang war started between %1 and %2", attackerGangId, defenderGangId));
        return true;
    }
    
    //! Award war points
    void AwardWarPoints(string killerId, string victimId, int points)
    {
        if (killerId == "" || victimId == "" || points <= 0)
            return;
            
        if (!IsPlayerInGang(killerId) || !IsPlayerInGang(victimId))
            return;
            
        int killerGangId = GetPlayerGangId(killerId);
        int victimGangId = GetPlayerGangId(victimId);
        
        if (killerGangId == victimGangId)
            return; // Same gang
            
        // Find active war
        string warKey = string.Format("%1_%2", killerGangId, victimGangId);
        string reverseWarKey = string.Format("%1_%2", victimGangId, killerGangId);
        
        EdenGangWarData warData = null;
        bool isAttacker = false;
        
        if (m_ActiveWars.Contains(warKey))
        {
            warData = m_ActiveWars.Get(warKey);
            isAttacker = true;
        }
        else if (m_ActiveWars.Contains(reverseWarKey))
        {
            warData = m_ActiveWars.Get(reverseWarKey);
            isAttacker = false;
        }
        
        if (!warData)
            return; // No active war
            
        // Award points
        if (isAttacker)
        {
            warData.AddAttackerPoints(points);
        }
        else
        {
            warData.AddDefenderPoints(points);
        }
        
        Print(string.Format("[EdenGangManager] Gang %1 awarded %2 war points against gang %3", killerGangId, points, victimGangId));
    }
    
    //! Purchase gang building
    bool PurchaseGangBuilding(string playerId, int gangId, vector position)
    {
        if (playerId == "" || gangId <= 0)
            return false;
            
        // Check permissions
        if (!CanManageGang(playerId, gangId))
            return false;
            
        // Check member count requirement
        if (GetGangMemberCount(gangId) < m_MinMembersForBuilding)
            return false;
            
        // Check if gang already has a building
        if (GangHasBuilding(gangId))
            return false;
            
        EdenGangData gangData = m_Gangs.Get(gangId);
        
        // Check gang bank funds
        if (gangData.GetBankBalance() < m_BuildingCost)
            return false;
            
        // Create building data
        EdenGangBuildingData buildingData = new EdenGangBuildingData();
        buildingData.SetGangId(gangId);
        buildingData.SetPosition(position);
        buildingData.SetOwnerId(playerId);
        buildingData.SetPurchaseTime(GetGame().GetWorld().GetWorldTime());
        buildingData.SetStorageCapacity(1000);
        buildingData.SetPhysicalStorageCapacity(300);
        
        string buildingKey = string.Format("%1_%2", gangId, position.ToString());
        m_GangBuildings.Set(buildingKey, buildingData);
        
        // Deduct from gang bank
        gangData.RemoveBankMoney(m_BuildingCost);
        
        // Save to database
        SaveGangBuildingToDatabase(buildingData);
        UpdateGangInDatabase(gangData);
        
        Print(string.Format("[EdenGangManager] Gang %1 purchased building at %2", gangId, position.ToString()));
        return true;
    }
    
    //! Update periodic systems
    protected void UpdateTerritories()
    {
        // Update territory capture progress and save to database
        foreach (string territoryName, EdenTerritoryData territory : m_Territories)
        {
            if (territory.IsBeingCaptured())
            {
                // Check if capture should continue or timeout
                int currentTime = GetGame().GetWorld().GetWorldTime();
                if (currentTime - territory.GetCaptureStartTime() > 600) // 10 minute timeout
                {
                    territory.SetBeingCaptured(false);
                    territory.SetCaptureProgress(0.0);
                }
            }
        }
    }
    
    protected void UpdateGangWars()
    {
        // Check for expired wars or victory conditions
        array<string> warsToRemove = {};
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        foreach (string warKey, EdenGangWarData warData : m_ActiveWars)
        {
            // Check for victory conditions or timeout
            if (currentTime - warData.GetStartTime() > 86400) // 24 hour limit
            {
                warsToRemove.Insert(warKey);
            }
        }
        
        foreach (string warKey : warsToRemove)
        {
            m_ActiveWars.Remove(warKey);
        }
    }
    
    protected void ProcessGangPayments()
    {
        // Process daily gang building payments
        foreach (string buildingKey, EdenGangBuildingData buildingData : m_GangBuildings)
        {
            int gangId = buildingData.GetGangId();
            if (m_Gangs.Contains(gangId))
            {
                EdenGangData gangData = m_Gangs.Get(gangId);
                // Deduct daily payment (implementation specific)
            }
        }
    }
    
    //! Helper methods
    protected bool IsPlayerInGang(string playerId)
    {
        return m_GangMembers.Contains(playerId);
    }
    
    protected int GetPlayerGangId(string playerId)
    {
        if (m_GangMembers.Contains(playerId))
        {
            return m_GangMembers.Get(playerId).GetGangId();
        }
        return -1;
    }
    
    protected bool CanManageGang(string playerId, int gangId)
    {
        if (!IsPlayerInGang(playerId))
            return false;
            
        EdenGangMemberData memberData = m_GangMembers.Get(playerId);
        return memberData.GetGangId() == gangId && memberData.GetRank() >= 4; // Rank 4+ can manage
    }
    
    protected int GetGangMemberCount(int gangId)
    {
        int count = 0;
        foreach (string playerId, EdenGangMemberData memberData : m_GangMembers)
        {
            if (memberData.GetGangId() == gangId)
                count++;
        }
        return count;
    }
    
    protected bool IsGangNameTaken(string gangName)
    {
        foreach (int gangId, EdenGangData gangData : m_Gangs)
        {
            if (gangData.GetGangName() == gangName)
                return true;
        }
        return false;
    }
    
    protected int GenerateGangId()
    {
        // Generate unique gang ID
        int id = 1;
        while (m_Gangs.Contains(id))
        {
            id++;
        }
        return id;
    }
    
    protected string FindNextLeader(int gangId, string excludePlayerId)
    {
        int highestRank = 0;
        string candidate = "";
        
        foreach (string playerId, EdenGangMemberData memberData : m_GangMembers)
        {
            if (memberData.GetGangId() == gangId && playerId != excludePlayerId)
            {
                if (memberData.GetRank() > highestRank)
                {
                    highestRank = memberData.GetRank();
                    candidate = playerId;
                }
            }
        }
        
        return candidate;
    }
    
    protected void PromoteToLeader(string playerId, int gangId)
    {
        if (m_GangMembers.Contains(playerId))
        {
            EdenGangMemberData memberData = m_GangMembers.Get(playerId);
            memberData.SetRank(5);
            UpdateGangMemberInDatabase(memberData);
        }
    }
    
    protected bool GangHasBuilding(int gangId)
    {
        foreach (string buildingKey, EdenGangBuildingData buildingData : m_GangBuildings)
        {
            if (buildingData.GetGangId() == gangId)
                return true;
        }
        return false;
    }
    
    protected void RemoveGangBuildings(int gangId)
    {
        array<string> toRemove = {};
        foreach (string buildingKey, EdenGangBuildingData buildingData : m_GangBuildings)
        {
            if (buildingData.GetGangId() == gangId)
            {
                toRemove.Insert(buildingKey);
            }
        }
        
        foreach (string buildingKey : toRemove)
        {
            m_GangBuildings.Remove(buildingKey);
        }
    }
    
    protected void RemoveGangFromTerritories(int gangId)
    {
        foreach (string territoryName, EdenTerritoryData territory : m_Territories)
        {
            if (territory.GetOwnerGangId() == gangId)
            {
                territory.SetOwnerGangId(-1);
                territory.SetCaptureProgress(0.5);
            }
        }
    }
    
    //! Database methods (placeholders)
    protected void LoadGangsFromDatabase() { }
    protected void LoadTerritoriesFromDatabase() { }
    protected void LoadGangBuildingsFromDatabase() { }
    protected void SaveGangToDatabase(EdenGangData gangData) { }
    protected void SaveGangMemberToDatabase(EdenGangMemberData memberData) { }
    protected void SaveGangBuildingToDatabase(EdenGangBuildingData buildingData) { }
    protected void UpdateGangInDatabase(EdenGangData gangData) { }
    protected void UpdateGangMemberInDatabase(EdenGangMemberData memberData) { }
    protected void RemoveGangFromDatabase(int gangId) { }
    protected void RemoveGangMemberFromDatabase(string playerId) { }
    protected void SendGangInvitation(string playerId, int gangId) { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    
    //! Public query methods
    EdenGangData GetGangData(int gangId)
    {
        if (m_Gangs.Contains(gangId))
            return m_Gangs.Get(gangId);
        return null;
    }
    
    EdenGangMemberData GetGangMemberData(string playerId)
    {
        if (m_GangMembers.Contains(playerId))
            return m_GangMembers.Get(playerId);
        return null;
    }
    
    array<string> GetAvailableTerritories() { return m_AvailableTerritories; }
    
    EdenTerritoryData GetTerritoryData(string territoryName)
    {
        if (m_Territories.Contains(territoryName))
            return m_Territories.Get(territoryName);
        return null;
    }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenGangManager] Cleaning up gang systems...");
        
        m_Gangs.Clear();
        m_GangMembers.Clear();
        m_Territories.Clear();
        m_GangBuildings.Clear();
        m_ActiveWars.Clear();
    }
}
