//! Market dialog for Eden Reforger
//! Converted from original yMenuMarket.hpp
//! Displays market prices and trends for items

class EdenMarketDialog : EdenBaseDialog
{
    protected Widget m_MarketItemsList;
    protected Widget m_ItemDetailsPanel;
    protected Widget m_SelectedItemLabel;
    protected Widget m_CurrentPriceLabel;
    protected Widget m_PriceChangeLabel;
    protected Widget m_SalesVolumeLabel;
    protected Widget m_LegalityLabel;
    protected Widget m_CategoryComboBox;
    protected Widget m_SortComboBox;
    protected Widget m_RefreshButton;
    protected Widget m_PriceHistoryButton;
    
    protected ref array<ref EdenMarketItem> m_MarketItems;
    protected int m_SelectedItemIndex;
    protected string m_CurrentCategory;
    protected string m_CurrentSortMode;
    
    //! Constructor
    void EdenMarketDialog()
    {
        m_MarketItems = new array<ref EdenMarketItem>();
        m_SelectedItemIndex = -1;
        m_CurrentCategory = "All";
        m_CurrentSortMode = "Name";
    }
    
    //! Initialize the dialog
    override void OnCreate()
    {
        super.OnCreate();
        
        // Get widget references
        m_MarketItemsList = m_Root.FindAnyWidget("MarketItemsList");
        m_ItemDetailsPanel = m_Root.FindAnyWidget("ItemDetailsPanel");
        m_SelectedItemLabel = m_Root.FindAnyWidget("SelectedItemLabel");
        m_CurrentPriceLabel = m_Root.FindAnyWidget("CurrentPriceLabel");
        m_PriceChangeLabel = m_Root.FindAnyWidget("PriceChangeLabel");
        m_SalesVolumeLabel = m_Root.FindAnyWidget("SalesVolumeLabel");
        m_LegalityLabel = m_Root.FindAnyWidget("LegalityLabel");
        m_CategoryComboBox = m_Root.FindAnyWidget("CategoryComboBox");
        m_SortComboBox = m_Root.FindAnyWidget("SortComboBox");
        m_RefreshButton = m_Root.FindAnyWidget("RefreshButton");
        m_PriceHistoryButton = m_Root.FindAnyWidget("PriceHistoryButton");
        
        // Initialize display
        LoadMarketData();
        RefreshMarketItemsList();
        UpdateItemDetails();
    }
    
    //! Get dialog title
    override string GetDialogTitle()
    {
        return "Market";
    }
    
    //! Get active tab ID
    override int GetActiveTabId()
    {
        return 9; // Market tab
    }
    
    //! Load market data
    void LoadMarketData()
    {
        EdenGameMode gameMode = EdenGameMode.Cast(GetGame().GetGameMode());
        if (!gameMode)
            return;
            
        EdenDataManager dataManager = gameMode.GetDataManager();
        if (!dataManager)
            return;
            
        // TODO: Load actual market data from data manager
        // For now, create sample data
        CreateSampleMarketData();
    }
    
    //! Create sample market data for testing
    void CreateSampleMarketData()
    {
        m_MarketItems.Clear();
        
        // Legal items
        CreateMarketItem("Apples", 25, 20, 15, 35, 150, "Legal");
        CreateMarketItem("Peaches", 30, 25, 20, 40, 120, "Legal");
        CreateMarketItem("Copper Ore", 45, 40, 30, 60, 200, "Legal");
        CreateMarketItem("Iron Ore", 65, 60, 45, 85, 180, "Legal");
        CreateMarketItem("Salt", 55, 50, 40, 70, 90, "Legal");
        CreateMarketItem("Oil", 85, 80, 60, 110, 75, "Legal");
        
        // Illegal items
        CreateMarketItem("Cocaine", 2500, 2200, 1800, 3200, 45, "Illegal");
        CreateMarketItem("Heroin", 3200, 2800, 2200, 4000, 35, "Illegal");
        CreateMarketItem("Marijuana", 1800, 1500, 1200, 2400, 65, "Illegal");
        CreateMarketItem("Turtle Soup", 4500, 4000, 3000, 6000, 25, "Illegal");
        CreateMarketItem("Diamonds", 3800, 3500, 2800, 4800, 30, "Illegal");
    }
    
    //! Create a market item with specified parameters
    void CreateMarketItem(string itemName, int currentPrice, int startingPrice, int minPrice, int maxPrice, int salesCount, string legality)
    {
        EdenMarketItem item = new EdenMarketItem();
        item.SetItemName(itemName);
        item.SetCurrentPrice(currentPrice);
        item.SetStartingPrice(startingPrice);
        item.SetMinPrice(minPrice);
        item.SetMaxPrice(maxPrice);
        item.SetSalesCount(salesCount);
        item.SetLastUpdateTime(GetGame().GetWorld().GetWorldTime());
        
        // Calculate price change rate
        float changePercent = item.GetPriceChangePercent();
        item.SetPriceChangeRate(changePercent);
        
        m_MarketItems.Insert(item);
    }
    
    //! Refresh market items list
    void RefreshMarketItemsList()
    {
        if (!m_MarketItemsList)
            return;
            
        // Clear existing items
        Widget child = m_MarketItemsList.GetChildren();
        while (child)
        {
            Widget next = child.GetSibling();
            child.RemoveFromHierarchy();
            child = next;
        }
        
        // Filter and sort items
        ref array<ref EdenMarketItem> filteredItems = FilterAndSortItems();
        
        // Add market items
        for (int i = 0; i < filteredItems.Count(); i++)
        {
            EdenMarketItem item = filteredItems[i];
            if (!item)
                continue;
                
            string itemText = FormatMarketItemDisplay(item, i);
            
            // Create list item widget
            Widget itemWidget = GetGame().GetWorkspace().CreateWidgets("{2EFEA2AF1F38E7F0}UI/layouts/Menus/ContentBrowser/ContentBrowserEntry.layout", m_MarketItemsList);
            if (itemWidget)
            {
                TextWidget itemLabel = TextWidget.Cast(itemWidget.FindAnyWidget("Name"));
                if (itemLabel)
                    itemLabel.SetText(itemText);
            }
        }
    }
    
    //! Filter and sort market items
    ref array<ref EdenMarketItem> FilterAndSortItems()
    {
        ref array<ref EdenMarketItem> filteredItems = new array<ref EdenMarketItem>();
        
        // Filter by category
        for (int i = 0; i < m_MarketItems.Count(); i++)
        {
            EdenMarketItem item = m_MarketItems[i];
            if (!item)
                continue;
                
            // For now, just add all items (category filtering can be implemented later)
            filteredItems.Insert(item);
        }
        
        // Sort items based on current sort mode
        if (m_CurrentSortMode == "Price")
        {
            // TODO: Implement price sorting
        }
        else if (m_CurrentSortMode == "Change")
        {
            // TODO: Implement change sorting
        }
        // Default is name sorting (already in order)
        
        return filteredItems;
    }
    
    //! Format market item display text
    string FormatMarketItemDisplay(EdenMarketItem item, int index)
    {
        string priceChangeText = "";
        float changePercent = item.GetPriceChangePercent();
        
        if (changePercent > 0)
            priceChangeText = string.Format("(+%.1f%%)", changePercent);
        else if (changePercent < 0)
            priceChangeText = string.Format("(%.1f%%)", changePercent);
        else
            priceChangeText = "(0.0%)";
            
        return string.Format("[%1] %2 - $%3 %4", 
            index + 1, 
            item.GetItemName(), 
            item.GetCurrentPrice(), 
            priceChangeText);
    }
    
    //! Update item details panel
    void UpdateItemDetails()
    {
        if (m_SelectedItemIndex < 0 || m_SelectedItemIndex >= m_MarketItems.Count())
        {
            if (m_SelectedItemLabel) m_SelectedItemLabel.SetText("No item selected");
            if (m_CurrentPriceLabel) m_CurrentPriceLabel.SetText("Price: N/A");
            if (m_PriceChangeLabel) m_PriceChangeLabel.SetText("Change: N/A");
            if (m_SalesVolumeLabel) m_SalesVolumeLabel.SetText("Volume: N/A");
            if (m_LegalityLabel) m_LegalityLabel.SetText("Status: N/A");
            return;
        }
        
        EdenMarketItem item = m_MarketItems[m_SelectedItemIndex];
        if (!item)
            return;
            
        if (m_SelectedItemLabel)
            m_SelectedItemLabel.SetText(string.Format("Item: %1", item.GetItemName()));
            
        if (m_CurrentPriceLabel)
            m_CurrentPriceLabel.SetText(string.Format("Current Price: $%1", item.GetCurrentPrice()));
            
        if (m_PriceChangeLabel)
        {
            float changePercent = item.GetPriceChangePercent();
            string changeText = "";
            if (changePercent > 0)
                changeText = string.Format("Price Change: +%.2f%% ↑", changePercent);
            else if (changePercent < 0)
                changeText = string.Format("Price Change: %.2f%% ↓", changePercent);
            else
                changeText = "Price Change: 0.00% →";
                
            m_PriceChangeLabel.SetText(changeText);
        }
        
        if (m_SalesVolumeLabel)
            m_SalesVolumeLabel.SetText(string.Format("Sales Volume: %1 units", item.GetSalesCount()));
            
        if (m_LegalityLabel)
        {
            // Determine legality based on price range (simple heuristic)
            string legalityText = item.GetCurrentPrice() > 1000 ? "Status: Illegal" : "Status: Legal";
            m_LegalityLabel.SetText(legalityText);
        }
    }
    
    //! Handle widget click events
    override bool OnClick(Widget w, int x, int y, int button)
    {
        if (super.OnClick(w, x, y, button))
            return true;
            
        if (w == m_CategoryComboBox)
        {
            OnCategoryChanged();
            return true;
        }
        else if (w == m_SortComboBox)
        {
            OnSortModeChanged();
            return true;
        }
        else if (w == m_RefreshButton)
        {
            OnRefreshButtonClick();
            return true;
        }
        else if (w == m_PriceHistoryButton)
        {
            OnPriceHistoryButtonClick();
            return true;
        }
        
        return false;
    }
    
    //! Handle category change
    void OnCategoryChanged()
    {
        // TODO: Get selected category from combobox
        m_CurrentCategory = "All"; // Default for now
        RefreshMarketItemsList();
    }
    
    //! Handle sort mode change
    void OnSortModeChanged()
    {
        // TODO: Get selected sort mode from combobox
        m_CurrentSortMode = "Name"; // Default for now
        RefreshMarketItemsList();
    }
    
    //! Handle refresh button click
    void OnRefreshButtonClick()
    {
        LoadMarketData();
        RefreshMarketItemsList();
        UpdateItemDetails();
        Print("EdenMarketDialog: Market data refreshed");
    }
    
    //! Handle price history button click
    void OnPriceHistoryButtonClick()
    {
        if (m_SelectedItemIndex >= 0 && m_SelectedItemIndex < m_MarketItems.Count())
        {
            EdenMarketItem item = m_MarketItems[m_SelectedItemIndex];
            Print(string.Format("EdenMarketDialog: Showing price history for %1", item.GetItemName()));
            // TODO: Open price history dialog
        }
        else
        {
            Print("EdenMarketDialog: No item selected for price history");
        }
    }
    
    //! Update dialog content
    override void OnUpdate()
    {
        super.OnUpdate();
        
        // Update market data periodically (every 30 seconds)
        static int lastUpdateTime = 0;
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        if (currentTime - lastUpdateTime > 30)
        {
            // Simulate price fluctuations
            SimulatePriceChanges();
            RefreshMarketItemsList();
            UpdateItemDetails();
            lastUpdateTime = currentTime;
        }
    }
    
    //! Simulate market price changes
    void SimulatePriceChanges()
    {
        for (int i = 0; i < m_MarketItems.Count(); i++)
        {
            EdenMarketItem item = m_MarketItems[i];
            if (!item)
                continue;
                
            // Random price fluctuation (-5% to +5%)
            float changePercent = (Math.RandomFloat01() - 0.5) * 0.1; // -0.05 to +0.05
            int currentPrice = item.GetCurrentPrice();
            int newPrice = Math.Round(currentPrice * (1.0 + changePercent));
            
            // Clamp to min/max bounds
            if (newPrice < item.GetMinPrice())
                newPrice = item.GetMinPrice();
            else if (newPrice > item.GetMaxPrice())
                newPrice = item.GetMaxPrice();
                
            item.SetCurrentPrice(newPrice);
            item.SetLastUpdateTime(GetGame().GetWorld().GetWorldTime());
        }
    }
}
