---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-01-51\console.log started at 2025-07-27 21:01:51 (2025-07-28 01:01:51 UTC)

21:01:51.157 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-01-51' to filesystem under name logs
21:01:51.157 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
21:01:51.159 ENGINE       : Initializing engine, version 184702
21:01:51.159 ENGINE       : CLI Params: -config ..\..\..\server.json -profile ..\..\..\saves -addonsDir addons -logLevel 3 -loadMod EdenRP 
21:01:51.163 ENGINE       : Addon dirs:
21:01:51.163  ENGINE       : dir: 'addons'
21:01:51.163  ENGINE       : dir: './addons'
21:01:51.163  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
21:01:51.163 ENGINE       : Available addons:
21:01:51.163  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
21:01:51.163  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
21:01:51.211 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
21:01:51.213 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
21:01:51.213 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
21:01:51.349 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
21:01:51.350 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
21:01:51.351 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-01-51/resourceDatabase.rdb)
21:01:51.455 ENGINE       : GameProject load
21:01:51.455  ENGINE       : Loaded addons:
21:01:51.455   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
21:01:51.455   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
21:01:51.457 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
21:01:51.457 INIT         : GameProject engine user settings load @"$profile:.save/settings/ReforgerEngineSettings.conf"
21:01:51.457  ENGINE       : Engine user settings config loaded
21:01:51.457 PROFILING    : Settings changed took: 0.001100 ms
21:01:51.457 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
21:01:51.457 RENDER       : Basic screen setup:
21:01:51.457 RENDER       :   Back buffer width x height: 0x0
21:01:51.459 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
21:01:51.459 RENDER       :   MSAA                      : none
21:01:51.459 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
21:01:51.459 RENDER       : Detected output devices:
21:01:51.459 RENDER       :  * 1 device/s connected to Dummy:
21:01:51.459 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
21:01:51.459 RENDER       :   Concurrent frames limit 2
21:01:51.459 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
21:01:51.514 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
21:01:51.519 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
21:01:51.519 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
21:01:51.519 PROFILING    : Settings changed took: 0.020400 ms
21:01:51.523 ENGINE       : Initializing inputs.
21:01:51.528 NETWORK      : Initializing networking.
21:01:51.529 SCRIPT       : SCRIPT       : Initializing scripts
21:01:51.529  SCRIPT       : SCRIPT       : ScriptProjectManager init
21:01:51.529  PROFILING    : ScriptProjectManager init took: 0.029200 ms
21:01:51.547 PROFILING    : Initializing scripts took: 17.932600 ms
21:01:51.547 ENGINE       : Enfusion engine successfully created.
21:01:51.555 GUI          : Using default language (en_us)
21:01:51.555 GUI          : Loading 'en_us' localization file.
21:01:51.555 INIT         : INIT         : Loading StringTable
21:01:51.577 PROFILING    : Loading StringTable took: 21.379900 ms
21:01:51.577 SCRIPT       : SCRIPT       : Compiling GameLib scripts
21:01:51.609  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
21:01:51.613 PROFILING    : Compiling GameLib scripts took: 35.828500 ms
21:01:51.613 SCRIPT       : SCRIPT       : Compiling Game scripts
21:01:52.083 SCRIPT       : Compiling Game scripts
21:01:52.083  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
21:01:52.118  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
21:01:52.315  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:01:52.315  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:01:52.317  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:01:52.331  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
21:01:52.362  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
21:01:52.436  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
21:01:52.493  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:01:52.537  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
21:01:52.538  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
21:01:52.605  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
21:01:52.605  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:01:52.606  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:01:52.606  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:01:52.606  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:01:52.607  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:01:52.645  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:01:52.645  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:01:52.646  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:01:52.646  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:01:52.646  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:01:52.646  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:01:52.659  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:01:52.670  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:01:52.671  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:01:52.671  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:01:52.696  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
21:01:52.696  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
21:01:52.700  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:01:52.700  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:01:52.701  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:01:52.702  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
21:01:52.702  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:01:52.746  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
21:01:53.463 PROFILING    : Compiling Game scripts took: 1850.032400 ms
21:01:53.466 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
21:01:53.480 BACKEND      : Loading dedicated server config.
21:01:53.480  BACKEND      : Server config loaded.
21:01:53.480  BACKEND      : JSON Schema Validation:
21:01:53.480   BACKEND      : JSON is Valid
21:01:53.482 BACKEND      : Loading dedicated server config.
21:01:53.482  RESOURCES    : GetResourceObject @"{F8B2E3A14C5D6E7F}Missions/EdenRP.conf"
21:01:53.482   RESOURCES (E): Failed to open
21:01:53.482  RESOURCES (E): MissionHeader::ReadMissionHeader cannot load the resource 'Missions/EdenRP.conf'!
21:01:53.487 ENGINE       : Game successfully created.
21:01:53.541 PLATFORM     : Save data from container 'settings' have been loaded
21:01:53.641 PLATFORM     : Save data from container 'sessions' have been loaded
21:01:53.643 PLATFORM     : GameProject user settings load from profile @"ReforgerGameSettings.conf"
21:01:53.643  ENGINE       : Game user settings config loaded
21:01:53.643 NETWORK      : Starting dedicated server using command line args.
21:01:53.740 BACKEND      : Required addons are ready to use.
21:01:54.038 PLATFORM     : Save data from container 'settings' have been loaded
21:01:54.138 PLATFORM     : Save data from container 'sessions' have been loaded
21:01:54.138 PLATFORM     : GameProject user settings load from profile @"ReforgerGameSettings.conf"
21:01:54.138  ENGINE       : Game user settings config loaded
21:01:54.138 RESOURCES    : GetResourceObject @"{F8B2E3A14C5D6E7F}Missions/EdenRP.conf"
21:01:54.138  RESOURCES (E): Failed to open
21:01:54.138 RESOURCES (E): MissionHeader::ReadMissionHeader cannot load the resource 'Missions/EdenRP.conf'!
21:01:54.138 ENGINE    (E): Unable to read the mission header '{F8B2E3A14C5D6E7F}Missions/EdenRP.conf'
21:01:54.338 ENGINE    (E): Unable to initialize the game
21:01:54.369 ENGINE       : Game destroyed.
21:01:54.385 RPL          : Pip::Destroy
21:01:54.391 RESOURCES (E): ==== Resource leaks ====
21:01:54.391 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
