//! Eden Medical Manager - Handles all EMS/medical systems
//! Converted from original R&R medical systems

class EdenMedicalManager
{
    protected ref map<string, ref EdenMedicalRequest> m_ActiveRequests;
    protected ref map<string, ref EdenMedicalInvoice> m_ActiveInvoices;
    protected ref map<string, ref EdenDeathData> m_DeadPlayers;
    protected ref map<string, string> m_MedicBuddies; // medic -> buddy pairs
    
    // Configuration
    protected int m_iReviveCost;
    protected int m_iReducedReviveCost;
    protected int m_iMaxDeathTime;
    protected int m_iRespawnLockoutTime;
    protected float m_fMedicPayoutMultiplier;
    protected float m_fBuddyPayoutMultiplier;
    
    // Death system
    protected bool m_bDeathSystemActive;
    protected int m_iPlaytimeThreshold; // 6000 minutes for reduced cost
    
    void EdenMedicalManager()
    {
        m_ActiveRequests = new map<string, ref EdenMedicalRequest>();
        m_ActiveInvoices = new map<string, ref EdenMedicalInvoice>();
        m_DeadPlayers = new map<string, ref EdenDeathData>();
        m_MedicBuddies = new map<string, string>();
        
        // Default configuration values
        m_iReviveCost = 15000; // $15k for experienced players
        m_iReducedReviveCost = 10000; // $10k for new players
        m_iMaxDeathTime = 900; // 15 minutes max death time
        m_iRespawnLockoutTime = 450; // 7.5 minutes respawn lockout
        m_fMedicPayoutMultiplier = 0.75; // 75% of revive cost to medic
        m_fBuddyPayoutMultiplier = 0.5; // 50% split with buddy
        
        m_bDeathSystemActive = true;
        m_iPlaytimeThreshold = 6000; // 100 hours
    }
    
    //! Initialize medical system
    void Initialize()
    {
        Print("[EdenMedicalManager] Initializing medical systems...");
        
        SetupDeathSystem();
        
        // Set up periodic updates
        GetGame().GetCallqueue().CallLater(UpdateMedicalRequests, 30000, true); // Every 30 seconds
        GetGame().GetCallqueue().CallLater(ProcessInvoices, 60000, true); // Every minute
        GetGame().GetCallqueue().CallLater(UpdateDeadPlayers, 5000, true); // Every 5 seconds
        
        Print("[EdenMedicalManager] Medical systems initialized");
    }
    
    //! Set up death system
    protected void SetupDeathSystem()
    {
        Print("[EdenMedicalManager] Setting up death system");
        // Death system setup would be implemented here
    }
    
    //! Handle player death
    void OnPlayerKilled(string playerId, string killerId, string instigatorId)
    {
        if (playerId == "")
            return;
            
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return;
            
        // Create death data
        EdenDeathData deathData = new EdenDeathData();
        deathData.SetPlayerId(playerId);
        deathData.SetKillerId(killerId);
        deathData.SetInstigatorId(instigatorId);
        deathData.SetDeathTime(GetGame().GetWorld().GetWorldTime());
        deathData.SetDeathPosition(playerEntity.GetOrigin());
        deathData.SetMaxReviveTime(GetGame().GetWorld().GetWorldTime() + m_iMaxDeathTime);
        
        m_DeadPlayers.Set(playerId, deathData);
        
        // Set up death screen
        SetupDeathScreen(playerId);
        
        // Update statistics
        UpdateDeathStatistics(playerId, killerId, instigatorId);
        
        Print(string.Format("[EdenMedicalManager] Player %1 killed by %2", playerId, killerId));
    }
    
    //! Request medical assistance
    bool RequestMedic(string playerId)
    {
        if (!IsPlayerDead(playerId))
            return false;
            
        // Check if player can afford medical services
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player is arrested (prisoners can't request medics)
        EdenDeathData deathData = m_DeadPlayers.Get(playerId);
        if (deathData && deathData.IsArrested())
            return false;
            
        // Calculate revive cost based on playtime
        int reviveCost = GetReviveCost(playerComp);
        if (playerComp.GetBankAccount() < reviveCost)
            return false;
            
        // Check if already has active request
        if (m_ActiveRequests.Contains(playerId))
            return false;
            
        // Check if medics are online
        if (!AreMedicsOnline())
            return false;
            
        // Create medical request
        EdenMedicalRequest request = new EdenMedicalRequest();
        request.SetPlayerId(playerId);
        request.SetRequestTime(GetGame().GetWorld().GetWorldTime());
        request.SetReviveCost(reviveCost);
        
        m_ActiveRequests.Set(playerId, request);
        
        // Set respawn lockout
        if (deathData)
        {
            deathData.SetRespawnLockoutTime(GetGame().GetWorld().GetWorldTime() + m_iRespawnLockoutTime);
        }
        
        // Notify medics
        NotifyMedicsOfRequest(playerId);
        
        Print(string.Format("[EdenMedicalManager] Medical request from %1 (Cost: $%2)", playerId, reviveCost));
        return true;
    }
    
    //! Revive a player
    bool RevivePlayer(string targetId, string medicId, bool isAdminRevive = false)
    {
        if (!IsPlayerDead(targetId))
            return false;
            
        IEntity targetEntity = GetPlayerEntity(targetId);
        IEntity medicEntity = GetPlayerEntity(medicId);
        
        if (!targetEntity || (!medicEntity && !isAdminRevive))
            return false;
            
        // Check distance for non-admin revives
        if (!isAdminRevive && vector.Distance(targetEntity.GetOrigin(), medicEntity.GetOrigin()) > 5.0)
            return false;
            
        // Check if medic has permission
        if (!isAdminRevive)
        {
            EdenPlayerComponent medicComp = EdenPlayerComponent.Cast(medicEntity.FindComponent(EdenPlayerComponent));
            if (!medicComp || medicComp.GetCurrentFaction() != "medic")
                return false;
        }
        
        // Get death data
        EdenDeathData deathData = m_DeadPlayers.Get(targetId);
        if (!deathData)
            return false;
            
        // Calculate costs and payments
        int reviveCost = 0;
        int medicPayout = 0;
        
        if (!isAdminRevive)
        {
            EdenPlayerComponent targetComp = EdenPlayerComponent.Cast(targetEntity.FindComponent(EdenPlayerComponent));
            if (!targetComp)
                return false;
                
            reviveCost = GetReviveCost(targetComp);
            
            // Check if player can afford it
            if (targetComp.GetBankAccount() < reviveCost)
                return false;
                
            // Charge patient
            targetComp.RemoveBankMoney(reviveCost);
            
            // Calculate medic payout
            medicPayout = Math.Round(reviveCost * m_fMedicPayoutMultiplier);
            
            // Check for federal event penalty
            if (IsInFederalEventArea(targetEntity.GetOrigin()))
            {
                medicPayout = Math.Round(medicPayout * 0.5);
            }
            
            // Pay medic
            EdenPlayerComponent medicComp = EdenPlayerComponent.Cast(medicEntity.FindComponent(EdenPlayerComponent));
            if (medicComp)
            {
                // Check for buddy system
                if (m_MedicBuddies.Contains(medicId))
                {
                    string buddyId = m_MedicBuddies.Get(medicId);
                    IEntity buddyEntity = GetPlayerEntity(buddyId);
                    
                    if (buddyEntity)
                    {
                        EdenPlayerComponent buddyComp = EdenPlayerComponent.Cast(buddyEntity.FindComponent(EdenPlayerComponent));
                        if (buddyComp && vector.Distance(medicEntity.GetOrigin(), buddyEntity.GetOrigin()) < 100.0)
                        {
                            // Split payment with buddy
                            int buddyPayout = Math.Round(medicPayout * m_fBuddyPayoutMultiplier);
                            medicPayout = Math.Round(medicPayout * m_fBuddyPayoutMultiplier);
                            
                            medicComp.AddBankMoney(medicPayout);
                            buddyComp.AddBankMoney(buddyPayout);
                        }
                        else
                        {
                            // Buddy too far, give full payment to medic
                            medicComp.AddBankMoney(medicPayout);
                        }
                    }
                    else
                    {
                        // Buddy not found, give full payment to medic
                        medicComp.AddBankMoney(medicPayout);
                    }
                }
                else
                {
                    // No buddy, give full payment to medic
                    medicComp.AddBankMoney(medicPayout);
                }
                
                // Update medic statistics
                medicComp.AddStat(0, 1); // Revives stat
            }
        }
        
        // Perform revive
        PerformRevive(targetEntity, deathData);
        
        // Remove from dead players and active requests
        m_DeadPlayers.Remove(targetId);
        m_ActiveRequests.Remove(targetId);
        
        // Notify players
        if (isAdminRevive)
        {
            NotifyAdminRevive(targetId, medicId);
        }
        else
        {
            NotifyRevive(targetId, medicId, reviveCost, medicPayout);
        }
        
        Print(string.Format("[EdenMedicalManager] Player %1 revived by %2 (Cost: $%3, Payout: $%4)", 
            targetId, medicId, reviveCost, medicPayout));
        return true;
    }
    
    //! Issue medical invoice
    void IssueMedicalInvoice(string patientId, string medicId, int amount)
    {
        if (patientId == "" || medicId == "" || amount <= 0)
            return;
            
        EdenMedicalInvoice invoice = new EdenMedicalInvoice();
        invoice.SetPatientId(patientId);
        invoice.SetMedicId(medicId);
        invoice.SetAmount(amount);
        invoice.SetTimestamp(GetGame().GetWorld().GetWorldTime());
        
        string invoiceId = string.Format("%1_%2", patientId, invoice.GetTimestamp());
        m_ActiveInvoices.Set(invoiceId, invoice);
        
        // Notify patient about invoice
        NotifyPatientInvoice(patientId, invoice);
        
        Print(string.Format("[EdenMedicalManager] Medical invoice issued to %1: $%2", patientId, amount));
    }
    
    //! Pay medical invoice
    bool PayMedicalInvoice(string patientId, string invoiceId)
    {
        if (!m_ActiveInvoices.Contains(invoiceId))
            return false;
            
        EdenMedicalInvoice invoice = m_ActiveInvoices.Get(invoiceId);
        if (invoice.GetPatientId() != patientId)
            return false;
            
        // Get patient component
        IEntity patientEntity = GetPlayerEntity(patientId);
        if (!patientEntity)
            return false;
            
        EdenPlayerComponent patientComp = EdenPlayerComponent.Cast(patientEntity.FindComponent(EdenPlayerComponent));
        if (!patientComp)
            return false;
            
        // Check if patient has enough money
        int totalMoney = patientComp.GetCash() + patientComp.GetBankAccount();
        if (totalMoney < invoice.GetAmount())
            return false;
            
        // Deduct money (prefer bank account)
        if (patientComp.GetBankAccount() >= invoice.GetAmount())
        {
            patientComp.RemoveBankMoney(invoice.GetAmount());
        }
        else
        {
            int bankAmount = patientComp.GetBankAccount();
            int cashAmount = invoice.GetAmount() - bankAmount;
            patientComp.SetBankAccount(0);
            patientComp.RemoveCash(cashAmount);
        }
        
        // Pay medic
        IEntity medicEntity = GetPlayerEntity(invoice.GetMedicId());
        if (medicEntity)
        {
            EdenPlayerComponent medicComp = EdenPlayerComponent.Cast(medicEntity.FindComponent(EdenPlayerComponent));
            if (medicComp)
            {
                medicComp.AddBankMoney(invoice.GetAmount());
            }
        }
        
        // Remove invoice
        m_ActiveInvoices.Remove(invoiceId);
        
        Print(string.Format("[EdenMedicalManager] Patient %1 paid medical invoice: $%2", patientId, invoice.GetAmount()));
        return true;
    }
    
    //! Set medic buddy
    void SetMedicBuddy(string medicId, string buddyId)
    {
        if (medicId == "" || buddyId == "")
            return;
            
        // Remove existing buddy relationships
        if (m_MedicBuddies.Contains(medicId))
        {
            m_MedicBuddies.Remove(medicId);
        }
        
        // Set new buddy
        m_MedicBuddies.Set(medicId, buddyId);
        
        Print(string.Format("[EdenMedicalManager] Medic %1 set buddy to %2", medicId, buddyId));
    }
    
    //! Remove medic buddy
    void RemoveMedicBuddy(string medicId)
    {
        if (m_MedicBuddies.Contains(medicId))
        {
            m_MedicBuddies.Remove(medicId);
            Print(string.Format("[EdenMedicalManager] Removed buddy for medic %1", medicId));
        }
    }
    
    //! Handle player respawn
    void OnPlayerRespawn(string playerId)
    {
        // Remove from dead players
        if (m_DeadPlayers.Contains(playerId))
        {
            m_DeadPlayers.Remove(playerId);
        }
        
        // Remove active medical request
        if (m_ActiveRequests.Contains(playerId))
        {
            m_ActiveRequests.Remove(playerId);
        }
        
        Print(string.Format("[EdenMedicalManager] Player %1 respawned", playerId));
    }
    
    //! Update medical requests
    protected void UpdateMedicalRequests()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        array<string> expiredRequests = {};
        
        foreach (string playerId, EdenMedicalRequest request : m_ActiveRequests)
        {
            // Remove expired requests (15 minutes)
            if (currentTime - request.GetRequestTime() > 900)
            {
                expiredRequests.Insert(playerId);
            }
        }
        
        foreach (string playerId : expiredRequests)
        {
            m_ActiveRequests.Remove(playerId);
        }
    }
    
    //! Process medical invoices
    protected void ProcessInvoices()
    {
        // Remove expired invoices (24 hours)
        int currentTime = GetGame().GetWorld().GetWorldTime();
        array<string> expiredInvoices = {};
        
        foreach (string invoiceId, EdenMedicalInvoice invoice : m_ActiveInvoices)
        {
            if (currentTime - invoice.GetTimestamp() > 86400) // 24 hours
            {
                expiredInvoices.Insert(invoiceId);
            }
        }
        
        foreach (string invoiceId : expiredInvoices)
        {
            m_ActiveInvoices.Remove(invoiceId);
        }
    }
    
    //! Update dead players
    protected void UpdateDeadPlayers()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        array<string> expiredDeaths = {};
        
        foreach (string playerId, EdenDeathData deathData : m_DeadPlayers)
        {
            // Check if max death time exceeded
            if (currentTime > deathData.GetMaxReviveTime())
            {
                // Force respawn
                ForcePlayerRespawn(playerId);
                expiredDeaths.Insert(playerId);
            }
        }
        
        foreach (string playerId : expiredDeaths)
        {
            m_DeadPlayers.Remove(playerId);
            m_ActiveRequests.Remove(playerId);
        }
    }
    
    //! Helper methods
    protected IEntity GetPlayerEntity(string playerId)
    {
        // Implementation to get player entity by ID
        return null; // Placeholder
    }
    
    protected int GetReviveCost(EdenPlayerComponent playerComp)
    {
        // Check playtime to determine cost
        int playtime = playerComp.GetStat(1); // Assuming stat index 1 is playtime
        return (playtime > m_iPlaytimeThreshold) ? m_iReviveCost : m_iReducedReviveCost;
    }
    
    protected bool AreMedicsOnline()
    {
        // Check if any medics are online
        return true; // Placeholder
    }
    
    protected bool IsInFederalEventArea(vector position)
    {
        // Check if position is in federal event area
        return false; // Placeholder
    }
    
    protected void SetupDeathScreen(string playerId)
    {
        // Set up death screen UI for player
    }
    
    protected void PerformRevive(IEntity playerEntity, EdenDeathData deathData)
    {
        // Perform the actual revive process
    }
    
    protected void ForcePlayerRespawn(string playerId)
    {
        // Force player to respawn
    }
    
    protected void UpdateDeathStatistics(string playerId, string killerId, string instigatorId)
    {
        // Update death/kill statistics
    }
    
    protected void NotifyMedicsOfRequest(string playerId)
    {
        // Notify all online medics of the request
    }
    
    protected void NotifyRevive(string targetId, string medicId, int cost, int payout)
    {
        // Notify players about successful revive
    }
    
    protected void NotifyAdminRevive(string targetId, string adminId)
    {
        // Notify about admin revive
    }
    
    protected void NotifyPatientInvoice(string patientId, EdenMedicalInvoice invoice)
    {
        // Notify patient about medical invoice
    }
    
    //! Public query methods
    bool IsPlayerDead(string playerId)
    {
        return m_DeadPlayers.Contains(playerId);
    }
    
    bool HasActiveRequest(string playerId)
    {
        return m_ActiveRequests.Contains(playerId);
    }
    
    EdenDeathData GetDeathData(string playerId)
    {
        if (m_DeadPlayers.Contains(playerId))
            return m_DeadPlayers.Get(playerId);
        return null;
    }
    
    string GetMedicBuddy(string medicId)
    {
        if (m_MedicBuddies.Contains(medicId))
            return m_MedicBuddies.Get(medicId);
        return "";
    }
    
    //! Server ready callback
    void OnServerReady()
    {
        Print("[EdenMedicalManager] Server ready - Medical systems active");
    }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenMedicalManager] Cleaning up medical systems...");
        
        // Revive all dead players
        foreach (string playerId, EdenDeathData deathData : m_DeadPlayers)
        {
            ForcePlayerRespawn(playerId);
        }
        
        m_ActiveRequests.Clear();
        m_ActiveInvoices.Clear();
        m_DeadPlayers.Clear();
        m_MedicBuddies.Clear();
    }
    
    //! Getters
    map<string, ref EdenMedicalRequest> GetActiveRequests() { return m_ActiveRequests; }
    map<string, ref EdenMedicalInvoice> GetActiveInvoices() { return m_ActiveInvoices; }
    bool IsDeathSystemActive() { return m_bDeathSystemActive; }
}
