//! Eden Base Dialog
//! Base class for all Eden UI dialogs
//! Provides common functionality and tab system integration

class EdenBaseDialog
{
    protected Widget m_RootWidget;
    protected Widget m_BackgroundWidget;
    protected TextWidget m_TitleWidget;
    protected ref array<ButtonWidget> m_TabButtons;
    protected string m_PlayerId;
    protected bool m_IsInitialized;
    
    // Tab button references
    protected ButtonWidget m_Tab1Button;
    protected ButtonWidget m_Tab2Button;
    protected ButtonWidget m_Tab3Button;
    protected ButtonWidget m_Tab4Button;
    protected ButtonWidget m_Tab5Button;
    protected ButtonWidget m_Tab6Button;
    protected ButtonWidget m_Tab7Button;
    protected ButtonWidget m_Tab8Button;
    protected ButtonWidget m_Tab9Button;
    protected ButtonWidget m_Tab10Button;
    protected ButtonWidget m_Tab11Button;
    protected ButtonWidget m_Tab12Button;
    protected ButtonWidget m_CloseButton;
    
    void EdenBaseDialog()
    {
        m_RootWidget = null;
        m_BackgroundWidget = null;
        m_TitleWidget = null;
        m_TabButtons = new array<ButtonWidget>();
        m_PlayerId = "";
        m_IsInitialized = false;
    }
    
    //! Called when dialog is created
    void OnCreate(string playerId, string parameter = "")
    {
        m_PlayerId = playerId;
        
        CreateRootWidget();
        InitializeBaseWidgets();
        SetupTabButtons();
        UpdateTabStates();
        
        m_IsInitialized = true;
        
        Print("[EdenBaseDialog] Base dialog created for player: " + playerId);
    }
    
    //! Create the root widget for this dialog
    protected void CreateRootWidget()
    {
        // This would create the actual widget hierarchy
        // For now, create a placeholder
        Print("[EdenBaseDialog] Creating root widget for dialog");
        
        // In a full implementation, this would use the Reforger UI system
        // to create the widget hierarchy based on layout files
    }
    
    //! Initialize base widgets common to all dialogs
    protected void InitializeBaseWidgets()
    {
        if (!m_RootWidget)
            return;
        
        // Find background widget
        m_BackgroundWidget = m_RootWidget.FindAnyWidget("Background");
        if (!m_BackgroundWidget)
        {
            Print("[EdenBaseDialog] Warning: Background widget not found");
        }
        
        // Find title widget
        m_TitleWidget = TextWidget.Cast(m_RootWidget.FindAnyWidget("Title"));
        if (m_TitleWidget)
        {
            m_TitleWidget.SetText(GetDialogTitle());
        }
        else
        {
            Print("[EdenBaseDialog] Warning: Title widget not found");
        }
    }
    
    //! Setup tab button references and handlers
    protected void SetupTabButtons()
    {
        if (!m_RootWidget)
            return;
        
        // Find all tab buttons
        m_Tab1Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab1"));
        m_Tab2Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab2"));
        m_Tab3Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab3"));
        m_Tab4Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab4"));
        m_Tab5Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab5"));
        m_Tab6Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab6"));
        m_Tab7Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab7"));
        m_Tab8Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab8"));
        m_Tab9Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab9"));
        m_Tab10Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab10"));
        m_Tab11Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab11"));
        m_Tab12Button = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("Tab12"));
        
        // Find close button
        m_CloseButton = ButtonWidget.Cast(m_RootWidget.FindAnyWidget("CloseButton"));
        
        // Add buttons to array for easier management
        m_TabButtons.Insert(m_Tab1Button);
        m_TabButtons.Insert(m_Tab2Button);
        m_TabButtons.Insert(m_Tab3Button);
        m_TabButtons.Insert(m_Tab4Button);
        m_TabButtons.Insert(m_Tab5Button);
        m_TabButtons.Insert(m_Tab6Button);
        m_TabButtons.Insert(m_Tab7Button);
        m_TabButtons.Insert(m_Tab8Button);
        m_TabButtons.Insert(m_Tab9Button);
        m_TabButtons.Insert(m_Tab10Button);
        m_TabButtons.Insert(m_Tab11Button);
        m_TabButtons.Insert(m_Tab12Button);
    }
    
    //! Update tab button states based on current dialog
    protected void UpdateTabStates()
    {
        int activeTabId = GetActiveTabId();
        
        for (int i = 0; i < m_TabButtons.Count(); i++)
        {
            ButtonWidget tabButton = m_TabButtons[i];
            if (!tabButton)
                continue;
            
            int tabId = i + 1;
            
            // Set active state for current tab
            if (tabId == activeTabId)
            {
                SetTabButtonActive(tabButton, true);
            }
            else
            {
                SetTabButtonActive(tabButton, false);
            }
            
            // Check if tab should be visible for this player
            if (!IsTabAvailableForPlayer(tabId, m_PlayerId))
            {
                tabButton.SetVisible(false);
            }
        }
    }
    
    //! Set tab button active state
    protected void SetTabButtonActive(ButtonWidget button, bool active)
    {
        if (!button)
            return;
        
        // This would set the visual state of the button
        // In a full implementation, this would change colors, etc.
        button.SetEnabled(!active); // Disable active tab button
    }
    
    //! Check if tab is available for player
    protected bool IsTabAvailableForPlayer(int tabId, string playerId)
    {
        // Tab 12 (Admin) requires admin privileges
        if (tabId == 12)
        {
            return IsPlayerAdmin(playerId);
        }
        
        return true;
    }
    
    //! Check if player is admin
    protected bool IsPlayerAdmin(string playerId)
    {
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenAdminManager adminManager = gameMode.GetAdminManager();
            if (adminManager)
            {
                return adminManager.IsAdmin(playerId);
            }
        }
        
        return false;
    }
    
    //! Handle tab button click
    void OnTabButtonClick(int tabId)
    {
        if (tabId == GetActiveTabId())
        {
            // Already on this tab
            return;
        }
        
        EdenUIManager uiManager = GetUIManager();
        if (uiManager)
        {
            uiManager.SwitchTab(tabId, m_PlayerId);
        }
        
        Print("[EdenBaseDialog] Tab button clicked: " + tabId);
    }
    
    //! Handle close button click
    void OnCloseButtonClick()
    {
        EdenUIManager uiManager = GetUIManager();
        if (uiManager)
        {
            uiManager.CloseAllDialogs();
        }
        
        Print("[EdenBaseDialog] Close button clicked");
    }
    
    //! Get UI manager instance
    protected EdenUIManager GetUIManager()
    {
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            return gameMode.GetUIManager();
        }
        
        return null;
    }
    
    //! Virtual methods to be overridden by derived classes
    
    //! Get the title for this dialog
    string GetDialogTitle()
    {
        return "Eden Dialog";
    }
    
    //! Get the active tab ID for this dialog
    int GetActiveTabId()
    {
        return 1;
    }
    
    //! Called every frame while dialog is open
    void OnUpdate()
    {
        // Override in derived classes for custom update logic
    }
    
    //! Called when dialog is closed
    void OnClose()
    {
        m_IsInitialized = false;
        
        Print("[EdenBaseDialog] Base dialog closed for player: " + m_PlayerId);
    }
    
    //! Getters
    Widget GetRootWidget() { return m_RootWidget; }
    string GetPlayerId() { return m_PlayerId; }
    bool IsInitialized() { return m_IsInitialized; }
    
    //! Handle widget events
    bool OnClick(Widget w, int x, int y, int button)
    {
        // Handle tab button clicks
        for (int i = 0; i < m_TabButtons.Count(); i++)
        {
            if (w == m_TabButtons[i])
            {
                OnTabButtonClick(i + 1);
                return true;
            }
        }
        
        // Handle close button click
        if (w == m_CloseButton)
        {
            OnCloseButtonClick();
            return true;
        }
        
        return false;
    }
    
    //! Handle key events
    bool OnKeyDown(Widget w, int x, int y, int key)
    {
        // ESC key closes dialog
        if (key == KeyCode.KC_ESCAPE)
        {
            OnCloseButtonClick();
            return true;
        }
        
        return false;
    }
    
    //! Cleanup
    void Cleanup()
    {
        if (m_RootWidget)
        {
            m_RootWidget.RemoveFromHierarchy();
            m_RootWidget = null;
        }
        
        m_TabButtons.Clear();
        
        Print("[EdenBaseDialog] Dialog cleaned up");
    }
}
