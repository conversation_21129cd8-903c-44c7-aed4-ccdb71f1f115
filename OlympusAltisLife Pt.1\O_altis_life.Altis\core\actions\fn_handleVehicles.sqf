//	File: fn_handleVehicles.sqf
//	Author: Fusah
//	Description: <PERSON><PERSON> opening of player garages.

if(oev_garageCooldown > time) exitWith {hint "Please do not spam your garage. It may take a bit to show your vehicles if the server is under heavy load.";};

params ["_type","_spawn"];

[[getPlayerUID player,playerSide,_type,player],"OES_fnc_getVehicles",false,false] spawn OEC_fnc_MP;
["Life_impound_menu"] call OEC_fnc_createDialog;
disableSerialization;
ctrlSetText[2802,"Fetching Vehicles...."];
oev_garage_sp = _spawn;
oev_garage_type = _type;
oev_garageCooldown = time+5;