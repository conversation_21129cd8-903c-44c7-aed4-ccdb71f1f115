//! Supporting data structures for Eden Reforger framework

//! Player gear loadout structure
class EdenGearLoadout
{
    protected ref array<string> m_Weapons;
    protected ref array<string> m_Items;
    protected ref array<string> m_Magazines;
    protected string m_Uniform;
    protected string m_Vest;
    protected string m_Headgear;
    protected string m_Backpack;
    protected string m_Goggles;
    
    void EdenGearLoadout()
    {
        m_Weapons = new array<string>();
        m_Items = new array<string>();
        m_Magazines = new array<string>();
        m_Uniform = "";
        m_Vest = "";
        m_Headgear = "";
        m_Backpack = "";
        m_Goggles = "";
    }
    
    // Getters and setters
    array<string> GetWeapons() { return m_Weapons; }
    array<string> GetItems() { return m_Items; }
    array<string> GetMagazines() { return m_Magazines; }
    
    string GetUniform() { return m_Uniform; }
    void SetUniform(string uniform) { m_Uniform = uniform; }
    
    string GetVest() { return m_Vest; }
    void SetVest(string vest) { m_Vest = vest; }
    
    string GetHeadgear() { return m_Headgear; }
    void SetHeadgear(string headgear) { m_Headgear = headgear; }
    
    string GetBackpack() { return m_Backpack; }
    void SetBackpack(string backpack) { m_Backpack = backpack; }
    
    string GetGoggles() { return m_Goggles; }
    void SetGoggles(string goggles) { m_Goggles = goggles; }
    
    void AddWeapon(string weapon) { m_Weapons.Insert(weapon); }
    void AddItem(string item) { m_Items.Insert(item); }
    void AddMagazine(string magazine) { m_Magazines.Insert(magazine); }
    
    void Clear()
    {
        m_Weapons.Clear();
        m_Items.Clear();
        m_Magazines.Clear();
        m_Uniform = "";
        m_Vest = "";
        m_Headgear = "";
        m_Backpack = "";
        m_Goggles = "";
    }
}

//! Player position data
class EdenPlayerPosition
{
    protected vector m_Position;
    protected vector m_Direction;
    
    void EdenPlayerPosition()
    {
        m_Position = "0 0 0";
        m_Direction = "0 0 0";
    }
    
    vector GetPosition() { return m_Position; }
    void SetPosition(vector pos) { m_Position = pos; }
    
    vector GetDirection() { return m_Direction; }
    void SetDirection(vector dir) { m_Direction = dir; }
}

//! Wanted system entry
class EdenWantedEntry
{
    protected string m_Crime;
    protected int m_Bounty;
    protected string m_IssuedBy;
    protected int m_Timestamp;
    
    void EdenWantedEntry(string crime, int bounty, string issuedBy)
    {
        m_Crime = crime;
        m_Bounty = bounty;
        m_IssuedBy = issuedBy;
        m_Timestamp = GetGame().GetWorld().GetWorldTime();
    }
    
    string GetCrime() { return m_Crime; }
    int GetBounty() { return m_Bounty; }
    string GetIssuedBy() { return m_IssuedBy; }
    int GetTimestamp() { return m_Timestamp; }
}

//! Vehicle data structure
class EdenVehicleData
{
    protected int m_Id;
    protected string m_Side;
    protected string m_ClassName;
    protected string m_Type;
    protected string m_OwnerId;
    protected bool m_IsAlive;
    protected bool m_IsActive;
    protected int m_PlateNumber;
    protected ref array<int> m_Color;
    protected ref array<ref EdenInventoryItem> m_Inventory;
    protected bool m_IsInsured;
    protected ref array<string> m_Modifications;
    protected bool m_IsPersistent;
    protected vector m_PersistentPosition;
    protected float m_PersistentDirection;
    protected bool m_InAuctionHouse;
    protected string m_CustomName;
    
    void EdenVehicleData()
    {
        m_Color = new array<int>();
        m_Inventory = new array<ref EdenInventoryItem>();
        m_Modifications = new array<string>();
        m_IsAlive = true;
        m_IsActive = false;
        m_IsInsured = false;
        m_IsPersistent = false;
        m_InAuctionHouse = false;
        m_PersistentPosition = "0 0 0";
        m_PersistentDirection = 0.0;
    }
    
    // Getters and setters
    int GetId() { return m_Id; }
    void SetId(int id) { m_Id = id; }
    
    string GetSide() { return m_Side; }
    void SetSide(string side) { m_Side = side; }
    
    string GetClassName() { return m_ClassName; }
    void SetClassName(string className) { m_ClassName = className; }
    
    string GetOwnerId() { return m_OwnerId; }
    void SetOwnerId(string ownerId) { m_OwnerId = ownerId; }
    
    bool IsAlive() { return m_IsAlive; }
    void SetAlive(bool alive) { m_IsAlive = alive; }
    
    bool IsActive() { return m_IsActive; }
    void SetActive(bool active) { m_IsActive = active; }
    
    int GetPlateNumber() { return m_PlateNumber; }
    void SetPlateNumber(int plate) { m_PlateNumber = plate; }
    
    array<int> GetColor() { return m_Color; }
    void SetColor(array<int> color) { m_Color = color; }
    
    array<ref EdenInventoryItem> GetInventory() { return m_Inventory; }
    
    bool IsInsured() { return m_IsInsured; }
    void SetInsured(bool insured) { m_IsInsured = insured; }
    
    string GetCustomName() { return m_CustomName; }
    void SetCustomName(string name) { m_CustomName = name; }
}

//! Gang data structure
class EdenGangData
{
    protected int m_Id;
    protected string m_Name;
    protected int m_Bank;
    protected bool m_IsActive;
    protected int m_Kills;
    protected int m_Deaths;
    protected ref array<string> m_Members;
    protected ref array<string> m_Officers;
    protected string m_Leader;
    
    void EdenGangData()
    {
        m_Members = new array<string>();
        m_Officers = new array<string>();
        m_Bank = 0;
        m_IsActive = true;
        m_Kills = 0;
        m_Deaths = 0;
    }
    
    // Getters and setters
    int GetId() { return m_Id; }
    void SetId(int id) { m_Id = id; }
    
    string GetName() { return m_Name; }
    void SetName(string name) { m_Name = name; }
    
    int GetBank() { return m_Bank; }
    void SetBank(int amount) { m_Bank = amount; }
    void AddBank(int amount) { m_Bank += amount; }
    bool RemoveBank(int amount) 
    { 
        if (m_Bank >= amount) 
        { 
            m_Bank -= amount; 
            return true; 
        } 
        return false; 
    }
    
    bool IsActive() { return m_IsActive; }
    void SetActive(bool active) { m_IsActive = active; }
    
    array<string> GetMembers() { return m_Members; }
    void AddMember(string playerId) 
    { 
        if (m_Members.Find(playerId) == -1) 
            m_Members.Insert(playerId); 
    }
    void RemoveMember(string playerId) { m_Members.RemoveItem(playerId); }
    
    string GetLeader() { return m_Leader; }
    void SetLeader(string playerId) { m_Leader = playerId; }
}

//! House data structure
class EdenHouseData
{
    protected int m_Id;
    protected string m_OwnerId;
    protected vector m_Position;
    protected ref array<ref EdenInventoryItem> m_Inventory;
    protected ref array<ref EdenInventoryItem> m_PhysicalInventory;
    protected int m_StorageCapacity;
    protected int m_PhysicalStorageCapacity;
    protected bool m_IsOwned;
    protected int m_LastActive;
    protected ref array<string> m_PlayerKeys;
    protected bool m_InAuctionHouse;
    protected bool m_HasOil;
    protected int m_Server;
    
    void EdenHouseData()
    {
        m_Inventory = new array<ref EdenInventoryItem>();
        m_PhysicalInventory = new array<ref EdenInventoryItem>();
        m_PlayerKeys = new array<string>();
        m_StorageCapacity = 100;
        m_PhysicalStorageCapacity = 100;
        m_IsOwned = false;
        m_InAuctionHouse = false;
        m_HasOil = false;
        m_Server = 0;
        m_Position = "0 0 0";
    }
    
    // Getters and setters
    int GetId() { return m_Id; }
    void SetId(int id) { m_Id = id; }
    
    string GetOwnerId() { return m_OwnerId; }
    void SetOwnerId(string ownerId) { m_OwnerId = ownerId; }
    
    vector GetPosition() { return m_Position; }
    void SetPosition(vector pos) { m_Position = pos; }
    
    bool IsOwned() { return m_IsOwned; }
    void SetOwned(bool owned) { m_IsOwned = owned; }
    
    array<ref EdenInventoryItem> GetInventory() { return m_Inventory; }
    array<ref EdenInventoryItem> GetPhysicalInventory() { return m_PhysicalInventory; }
    
    int GetStorageCapacity() { return m_StorageCapacity; }
    void SetStorageCapacity(int capacity) { m_StorageCapacity = capacity; }
    
    array<string> GetPlayerKeys() { return m_PlayerKeys; }
    void AddPlayerKey(string playerId) 
    { 
        if (m_PlayerKeys.Find(playerId) == -1) 
            m_PlayerKeys.Insert(playerId); 
    }
    void RemovePlayerKey(string playerId) { m_PlayerKeys.RemoveItem(playerId); }
    bool HasPlayerKey(string playerId) { return m_PlayerKeys.Find(playerId) != -1; }
}

//! Market data structure
class EdenMarketData
{
    protected string m_ItemName;
    protected int m_BuyPrice;
    protected int m_SellPrice;
    protected int m_Stock;
    protected int m_MaxStock;
    protected float m_PriceMultiplier;
    protected int m_LastUpdate;
    
    void EdenMarketData()
    {
        m_BuyPrice = 0;
        m_SellPrice = 0;
        m_Stock = 0;
        m_MaxStock = 1000;
        m_PriceMultiplier = 1.0;
        m_LastUpdate = GetGame().GetWorld().GetWorldTime();
    }
    
    // Getters and setters
    string GetItemName() { return m_ItemName; }
    void SetItemName(string name) { m_ItemName = name; }
    
    int GetBuyPrice() { return m_BuyPrice; }
    void SetBuyPrice(int price) { m_BuyPrice = price; }
    
    int GetSellPrice() { return m_SellPrice; }
    void SetSellPrice(int price) { m_SellPrice = price; }
    
    int GetStock() { return m_Stock; }
    void SetStock(int stock) { m_Stock = stock; }
    void AddStock(int amount) { m_Stock += amount; }
    bool RemoveStock(int amount) 
    { 
        if (m_Stock >= amount) 
        { 
            m_Stock -= amount; 
            return true; 
        } 
        return false; 
    }
    
    float GetPriceMultiplier() { return m_PriceMultiplier; }
    void SetPriceMultiplier(float multiplier) { m_PriceMultiplier = multiplier; }
}

//! Inventory item structure
class EdenInventoryItem
{
    protected string m_ItemName;
    protected int m_Quantity;
    protected ref map<string, string> m_Properties;

    void EdenInventoryItem(string itemName, int quantity)
    {
        m_ItemName = itemName;
        m_Quantity = quantity;
        m_Properties = new map<string, string>();
    }

    string GetItemName() { return m_ItemName; }
    int GetQuantity() { return m_Quantity; }
    void SetQuantity(int quantity) { m_Quantity = quantity; }
    void AddQuantity(int amount) { m_Quantity += amount; }

    map<string, string> GetProperties() { return m_Properties; }
    void SetProperty(string key, string value) { m_Properties.Set(key, value); }
    string GetProperty(string key) { return m_Properties.Get(key); }
}

//! Enhanced wanted entry for police system
class EdenWantedEntry
{
    protected string m_PlayerId;
    protected string m_PlayerName;
    protected ref array<string> m_Crimes;
    protected ref array<int> m_Bounties;
    protected string m_IssuedBy;
    protected int m_Timestamp;
    protected int m_TotalBounty;

    void EdenWantedEntry()
    {
        m_Crimes = new array<string>();
        m_Bounties = new array<int>();
        m_TotalBounty = 0;
        m_Timestamp = GetGame().GetWorld().GetWorldTime();
    }

    // Getters and setters
    string GetPlayerId() { return m_PlayerId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }

    string GetPlayerName() { return m_PlayerName; }
    void SetPlayerName(string playerName) { m_PlayerName = playerName; }

    array<string> GetCrimes() { return m_Crimes; }
    array<int> GetBounties() { return m_Bounties; }

    string GetIssuedBy() { return m_IssuedBy; }
    void SetIssuedBy(string issuedBy) { m_IssuedBy = issuedBy; }

    int GetTimestamp() { return m_Timestamp; }
    int GetTotalBounty() { return m_TotalBounty; }

    void AddCrime(string crime, int bounty)
    {
        m_Crimes.Insert(crime);
        m_Bounties.Insert(bounty);
        m_TotalBounty += bounty;
    }

    void RemoveCrime(int index)
    {
        if (index >= 0 && index < m_Crimes.Count())
        {
            m_TotalBounty -= m_Bounties[index];
            m_Crimes.Remove(index);
            m_Bounties.Remove(index);
        }
    }

    void ClearCrimes()
    {
        m_Crimes.Clear();
        m_Bounties.Clear();
        m_TotalBounty = 0;
    }
}

//! Ticket data structure
class EdenTicket
{
    protected string m_PlayerId;
    protected string m_PlayerName;
    protected string m_OfficerId;
    protected string m_OfficerName;
    protected string m_Violation;
    protected int m_Amount;
    protected int m_Timestamp;
    protected bool m_IsPaid;

    void EdenTicket()
    {
        m_Amount = 0;
        m_Timestamp = GetGame().GetWorld().GetWorldTime();
        m_IsPaid = false;
    }

    // Getters and setters
    string GetPlayerId() { return m_PlayerId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }

    string GetPlayerName() { return m_PlayerName; }
    void SetPlayerName(string playerName) { m_PlayerName = playerName; }

    string GetOfficerId() { return m_OfficerId; }
    void SetOfficerId(string officerId) { m_OfficerId = officerId; }

    string GetOfficerName() { return m_OfficerName; }
    void SetOfficerName(string officerName) { m_OfficerName = officerName; }

    string GetViolation() { return m_Violation; }
    void SetViolation(string violation) { m_Violation = violation; }

    int GetAmount() { return m_Amount; }
    void SetAmount(int amount) { m_Amount = amount; }

    int GetTimestamp() { return m_Timestamp; }
    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }

    bool IsPaid() { return m_IsPaid; }
    void SetPaid(bool paid) { m_IsPaid = paid; }
}

//! Arrest data structure
class EdenArrestData
{
    protected string m_PlayerId;
    protected string m_PlayerName;
    protected string m_OfficerId;
    protected string m_OfficerName;
    protected int m_Bounty;
    protected int m_JailTime;
    protected int m_StartTime;
    protected bool m_IsVigi;
    protected bool m_BailEligible;

    void EdenArrestData()
    {
        m_Bounty = 0;
        m_JailTime = 0;
        m_StartTime = GetGame().GetWorld().GetWorldTime();
        m_IsVigi = false;
        m_BailEligible = false;
    }

    // Getters and setters
    string GetPlayerId() { return m_PlayerId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }

    string GetPlayerName() { return m_PlayerName; }
    void SetPlayerName(string playerName) { m_PlayerName = playerName; }

    string GetOfficerId() { return m_OfficerId; }
    void SetOfficerId(string officerId) { m_OfficerId = officerId; }

    string GetOfficerName() { return m_OfficerName; }
    void SetOfficerName(string officerName) { m_OfficerName = officerName; }

    int GetBounty() { return m_Bounty; }
    void SetBounty(int bounty) { m_Bounty = bounty; }

    int GetJailTime() { return m_JailTime; }
    void SetJailTime(int jailTime) { m_JailTime = jailTime; }

    int GetStartTime() { return m_StartTime; }
    void SetStartTime(int startTime) { m_StartTime = startTime; }

    bool IsVigi() { return m_IsVigi; }
    void SetIsVigi(bool isVigi) { m_IsVigi = isVigi; }

    bool IsBailEligible() { return m_BailEligible; }
    void SetBailEligible(bool eligible) { m_BailEligible = eligible; }

    int GetRemainingTime()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int elapsed = currentTime - m_StartTime;
        return Math.Max(0, m_JailTime - elapsed);
    }

    int GetCurrentBailAmount()
    {
        int remainingTime = GetRemainingTime();
        return Math.Round(remainingTime / 0.0048);
    }
}

//! Restraint data structure
class EdenRestraintData
{
    protected string m_TargetId;
    protected string m_TargetName;
    protected string m_OfficerId;
    protected string m_OfficerName;
    protected bool m_IsZipTie;
    protected int m_StartTime;
    protected bool m_IsBlindfolded;

    void EdenRestraintData()
    {
        m_IsZipTie = false;
        m_IsBlindfolded = false;
        m_StartTime = GetGame().GetWorld().GetWorldTime();
    }

    // Getters and setters
    string GetTargetId() { return m_TargetId; }
    void SetTargetId(string targetId) { m_TargetId = targetId; }

    string GetTargetName() { return m_TargetName; }
    void SetTargetName(string targetName) { m_TargetName = targetName; }

    string GetOfficerId() { return m_OfficerId; }
    void SetOfficerId(string officerId) { m_OfficerId = officerId; }

    string GetOfficerName() { return m_OfficerName; }
    void SetOfficerName(string officerName) { m_OfficerName = officerName; }

    bool IsZipTie() { return m_IsZipTie; }
    void SetIsZipTie(bool isZipTie) { m_IsZipTie = isZipTie; }

    bool IsBlindfolded() { return m_IsBlindfolded; }
    void SetBlindfolded(bool blindfolded) { m_IsBlindfolded = blindfolded; }

    int GetStartTime() { return m_StartTime; }
    void SetStartTime(int startTime) { m_StartTime = startTime; }

    int GetDuration()
    {
        return GetGame().GetWorld().GetWorldTime() - m_StartTime;
    }
}

//! Escort data structure
class EdenEscortData
{
    protected string m_TargetId;
    protected string m_TargetName;
    protected string m_OfficerId;
    protected string m_OfficerName;
    protected int m_StartTime;
    protected vector m_LastPosition;

    void EdenEscortData()
    {
        m_StartTime = GetGame().GetWorld().GetWorldTime();
        m_LastPosition = "0 0 0";
    }

    // Getters and setters
    string GetTargetId() { return m_TargetId; }
    void SetTargetId(string targetId) { m_TargetId = targetId; }

    string GetTargetName() { return m_TargetName; }
    void SetTargetName(string targetName) { m_TargetName = targetName; }

    string GetOfficerId() { return m_OfficerId; }
    void SetOfficerId(string officerId) { m_OfficerId = officerId; }

    string GetOfficerName() { return m_OfficerName; }
    void SetOfficerName(string officerName) { m_OfficerName = officerName; }

    int GetStartTime() { return m_StartTime; }
    void SetStartTime(int startTime) { m_StartTime = startTime; }

    vector GetLastPosition() { return m_LastPosition; }
    void SetLastPosition(vector position) { m_LastPosition = position; }

    int GetDuration()
    {
        return GetGame().GetWorld().GetWorldTime() - m_StartTime;
    }
}

//! Medical request data structure
class EdenMedicalRequest
{
    protected string m_PlayerId;
    protected string m_PlayerName;
    protected int m_RequestTime;
    protected int m_ReviveCost;
    protected vector m_RequestPosition;
    protected bool m_IsUrgent;
    protected string m_RequestReason;

    void EdenMedicalRequest()
    {
        m_RequestTime = GetGame().GetWorld().GetWorldTime();
        m_IsUrgent = false;
        m_RequestReason = "General medical assistance";
    }

    // Getters and setters
    string GetPlayerId() { return m_PlayerId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }

    string GetPlayerName() { return m_PlayerName; }
    void SetPlayerName(string playerName) { m_PlayerName = playerName; }

    int GetRequestTime() { return m_RequestTime; }
    void SetRequestTime(int requestTime) { m_RequestTime = requestTime; }

    int GetReviveCost() { return m_ReviveCost; }
    void SetReviveCost(int reviveCost) { m_ReviveCost = reviveCost; }

    vector GetRequestPosition() { return m_RequestPosition; }
    void SetRequestPosition(vector position) { m_RequestPosition = position; }

    bool IsUrgent() { return m_IsUrgent; }
    void SetUrgent(bool urgent) { m_IsUrgent = urgent; }

    string GetRequestReason() { return m_RequestReason; }
    void SetRequestReason(string reason) { m_RequestReason = reason; }

    int GetElapsedTime()
    {
        return GetGame().GetWorld().GetWorldTime() - m_RequestTime;
    }
}

//! Medical invoice data structure
class EdenMedicalInvoice
{
    protected string m_PatientId;
    protected string m_PatientName;
    protected string m_MedicId;
    protected string m_MedicName;
    protected int m_Amount;
    protected int m_Timestamp;
    protected string m_Description;
    protected bool m_IsPaid;

    void EdenMedicalInvoice()
    {
        m_Timestamp = GetGame().GetWorld().GetWorldTime();
        m_IsPaid = false;
        m_Description = "Medical services";
    }

    // Getters and setters
    string GetPatientId() { return m_PatientId; }
    void SetPatientId(string patientId) { m_PatientId = patientId; }

    string GetPatientName() { return m_PatientName; }
    void SetPatientName(string patientName) { m_PatientName = patientName; }

    string GetMedicId() { return m_MedicId; }
    void SetMedicId(string medicId) { m_MedicId = medicId; }

    string GetMedicName() { return m_MedicName; }
    void SetMedicName(string medicName) { m_MedicName = medicName; }

    int GetAmount() { return m_Amount; }
    void SetAmount(int amount) { m_Amount = amount; }

    int GetTimestamp() { return m_Timestamp; }
    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }

    string GetDescription() { return m_Description; }
    void SetDescription(string description) { m_Description = description; }

    bool IsPaid() { return m_IsPaid; }
    void SetPaid(bool paid) { m_IsPaid = paid; }

    int GetAge()
    {
        return GetGame().GetWorld().GetWorldTime() - m_Timestamp;
    }
}

//! Death data structure
class EdenDeathData
{
    protected string m_PlayerId;
    protected string m_PlayerName;
    protected string m_KillerId;
    protected string m_KillerName;
    protected string m_InstigatorId;
    protected string m_InstigatorName;
    protected int m_DeathTime;
    protected int m_MaxReviveTime;
    protected int m_RespawnLockoutTime;
    protected vector m_DeathPosition;
    protected string m_DeathCause;
    protected bool m_IsArrested;
    protected bool m_CanRequestMedic;
    protected bool m_HasRequestedMedic;

    void EdenDeathData()
    {
        m_DeathTime = GetGame().GetWorld().GetWorldTime();
        m_MaxReviveTime = m_DeathTime + 900; // 15 minutes default
        m_RespawnLockoutTime = 0;
        m_IsArrested = false;
        m_CanRequestMedic = true;
        m_HasRequestedMedic = false;
        m_DeathCause = "Unknown";
    }

    // Getters and setters
    string GetPlayerId() { return m_PlayerId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }

    string GetPlayerName() { return m_PlayerName; }
    void SetPlayerName(string playerName) { m_PlayerName = playerName; }

    string GetKillerId() { return m_KillerId; }
    void SetKillerId(string killerId) { m_KillerId = killerId; }

    string GetKillerName() { return m_KillerName; }
    void SetKillerName(string killerName) { m_KillerName = killerName; }

    string GetInstigatorId() { return m_InstigatorId; }
    void SetInstigatorId(string instigatorId) { m_InstigatorId = instigatorId; }

    string GetInstigatorName() { return m_InstigatorName; }
    void SetInstigatorName(string instigatorName) { m_InstigatorName = instigatorName; }

    int GetDeathTime() { return m_DeathTime; }
    void SetDeathTime(int deathTime) { m_DeathTime = deathTime; }

    int GetMaxReviveTime() { return m_MaxReviveTime; }
    void SetMaxReviveTime(int maxReviveTime) { m_MaxReviveTime = maxReviveTime; }

    int GetRespawnLockoutTime() { return m_RespawnLockoutTime; }
    void SetRespawnLockoutTime(int lockoutTime) { m_RespawnLockoutTime = lockoutTime; }

    vector GetDeathPosition() { return m_DeathPosition; }
    void SetDeathPosition(vector position) { m_DeathPosition = position; }

    string GetDeathCause() { return m_DeathCause; }
    void SetDeathCause(string cause) { m_DeathCause = cause; }

    bool IsArrested() { return m_IsArrested; }
    void SetArrested(bool arrested) { m_IsArrested = arrested; }

    bool CanRequestMedic() { return m_CanRequestMedic; }
    void SetCanRequestMedic(bool canRequest) { m_CanRequestMedic = canRequest; }

    bool HasRequestedMedic() { return m_HasRequestedMedic; }
    void SetHasRequestedMedic(bool hasRequested) { m_HasRequestedMedic = hasRequested; }

    int GetTimeSinceDeath()
    {
        return GetGame().GetWorld().GetWorldTime() - m_DeathTime;
    }

    int GetTimeUntilMaxRevive()
    {
        return m_MaxReviveTime - GetGame().GetWorld().GetWorldTime();
    }

    bool CanRespawn()
    {
        return GetGame().GetWorld().GetWorldTime() >= m_RespawnLockoutTime;
    }

    bool IsExpired()
    {
        return GetGame().GetWorld().GetWorldTime() >= m_MaxReviveTime;
    }
}

//! Medical loadout data structure
class EdenMedicalLoadout
{
    protected string m_RankName;
    protected int m_RankLevel;
    protected string m_Uniform;
    protected string m_Vest;
    protected string m_Helmet;
    protected string m_Backpack;
    protected ref map<string, int> m_Items; // item name -> quantity
    protected ref array<string> m_Weapons;
    protected ref array<string> m_Magazines;

    void EdenMedicalLoadout()
    {
        m_Items = new map<string, int>();
        m_Weapons = new array<string>();
        m_Magazines = new array<string>();
        m_RankLevel = 1;
        m_RankName = "EMT Basic";
    }

    // Getters and setters
    string GetRankName() { return m_RankName; }
    void SetRankName(string rankName) { m_RankName = rankName; }

    int GetRankLevel() { return m_RankLevel; }
    void SetRankLevel(int rankLevel) { m_RankLevel = rankLevel; }

    string GetUniform() { return m_Uniform; }
    void SetUniform(string uniform) { m_Uniform = uniform; }

    string GetVest() { return m_Vest; }
    void SetVest(string vest) { m_Vest = vest; }

    string GetHelmet() { return m_Helmet; }
    void SetHelmet(string helmet) { m_Helmet = helmet; }

    string GetBackpack() { return m_Backpack; }
    void SetBackpack(string backpack) { m_Backpack = backpack; }

    map<string, int> GetItems() { return m_Items; }
    array<string> GetWeapons() { return m_Weapons; }
    array<string> GetMagazines() { return m_Magazines; }

    // Item management
    void AddItem(string itemName, int quantity)
    {
        if (m_Items.Contains(itemName))
        {
            int currentQuantity = m_Items.Get(itemName);
            m_Items.Set(itemName, currentQuantity + quantity);
        }
        else
        {
            m_Items.Set(itemName, quantity);
        }
    }

    void RemoveItem(string itemName)
    {
        if (m_Items.Contains(itemName))
        {
            m_Items.Remove(itemName);
        }
    }

    int GetItemQuantity(string itemName)
    {
        if (m_Items.Contains(itemName))
            return m_Items.Get(itemName);
        return 0;
    }

    // Weapon management
    void AddWeapon(string weaponName)
    {
        if (m_Weapons.Find(weaponName) == -1)
        {
            m_Weapons.Insert(weaponName);
        }
    }

    void RemoveWeapon(string weaponName)
    {
        int index = m_Weapons.Find(weaponName);
        if (index != -1)
        {
            m_Weapons.Remove(index);
        }
    }

    // Magazine management
    void AddMagazine(string magazineName)
    {
        m_Magazines.Insert(magazineName);
    }

    void RemoveMagazine(string magazineName)
    {
        int index = m_Magazines.Find(magazineName);
        if (index != -1)
        {
            m_Magazines.Remove(index);
        }
    }

    // Utility methods
    int GetTotalItems()
    {
        int total = 0;
        foreach (string itemName, int quantity : m_Items)
        {
            total += quantity;
        }
        return total;
    }

    bool HasItem(string itemName)
    {
        return m_Items.Contains(itemName) && m_Items.Get(itemName) > 0;
    }

    bool HasWeapon(string weaponName)
    {
        return m_Weapons.Find(weaponName) != -1;
    }
}

//! Civilian job configuration data
class EdenJobConfig
{
    protected string m_JobName;
    protected string m_JobType;
    protected string m_RequiredTool;
    protected string m_RequiredLicense;
    protected int m_BasePayment;
    protected int m_ExperienceGain;
    protected float m_ProcessingTime;
    protected ref map<string, int> m_Resources;

    void EdenJobConfig()
    {
        m_Resources = new map<string, int>();
    }

    void SetJobName(string name) { m_JobName = name; }
    void SetJobType(string type) { m_JobType = type; }
    void SetRequiredTool(string tool) { m_RequiredTool = tool; }
    void SetRequiredLicense(string license) { m_RequiredLicense = license; }
    void SetBasePayment(int payment) { m_BasePayment = payment; }
    void SetExperienceGain(int experience) { m_ExperienceGain = experience; }
    void SetProcessingTime(float time) { m_ProcessingTime = time; }
    void AddResource(string resourceName, int quantity) { m_Resources.Set(resourceName, quantity); }

    string GetJobName() { return m_JobName; }
    string GetJobType() { return m_JobType; }
    string GetRequiredTool() { return m_RequiredTool; }
    string GetRequiredLicense() { return m_RequiredLicense; }
    int GetBasePayment() { return m_BasePayment; }
    int GetExperienceGain() { return m_ExperienceGain; }
    float GetProcessingTime() { return m_ProcessingTime; }
    map<string, int> GetResources() { return m_Resources; }
}

//! Active job data for a player
class EdenJobData
{
    protected string m_PlayerId;
    protected string m_JobType;
    protected int m_StartTime;
    protected int m_LastActionTime;
    protected vector m_Location;
    protected int m_ActionsCompleted;
    protected ref EdenJobConfig m_Config;

    void EdenJobData()
    {
        m_ActionsCompleted = 0;
        m_LastActionTime = 0;
    }

    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    void SetJobType(string jobType) { m_JobType = jobType; }
    void SetStartTime(int startTime) { m_StartTime = startTime; m_LastActionTime = startTime; }
    void SetLastActionTime(int time) { m_LastActionTime = time; }
    void SetLocation(vector location) { m_Location = location; }
    void SetConfig(EdenJobConfig config) { m_Config = config; }
    void IncrementActionsCompleted() { m_ActionsCompleted++; }

    string GetPlayerId() { return m_PlayerId; }
    string GetJobType() { return m_JobType; }
    int GetStartTime() { return m_StartTime; }
    int GetLastActionTime() { return m_LastActionTime; }
    vector GetLocation() { return m_Location; }
    int GetActionsCompleted() { return m_ActionsCompleted; }
    EdenJobConfig GetConfig() { return m_Config; }
}

//! Job location data
class EdenJobLocation
{
    protected string m_JobType;
    protected vector m_Position;
    protected float m_Radius;
    protected string m_MarkerName;
    protected bool m_IsActive;

    void SetJobType(string jobType) { m_JobType = jobType; }
    void SetPosition(vector position) { m_Position = position; }
    void SetRadius(float radius) { m_Radius = radius; }
    void SetMarkerName(string markerName) { m_MarkerName = markerName; }
    void SetActive(bool active) { m_IsActive = active; }

    string GetJobType() { return m_JobType; }
    vector GetPosition() { return m_Position; }
    float GetRadius() { return m_Radius; }
    string GetMarkerName() { return m_MarkerName; }
    bool IsActive() { return m_IsActive; }
}

//! Player license data
class EdenLicenseData
{
    protected ref array<string> m_Licenses;
    protected ref map<string, int> m_LicenseTimestamps;

    void EdenLicenseData()
    {
        m_Licenses = new array<string>();
        m_LicenseTimestamps = new map<string, int>();
    }

    void AddLicense(string licenseType)
    {
        if (m_Licenses.Find(licenseType) == -1)
        {
            m_Licenses.Insert(licenseType);
            m_LicenseTimestamps.Set(licenseType, GetGame().GetWorld().GetWorldTime());
        }
    }

    void RemoveLicense(string licenseType)
    {
        int index = m_Licenses.Find(licenseType);
        if (index != -1)
        {
            m_Licenses.Remove(index);
            m_LicenseTimestamps.Remove(licenseType);
        }
    }

    bool HasLicense(string licenseType)
    {
        return m_Licenses.Find(licenseType) != -1;
    }

    array<string> GetAllLicenses() { return m_Licenses; }

    int GetLicenseTimestamp(string licenseType)
    {
        if (m_LicenseTimestamps.Contains(licenseType))
            return m_LicenseTimestamps.Get(licenseType);
        return 0;
    }
}

//! Processing data
class EdenProcessingData
{
    protected string m_PlayerId;
    protected string m_ProcessType;
    protected int m_StartTime;
    protected int m_ProcessingTime;
    protected vector m_Location;
    protected int m_Quantity;

    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    void SetProcessType(string processType) { m_ProcessType = processType; }
    void SetStartTime(int startTime) { m_StartTime = startTime; }
    void SetProcessingTime(int processingTime) { m_ProcessingTime = processingTime; }
    void SetLocation(vector location) { m_Location = location; }
    void SetQuantity(int quantity) { m_Quantity = quantity; }

    string GetPlayerId() { return m_PlayerId; }
    string GetProcessType() { return m_ProcessType; }
    int GetStartTime() { return m_StartTime; }
    int GetProcessingTime() { return m_ProcessingTime; }
    vector GetLocation() { return m_Location; }
    int GetQuantity() { return m_Quantity; }
}

//! Processing location data
class EdenProcessingLocation
{
    protected string m_ProcessType;
    protected vector m_Position;
    protected float m_Radius;
    protected string m_RequiredLicense;
    protected bool m_IsLegal;
    protected bool m_IsActive;

    void SetProcessType(string processType) { m_ProcessType = processType; }
    void SetPosition(vector position) { m_Position = position; }
    void SetRadius(float radius) { m_Radius = radius; }
    void SetRequiredLicense(string license) { m_RequiredLicense = license; }
    void SetLegal(bool legal) { m_IsLegal = legal; }
    void SetActive(bool active) { m_IsActive = active; }

    string GetProcessType() { return m_ProcessType; }
    vector GetPosition() { return m_Position; }
    float GetRadius() { return m_Radius; }
    string GetRequiredLicense() { return m_RequiredLicense; }
    bool IsLegal() { return m_IsLegal; }
    bool IsActive() { return m_IsActive; }
}

//! Robbery data
class EdenRobberyData
{
    protected string m_RobberId;
    protected string m_TargetId;
    protected int m_StartTime;
    protected vector m_Location;
    protected int m_Progress;
    protected bool m_IsCompleted;

    void EdenRobberyData()
    {
        m_Progress = 0;
        m_IsCompleted = false;
    }

    void SetRobberId(string robberId) { m_RobberId = robberId; }
    void SetTargetId(string targetId) { m_TargetId = targetId; }
    void SetStartTime(int startTime) { m_StartTime = startTime; }
    void SetLocation(vector location) { m_Location = location; }
    void SetProgress(int progress) { m_Progress = progress; }
    void SetCompleted(bool completed) { m_IsCompleted = completed; }

    string GetRobberId() { return m_RobberId; }
    string GetTargetId() { return m_TargetId; }
    int GetStartTime() { return m_StartTime; }
    vector GetLocation() { return m_Location; }
    int GetProgress() { return m_Progress; }
    bool IsCompleted() { return m_IsCompleted; }
}

//! Civilian loadout data
class EdenCivilianLoadout
{
    protected string m_LoadoutName;
    protected string m_LoadoutType;
    protected int m_RequiredLevel;
    protected ref array<string> m_UniformOptions;
    protected ref array<string> m_VestOptions;
    protected ref array<string> m_HelmetOptions;
    protected ref array<string> m_BackpackOptions;
    protected ref map<int, string> m_UniformsByLevel; // level -> uniform
    protected ref map<int, string> m_VestsByLevel; // level -> vest
    protected ref map<int, string> m_HelmetsByLevel; // level -> helmet
    protected ref map<int, string> m_BackpacksByLevel; // level -> backpack
    protected ref map<string, int> m_Items; // item name -> quantity
    protected ref array<string> m_Weapons;
    protected ref array<string> m_Magazines;

    void EdenCivilianLoadout()
    {
        m_UniformOptions = new array<string>();
        m_VestOptions = new array<string>();
        m_HelmetOptions = new array<string>();
        m_BackpackOptions = new array<string>();
        m_UniformsByLevel = new map<int, string>();
        m_VestsByLevel = new map<int, string>();
        m_HelmetsByLevel = new map<int, string>();
        m_BackpacksByLevel = new map<int, string>();
        m_Items = new map<string, int>();
        m_Weapons = new array<string>();
        m_Magazines = new array<string>();
        m_RequiredLevel = 1;
    }

    // Basic setters
    void SetLoadoutName(string name) { m_LoadoutName = name; }
    void SetLoadoutType(string type) { m_LoadoutType = type; }
    void SetRequiredLevel(int level) { m_RequiredLevel = level; }

    // Add equipment options
    void AddUniformOption(string uniform) { m_UniformOptions.Insert(uniform); }
    void AddVestOption(string vest) { m_VestOptions.Insert(vest); }
    void AddHelmetOption(string helmet) { m_HelmetOptions.Insert(helmet); }
    void AddBackpackOption(string backpack) { m_BackpackOptions.Insert(backpack); }

    // Add level-specific equipment
    void AddUniformByLevel(int level, string uniform) { m_UniformsByLevel.Set(level, uniform); }
    void AddVestByLevel(int level, string vest) { m_VestsByLevel.Set(level, vest); }
    void AddHelmetByLevel(int level, string helmet) { m_HelmetsByLevel.Set(level, helmet); }
    void AddBackpackByLevel(int level, string backpack) { m_BackpacksByLevel.Set(level, backpack); }

    // Add items and weapons
    void AddItem(string itemName, int quantity) { m_Items.Set(itemName, quantity); }
    void AddWeapon(string weaponName) { m_Weapons.Insert(weaponName); }
    void AddMagazine(string magazineName) { m_Magazines.Insert(magazineName); }

    // Getters
    string GetLoadoutName() { return m_LoadoutName; }
    string GetLoadoutType() { return m_LoadoutType; }
    int GetRequiredLevel() { return m_RequiredLevel; }

    // Get equipment for level
    string GetUniformForLevel(int level)
    {
        if (m_UniformsByLevel.Contains(level))
            return m_UniformsByLevel.Get(level);
        if (m_UniformOptions.Count() > 0)
            return m_UniformOptions.GetRandomElement();
        return "";
    }

    string GetVestForLevel(int level)
    {
        if (m_VestsByLevel.Contains(level))
            return m_VestsByLevel.Get(level);
        if (m_VestOptions.Count() > 0)
            return m_VestOptions.GetRandomElement();
        return "";
    }

    string GetHelmetForLevel(int level)
    {
        if (m_HelmetsByLevel.Contains(level))
            return m_HelmetsByLevel.Get(level);
        if (m_HelmetOptions.Count() > 0)
            return m_HelmetOptions.GetRandomElement();
        return "";
    }

    string GetBackpackForLevel(int level)
    {
        if (m_BackpacksByLevel.Contains(level))
            return m_BackpacksByLevel.Get(level);
        if (m_BackpackOptions.Count() > 0)
            return m_BackpackOptions.GetRandomElement();
        return "";
    }

    map<string, int> GetItems() { return m_Items; }
    array<string> GetWeapons() { return m_Weapons; }
    array<string> GetMagazines() { return m_Magazines; }
}

//! Tool data structure
class EdenToolData
{
    protected string m_ToolName;
    protected string m_ToolType;
    protected string m_ItemClass;
    protected float m_UsageTime;
    protected int m_Durability;
    protected int m_Price;
    protected bool m_IsIllegal;
    protected ref array<string> m_CompatibleResources;

    void EdenToolData()
    {
        m_CompatibleResources = new array<string>();
        m_IsIllegal = false;
        m_Durability = 100;
        m_UsageTime = 1.0;
    }

    void SetToolName(string name) { m_ToolName = name; }
    void SetToolType(string type) { m_ToolType = type; }
    void SetItemClass(string itemClass) { m_ItemClass = itemClass; }
    void SetUsageTime(float time) { m_UsageTime = time; }
    void SetDurability(int durability) { m_Durability = durability; }
    void SetPrice(int price) { m_Price = price; }
    void SetIllegal(bool illegal) { m_IsIllegal = illegal; }
    void AddCompatibleResource(string resource) { m_CompatibleResources.Insert(resource); }

    string GetToolName() { return m_ToolName; }
    string GetToolType() { return m_ToolType; }
    string GetItemClass() { return m_ItemClass; }
    float GetUsageTime() { return m_UsageTime; }
    int GetDurability() { return m_Durability; }
    int GetPrice() { return m_Price; }
    bool IsIllegal() { return m_IsIllegal; }
    array<string> GetCompatibleResources() { return m_CompatibleResources; }

    bool IsCompatibleWithResource(string resource)
    {
        return m_CompatibleResources.Find(resource) != -1;
    }
}

//! Gang data structure
class EdenGangData
{
    protected int m_GangId;
    protected string m_GangName;
    protected string m_OwnerId;
    protected int m_CreationTime;
    protected int m_BankBalance;
    protected int m_MemberCount;
    protected bool m_IsActive;

    void EdenGangData()
    {
        m_GangId = -1;
        m_BankBalance = 0;
        m_MemberCount = 0;
        m_IsActive = true;
    }

    void SetGangId(int gangId) { m_GangId = gangId; }
    void SetGangName(string gangName) { m_GangName = gangName; }
    void SetOwnerId(string ownerId) { m_OwnerId = ownerId; }
    void SetCreationTime(int creationTime) { m_CreationTime = creationTime; }
    void SetBankBalance(int bankBalance) { m_BankBalance = bankBalance; }
    void SetMemberCount(int memberCount) { m_MemberCount = memberCount; }
    void SetActive(bool active) { m_IsActive = active; }

    int GetGangId() { return m_GangId; }
    string GetGangName() { return m_GangName; }
    string GetOwnerId() { return m_OwnerId; }
    int GetCreationTime() { return m_CreationTime; }
    int GetBankBalance() { return m_BankBalance; }
    int GetMemberCount() { return m_MemberCount; }
    bool IsActive() { return m_IsActive; }

    void AddBankMoney(int amount) { m_BankBalance += amount; }
    void RemoveBankMoney(int amount) { m_BankBalance -= amount; if (m_BankBalance < 0) m_BankBalance = 0; }
}

//! Gang member data structure
class EdenGangMemberData
{
    protected string m_PlayerId;
    protected int m_GangId;
    protected int m_Rank;
    protected int m_JoinTime;
    protected int m_WarPoints;
    protected bool m_IsOnline;

    void EdenGangMemberData()
    {
        m_Rank = 1;
        m_WarPoints = 0;
        m_IsOnline = false;
    }

    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    void SetGangId(int gangId) { m_GangId = gangId; }
    void SetRank(int rank) { m_Rank = rank; }
    void SetJoinTime(int joinTime) { m_JoinTime = joinTime; }
    void SetWarPoints(int warPoints) { m_WarPoints = warPoints; }
    void SetOnline(bool online) { m_IsOnline = online; }

    string GetPlayerId() { return m_PlayerId; }
    int GetGangId() { return m_GangId; }
    int GetRank() { return m_Rank; }
    int GetJoinTime() { return m_JoinTime; }
    int GetWarPoints() { return m_WarPoints; }
    bool IsOnline() { return m_IsOnline; }

    void AddWarPoints(int points) { m_WarPoints += points; }
    void RemoveWarPoints(int points) { m_WarPoints -= points; if (m_WarPoints < 0) m_WarPoints = 0; }

    string GetRankName()
    {
        switch (m_Rank)
        {
            case 1: return "Member";
            case 2: return "Trusted";
            case 3: return "Moderator";
            case 4: return "Admin";
            case 5: return "Leader";
            default: return "Unknown";
        }
    }
}

//! Territory data structure
class EdenTerritoryData
{
    protected string m_TerritoryName;
    protected int m_OwnerGangId;
    protected string m_OwnerGangName;
    protected float m_CaptureProgress;
    protected vector m_Position;
    protected float m_Radius;
    protected bool m_IsBeingCaptured;
    protected int m_CaptureStartTime;
    protected int m_CapturingGangId;

    void EdenTerritoryData()
    {
        m_OwnerGangId = -1;
        m_CaptureProgress = 0.5;
        m_Radius = 150.0;
        m_IsBeingCaptured = false;
        m_CaptureStartTime = 0;
        m_CapturingGangId = -1;
    }

    void SetTerritoryName(string territoryName) { m_TerritoryName = territoryName; }
    void SetOwnerGangId(int ownerGangId) { m_OwnerGangId = ownerGangId; }
    void SetOwnerGangName(string ownerGangName) { m_OwnerGangName = ownerGangName; }
    void SetCaptureProgress(float captureProgress) { m_CaptureProgress = captureProgress; }
    void SetPosition(vector position) { m_Position = position; }
    void SetRadius(float radius) { m_Radius = radius; }
    void SetBeingCaptured(bool beingCaptured) { m_IsBeingCaptured = beingCaptured; }
    void SetCaptureStartTime(int captureStartTime) { m_CaptureStartTime = captureStartTime; }
    void SetCapturingGangId(int capturingGangId) { m_CapturingGangId = capturingGangId; }

    string GetTerritoryName() { return m_TerritoryName; }
    int GetOwnerGangId() { return m_OwnerGangId; }
    string GetOwnerGangName() { return m_OwnerGangName; }
    float GetCaptureProgress() { return m_CaptureProgress; }
    vector GetPosition() { return m_Position; }
    float GetRadius() { return m_Radius; }
    bool IsBeingCaptured() { return m_IsBeingCaptured; }
    int GetCaptureStartTime() { return m_CaptureStartTime; }
    int GetCapturingGangId() { return m_CapturingGangId; }

    void StartCapture(int gangId, int startTime)
    {
        m_IsBeingCaptured = true;
        m_CapturingGangId = gangId;
        m_CaptureStartTime = startTime;
    }

    bool IsNeutral() { return m_OwnerGangId == -1; }
}

//! Gang building data structure
class EdenGangBuildingData
{
    protected int m_GangId;
    protected string m_OwnerId;
    protected vector m_Position;
    protected string m_ClassName;
    protected int m_PurchaseTime;
    protected int m_StorageCapacity;
    protected int m_PhysicalStorageCapacity;
    protected bool m_IsLocked;
    protected int m_LastPayment;
    protected int m_NextPayment;
    protected ref array<ref EdenInventoryItem> m_StorageItems;
    protected ref array<ref EdenInventoryItem> m_PhysicalItems;

    void EdenGangBuildingData()
    {
        m_ClassName = "Land_i_Shed_Ind_F";
        m_StorageCapacity = 1000;
        m_PhysicalStorageCapacity = 300;
        m_IsLocked = true;
        m_StorageItems = new array<ref EdenInventoryItem>();
        m_PhysicalItems = new array<ref EdenInventoryItem>();
    }

    void SetGangId(int gangId) { m_GangId = gangId; }
    void SetOwnerId(string ownerId) { m_OwnerId = ownerId; }
    void SetPosition(vector position) { m_Position = position; }
    void SetClassName(string className) { m_ClassName = className; }
    void SetPurchaseTime(int purchaseTime) { m_PurchaseTime = purchaseTime; }
    void SetStorageCapacity(int storageCapacity) { m_StorageCapacity = storageCapacity; }
    void SetPhysicalStorageCapacity(int physicalStorageCapacity) { m_PhysicalStorageCapacity = physicalStorageCapacity; }
    void SetLocked(bool locked) { m_IsLocked = locked; }
    void SetLastPayment(int lastPayment) { m_LastPayment = lastPayment; }
    void SetNextPayment(int nextPayment) { m_NextPayment = nextPayment; }

    int GetGangId() { return m_GangId; }
    string GetOwnerId() { return m_OwnerId; }
    vector GetPosition() { return m_Position; }
    string GetClassName() { return m_ClassName; }
    int GetPurchaseTime() { return m_PurchaseTime; }
    int GetStorageCapacity() { return m_StorageCapacity; }
    int GetPhysicalStorageCapacity() { return m_PhysicalStorageCapacity; }
    bool IsLocked() { return m_IsLocked; }
    int GetLastPayment() { return m_LastPayment; }
    int GetNextPayment() { return m_NextPayment; }
    array<ref EdenInventoryItem> GetStorageItems() { return m_StorageItems; }
    array<ref EdenInventoryItem> GetPhysicalItems() { return m_PhysicalItems; }
}

//! Gang war data structure
class EdenGangWarData
{
    protected int m_AttackerGangId;
    protected int m_DefenderGangId;
    protected string m_AttackerGangName;
    protected string m_DefenderGangName;
    protected int m_StartTime;
    protected int m_EndTime;
    protected int m_AttackerPoints;
    protected int m_DefenderPoints;
    protected bool m_IsActive;
    protected int m_Duration;

    void EdenGangWarData()
    {
        m_AttackerPoints = 0;
        m_DefenderPoints = 0;
        m_IsActive = true;
        m_Duration = 86400; // 24 hours default
        m_EndTime = 0;
    }

    void SetAttackerGangId(int attackerGangId) { m_AttackerGangId = attackerGangId; }
    void SetDefenderGangId(int defenderGangId) { m_DefenderGangId = defenderGangId; }
    void SetAttackerGangName(string attackerGangName) { m_AttackerGangName = attackerGangName; }
    void SetDefenderGangName(string defenderGangName) { m_DefenderGangName = defenderGangName; }
    void SetStartTime(int startTime) { m_StartTime = startTime; }
    void SetEndTime(int endTime) { m_EndTime = endTime; }
    void SetAttackerPoints(int attackerPoints) { m_AttackerPoints = attackerPoints; }
    void SetDefenderPoints(int defenderPoints) { m_DefenderPoints = defenderPoints; }
    void SetActive(bool active) { m_IsActive = active; }
    void SetDuration(int duration) { m_Duration = duration; }

    int GetAttackerGangId() { return m_AttackerGangId; }
    int GetDefenderGangId() { return m_DefenderGangId; }
    string GetAttackerGangName() { return m_AttackerGangName; }
    string GetDefenderGangName() { return m_DefenderGangName; }
    int GetStartTime() { return m_StartTime; }
    int GetEndTime() { return m_EndTime; }
    int GetAttackerPoints() { return m_AttackerPoints; }
    int GetDefenderPoints() { return m_DefenderPoints; }
    bool IsActive() { return m_IsActive; }
    int GetDuration() { return m_Duration; }

    void AddAttackerPoints(int points) { m_AttackerPoints += points; }
    void AddDefenderPoints(int points) { m_DefenderPoints += points; }

    int GetWinningGangId()
    {
        if (m_AttackerPoints > m_DefenderPoints)
            return m_AttackerGangId;
        else if (m_DefenderPoints > m_AttackerPoints)
            return m_DefenderGangId;
        return -1; // Tie
    }

    bool IsExpired(int currentTime)
    {
        return currentTime >= (m_StartTime + m_Duration);
    }
}

//! Gang invitation data structure
class EdenGangInvitationData
{
    protected string m_InviterId;
    protected string m_TargetId;
    protected int m_GangId;
    protected string m_GangName;
    protected int m_InviteTime;
    protected int m_ExpirationTime;
    protected bool m_IsActive;

    void EdenGangInvitationData()
    {
        m_IsActive = true;
        m_ExpirationTime = 300; // 5 minutes default
    }

    void SetInviterId(string inviterId) { m_InviterId = inviterId; }
    void SetTargetId(string targetId) { m_TargetId = targetId; }
    void SetGangId(int gangId) { m_GangId = gangId; }
    void SetGangName(string gangName) { m_GangName = gangName; }
    void SetInviteTime(int inviteTime) { m_InviteTime = inviteTime; }
    void SetExpirationTime(int expirationTime) { m_ExpirationTime = expirationTime; }
    void SetActive(bool active) { m_IsActive = active; }

    string GetInviterId() { return m_InviterId; }
    string GetTargetId() { return m_TargetId; }
    int GetGangId() { return m_GangId; }
    string GetGangName() { return m_GangName; }
    int GetInviteTime() { return m_InviteTime; }
    int GetExpirationTime() { return m_ExpirationTime; }
    bool IsActive() { return m_IsActive; }

    bool IsExpired(int currentTime)
    {
        return currentTime >= (m_InviteTime + m_ExpirationTime);
    }
}

//! Bank account data structure
class EdenBankAccount
{
    protected string m_PlayerId;
    protected string m_PlayerName;
    protected int m_Balance;
    protected int m_CreationTime;
    protected int m_LastAccessTime;
    protected bool m_IsActive;
    protected int m_TotalDeposits;
    protected int m_TotalWithdrawals;

    void EdenBankAccount()
    {
        m_Balance = 0;
        m_IsActive = true;
        m_TotalDeposits = 0;
        m_TotalWithdrawals = 0;
    }

    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    void SetPlayerName(string playerName) { m_PlayerName = playerName; }
    void SetBalance(int balance) { m_Balance = balance; }
    void SetCreationTime(int creationTime) { m_CreationTime = creationTime; }
    void SetLastAccessTime(int lastAccessTime) { m_LastAccessTime = lastAccessTime; }
    void SetActive(bool active) { m_IsActive = active; }
    void SetTotalDeposits(int totalDeposits) { m_TotalDeposits = totalDeposits; }
    void SetTotalWithdrawals(int totalWithdrawals) { m_TotalWithdrawals = totalWithdrawals; }

    string GetPlayerId() { return m_PlayerId; }
    string GetPlayerName() { return m_PlayerName; }
    int GetBalance() { return m_Balance; }
    int GetCreationTime() { return m_CreationTime; }
    int GetLastAccessTime() { return m_LastAccessTime; }
    bool IsActive() { return m_IsActive; }
    int GetTotalDeposits() { return m_TotalDeposits; }
    int GetTotalWithdrawals() { return m_TotalWithdrawals; }

    void AddBalance(int amount)
    {
        m_Balance += amount;
        m_TotalDeposits += amount;
    }

    void RemoveBalance(int amount)
    {
        m_Balance -= amount;
        if (m_Balance < 0) m_Balance = 0;
        m_TotalWithdrawals += amount;
    }
}

//! Transaction record data structure
class EdenTransactionRecord
{
    protected string m_TransactionId;
    protected string m_PlayerId;
    protected string m_TransactionType;
    protected int m_Amount;
    protected int m_NewBalance;
    protected int m_Timestamp;
    protected string m_Description;

    void SetTransactionId(string transactionId) { m_TransactionId = transactionId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    void SetTransactionType(string transactionType) { m_TransactionType = transactionType; }
    void SetAmount(int amount) { m_Amount = amount; }
    void SetNewBalance(int newBalance) { m_NewBalance = newBalance; }
    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }
    void SetDescription(string description) { m_Description = description; }

    string GetTransactionId() { return m_TransactionId; }
    string GetPlayerId() { return m_PlayerId; }
    string GetTransactionType() { return m_TransactionType; }
    int GetAmount() { return m_Amount; }
    int GetNewBalance() { return m_NewBalance; }
    int GetTimestamp() { return m_Timestamp; }
    string GetDescription() { return m_Description; }
}

//! ATM location data structure
class EdenATMLocation
{
    protected string m_ATMId;
    protected string m_LocationName;
    protected vector m_Position;
    protected bool m_IsActive;
    protected int m_LastRobberyTime;

    void EdenATMLocation()
    {
        m_IsActive = true;
        m_LastRobberyTime = 0;
    }

    void SetATMId(string atmId) { m_ATMId = atmId; }
    void SetLocationName(string locationName) { m_LocationName = locationName; }
    void SetPosition(vector position) { m_Position = position; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    void SetLastRobberyTime(int lastRobberyTime) { m_LastRobberyTime = lastRobberyTime; }

    string GetATMId() { return m_ATMId; }
    string GetLocationName() { return m_LocationName; }
    vector GetPosition() { return m_Position; }
    bool IsActive() { return m_IsActive; }
    int GetLastRobberyTime() { return m_LastRobberyTime; }
}

//! Market item data structure
class EdenMarketItem
{
    protected string m_ItemName;
    protected int m_CurrentPrice;
    protected int m_StartingPrice;
    protected int m_MinPrice;
    protected int m_MaxPrice;
    protected int m_SalesCount;
    protected int m_LastUpdateTime;
    protected float m_PriceChangeRate;

    void EdenMarketItem()
    {
        m_SalesCount = 0;
        m_PriceChangeRate = 0.0;
    }

    void SetItemName(string itemName) { m_ItemName = itemName; }
    void SetCurrentPrice(int currentPrice) { m_CurrentPrice = currentPrice; }
    void SetStartingPrice(int startingPrice) { m_StartingPrice = startingPrice; }
    void SetMinPrice(int minPrice) { m_MinPrice = minPrice; }
    void SetMaxPrice(int maxPrice) { m_MaxPrice = maxPrice; }
    void SetSalesCount(int salesCount) { m_SalesCount = salesCount; }
    void SetLastUpdateTime(int lastUpdateTime) { m_LastUpdateTime = lastUpdateTime; }
    void SetPriceChangeRate(float priceChangeRate) { m_PriceChangeRate = priceChangeRate; }

    string GetItemName() { return m_ItemName; }
    int GetCurrentPrice() { return m_CurrentPrice; }
    int GetStartingPrice() { return m_StartingPrice; }
    int GetMinPrice() { return m_MinPrice; }
    int GetMaxPrice() { return m_MaxPrice; }
    int GetSalesCount() { return m_SalesCount; }
    int GetLastUpdateTime() { return m_LastUpdateTime; }
    float GetPriceChangeRate() { return m_PriceChangeRate; }

    void AddSalesCount(int amount) { m_SalesCount += amount; }

    float GetPriceChangePercent()
    {
        if (m_StartingPrice == 0) return 0.0;
        return ((float)(m_CurrentPrice - m_StartingPrice) / (float)m_StartingPrice) * 100.0;
    }
}

//! Market configuration data structure
class EdenMarketConfig
{
    protected string m_ItemName;
    protected int m_MinPrice;
    protected int m_MaxPrice;
    protected int m_Legality; // 0 = legal, 1 = illegal
    protected float m_DecreaseRate;
    protected float m_IncreaseRate;
    protected bool m_IsActive;

    void EdenMarketConfig()
    {
        m_Legality = 0;
        m_IsActive = true;
    }

    void SetItemName(string itemName) { m_ItemName = itemName; }
    void SetMinPrice(int minPrice) { m_MinPrice = minPrice; }
    void SetMaxPrice(int maxPrice) { m_MaxPrice = maxPrice; }
    void SetLegality(int legality) { m_Legality = legality; }
    void SetDecreaseRate(float decreaseRate) { m_DecreaseRate = decreaseRate; }
    void SetIncreaseRate(float increaseRate) { m_IncreaseRate = increaseRate; }
    void SetActive(bool active) { m_IsActive = active; }

    string GetItemName() { return m_ItemName; }
    int GetMinPrice() { return m_MinPrice; }
    int GetMaxPrice() { return m_MaxPrice; }
    int GetLegality() { return m_Legality; }
    float GetDecreaseRate() { return m_DecreaseRate; }
    float GetIncreaseRate() { return m_IncreaseRate; }
    bool IsActive() { return m_IsActive; }

    bool IsLegal() { return m_Legality == 0; }
}

//! Shop data structure
class EdenShopData
{
    protected string m_ShopId;
    protected string m_ShopName;
    protected string m_ShopType;
    protected vector m_Position;
    protected bool m_IsActive;
    protected int m_TotalSales;
    protected int m_ItemsSold;
    protected int m_LastUpdateTime;

    void EdenShopData()
    {
        m_IsActive = true;
        m_TotalSales = 0;
        m_ItemsSold = 0;
    }

    void SetShopId(string shopId) { m_ShopId = shopId; }
    void SetShopName(string shopName) { m_ShopName = shopName; }
    void SetShopType(string shopType) { m_ShopType = shopType; }
    void SetPosition(vector position) { m_Position = position; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    void SetTotalSales(int totalSales) { m_TotalSales = totalSales; }
    void SetItemsSold(int itemsSold) { m_ItemsSold = itemsSold; }
    void SetLastUpdateTime(int lastUpdateTime) { m_LastUpdateTime = lastUpdateTime; }

    string GetShopId() { return m_ShopId; }
    string GetShopName() { return m_ShopName; }
    string GetShopType() { return m_ShopType; }
    vector GetPosition() { return m_Position; }
    bool IsActive() { return m_IsActive; }
    int GetTotalSales() { return m_TotalSales; }
    int GetItemsSold() { return m_ItemsSold; }
    int GetLastUpdateTime() { return m_LastUpdateTime; }

    void AddTotalSales(int amount) { m_TotalSales += amount; }
    void AddItemsSold(int amount) { m_ItemsSold += amount; }
}

//! Shop item data structure
class EdenShopItem
{
    protected string m_ItemName;
    protected int m_BasePrice;
    protected int m_CurrentPrice;
    protected bool m_IsAvailable;
    protected int m_StockQuantity;
    protected int m_SoldQuantity;
    protected float m_DiscountRate;

    void EdenShopItem()
    {
        m_IsAvailable = true;
        m_StockQuantity = -1; // Unlimited
        m_SoldQuantity = 0;
        m_DiscountRate = 0.0;
    }

    void SetItemName(string itemName) { m_ItemName = itemName; }
    void SetBasePrice(int basePrice) { m_BasePrice = basePrice; }
    void SetCurrentPrice(int currentPrice) { m_CurrentPrice = currentPrice; }
    void SetIsAvailable(bool isAvailable) { m_IsAvailable = isAvailable; }
    void SetStockQuantity(int stockQuantity) { m_StockQuantity = stockQuantity; }
    void SetSoldQuantity(int soldQuantity) { m_SoldQuantity = soldQuantity; }
    void SetDiscountRate(float discountRate) { m_DiscountRate = discountRate; }

    string GetItemName() { return m_ItemName; }
    int GetBasePrice() { return m_BasePrice; }
    int GetCurrentPrice() { return m_CurrentPrice; }
    bool IsAvailable() { return m_IsAvailable; }
    int GetStockQuantity() { return m_StockQuantity; }
    int GetSoldQuantity() { return m_SoldQuantity; }
    float GetDiscountRate() { return m_DiscountRate; }

    void AddSoldQuantity(int amount) { m_SoldQuantity += amount; }

    bool IsInStock(int requestedQuantity)
    {
        if (m_StockQuantity == -1) return true; // Unlimited stock
        return m_StockQuantity >= requestedQuantity;
    }

    void RemoveStock(int amount)
    {
        if (m_StockQuantity != -1)
        {
            m_StockQuantity -= amount;
            if (m_StockQuantity < 0) m_StockQuantity = 0;
        }
    }
}

//! Shop location data structure
class EdenShopLocation
{
    protected string m_ShopId;
    protected string m_LocationName;
    protected vector m_Position;
    protected float m_InteractionRadius;
    protected bool m_IsActive;

    void EdenShopLocation()
    {
        m_InteractionRadius = 5.0;
        m_IsActive = true;
    }

    void SetShopId(string shopId) { m_ShopId = shopId; }
    void SetLocationName(string locationName) { m_LocationName = locationName; }
    void SetPosition(vector position) { m_Position = position; }
    void SetInteractionRadius(float radius) { m_InteractionRadius = radius; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }

    string GetShopId() { return m_ShopId; }
    string GetLocationName() { return m_LocationName; }
    vector GetPosition() { return m_Position; }
    float GetInteractionRadius() { return m_InteractionRadius; }
    bool IsActive() { return m_IsActive; }

    bool IsPlayerInRange(vector playerPosition)
    {
        return vector.Distance(m_Position, playerPosition) <= m_InteractionRadius;
    }
}

//! Vehicle data structure
class EdenVehicleData
{
    protected string m_VehicleId;
    protected string m_OwnerId;
    protected string m_OwnerName;
    protected string m_VehicleClass;
    protected string m_VehicleType;
    protected string m_Plate;
    protected bool m_IsAlive;
    protected bool m_IsActive;
    protected int m_InsuranceLevel;
    protected int m_GangId;
    protected int m_PurchaseTime;
    protected int m_LastSpawnTime;
    protected int m_LastStoreTime;
    protected string m_Color;
    protected string m_Modifications;
    protected string m_Inventory;
    protected bool m_IsImpounded;
    protected int m_ImpoundTime;
    protected string m_CustomName;

    void EdenVehicleData()
    {
        m_IsAlive = true;
        m_IsActive = false;
        m_InsuranceLevel = 0;
        m_GangId = 0;
        m_IsImpounded = false;
        m_ImpoundTime = 0;
        m_CustomName = "";
    }

    void SetVehicleId(string vehicleId) { m_VehicleId = vehicleId; }
    void SetOwnerId(string ownerId) { m_OwnerId = ownerId; }
    void SetOwnerName(string ownerName) { m_OwnerName = ownerName; }
    void SetVehicleClass(string vehicleClass) { m_VehicleClass = vehicleClass; }
    void SetVehicleType(string vehicleType) { m_VehicleType = vehicleType; }
    void SetPlate(string plate) { m_Plate = plate; }
    void SetIsAlive(bool isAlive) { m_IsAlive = isAlive; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    void SetInsuranceLevel(int insuranceLevel) { m_InsuranceLevel = insuranceLevel; }
    void SetGangId(int gangId) { m_GangId = gangId; }
    void SetPurchaseTime(int purchaseTime) { m_PurchaseTime = purchaseTime; }
    void SetLastSpawnTime(int lastSpawnTime) { m_LastSpawnTime = lastSpawnTime; }
    void SetLastStoreTime(int lastStoreTime) { m_LastStoreTime = lastStoreTime; }
    void SetColor(string color) { m_Color = color; }
    void SetModifications(string modifications) { m_Modifications = modifications; }
    void SetInventory(string inventory) { m_Inventory = inventory; }
    void SetIsImpounded(bool isImpounded) { m_IsImpounded = isImpounded; }
    void SetImpoundTime(int impoundTime) { m_ImpoundTime = impoundTime; }
    void SetCustomName(string customName) { m_CustomName = customName; }

    string GetVehicleId() { return m_VehicleId; }
    string GetOwnerId() { return m_OwnerId; }
    string GetOwnerName() { return m_OwnerName; }
    string GetVehicleClass() { return m_VehicleClass; }
    string GetVehicleType() { return m_VehicleType; }
    string GetPlate() { return m_Plate; }
    bool IsAlive() { return m_IsAlive; }
    bool IsActive() { return m_IsActive; }
    int GetInsuranceLevel() { return m_InsuranceLevel; }
    int GetGangId() { return m_GangId; }
    int GetPurchaseTime() { return m_PurchaseTime; }
    int GetLastSpawnTime() { return m_LastSpawnTime; }
    int GetLastStoreTime() { return m_LastStoreTime; }
    string GetColor() { return m_Color; }
    string GetModifications() { return m_Modifications; }
    string GetInventory() { return m_Inventory; }
    bool IsImpounded() { return m_IsImpounded; }
    int GetImpoundTime() { return m_ImpoundTime; }
    string GetCustomName() { return m_CustomName; }

    bool IsInsured() { return m_InsuranceLevel > 0; }
    bool IsGangVehicle() { return m_GangId > 0; }
}

//! Garage location data structure
class EdenGarageLocation
{
    protected string m_GarageId;
    protected string m_GarageName;
    protected vector m_Position;
    protected string m_VehicleType;
    protected bool m_IsActive;
    protected vector m_SpawnPosition;
    protected float m_SpawnDirection;
    protected float m_InteractionRadius;

    void EdenGarageLocation()
    {
        m_IsActive = true;
        m_SpawnDirection = 0;
        m_InteractionRadius = 10.0;
    }

    void SetGarageId(string garageId) { m_GarageId = garageId; }
    void SetGarageName(string garageName) { m_GarageName = garageName; }
    void SetPosition(vector position) { m_Position = position; }
    void SetVehicleType(string vehicleType) { m_VehicleType = vehicleType; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    void SetSpawnPosition(vector spawnPosition) { m_SpawnPosition = spawnPosition; }
    void SetSpawnDirection(float spawnDirection) { m_SpawnDirection = spawnDirection; }
    void SetInteractionRadius(float radius) { m_InteractionRadius = radius; }

    string GetGarageId() { return m_GarageId; }
    string GetGarageName() { return m_GarageName; }
    vector GetPosition() { return m_Position; }
    string GetVehicleType() { return m_VehicleType; }
    bool IsActive() { return m_IsActive; }
    vector GetSpawnPosition() { return m_SpawnPosition; }
    float GetSpawnDirection() { return m_SpawnDirection; }
    float GetInteractionRadius() { return m_InteractionRadius; }

    bool IsPlayerInRange(vector playerPosition)
    {
        return vector.Distance(m_Position, playerPosition) <= m_InteractionRadius;
    }
}

//! Chop shop location data structure
class EdenChopShopLocation
{
    protected string m_ChopShopId;
    protected string m_ChopShopName;
    protected vector m_Position;
    protected bool m_IsActive;
    protected float m_InteractionRadius;
    protected int m_LastRaidTime;
    protected bool m_IsRaided;

    void EdenChopShopLocation()
    {
        m_IsActive = true;
        m_InteractionRadius = 25.0;
        m_LastRaidTime = 0;
        m_IsRaided = false;
    }

    void SetChopShopId(string chopShopId) { m_ChopShopId = chopShopId; }
    void SetChopShopName(string chopShopName) { m_ChopShopName = chopShopName; }
    void SetPosition(vector position) { m_Position = position; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    void SetInteractionRadius(float radius) { m_InteractionRadius = radius; }
    void SetLastRaidTime(int lastRaidTime) { m_LastRaidTime = lastRaidTime; }
    void SetIsRaided(bool isRaided) { m_IsRaided = isRaided; }

    string GetChopShopId() { return m_ChopShopId; }
    string GetChopShopName() { return m_ChopShopName; }
    vector GetPosition() { return m_Position; }
    bool IsActive() { return m_IsActive; }
    float GetInteractionRadius() { return m_InteractionRadius; }
    int GetLastRaidTime() { return m_LastRaidTime; }
    bool IsRaided() { return m_IsRaided; }

    bool IsPlayerInRange(vector playerPosition)
    {
        return vector.Distance(m_Position, playerPosition) <= m_InteractionRadius;
    }

    bool CanBeUsed(int currentTime)
    {
        return m_IsActive && !m_IsRaided;
    }
}

//! Vehicle modification configuration
class EdenVehicleModConfig
{
    protected string m_ModId;
    protected string m_ModName;
    protected int m_Level;
    protected int m_Price;
    protected float m_Multiplier;
    protected bool m_IsAvailable;

    void SetModId(string modId) { m_ModId = modId; }
    void SetModName(string modName) { m_ModName = modName; }
    void SetLevel(int level) { m_Level = level; }
    void SetPrice(int price) { m_Price = price; }
    void SetMultiplier(float multiplier) { m_Multiplier = multiplier; }
    void SetIsAvailable(bool isAvailable) { m_IsAvailable = isAvailable; }

    string GetModId() { return m_ModId; }
    string GetModName() { return m_ModName; }
    int GetLevel() { return m_Level; }
    int GetPrice() { return m_Price; }
    float GetMultiplier() { return m_Multiplier; }
    bool IsAvailable() { return m_IsAvailable; }
}

//! Vehicle color configuration
class EdenVehicleColorConfig
{
    protected string m_ColorId;
    protected string m_ColorName;
    protected string m_ColorData;
    protected int m_Price;
    protected bool m_IsAvailable;

    void SetColorId(string colorId) { m_ColorId = colorId; }
    void SetColorName(string colorName) { m_ColorName = colorName; }
    void SetColorData(string colorData) { m_ColorData = colorData; }
    void SetPrice(int price) { m_Price = price; }
    void SetIsAvailable(bool isAvailable) { m_IsAvailable = isAvailable; }

    string GetColorId() { return m_ColorId; }
    string GetColorName() { return m_ColorName; }
    string GetColorData() { return m_ColorData; }
    int GetPrice() { return m_Price; }
    bool IsAvailable() { return m_IsAvailable; }
}

//! Mod shop location
class EdenModShopLocation
{
    protected string m_ShopId;
    protected string m_ShopName;
    protected vector m_Position;
    protected bool m_IsActive;
    protected float m_InteractionRadius;

    void SetShopId(string shopId) { m_ShopId = shopId; }
    void SetShopName(string shopName) { m_ShopName = shopName; }
    void SetPosition(vector position) { m_Position = position; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    void SetInteractionRadius(float radius) { m_InteractionRadius = radius; }

    string GetShopId() { return m_ShopId; }
    string GetShopName() { return m_ShopName; }
    vector GetPosition() { return m_Position; }
    bool IsActive() { return m_IsActive; }
    float GetInteractionRadius() { return m_InteractionRadius; }

    bool IsPlayerInRange(vector playerPosition)
    {
        return vector.Distance(m_Position, playerPosition) <= m_InteractionRadius;
    }
}

//! Vehicle modification request
class EdenVehicleModificationRequest
{
    protected int m_TurboLevel;
    protected int m_StorageLevel;
    protected int m_SecurityLevel;
    protected int m_InsuranceLevel;
    protected string m_ColorId;
    protected bool m_RepairVehicle;

    void EdenVehicleModificationRequest()
    {
        m_TurboLevel = 0;
        m_StorageLevel = 0;
        m_SecurityLevel = 0;
        m_InsuranceLevel = 0;
        m_ColorId = "";
        m_RepairVehicle = true;
    }

    void SetTurboLevel(int turboLevel) { m_TurboLevel = turboLevel; }
    void SetStorageLevel(int storageLevel) { m_StorageLevel = storageLevel; }
    void SetSecurityLevel(int securityLevel) { m_SecurityLevel = securityLevel; }
    void SetInsuranceLevel(int insuranceLevel) { m_InsuranceLevel = insuranceLevel; }
    void SetColorId(string colorId) { m_ColorId = colorId; }
    void SetRepairVehicle(bool repairVehicle) { m_RepairVehicle = repairVehicle; }

    int GetTurboLevel() { return m_TurboLevel; }
    int GetStorageLevel() { return m_StorageLevel; }
    int GetSecurityLevel() { return m_SecurityLevel; }
    int GetInsuranceLevel() { return m_InsuranceLevel; }
    string GetColorId() { return m_ColorId; }
    bool ShouldRepairVehicle() { return m_RepairVehicle; }
}

//! Vehicle modification options
class EdenVehicleModificationOptions
{
    protected int m_MaxTurboLevel;
    protected int m_MaxStorageLevel;
    protected int m_MaxSecurityLevel;
    protected bool m_InsuranceAvailable;
    protected ref array<string> m_AvailableColors;

    void EdenVehicleModificationOptions()
    {
        m_MaxTurboLevel = 0;
        m_MaxStorageLevel = 0;
        m_MaxSecurityLevel = 0;
        m_InsuranceAvailable = false;
        m_AvailableColors = new array<string>();
    }

    void SetMaxTurboLevel(int maxLevel) { m_MaxTurboLevel = maxLevel; }
    void SetMaxStorageLevel(int maxLevel) { m_MaxStorageLevel = maxLevel; }
    void SetMaxSecurityLevel(int maxLevel) { m_MaxSecurityLevel = maxLevel; }
    void SetInsuranceAvailable(bool available) { m_InsuranceAvailable = available; }
    void SetAvailableColors(array<string> colors) { m_AvailableColors = colors; }

    int GetMaxTurboLevel() { return m_MaxTurboLevel; }
    int GetMaxStorageLevel() { return m_MaxStorageLevel; }
    int GetMaxSecurityLevel() { return m_MaxSecurityLevel; }
    bool IsInsuranceAvailable() { return m_InsuranceAvailable; }
    array<string> GetAvailableColors() { return m_AvailableColors; }
}

//! Modification prices
class EdenModificationPrices
{
    protected int m_TurboPrice;
    protected int m_StoragePrice;
    protected int m_SecurityPrice;
    protected int m_BasicInsurancePrice;
    protected int m_FullInsurancePrice;
    protected int m_PaintJobBasePrice;

    void SetTurboPrice(int price) { m_TurboPrice = price; }
    void SetStoragePrice(int price) { m_StoragePrice = price; }
    void SetSecurityPrice(int price) { m_SecurityPrice = price; }
    void SetBasicInsurancePrice(int price) { m_BasicInsurancePrice = price; }
    void SetFullInsurancePrice(int price) { m_FullInsurancePrice = price; }
    void SetPaintJobBasePrice(int price) { m_PaintJobBasePrice = price; }

    int GetTurboPrice() { return m_TurboPrice; }
    int GetStoragePrice() { return m_StoragePrice; }
    int GetSecurityPrice() { return m_SecurityPrice; }
    int GetBasicInsurancePrice() { return m_BasicInsurancePrice; }
    int GetFullInsurancePrice() { return m_FullInsurancePrice; }
    int GetPaintJobBasePrice() { return m_PaintJobBasePrice; }
}

//! Vehicle key data structure
class EdenVehicleKeyData
{
    protected string m_KeyId;
    protected string m_PlayerId;
    protected string m_VehicleId;
    protected bool m_IsPermanent;
    protected int m_CreationTime;
    protected int m_ExpirationTime;
    protected bool m_IsActive;
    protected string m_KeyType;
    protected int m_GangId;

    void EdenVehicleKeyData()
    {
        m_IsPermanent = true;
        m_CreationTime = 0;
        m_ExpirationTime = 0;
        m_IsActive = true;
        m_KeyType = "Standard";
        m_GangId = 0;
    }

    void SetKeyId(string keyId) { m_KeyId = keyId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    void SetVehicleId(string vehicleId) { m_VehicleId = vehicleId; }
    void SetIsPermanent(bool isPermanent) { m_IsPermanent = isPermanent; }
    void SetCreationTime(int creationTime) { m_CreationTime = creationTime; }
    void SetExpirationTime(int expirationTime) { m_ExpirationTime = expirationTime; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    void SetKeyType(string keyType) { m_KeyType = keyType; }
    void SetGangId(int gangId) { m_GangId = gangId; }

    string GetKeyId() { return m_KeyId; }
    string GetPlayerId() { return m_PlayerId; }
    string GetVehicleId() { return m_VehicleId; }
    bool IsPermanent() { return m_IsPermanent; }
    int GetCreationTime() { return m_CreationTime; }
    int GetExpirationTime() { return m_ExpirationTime; }
    bool IsActive() { return m_IsActive; }
    string GetKeyType() { return m_KeyType; }
    int GetGangId() { return m_GangId; }

    bool IsExpired(int currentTime)
    {
        if (m_IsPermanent)
            return false;

        return m_ExpirationTime > 0 && currentTime > m_ExpirationTime;
    }

    bool IsGangKey() { return m_GangId > 0; }
}

//! Insurance policy data structure
class EdenInsurancePolicy
{
    protected string m_PolicyId;
    protected string m_PlayerId;
    protected string m_VehicleId;
    protected int m_InsuranceLevel;
    protected string m_CompanyId;
    protected int m_PurchaseTime;
    protected int m_ExpirationTime;
    protected bool m_IsActive;
    protected int m_ClaimsUsed;
    protected int m_PremiumPaid;

    void SetPolicyId(string policyId) { m_PolicyId = policyId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    void SetVehicleId(string vehicleId) { m_VehicleId = vehicleId; }
    void SetInsuranceLevel(int insuranceLevel) { m_InsuranceLevel = insuranceLevel; }
    void SetCompanyId(string companyId) { m_CompanyId = companyId; }
    void SetPurchaseTime(int purchaseTime) { m_PurchaseTime = purchaseTime; }
    void SetExpirationTime(int expirationTime) { m_ExpirationTime = expirationTime; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    void SetClaimsUsed(int claimsUsed) { m_ClaimsUsed = claimsUsed; }
    void SetPremiumPaid(int premiumPaid) { m_PremiumPaid = premiumPaid; }

    string GetPolicyId() { return m_PolicyId; }
    string GetPlayerId() { return m_PlayerId; }
    string GetVehicleId() { return m_VehicleId; }
    int GetInsuranceLevel() { return m_InsuranceLevel; }
    string GetCompanyId() { return m_CompanyId; }
    int GetPurchaseTime() { return m_PurchaseTime; }
    int GetExpirationTime() { return m_ExpirationTime; }
    bool IsActive() { return m_IsActive; }
    int GetClaimsUsed() { return m_ClaimsUsed; }
    int GetPremiumPaid() { return m_PremiumPaid; }

    bool IsExpired(int currentTime) { return currentTime > m_ExpirationTime; }
    bool IsBasicCoverage() { return m_InsuranceLevel == 1; }
    bool IsFullCoverage() { return m_InsuranceLevel == 2; }
}

//! Insurance claim data structure
class EdenInsuranceClaim
{
    protected string m_ClaimId;
    protected string m_PolicyId;
    protected string m_PlayerId;
    protected string m_VehicleId;
    protected string m_ClaimReason;
    protected int m_DamageAmount;
    protected int m_PayoutAmount;
    protected int m_ClaimTime;
    protected int m_ProcessTime;
    protected string m_ClaimStatus;
    protected bool m_IsProcessed;

    void SetClaimId(string claimId) { m_ClaimId = claimId; }
    void SetPolicyId(string policyId) { m_PolicyId = policyId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    void SetVehicleId(string vehicleId) { m_VehicleId = vehicleId; }
    void SetClaimReason(string claimReason) { m_ClaimReason = claimReason; }
    void SetDamageAmount(int damageAmount) { m_DamageAmount = damageAmount; }
    void SetPayoutAmount(int payoutAmount) { m_PayoutAmount = payoutAmount; }
    void SetClaimTime(int claimTime) { m_ClaimTime = claimTime; }
    void SetProcessTime(int processTime) { m_ProcessTime = processTime; }
    void SetClaimStatus(string claimStatus) { m_ClaimStatus = claimStatus; }
    void SetIsProcessed(bool isProcessed) { m_IsProcessed = isProcessed; }

    string GetClaimId() { return m_ClaimId; }
    string GetPolicyId() { return m_PolicyId; }
    string GetPlayerId() { return m_PlayerId; }
    string GetVehicleId() { return m_VehicleId; }
    string GetClaimReason() { return m_ClaimReason; }
    int GetDamageAmount() { return m_DamageAmount; }
    int GetPayoutAmount() { return m_PayoutAmount; }
    int GetClaimTime() { return m_ClaimTime; }
    int GetProcessTime() { return m_ProcessTime; }
    string GetClaimStatus() { return m_ClaimStatus; }
    bool IsProcessed() { return m_IsProcessed; }

    bool IsApproved() { return m_ClaimStatus == "Approved"; }
    bool IsDenied() { return m_ClaimStatus == "Denied"; }
    bool IsPending() { return m_ClaimStatus == "Pending"; }
}

//! Insurance company data structure
class EdenInsuranceCompany
{
    protected string m_CompanyId;
    protected string m_CompanyName;
    protected float m_PayoutRate;
    protected bool m_IsActive;
    protected int m_ClaimsProcessed;
    protected int m_TotalPayouts;

    void SetCompanyId(string companyId) { m_CompanyId = companyId; }
    void SetCompanyName(string companyName) { m_CompanyName = companyName; }
    void SetPayoutRate(float payoutRate) { m_PayoutRate = payoutRate; }
    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    void SetClaimsProcessed(int claimsProcessed) { m_ClaimsProcessed = claimsProcessed; }
    void SetTotalPayouts(int totalPayouts) { m_TotalPayouts = totalPayouts; }

    string GetCompanyId() { return m_CompanyId; }
    string GetCompanyName() { return m_CompanyName; }
    float GetPayoutRate() { return m_PayoutRate; }
    bool IsActive() { return m_IsActive; }
    int GetClaimsProcessed() { return m_ClaimsProcessed; }
    int GetTotalPayouts() { return m_TotalPayouts; }
}

//! ========================================
//! HOUSING SYSTEM DATA STRUCTURES
//! ========================================

//! House data structure
class EdenHouseData
{
    protected int m_HouseId;
    protected string m_OwnerId;
    protected string m_OwnerName;
    protected string m_HouseClass;
    protected vector m_Position;
    protected bool m_IsOwned;
    protected int m_PurchaseTime;
    protected int m_ExpirationTime;
    protected string m_VirtualInventory;
    protected string m_PhysicalInventory;
    protected int m_VirtualStorageCapacity;
    protected int m_PhysicalStorageCapacity;
    protected string m_PlayerKeys;
    protected bool m_IsLocked;
    protected bool m_HasOilStorage;
    protected bool m_IsInAuctionHouse;
    protected int m_SalePrice;
    protected int m_SaleTime;

    void SetHouseId(int id) { m_HouseId = id; }
    int GetHouseId() { return m_HouseId; }

    void SetOwnerId(string id) { m_OwnerId = id; }
    string GetOwnerId() { return m_OwnerId; }

    void SetOwnerName(string name) { m_OwnerName = name; }
    string GetOwnerName() { return m_OwnerName; }

    void SetHouseClass(string houseClass) { m_HouseClass = houseClass; }
    string GetHouseClass() { return m_HouseClass; }

    void SetPosition(vector pos) { m_Position = pos; }
    vector GetPosition() { return m_Position; }

    void SetIsOwned(bool owned) { m_IsOwned = owned; }
    bool IsOwned() { return m_IsOwned; }

    void SetPurchaseTime(int time) { m_PurchaseTime = time; }
    int GetPurchaseTime() { return m_PurchaseTime; }

    void SetExpirationTime(int time) { m_ExpirationTime = time; }
    int GetExpirationTime() { return m_ExpirationTime; }

    void SetVirtualInventory(string inventory) { m_VirtualInventory = inventory; }
    string GetVirtualInventory() { return m_VirtualInventory; }

    void SetPhysicalInventory(string inventory) { m_PhysicalInventory = inventory; }
    string GetPhysicalInventory() { return m_PhysicalInventory; }

    void SetVirtualStorageCapacity(int capacity) { m_VirtualStorageCapacity = capacity; }
    int GetVirtualStorageCapacity() { return m_VirtualStorageCapacity; }

    void SetPhysicalStorageCapacity(int capacity) { m_PhysicalStorageCapacity = capacity; }
    int GetPhysicalStorageCapacity() { return m_PhysicalStorageCapacity; }

    void SetPlayerKeys(string keys) { m_PlayerKeys = keys; }
    string GetPlayerKeys() { return m_PlayerKeys; }

    void SetIsLocked(bool locked) { m_IsLocked = locked; }
    bool IsLocked() { return m_IsLocked; }

    void SetHasOilStorage(bool hasOil) { m_HasOilStorage = hasOil; }
    bool HasOilStorage() { return m_HasOilStorage; }

    void SetIsInAuctionHouse(bool inAH) { m_IsInAuctionHouse = inAH; }
    bool IsInAuctionHouse() { return m_IsInAuctionHouse; }

    void SetSalePrice(int price) { m_SalePrice = price; }
    int GetSalePrice() { return m_SalePrice; }

    void SetSaleTime(int time) { m_SaleTime = time; }
    int GetSaleTime() { return m_SaleTime; }
}

//! House configuration data
class EdenHouseConfig
{
    protected string m_HouseClass;
    protected int m_Price;
    protected int m_MaxCrates;
    protected int m_BaseVirtualStorage;
    protected int m_BasePhysicalStorage;
    protected bool m_IsAvailable;

    void SetHouseClass(string houseClass) { m_HouseClass = houseClass; }
    string GetHouseClass() { return m_HouseClass; }

    void SetPrice(int price) { m_Price = price; }
    int GetPrice() { return m_Price; }

    void SetMaxCrates(int maxCrates) { m_MaxCrates = maxCrates; }
    int GetMaxCrates() { return m_MaxCrates; }

    void SetBaseVirtualStorage(int storage) { m_BaseVirtualStorage = storage; }
    int GetBaseVirtualStorage() { return m_BaseVirtualStorage; }

    void SetBasePhysicalStorage(int storage) { m_BasePhysicalStorage = storage; }
    int GetBasePhysicalStorage() { return m_BasePhysicalStorage; }

    void SetIsAvailable(bool available) { m_IsAvailable = available; }
    bool IsAvailable() { return m_IsAvailable; }
}

//! House inventory data
class EdenHouseInventory
{
    protected int m_HouseId;
    protected ref map<string, int> m_VirtualItems; // Item class -> quantity
    protected ref map<string, int> m_PhysicalItems; // Item class -> quantity
    protected float m_VirtualWeight;
    protected float m_PhysicalWeight;
    protected int m_VirtualCapacity;
    protected int m_PhysicalCapacity;
    protected bool m_CurrentInventoryType; // true = virtual, false = physical

    void EdenHouseInventory()
    {
        m_VirtualItems = new map<string, int>();
        m_PhysicalItems = new map<string, int>();
        m_VirtualWeight = 0.0;
        m_PhysicalWeight = 0.0;
        m_VirtualCapacity = 100;
        m_PhysicalCapacity = 100;
        m_CurrentInventoryType = true;
    }

    void Initialize()
    {
        // Initialize inventory system
    }

    void SetHouseId(int id) { m_HouseId = id; }
    int GetHouseId() { return m_HouseId; }

    void SetCurrentInventoryType(bool isVirtual) { m_CurrentInventoryType = isVirtual; }
    bool GetCurrentInventoryType() { return m_CurrentInventoryType; }

    void SetVirtualCapacity(int capacity) { m_VirtualCapacity = capacity; }
    int GetVirtualCapacity() { return m_VirtualCapacity; }

    void SetPhysicalCapacity(int capacity) { m_PhysicalCapacity = capacity; }
    int GetPhysicalCapacity() { return m_PhysicalCapacity; }

    float GetVirtualWeight() { return m_VirtualWeight; }
    float GetPhysicalWeight() { return m_PhysicalWeight; }

    bool AddVirtualItem(string itemClass, int quantity, float weight)
    {
        if (m_VirtualItems.Contains(itemClass))
        {
            m_VirtualItems.Set(itemClass, m_VirtualItems.Get(itemClass) + quantity);
        }
        else
        {
            m_VirtualItems.Set(itemClass, quantity);
        }
        m_VirtualWeight += weight;
        return true;
    }

    bool AddPhysicalItem(string itemClass, int quantity, float weight)
    {
        if (m_PhysicalItems.Contains(itemClass))
        {
            m_PhysicalItems.Set(itemClass, m_PhysicalItems.Get(itemClass) + quantity);
        }
        else
        {
            m_PhysicalItems.Set(itemClass, quantity);
        }
        m_PhysicalWeight += weight;
        return true;
    }

    bool RemoveVirtualItem(string itemClass, int quantity, float weight)
    {
        if (!m_VirtualItems.Contains(itemClass))
            return false;

        int currentQuantity = m_VirtualItems.Get(itemClass);
        if (currentQuantity < quantity)
            return false;

        if (currentQuantity == quantity)
        {
            m_VirtualItems.Remove(itemClass);
        }
        else
        {
            m_VirtualItems.Set(itemClass, currentQuantity - quantity);
        }

        m_VirtualWeight -= weight;
        return true;
    }

    bool RemovePhysicalItem(string itemClass, int quantity, float weight)
    {
        if (!m_PhysicalItems.Contains(itemClass))
            return false;

        int currentQuantity = m_PhysicalItems.Get(itemClass);
        if (currentQuantity < quantity)
            return false;

        if (currentQuantity == quantity)
        {
            m_PhysicalItems.Remove(itemClass);
        }
        else
        {
            m_PhysicalItems.Set(itemClass, currentQuantity - quantity);
        }

        m_PhysicalWeight -= weight;
        return true;
    }

    bool HasVirtualItem(string itemClass, int quantity)
    {
        if (!m_VirtualItems.Contains(itemClass))
            return false;

        return m_VirtualItems.Get(itemClass) >= quantity;
    }

    bool HasPhysicalItem(string itemClass, int quantity)
    {
        if (!m_PhysicalItems.Contains(itemClass))
            return false;

        return m_PhysicalItems.Get(itemClass) >= quantity;
    }

    map<string, int> GetVirtualItems() { return m_VirtualItems; }
    map<string, int> GetPhysicalItems() { return m_PhysicalItems; }
}

//! House interaction data
class EdenHouseInteraction
{
    protected int m_HouseId;
    protected bool m_IsLocked;
    protected bool m_HasLighting;
    protected bool m_DoorsNeedRepair;
    protected float m_DoorHealth;
    protected int m_LastInteractionTime;

    void SetHouseId(int id) { m_HouseId = id; }
    int GetHouseId() { return m_HouseId; }

    void SetIsLocked(bool locked) { m_IsLocked = locked; }
    bool IsLocked() { return m_IsLocked; }

    void SetHasLighting(bool lighting) { m_HasLighting = lighting; }
    bool HasLighting() { return m_HasLighting; }

    void SetDoorsNeedRepair(bool needRepair) { m_DoorsNeedRepair = needRepair; }
    bool DoorsNeedRepair() { return m_DoorsNeedRepair; }

    void SetDoorHealth(float health) { m_DoorHealth = health; }
    float GetDoorHealth() { return m_DoorHealth; }

    void SetLastInteractionTime(int time) { m_LastInteractionTime = time; }
    int GetLastInteractionTime() { return m_LastInteractionTime; }
}

//! Inventory item data
class EdenInventoryItem
{
    protected string m_ItemClass;
    protected int m_Quantity;
    protected float m_Weight;
    protected bool m_IsIllegal;

    void SetItemClass(string itemClass) { m_ItemClass = itemClass; }
    string GetItemClass() { return m_ItemClass; }

    void SetQuantity(int quantity) { m_Quantity = quantity; }
    int GetQuantity() { return m_Quantity; }

    void SetWeight(float weight) { m_Weight = weight; }
    float GetWeight() { return m_Weight; }

    void SetIsIllegal(bool illegal) { m_IsIllegal = illegal; }
    bool IsIllegal() { return m_IsIllegal; }
}

//! ========================================
//! ADMIN SYSTEM DATA STRUCTURES
//! ========================================

//! Admin command data class
class EdenAdminCommand
{
    protected string m_CommandName;
    protected string m_Description;
    protected int m_RequiredLevel;
    protected string m_Usage;

    void EdenAdminCommand(string commandName = "", string description = "", int requiredLevel = 0, string usage = "")
    {
        m_CommandName = commandName;
        m_Description = description;
        m_RequiredLevel = requiredLevel;
        m_Usage = usage;
    }

    // Getters and setters
    void SetCommandName(string name) { m_CommandName = name; }
    string GetCommandName() { return m_CommandName; }

    void SetDescription(string description) { m_Description = description; }
    string GetDescription() { return m_Description; }

    void SetRequiredLevel(int level) { m_RequiredLevel = level; }
    int GetRequiredLevel() { return m_RequiredLevel; }

    void SetUsage(string usage) { m_Usage = usage; }
    string GetUsage() { return m_Usage; }
}

//! Admin log data class
class EdenAdminLog
{
    protected string m_ActionType;
    protected string m_AdminId;
    protected string m_TargetId;
    protected string m_Description;
    protected int m_Timestamp;
    protected vector m_Position;

    void EdenAdminLog()
    {
        m_ActionType = "";
        m_AdminId = "";
        m_TargetId = "";
        m_Description = "";
        m_Timestamp = 0;
        m_Position = "0 0 0";
    }

    // Getters and setters
    void SetActionType(string actionType) { m_ActionType = actionType; }
    string GetActionType() { return m_ActionType; }

    void SetAdminId(string adminId) { m_AdminId = adminId; }
    string GetAdminId() { return m_AdminId; }

    void SetTargetId(string targetId) { m_TargetId = targetId; }
    string GetTargetId() { return m_TargetId; }

    void SetDescription(string description) { m_Description = description; }
    string GetDescription() { return m_Description; }

    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }
    int GetTimestamp() { return m_Timestamp; }

    void SetPosition(vector position) { m_Position = position; }
    vector GetPosition() { return m_Position; }
}

//! Violation severity enum
enum ViolationSeverity
{
    LOW = 0,
    MEDIUM = 1,
    HIGH = 2,
    CRITICAL = 3
}

//! Violation report data class
class EdenViolationReport
{
    protected string m_PlayerId;
    protected string m_ViolationType;
    protected string m_Description;
    protected ViolationSeverity m_Severity;
    protected int m_Timestamp;
    protected vector m_Position;

    void EdenViolationReport()
    {
        m_PlayerId = "";
        m_ViolationType = "";
        m_Description = "";
        m_Severity = ViolationSeverity.LOW;
        m_Timestamp = 0;
        m_Position = "0 0 0";
    }

    // Getters and setters
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    string GetPlayerId() { return m_PlayerId; }

    void SetViolationType(string violationType) { m_ViolationType = violationType; }
    string GetViolationType() { return m_ViolationType; }

    void SetDescription(string description) { m_Description = description; }
    string GetDescription() { return m_Description; }

    void SetSeverity(ViolationSeverity severity) { m_Severity = severity; }
    ViolationSeverity GetSeverity() { return m_Severity; }

    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }
    int GetTimestamp() { return m_Timestamp; }

    void SetPosition(vector position) { m_Position = position; }
    vector GetPosition() { return m_Position; }
}

//! Player monitor data class
class EdenPlayerMonitorData
{
    protected string m_PlayerId;
    protected ref array<vector> m_PositionHistory;
    protected ref array<float> m_SpeedHistory;
    protected ref array<int> m_TimeHistory;
    protected float m_CurrentSpeed;
    protected bool m_HasGodMode;
    protected bool m_IsInvisible;
    protected bool m_InVehicle;
    protected string m_CurrentVehicle;
    protected int m_LastUpdate;

    void EdenPlayerMonitorData()
    {
        m_PlayerId = "";
        m_PositionHistory = new array<vector>();
        m_SpeedHistory = new array<float>();
        m_TimeHistory = new array<int>();
        m_CurrentSpeed = 0.0;
        m_HasGodMode = false;
        m_IsInvisible = false;
        m_InVehicle = false;
        m_CurrentVehicle = "";
        m_LastUpdate = 0;
    }

    // Getters and setters
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    string GetPlayerId() { return m_PlayerId; }

    void AddPositionEntry(vector position, int time)
    {
        m_PositionHistory.Insert(position);
        m_TimeHistory.Insert(time);

        // Keep only last 10 positions
        if (m_PositionHistory.Count() > 10)
        {
            m_PositionHistory.RemoveOrdered(0);
            m_TimeHistory.RemoveOrdered(0);
        }
    }

    array<vector> GetPositionHistory() { return m_PositionHistory; }

    vector GetLastPosition()
    {
        if (m_PositionHistory.Count() > 0)
            return m_PositionHistory[m_PositionHistory.Count() - 1];
        return "0 0 0";
    }

    int GetLastPositionTime()
    {
        if (m_TimeHistory.Count() > 0)
            return m_TimeHistory[m_TimeHistory.Count() - 1];
        return 0;
    }

    void AddSpeedEntry(float speed, int time)
    {
        m_SpeedHistory.Insert(speed);

        // Keep only last 10 speeds
        if (m_SpeedHistory.Count() > 10)
        {
            m_SpeedHistory.RemoveOrdered(0);
        }
    }

    array<float> GetSpeedHistory() { return m_SpeedHistory; }

    void SetCurrentSpeed(float speed) { m_CurrentSpeed = speed; }
    float GetCurrentSpeed() { return m_CurrentSpeed; }

    void SetHasGodMode(bool hasGodMode) { m_HasGodMode = hasGodMode; }
    bool HasGodMode() { return m_HasGodMode; }

    void SetIsInvisible(bool isInvisible) { m_IsInvisible = isInvisible; }
    bool IsInvisible() { return m_IsInvisible; }

    void SetInVehicle(bool inVehicle) { m_InVehicle = inVehicle; }
    bool IsInVehicle() { return m_InVehicle; }

    void SetCurrentVehicle(string vehicle) { m_CurrentVehicle = vehicle; }
    string GetCurrentVehicle() { return m_CurrentVehicle; }

    void SetLastUpdate(int time) { m_LastUpdate = time; }
    int GetLastUpdate() { return m_LastUpdate; }
}

//! Punishment type enum
enum PunishmentType
{
    WARNING = 0,
    KICK = 1,
    TEMP_BAN = 2,
    PERMANENT_BAN = 3
}

//! Ban data class
class EdenBanData
{
    protected string m_PlayerId;
    protected string m_AdminId;
    protected string m_Reason;
    protected int m_BanTime;
    protected int m_Duration;
    protected bool m_IsPermanent;
    protected bool m_IsActive;
    protected int m_BanLevel;

    void EdenBanData()
    {
        m_PlayerId = "";
        m_AdminId = "";
        m_Reason = "";
        m_BanTime = 0;
        m_Duration = 0;
        m_IsPermanent = false;
        m_IsActive = true;
        m_BanLevel = 1;
    }

    // Getters and setters
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    string GetPlayerId() { return m_PlayerId; }

    void SetAdminId(string adminId) { m_AdminId = adminId; }
    string GetAdminId() { return m_AdminId; }

    void SetReason(string reason) { m_Reason = reason; }
    string GetReason() { return m_Reason; }

    void SetBanTime(int banTime) { m_BanTime = banTime; }
    int GetBanTime() { return m_BanTime; }

    void SetDuration(int duration) { m_Duration = duration; }
    int GetDuration() { return m_Duration; }

    void SetIsPermanent(bool isPermanent) { m_IsPermanent = isPermanent; }
    bool IsPermanent() { return m_IsPermanent; }

    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    bool IsActive() { return m_IsActive; }

    void SetBanLevel(int banLevel) { m_BanLevel = banLevel; }
    int GetBanLevel() { return m_BanLevel; }

    bool IsExpired(int currentTime)
    {
        if (m_IsPermanent)
            return false;

        return currentTime >= (m_BanTime + m_Duration);
    }

    int GetExpirationTime()
    {
        if (m_IsPermanent)
            return -1;

        return m_BanTime + m_Duration;
    }
}

//! Warning data class
class EdenWarning
{
    protected string m_PlayerId;
    protected string m_AdminId;
    protected string m_Reason;
    protected int m_WarnTime;
    protected int m_ExpirationTime;
    protected bool m_IsActive;

    void EdenWarning()
    {
        m_PlayerId = "";
        m_AdminId = "";
        m_Reason = "";
        m_WarnTime = 0;
        m_ExpirationTime = 0;
        m_IsActive = true;
    }

    // Getters and setters
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    string GetPlayerId() { return m_PlayerId; }

    void SetAdminId(string adminId) { m_AdminId = adminId; }
    string GetAdminId() { return m_AdminId; }

    void SetReason(string reason) { m_Reason = reason; }
    string GetReason() { return m_Reason; }

    void SetWarnTime(int warnTime) { m_WarnTime = warnTime; }
    int GetWarnTime() { return m_WarnTime; }

    void SetExpirationTime(int expirationTime) { m_ExpirationTime = expirationTime; }
    int GetExpirationTime() { return m_ExpirationTime; }

    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    bool IsActive() { return m_IsActive; }

    bool IsExpired(int currentTime)
    {
        return currentTime >= m_ExpirationTime;
    }
}

//! Punishment record data class
class EdenPunishmentRecord
{
    protected string m_PlayerId;
    protected string m_AdminId;
    protected PunishmentType m_PunishmentType;
    protected string m_Reason;
    protected int m_Timestamp;
    protected int m_Duration;
    protected bool m_IsActive;

    void EdenPunishmentRecord()
    {
        m_PlayerId = "";
        m_AdminId = "";
        m_PunishmentType = PunishmentType.WARNING;
        m_Reason = "";
        m_Timestamp = 0;
        m_Duration = 0;
        m_IsActive = true;
    }

    // Getters and setters
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    string GetPlayerId() { return m_PlayerId; }

    void SetAdminId(string adminId) { m_AdminId = adminId; }
    string GetAdminId() { return m_AdminId; }

    void SetPunishmentType(PunishmentType punishmentType) { m_PunishmentType = punishmentType; }
    PunishmentType GetPunishmentType() { return m_PunishmentType; }

    void SetReason(string reason) { m_Reason = reason; }
    string GetReason() { return m_Reason; }

    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }
    int GetTimestamp() { return m_Timestamp; }

    void SetDuration(int duration) { m_Duration = duration; }
    int GetDuration() { return m_Duration; }

    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    bool IsActive() { return m_IsActive; }

    bool IsExpired(int currentTime)
    {
        if (m_PunishmentType == PunishmentType.PERMANENT_BAN)
            return false;

        if (m_Duration == 0)
            return false;

        return currentTime >= (m_Timestamp + m_Duration);
    }

    string GetPunishmentTypeString()
    {
        switch (m_PunishmentType)
        {
            case PunishmentType.WARNING:
                return "Warning";
            case PunishmentType.KICK:
                return "Kick";
            case PunishmentType.TEMP_BAN:
                return "Temporary Ban";
            case PunishmentType.PERMANENT_BAN:
                return "Permanent Ban";
            default:
                return "Unknown";
        }
    }
}

//! ========================================
//! UI/COMMUNICATION SYSTEM DATA STRUCTURES
//! ========================================

//! Contact data class
class EdenContact
{
    protected string m_ContactId;
    protected string m_Name;
    protected string m_PhoneNumber;
    protected string m_ContactType; // "player", "emergency", "service"
    protected bool m_IsDefault;

    void EdenContact()
    {
        m_ContactId = "";
        m_Name = "";
        m_PhoneNumber = "";
        m_ContactType = "player";
        m_IsDefault = false;
    }

    // Getters and setters
    void SetContactId(string contactId) { m_ContactId = contactId; }
    string GetContactId() { return m_ContactId; }

    void SetName(string name) { m_Name = name; }
    string GetName() { return m_Name; }

    void SetPhoneNumber(string phoneNumber) { m_PhoneNumber = phoneNumber; }
    string GetPhoneNumber() { return m_PhoneNumber; }

    void SetContactType(string contactType) { m_ContactType = contactType; }
    string GetContactType() { return m_ContactType; }

    void SetIsDefault(bool isDefault) { m_IsDefault = isDefault; }
    bool IsDefault() { return m_IsDefault; }
}

//! Message data class
class EdenMessage
{
    protected string m_MessageId;
    protected string m_SenderId;
    protected string m_SenderName;
    protected string m_ReceiverId;
    protected string m_Content;
    protected int m_Timestamp;
    protected bool m_IsRead;

    void EdenMessage()
    {
        m_MessageId = "";
        m_SenderId = "";
        m_SenderName = "";
        m_ReceiverId = "";
        m_Content = "";
        m_Timestamp = 0;
        m_IsRead = false;
    }

    // Getters and setters
    void SetMessageId(string messageId) { m_MessageId = messageId; }
    string GetMessageId() { return m_MessageId; }

    void SetSenderId(string senderId) { m_SenderId = senderId; }
    string GetSenderId() { return m_SenderId; }

    void SetSenderName(string senderName) { m_SenderName = senderName; }
    string GetSenderName() { return m_SenderName; }

    void SetReceiverId(string receiverId) { m_ReceiverId = receiverId; }
    string GetReceiverId() { return m_ReceiverId; }

    void SetContent(string content) { m_Content = content; }
    string GetContent() { return m_Content; }

    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }
    int GetTimestamp() { return m_Timestamp; }

    void SetIsRead(bool isRead) { m_IsRead = isRead; }
    bool IsRead() { return m_IsRead; }
}

//! Setting item data class
class EdenSettingItem
{
    protected string m_Name;
    protected string m_Description;
    protected string m_Type; // "slider", "checkbox", "combobox"
    protected float m_CurrentValue;
    protected float m_DefaultValue;
    protected float m_MinValue;
    protected float m_MaxValue;
    protected ref array<string> m_ComboOptions;

    void EdenSettingItem()
    {
        m_Name = "";
        m_Description = "";
        m_Type = "slider";
        m_CurrentValue = 0.0;
        m_DefaultValue = 0.0;
        m_MinValue = 0.0;
        m_MaxValue = 1.0;
        m_ComboOptions = new array<string>();
    }

    // Getters and setters
    void SetName(string name) { m_Name = name; }
    string GetName() { return m_Name; }

    void SetDescription(string description) { m_Description = description; }
    string GetDescription() { return m_Description; }

    void SetType(string type) { m_Type = type; }
    string GetType() { return m_Type; }

    void SetCurrentValue(float currentValue) { m_CurrentValue = currentValue; }
    float GetCurrentValue() { return m_CurrentValue; }

    void SetDefaultValue(float defaultValue) { m_DefaultValue = defaultValue; }
    float GetDefaultValue() { return m_DefaultValue; }

    void SetMinValue(float minValue) { m_MinValue = minValue; }
    float GetMinValue() { return m_MinValue; }

    void SetMaxValue(float maxValue) { m_MaxValue = maxValue; }
    float GetMaxValue() { return m_MaxValue; }

    void SetComboOptions(array<string> options) { m_ComboOptions = options; }
    array<string> GetComboOptions() { return m_ComboOptions; }
}

//! Wanted player data class
class EdenWantedPlayer
{
    protected string m_PlayerId;
    protected string m_PlayerName;
    protected int m_BountyAmount;
    protected int m_WantedLevel;
    protected ref array<string> m_Crimes;
    protected string m_LastSeenLocation;
    protected int m_LastSeenTime;
    protected bool m_IsActive;

    void EdenWantedPlayer()
    {
        m_PlayerId = "";
        m_PlayerName = "";
        m_BountyAmount = 0;
        m_WantedLevel = 1;
        m_Crimes = new array<string>();
        m_LastSeenLocation = "";
        m_LastSeenTime = 0;
        m_IsActive = true;
    }

    // Getters and setters
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    string GetPlayerId() { return m_PlayerId; }

    void SetPlayerName(string playerName) { m_PlayerName = playerName; }
    string GetPlayerName() { return m_PlayerName; }

    void SetBountyAmount(int bountyAmount) { m_BountyAmount = bountyAmount; }
    int GetBountyAmount() { return m_BountyAmount; }

    void SetWantedLevel(int wantedLevel) { m_WantedLevel = wantedLevel; }
    int GetWantedLevel() { return m_WantedLevel; }

    void SetCrimes(array<string> crimes) { m_Crimes = crimes; }
    array<string> GetCrimes() { return m_Crimes; }

    void SetLastSeenLocation(string location) { m_LastSeenLocation = location; }
    string GetLastSeenLocation() { return m_LastSeenLocation; }

    void SetLastSeenTime(int time) { m_LastSeenTime = time; }
    int GetLastSeenTime() { return m_LastSeenTime; }

    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    bool IsActive() { return m_IsActive; }

    void AddCrime(string crime) { m_Crimes.Insert(crime); }
    void AddBounty(int amount) { m_BountyAmount += amount; }
}

//! Title data class
class EdenTitleData
{
    protected string m_TitleName;
    protected string m_Description;
    protected string m_Category;
    protected string m_Requirement;
    protected int m_Progress;
    protected int m_MaxProgress;
    protected bool m_IsUnlocked;
    protected bool m_IsEquipped;
    protected int m_UnlockTime;

    void EdenTitleData()
    {
        m_TitleName = "";
        m_Description = "";
        m_Category = "";
        m_Requirement = "";
        m_Progress = 0;
        m_MaxProgress = 100;
        m_IsUnlocked = false;
        m_IsEquipped = false;
        m_UnlockTime = 0;
    }

    // Getters and setters
    void SetTitleName(string titleName) { m_TitleName = titleName; }
    string GetTitleName() { return m_TitleName; }

    void SetDescription(string description) { m_Description = description; }
    string GetDescription() { return m_Description; }

    void SetCategory(string category) { m_Category = category; }
    string GetCategory() { return m_Category; }

    void SetRequirement(string requirement) { m_Requirement = requirement; }
    string GetRequirement() { return m_Requirement; }

    void SetProgress(int progress) { m_Progress = progress; }
    int GetProgress() { return m_Progress; }

    void SetMaxProgress(int maxProgress) { m_MaxProgress = maxProgress; }
    int GetMaxProgress() { return m_MaxProgress; }

    void SetIsUnlocked(bool isUnlocked) { m_IsUnlocked = isUnlocked; }
    bool IsUnlocked() { return m_IsUnlocked; }

    void SetIsEquipped(bool isEquipped) { m_IsEquipped = isEquipped; }
    bool IsEquipped() { return m_IsEquipped; }

    void SetUnlockTime(int unlockTime) { m_UnlockTime = unlockTime; }
    int GetUnlockTime() { return m_UnlockTime; }

    void AddProgress(int amount) { m_Progress += amount; }

    float GetProgressPercent()
    {
        if (m_MaxProgress == 0) return 100.0;
        return ((float)m_Progress / (float)m_MaxProgress) * 100.0;
    }

    bool IsCompleted() { return m_Progress >= m_MaxProgress; }
}

//! Admin command data class
class EdenAdminCommand
{
    protected string m_CommandName;
    protected string m_Description;
    protected int m_RequiredLevel;
    protected string m_Usage;

    void EdenAdminCommand(string commandName = "", string description = "", int requiredLevel = 1, string usage = "")
    {
        m_CommandName = commandName;
        m_Description = description;
        m_RequiredLevel = requiredLevel;
        m_Usage = usage;
    }

    // Getters and setters
    void SetCommandName(string commandName) { m_CommandName = commandName; }
    string GetCommandName() { return m_CommandName; }

    void SetDescription(string description) { m_Description = description; }
    string GetDescription() { return m_Description; }

    void SetRequiredLevel(int requiredLevel) { m_RequiredLevel = requiredLevel; }
    int GetRequiredLevel() { return m_RequiredLevel; }

    void SetUsage(string usage) { m_Usage = usage; }
    string GetUsage() { return m_Usage; }
}

//! Event data class
class EdenEventData
{
    protected string m_EventType;
    protected string m_EventName;
    protected string m_Location;
    protected int m_StartTime;
    protected int m_Duration;
    protected int m_Reward;
    protected bool m_IsActive;
    protected ref array<string> m_Participants;

    void EdenEventData()
    {
        m_EventType = "";
        m_EventName = "";
        m_Location = "";
        m_StartTime = 0;
        m_Duration = 0;
        m_Reward = 0;
        m_IsActive = false;
        m_Participants = new array<string>();
    }

    // Getters and setters
    void SetEventType(string eventType) { m_EventType = eventType; }
    string GetEventType() { return m_EventType; }

    void SetEventName(string eventName) { m_EventName = eventName; }
    string GetEventName() { return m_EventName; }

    void SetLocation(string location) { m_Location = location; }
    string GetLocation() { return m_Location; }

    void SetStartTime(int startTime) { m_StartTime = startTime; }
    int GetStartTime() { return m_StartTime; }

    void SetDuration(int duration) { m_Duration = duration; }
    int GetDuration() { return m_Duration; }

    void SetReward(int reward) { m_Reward = reward; }
    int GetReward() { return m_Reward; }

    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    bool IsActive() { return m_IsActive; }

    void SetParticipants(array<string> participants) { m_Participants = participants; }
    array<string> GetParticipants() { return m_Participants; }

    void AddParticipant(string playerId) { m_Participants.Insert(playerId); }
    void RemoveParticipant(string playerId)
    {
        int index = m_Participants.Find(playerId);
        if (index >= 0) m_Participants.RemoveOrdered(index);
    }

    int GetTimeRemaining(int currentTime)
    {
        int endTime = m_StartTime + m_Duration;
        return Math.Max(0, endTime - currentTime);
    }
}

//! Conquest zone data class
class EdenConquestZone
{
    protected string m_ZoneName;
    protected ref array<vector> m_PointPositions;
    protected ref array<vector> m_ZonePolygon;
    protected ref array<ref EdenGangScore> m_GangScores;

    void EdenConquestZone(string zoneName = "", array<vector> points = null, array<vector> polygon = null)
    {
        m_ZoneName = zoneName;
        m_PointPositions = points ? points : new array<vector>();
        m_ZonePolygon = polygon ? polygon : new array<vector>();
        m_GangScores = new array<ref EdenGangScore>();
    }

    // Getters and setters
    void SetZoneName(string zoneName) { m_ZoneName = zoneName; }
    string GetZoneName() { return m_ZoneName; }

    void SetPointPositions(array<vector> points) { m_PointPositions = points; }
    array<vector> GetPointPositions() { return m_PointPositions; }

    void SetZonePolygon(array<vector> polygon) { m_ZonePolygon = polygon; }
    array<vector> GetZonePolygon() { return m_ZonePolygon; }

    void SetGangScores(array<ref EdenGangScore> scores) { m_GangScores = scores; }
    array<ref EdenGangScore> GetGangScores() { return m_GangScores; }

    void AddGangScore(EdenGangScore score) { m_GangScores.Insert(score); }

    EdenGangScore GetGangScore(int gangId)
    {
        foreach (EdenGangScore score : m_GangScores)
        {
            if (score && score.GetGangId() == gangId)
                return score;
        }
        return null;
    }
}

//! Gang score data class for conquest events
class EdenGangScore
{
    protected int m_GangId;
    protected string m_GangName;
    protected int m_Score;
    protected int m_MemberCount;
    protected int m_Kills;
    protected int m_Deaths;

    void EdenGangScore(int gangId = -1, string gangName = "", int score = 0)
    {
        m_GangId = gangId;
        m_GangName = gangName;
        m_Score = score;
        m_MemberCount = 0;
        m_Kills = 0;
        m_Deaths = 0;
    }

    // Getters and setters
    void SetGangId(int gangId) { m_GangId = gangId; }
    int GetGangId() { return m_GangId; }

    void SetGangName(string gangName) { m_GangName = gangName; }
    string GetGangName() { return m_GangName; }

    void SetScore(int score) { m_Score = score; }
    int GetScore() { return m_Score; }

    void SetMemberCount(int memberCount) { m_MemberCount = memberCount; }
    int GetMemberCount() { return m_MemberCount; }

    void SetKills(int kills) { m_Kills = kills; }
    int GetKills() { return m_Kills; }

    void SetDeaths(int deaths) { m_Deaths = deaths; }
    int GetDeaths() { return m_Deaths; }

    void AddScore(int points) { m_Score += points; }
    void AddKill() { m_Kills++; }
    void AddDeath() { m_Deaths++; }

    float GetKillDeathRatio()
    {
        if (m_Deaths == 0) return m_Kills;
        return (float)m_Kills / (float)m_Deaths;
    }
}

//! Federal event data class
class EdenFederalEvent
{
    protected string m_EventType;
    protected string m_PlayerId;
    protected string m_BankId;
    protected int m_StartTime;
    protected int m_BombTimer;
    protected bool m_IsActive;
    protected bool m_BombDefused;

    void EdenFederalEvent()
    {
        m_EventType = "";
        m_PlayerId = "";
        m_BankId = "";
        m_StartTime = 0;
        m_BombTimer = 0;
        m_IsActive = false;
        m_BombDefused = false;
    }

    // Getters and setters
    void SetEventType(string eventType) { m_EventType = eventType; }
    string GetEventType() { return m_EventType; }

    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    string GetPlayerId() { return m_PlayerId; }

    void SetBankId(string bankId) { m_BankId = bankId; }
    string GetBankId() { return m_BankId; }

    void SetStartTime(int startTime) { m_StartTime = startTime; }
    int GetStartTime() { return m_StartTime; }

    void SetBombTimer(int bombTimer) { m_BombTimer = bombTimer; }
    int GetBombTimer() { return m_BombTimer; }

    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    bool IsActive() { return m_IsActive; }

    void SetBombDefused(bool defused) { m_BombDefused = defused; }
    bool IsBombDefused() { return m_BombDefused; }

    int GetTimeRemaining(int currentTime)
    {
        int endTime = m_StartTime + m_BombTimer;
        return Math.Max(0, endTime - currentTime);
    }
}

//! Airdrop data class
class EdenAirdropData
{
    protected vector m_Location;
    protected int m_StartTime;
    protected int m_Duration;
    protected bool m_IsActive;
    protected bool m_IsClaimed;
    protected string m_ClaimedBy;
    protected int m_ClaimTime;
    protected ref array<ref EdenAirdropLoot> m_Loot;

    void EdenAirdropData()
    {
        m_Location = "0 0 0";
        m_StartTime = 0;
        m_Duration = 0;
        m_IsActive = false;
        m_IsClaimed = false;
        m_ClaimedBy = "";
        m_ClaimTime = 0;
        m_Loot = new array<ref EdenAirdropLoot>();
    }

    // Getters and setters
    void SetLocation(vector location) { m_Location = location; }
    vector GetLocation() { return m_Location; }

    void SetStartTime(int startTime) { m_StartTime = startTime; }
    int GetStartTime() { return m_StartTime; }

    void SetDuration(int duration) { m_Duration = duration; }
    int GetDuration() { return m_Duration; }

    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    bool IsActive() { return m_IsActive; }

    void SetIsClaimed(bool isClaimed) { m_IsClaimed = isClaimed; }
    bool IsClaimed() { return m_IsClaimed; }

    void SetClaimedBy(string claimedBy) { m_ClaimedBy = claimedBy; }
    string GetClaimedBy() { return m_ClaimedBy; }

    void SetClaimTime(int claimTime) { m_ClaimTime = claimTime; }
    int GetClaimTime() { return m_ClaimTime; }

    void SetLoot(array<ref EdenAirdropLoot> loot) { m_Loot = loot; }
    array<ref EdenAirdropLoot> GetLoot() { return m_Loot; }

    int GetTimeRemaining(int currentTime)
    {
        int endTime = m_StartTime + m_Duration;
        return Math.Max(0, endTime - currentTime);
    }
}

//! Airdrop loot class
class EdenAirdropLoot
{
    protected string m_ItemName;
    protected string m_ItemType;
    protected int m_SpawnChance;
    protected int m_Quantity;
    protected int m_MinQuantity;
    protected int m_MaxQuantity;

    void EdenAirdropLoot(string itemName = "", string itemType = "", int spawnChance = 0, int quantity = 1, int maxQuantity = 1)
    {
        m_ItemName = itemName;
        m_ItemType = itemType;
        m_SpawnChance = spawnChance;
        m_Quantity = quantity;
        m_MinQuantity = 1;
        m_MaxQuantity = maxQuantity;
    }

    // Getters and setters
    void SetItemName(string itemName) { m_ItemName = itemName; }
    string GetItemName() { return m_ItemName; }

    void SetItemType(string itemType) { m_ItemType = itemType; }
    string GetItemType() { return m_ItemType; }

    void SetSpawnChance(int spawnChance) { m_SpawnChance = spawnChance; }
    int GetSpawnChance() { return m_SpawnChance; }

    void SetQuantity(int quantity) { m_Quantity = quantity; }
    int GetQuantity() { return m_Quantity; }

    void SetMinQuantity(int minQuantity) { m_MinQuantity = minQuantity; }
    int GetMinQuantity() { return m_MinQuantity; }

    void SetMaxQuantity(int maxQuantity) { m_MaxQuantity = maxQuantity; }
    int GetMaxQuantity() { return m_MaxQuantity; }
}

//! Message data class
class EdenMessage
{
    protected string m_FromPlayerId;
    protected string m_ToPlayerId;
    protected string m_FromPlayerName;
    protected string m_ToPlayerName;
    protected string m_MessageContent;
    protected int m_MessageType;
    protected int m_Timestamp;
    protected bool m_IsRead;
    protected string m_MessageId;

    void EdenMessage()
    {
        m_FromPlayerId = "";
        m_ToPlayerId = "";
        m_FromPlayerName = "";
        m_ToPlayerName = "";
        m_MessageContent = "";
        m_MessageType = 0;
        m_Timestamp = 0;
        m_IsRead = false;
        m_MessageId = GenerateMessageId();
    }

    // Getters and setters
    void SetFromPlayerId(string fromPlayerId) { m_FromPlayerId = fromPlayerId; }
    string GetFromPlayerId() { return m_FromPlayerId; }

    void SetToPlayerId(string toPlayerId) { m_ToPlayerId = toPlayerId; }
    string GetToPlayerId() { return m_ToPlayerId; }

    void SetFromPlayerName(string fromPlayerName) { m_FromPlayerName = fromPlayerName; }
    string GetFromPlayerName() { return m_FromPlayerName; }

    void SetToPlayerName(string toPlayerName) { m_ToPlayerName = toPlayerName; }
    string GetToPlayerName() { return m_ToPlayerName; }

    void SetMessageContent(string messageContent) { m_MessageContent = messageContent; }
    string GetMessageContent() { return m_MessageContent; }

    void SetMessageType(int messageType) { m_MessageType = messageType; }
    int GetMessageType() { return m_MessageType; }

    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }
    int GetTimestamp() { return m_Timestamp; }

    void SetIsRead(bool isRead) { m_IsRead = isRead; }
    bool IsRead() { return m_IsRead; }

    void SetMessageId(string messageId) { m_MessageId = messageId; }
    string GetMessageId() { return m_MessageId; }

    protected string GenerateMessageId()
    {
        return string.Format("MSG_%1_%2", GetGame().GetWorld().GetWorldTime(), Math.RandomInt(1000, 9999));
    }

    string GetFormattedTimestamp()
    {
        // TODO: Implement proper time formatting
        return m_Timestamp.ToString();
    }
}

//! Contact data class
class EdenContact
{
    protected string m_ContactId;
    protected string m_Name;
    protected string m_PhoneNumber;
    protected string m_ContactType;
    protected bool m_IsEmergency;
    protected bool m_IsBlocked;
    protected int m_LastContactTime;

    void EdenContact()
    {
        m_ContactId = "";
        m_Name = "";
        m_PhoneNumber = "";
        m_ContactType = "player";
        m_IsEmergency = false;
        m_IsBlocked = false;
        m_LastContactTime = 0;
    }

    // Getters and setters
    void SetContactId(string contactId) { m_ContactId = contactId; }
    string GetContactId() { return m_ContactId; }

    void SetName(string name) { m_Name = name; }
    string GetName() { return m_Name; }

    void SetPhoneNumber(string phoneNumber) { m_PhoneNumber = phoneNumber; }
    string GetPhoneNumber() { return m_PhoneNumber; }

    void SetContactType(string contactType) { m_ContactType = contactType; }
    string GetContactType() { return m_ContactType; }

    void SetIsEmergency(bool isEmergency) { m_IsEmergency = isEmergency; }
    bool IsEmergency() { return m_IsEmergency; }

    void SetIsBlocked(bool isBlocked) { m_IsBlocked = isBlocked; }
    bool IsBlocked() { return m_IsBlocked; }

    void SetLastContactTime(int lastContactTime) { m_LastContactTime = lastContactTime; }
    int GetLastContactTime() { return m_LastContactTime; }

    void UpdateLastContactTime()
    {
        m_LastContactTime = GetGame().GetWorld().GetWorldTime();
    }
}

//! Dispatch call data class
class EdenDispatchCall
{
    protected string m_CallId;
    protected string m_PlayerId;
    protected string m_PlayerName;
    protected string m_ServiceType;
    protected string m_Description;
    protected vector m_Location;
    protected int m_Timestamp;
    protected bool m_IsActive;
    protected bool m_IsAssigned;
    protected string m_ResponderId;
    protected string m_ResponderName;
    protected int m_AssignedTime;
    protected int m_CompletedTime;
    protected int m_Priority;

    void EdenDispatchCall()
    {
        m_CallId = "";
        m_PlayerId = "";
        m_PlayerName = "";
        m_ServiceType = "";
        m_Description = "";
        m_Location = "0 0 0";
        m_Timestamp = 0;
        m_IsActive = false;
        m_IsAssigned = false;
        m_ResponderId = "";
        m_ResponderName = "";
        m_AssignedTime = 0;
        m_CompletedTime = 0;
        m_Priority = 1; // 1 = Low, 2 = Medium, 3 = High, 4 = Emergency
    }

    // Getters and setters
    void SetCallId(string callId) { m_CallId = callId; }
    string GetCallId() { return m_CallId; }

    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    string GetPlayerId() { return m_PlayerId; }

    void SetPlayerName(string playerName) { m_PlayerName = playerName; }
    string GetPlayerName() { return m_PlayerName; }

    void SetServiceType(string serviceType) { m_ServiceType = serviceType; }
    string GetServiceType() { return m_ServiceType; }

    void SetDescription(string description) { m_Description = description; }
    string GetDescription() { return m_Description; }

    void SetLocation(vector location) { m_Location = location; }
    vector GetLocation() { return m_Location; }

    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }
    int GetTimestamp() { return m_Timestamp; }

    void SetIsActive(bool isActive) { m_IsActive = isActive; }
    bool IsActive() { return m_IsActive; }

    void SetIsAssigned(bool isAssigned) { m_IsAssigned = isAssigned; }
    bool IsAssigned() { return m_IsAssigned; }

    void SetResponderId(string responderId) { m_ResponderId = responderId; }
    string GetResponderId() { return m_ResponderId; }

    void SetResponderName(string responderName) { m_ResponderName = responderName; }
    string GetResponderName() { return m_ResponderName; }

    void SetAssignedTime(int assignedTime) { m_AssignedTime = assignedTime; }
    int GetAssignedTime() { return m_AssignedTime; }

    void SetCompletedTime(int completedTime) { m_CompletedTime = completedTime; }
    int GetCompletedTime() { return m_CompletedTime; }

    void SetPriority(int priority) { m_Priority = priority; }
    int GetPriority() { return m_Priority; }

    string GetPriorityText()
    {
        switch (m_Priority)
        {
            case 1: return "Low";
            case 2: return "Medium";
            case 3: return "High";
            case 4: return "Emergency";
            default: return "Unknown";
        }
    }

    int GetElapsedTime(int currentTime)
    {
        return currentTime - m_Timestamp;
    }

    int GetResponseTime()
    {
        if (m_AssignedTime == 0) return 0;
        return m_AssignedTime - m_Timestamp;
    }

    int GetCompletionTime()
    {
        if (m_CompletedTime == 0) return 0;
        return m_CompletedTime - m_Timestamp;
    }
}

//! ========================================
//! PROGRESSION SYSTEM DATA STRUCTURES
//! ========================================

//! Player statistics data structure
class EdenPlayerStats
{
    protected string m_sPlayerId;
    protected int m_iCivExperience;
    protected int m_iCopExperience;
    protected int m_iMedicExperience;
    protected int m_iKills;
    protected int m_iDeaths;
    protected int m_iArrestsMade;
    protected int m_iRevives;
    protected int m_iLockpicksSuccess;
    protected int m_iLockpicksFailed;
    protected int m_iPlayersRobbed;
    protected int m_iDrugsSold;
    protected int m_iResourcesGathered;
    protected int m_iTotalMoneyEarned;
    protected int m_iTotalMoneySpent;
    protected int m_iDistanceFoot;
    protected int m_iDistanceVehicle;
    protected int m_iPlaytime;
    protected int m_iTicketsIssued;
    protected int m_iBombsDefused;
    protected int m_iBombsPlanted;
    protected int m_iVehiclesStolen;
    protected int m_iVehiclesImpounded;
    protected int m_iPrisonTime;
    protected int m_iBountiesReceived;
    protected int m_iContrabandSeized;
    protected int m_iEpipensUsed;
    protected int m_iBloodbagsUsed;
    protected int m_iToolkitsUsed;
    protected ref map<string, int> m_mCustomStats;

    void EdenPlayerStats()
    {
        m_mCustomStats = new map<string, int>();
    }

    // Getters
    string GetPlayerId() { return m_sPlayerId; }
    int GetCivExperience() { return m_iCivExperience; }
    int GetCopExperience() { return m_iCopExperience; }
    int GetMedicExperience() { return m_iMedicExperience; }
    int GetKills() { return m_iKills; }
    int GetDeaths() { return m_iDeaths; }
    int GetArrestsMade() { return m_iArrestsMade; }
    int GetRevives() { return m_iRevives; }
    int GetLockpicksSuccess() { return m_iLockpicksSuccess; }
    int GetLockpicksFailed() { return m_iLockpicksFailed; }
    int GetPlayersRobbed() { return m_iPlayersRobbed; }
    int GetDrugsSold() { return m_iDrugsSold; }
    int GetResourcesGathered() { return m_iResourcesGathered; }
    int GetTotalMoneyEarned() { return m_iTotalMoneyEarned; }
    int GetTotalMoneySpent() { return m_iTotalMoneySpent; }
    int GetDistanceFoot() { return m_iDistanceFoot; }
    int GetDistanceVehicle() { return m_iDistanceVehicle; }
    int GetPlaytime() { return m_iPlaytime; }
    int GetTicketsIssued() { return m_iTicketsIssued; }
    int GetBombsDefused() { return m_iBombsDefused; }
    int GetBombsPlanted() { return m_iBombsPlanted; }
    int GetVehiclesStolen() { return m_iVehiclesStolen; }
    int GetVehiclesImpounded() { return m_iVehiclesImpounded; }
    int GetPrisonTime() { return m_iPrisonTime; }
    int GetBountiesReceived() { return m_iBountiesReceived; }
    int GetContrabandSeized() { return m_iContrabandSeized; }
    int GetEpipensUsed() { return m_iEpipensUsed; }
    int GetBloodbagsUsed() { return m_iBloodbagsUsed; }
    int GetToolkitsUsed() { return m_iToolkitsUsed; }

    // Setters
    void SetPlayerId(string playerId) { m_sPlayerId = playerId; }
    void SetCivExperience(int experience) { m_iCivExperience = experience; }
    void SetCopExperience(int experience) { m_iCopExperience = experience; }
    void SetMedicExperience(int experience) { m_iMedicExperience = experience; }
    void SetKills(int kills) { m_iKills = kills; }
    void SetDeaths(int deaths) { m_iDeaths = deaths; }
    void SetArrestsMade(int arrests) { m_iArrestsMade = arrests; }
    void SetRevives(int revives) { m_iRevives = revives; }
    void SetLockpicksSuccess(int lockpicks) { m_iLockpicksSuccess = lockpicks; }
    void SetLockpicksFailed(int lockpicks) { m_iLockpicksFailed = lockpicks; }
    void SetPlayersRobbed(int robbed) { m_iPlayersRobbed = robbed; }
    void SetDrugsSold(int drugs) { m_iDrugsSold = drugs; }
    void SetResourcesGathered(int resources) { m_iResourcesGathered = resources; }
    void SetTotalMoneyEarned(int money) { m_iTotalMoneyEarned = money; }
    void SetTotalMoneySpent(int money) { m_iTotalMoneySpent = money; }
    void SetDistanceFoot(int distance) { m_iDistanceFoot = distance; }
    void SetDistanceVehicle(int distance) { m_iDistanceVehicle = distance; }
    void SetPlaytime(int playtime) { m_iPlaytime = playtime; }
    void SetTicketsIssued(int tickets) { m_iTicketsIssued = tickets; }
    void SetBombsDefused(int bombs) { m_iBombsDefused = bombs; }
    void SetBombsPlanted(int bombs) { m_iBombsPlanted = bombs; }
    void SetVehiclesStolen(int vehicles) { m_iVehiclesStolen = vehicles; }
    void SetVehiclesImpounded(int vehicles) { m_iVehiclesImpounded = vehicles; }
    void SetPrisonTime(int time) { m_iPrisonTime = time; }
    void SetBountiesReceived(int bounties) { m_iBountiesReceived = bounties; }
    void SetContrabandSeized(int contraband) { m_iContrabandSeized = contraband; }
    void SetEpipensUsed(int epipens) { m_iEpipensUsed = epipens; }
    void SetBloodbagsUsed(int bloodbags) { m_iBloodbagsUsed = bloodbags; }
    void SetToolkitsUsed(int toolkits) { m_iToolkitsUsed = toolkits; }

    // Experience adders
    void AddCivExperience(int amount) { m_iCivExperience += amount; }
    void AddCopExperience(int amount) { m_iCopExperience += amount; }
    void AddMedicExperience(int amount) { m_iMedicExperience += amount; }

    // Stat incrementers
    void IncrementKills(int amount = 1) { m_iKills += amount; }
    void IncrementDeaths(int amount = 1) { m_iDeaths += amount; }
    void IncrementArrestsMade(int amount = 1) { m_iArrestsMade += amount; }
    void IncrementRevives(int amount = 1) { m_iRevives += amount; }
    void IncrementLockpicksSuccess(int amount = 1) { m_iLockpicksSuccess += amount; }
    void IncrementLockpicksFailed(int amount = 1) { m_iLockpicksFailed += amount; }
    void IncrementPlayersRobbed(int amount = 1) { m_iPlayersRobbed += amount; }
    void IncrementDrugsSold(int amount = 1) { m_iDrugsSold += amount; }
    void IncrementResourcesGathered(int amount = 1) { m_iResourcesGathered += amount; }
    void AddMoneyEarned(int amount) { m_iTotalMoneyEarned += amount; }
    void AddMoneySpent(int amount) { m_iTotalMoneySpent += amount; }
    void AddDistanceFoot(int distance) { m_iDistanceFoot += distance; }
    void AddDistanceVehicle(int distance) { m_iDistanceVehicle += distance; }
    void AddPlaytime(int minutes) { m_iPlaytime += minutes; }
    void IncrementTicketsIssued(int amount = 1) { m_iTicketsIssued += amount; }
    void IncrementBombsDefused(int amount = 1) { m_iBombsDefused += amount; }
    void IncrementBombsPlanted(int amount = 1) { m_iBombsPlanted += amount; }
    void IncrementVehiclesStolen(int amount = 1) { m_iVehiclesStolen += amount; }
    void IncrementVehiclesImpounded(int amount = 1) { m_iVehiclesImpounded += amount; }
    void AddPrisonTime(int minutes) { m_iPrisonTime += minutes; }
    void IncrementBountiesReceived(int amount = 1) { m_iBountiesReceived += amount; }
    void AddContrabandSeized(int amount) { m_iContrabandSeized += amount; }
    void IncrementEpipensUsed(int amount = 1) { m_iEpipensUsed += amount; }
    void IncrementBloodbagsUsed(int amount = 1) { m_iBloodbagsUsed += amount; }
    void IncrementToolkitsUsed(int amount = 1) { m_iToolkitsUsed += amount; }

    // Custom statistics
    void SetStatistic(string statName, int value)
    {
        m_mCustomStats.Set(statName, value);
    }

    void IncrementStatistic(string statName, int amount = 1)
    {
        int currentValue = 0;
        if (m_mCustomStats.Contains(statName))
            currentValue = m_mCustomStats.Get(statName);
        m_mCustomStats.Set(statName, currentValue + amount);
    }

    int GetStatistic(string statName)
    {
        if (m_mCustomStats.Contains(statName))
            return m_mCustomStats.Get(statName);
        return 0;
    }
}

//! Title definition data structure
class EdenTitleDefinition
{
    protected string m_sTitleName;
    protected string m_sTitleCategory;
    protected string m_sRequirementStat;
    protected int m_iRequiredValue;
    protected string m_sDescription;

    void EdenTitleDefinition(string titleName = "", string titleCategory = "", string requirementStat = "", int requiredValue = 0, string description = "")
    {
        m_sTitleName = titleName;
        m_sTitleCategory = titleCategory;
        m_sRequirementStat = requirementStat;
        m_iRequiredValue = requiredValue;
        m_sDescription = description;
    }

    // Getters
    string GetTitleName() { return m_sTitleName; }
    string GetTitleCategory() { return m_sTitleCategory; }
    string GetRequirementStat() { return m_sRequirementStat; }
    int GetRequiredValue() { return m_iRequiredValue; }
    string GetDescription() { return m_sDescription; }

    // Setters
    void SetTitleName(string titleName) { m_sTitleName = titleName; }
    void SetTitleCategory(string titleCategory) { m_sTitleCategory = titleCategory; }
    void SetRequirementStat(string requirementStat) { m_sRequirementStat = requirementStat; }
    void SetRequiredValue(int requiredValue) { m_iRequiredValue = requiredValue; }
    void SetDescription(string description) { m_sDescription = description; }
}

//! Rank definition data structure
class EdenRankDefinition
{
    protected string m_sRankType;
    protected int m_iLevel;
    protected string m_sRankName;
    protected int m_iRequiredExperience;

    void EdenRankDefinition(string rankType = "", int level = 1, string rankName = "", int requiredExperience = 0)
    {
        m_sRankType = rankType;
        m_iLevel = level;
        m_sRankName = rankName;
        m_iRequiredExperience = requiredExperience;
    }

    // Getters
    string GetRankType() { return m_sRankType; }
    int GetLevel() { return m_iLevel; }
    string GetRankName() { return m_sRankName; }
    int GetRequiredExperience() { return m_iRequiredExperience; }

    // Setters
    void SetRankType(string rankType) { m_sRankType = rankType; }
    void SetLevel(int level) { m_iLevel = level; }
    void SetRankName(string rankName) { m_sRankName = rankName; }
    void SetRequiredExperience(int requiredExperience) { m_iRequiredExperience = requiredExperience; }
}

//! Achievement data structure
class EdenAchievement
{
    protected string m_sAchievementId;
    protected string m_sAchievementName;
    protected string m_sDescription;
    protected string m_sCategory;
    protected int m_iPoints;
    protected bool m_bIsHidden;
    protected string m_sRequirementType;
    protected int m_iRequiredValue;
    protected string m_sRewardType;
    protected int m_iRewardValue;

    void EdenAchievement()
    {
        m_iPoints = 10;
        m_bIsHidden = false;
        m_sRequirementType = "statistic";
        m_iRequiredValue = 1;
        m_sRewardType = "experience";
        m_iRewardValue = 100;
    }

    // Getters
    string GetAchievementId() { return m_sAchievementId; }
    string GetAchievementName() { return m_sAchievementName; }
    string GetDescription() { return m_sDescription; }
    string GetCategory() { return m_sCategory; }
    int GetPoints() { return m_iPoints; }
    bool IsHidden() { return m_bIsHidden; }
    string GetRequirementType() { return m_sRequirementType; }
    int GetRequiredValue() { return m_iRequiredValue; }
    string GetRewardType() { return m_sRewardType; }
    int GetRewardValue() { return m_iRewardValue; }

    // Setters
    void SetAchievementId(string achievementId) { m_sAchievementId = achievementId; }
    void SetAchievementName(string achievementName) { m_sAchievementName = achievementName; }
    void SetDescription(string description) { m_sDescription = description; }
    void SetCategory(string category) { m_sCategory = category; }
    void SetPoints(int points) { m_iPoints = points; }
    void SetIsHidden(bool isHidden) { m_bIsHidden = isHidden; }
    void SetRequirementType(string requirementType) { m_sRequirementType = requirementType; }
    void SetRequiredValue(int requiredValue) { m_iRequiredValue = requiredValue; }
    void SetRewardType(string rewardType) { m_sRewardType = rewardType; }
    void SetRewardValue(int rewardValue) { m_iRewardValue = rewardValue; }
}

//! Player achievement progress
class EdenPlayerAchievement
{
    protected string m_sPlayerId;
    protected string m_sAchievementId;
    protected int m_iProgress;
    protected bool m_bIsCompleted;
    protected int m_iCompletedTime;
    protected bool m_bIsRewarded;

    void EdenPlayerAchievement()
    {
        m_iProgress = 0;
        m_bIsCompleted = false;
        m_iCompletedTime = 0;
        m_bIsRewarded = false;
    }

    // Getters
    string GetPlayerId() { return m_sPlayerId; }
    string GetAchievementId() { return m_sAchievementId; }
    int GetProgress() { return m_iProgress; }
    bool IsCompleted() { return m_bIsCompleted; }
    int GetCompletedTime() { return m_iCompletedTime; }
    bool IsRewarded() { return m_bIsRewarded; }

    // Setters
    void SetPlayerId(string playerId) { m_sPlayerId = playerId; }
    void SetAchievementId(string achievementId) { m_sAchievementId = achievementId; }
    void SetProgress(int progress) { m_iProgress = progress; }
    void SetIsCompleted(bool isCompleted) { m_bIsCompleted = isCompleted; }
    void SetCompletedTime(int completedTime) { m_iCompletedTime = completedTime; }
    void SetIsRewarded(bool isRewarded) { m_bIsRewarded = isRewarded; }

    // Progress methods
    void AddProgress(int amount) { m_iProgress += amount; }
    void CompleteAchievement(int completedTime)
    {
        m_bIsCompleted = true;
        m_iCompletedTime = completedTime;
    }
}

//! ========================================
//! LOADING SCREEN DATA STRUCTURES
//! ========================================

//! Loading step definition
class EdenLoadingStep
{
    protected string m_sStepName;
    protected string m_sStepDescription;
    protected int m_iProgressStart;
    protected int m_iProgressEnd;
    protected int m_iDuration;

    void EdenLoadingStep()
    {
        m_sStepName = "";
        m_sStepDescription = "";
        m_iProgressStart = 0;
        m_iProgressEnd = 0;
        m_iDuration = 1000;
    }

    // Getters
    string GetStepName() { return m_sStepName; }
    string GetStepDescription() { return m_sStepDescription; }
    int GetProgressStart() { return m_iProgressStart; }
    int GetProgressEnd() { return m_iProgressEnd; }
    int GetDuration() { return m_iDuration; }

    // Setters
    void SetStepName(string stepName) { m_sStepName = stepName; }
    void SetStepDescription(string stepDescription) { m_sStepDescription = stepDescription; }
    void SetProgressStart(int progressStart) { m_iProgressStart = progressStart; }
    void SetProgressEnd(int progressEnd) { m_iProgressEnd = progressEnd; }
    void SetDuration(int duration) { m_iDuration = duration; }
}

//! Changelog entry for loading screen
class EdenChangelogEntry
{
    protected string m_sTitle;
    protected string m_sDate;
    protected string m_sFilename;
    protected string m_sContent;

    void EdenChangelogEntry()
    {
        m_sTitle = "";
        m_sDate = "";
        m_sFilename = "";
        m_sContent = "";
    }

    // Getters
    string GetTitle() { return m_sTitle; }
    string GetDate() { return m_sDate; }
    string GetFilename() { return m_sFilename; }
    string GetContent() { return m_sContent; }

    // Setters
    void SetTitle(string title) { m_sTitle = title; }
    void SetDate(string date) { m_sDate = date; }
    void SetFilename(string filename) { m_sFilename = filename; }
    void SetContent(string content) { m_sContent = content; }
}

//! Server information entry
class EdenServerInfo
{
    protected string m_sTitle;
    protected string m_sContent;
    protected string m_sCategory;

    void EdenServerInfo()
    {
        m_sTitle = "";
        m_sContent = "";
        m_sCategory = "";
    }

    // Getters
    string GetTitle() { return m_sTitle; }
    string GetContent() { return m_sContent; }
    string GetCategory() { return m_sCategory; }

    // Setters
    void SetTitle(string title) { m_sTitle = title; }
    void SetContent(string content) { m_sContent = content; }
    void SetCategory(string category) { m_sCategory = category; }
}

//! Content category for loading screen
class EdenContentCategory
{
    protected string m_sTitle;
    protected Color m_cColor;
    protected ref array<ref EdenContentItem> m_aItems;

    void EdenContentCategory()
    {
        m_sTitle = "";
        m_cColor = Color.WHITE;
        m_aItems = new array<ref EdenContentItem>();
    }

    // Getters
    string GetTitle() { return m_sTitle; }
    Color GetColor() { return m_cColor; }
    array<ref EdenContentItem> GetItems() { return m_aItems; }

    // Setters
    void SetTitle(string title) { m_sTitle = title; }
    void SetColor(Color color) { m_cColor = color; }

    // Item management
    void AddItem(EdenContentItem item)
    {
        if (item)
            m_aItems.Insert(item);
    }

    void RemoveItem(EdenContentItem item)
    {
        int index = m_aItems.Find(item);
        if (index != -1)
            m_aItems.Remove(index);
    }

    EdenContentItem GetItem(int index)
    {
        if (index >= 0 && index < m_aItems.Count())
            return m_aItems[index];
        return null;
    }
}

//! Content item for loading screen
class EdenContentItem
{
    protected string m_sTitle;
    protected string m_sFilename;
    protected string m_sContent;
    protected Color m_cColor;

    void EdenContentItem()
    {
        m_sTitle = "";
        m_sFilename = "";
        m_sContent = "";
        m_cColor = Color.WHITE;
    }

    // Getters
    string GetTitle() { return m_sTitle; }
    string GetFilename() { return m_sFilename; }
    string GetContent() { return m_sContent; }
    Color GetColor() { return m_cColor; }

    // Setters
    void SetTitle(string title) { m_sTitle = title; }
    void SetFilename(string filename) { m_sFilename = filename; }
    void SetContent(string content) { m_sContent = content; }
    void SetColor(Color color) { m_cColor = color; }
}

//! ========================================
//! LOADING SCREEN DATA STRUCTURES
//! ========================================

//! Changelog entry for loading screen
class EdenChangelogEntry
{
    protected string m_sTitle;
    protected string m_sFilename;
    protected string m_sContent;
    protected string m_sDate;

    void EdenChangelogEntry()
    {
        m_sTitle = "";
        m_sFilename = "";
        m_sContent = "";
        m_sDate = "";
    }

    // Getters
    string GetTitle() { return m_sTitle; }
    string GetFilename() { return m_sFilename; }
    string GetContent() { return m_sContent; }
    string GetDate() { return m_sDate; }

    // Setters
    void SetTitle(string title) { m_sTitle = title; }
    void SetFilename(string filename) { m_sFilename = filename; }
    void SetContent(string content) { m_sContent = content; }
    void SetDate(string date) { m_sDate = date; }
}

//! Content item for loading screen
class EdenContentItem
{
    protected string m_sTitle;
    protected string m_sFilename;
    protected string m_sContent;
    protected Color m_cColor;

    void EdenContentItem()
    {
        m_sTitle = "";
        m_sFilename = "";
        m_sContent = "";
        m_cColor = Color.WHITE;
    }

    // Getters
    string GetTitle() { return m_sTitle; }
    string GetFilename() { return m_sFilename; }
    string GetContent() { return m_sContent; }
    Color GetColor() { return m_cColor; }

    // Setters
    void SetTitle(string title) { m_sTitle = title; }
    void SetFilename(string filename) { m_sFilename = filename; }
    void SetContent(string content) { m_sContent = content; }
    void SetColor(Color color) { m_cColor = color; }
}

//! Content category for loading screen
class EdenContentCategory
{
    protected string m_sTitle;
    protected Color m_cColor;
    protected ref array<ref EdenContentItem> m_aItems;
    protected bool m_bExpanded;

    void EdenContentCategory()
    {
        m_sTitle = "";
        m_cColor = Color.WHITE;
        m_aItems = new array<ref EdenContentItem>();
        m_bExpanded = false;
    }

    // Getters
    string GetTitle() { return m_sTitle; }
    Color GetColor() { return m_cColor; }
    array<ref EdenContentItem> GetItems() { return m_aItems; }
    bool IsExpanded() { return m_bExpanded; }

    // Setters
    void SetTitle(string title) { m_sTitle = title; }
    void SetColor(Color color) { m_cColor = color; }
    void SetExpanded(bool expanded) { m_bExpanded = expanded; }

    // Item management
    void AddItem(EdenContentItem item)
    {
        if (item)
            m_aItems.Insert(item);
    }

    void RemoveItem(EdenContentItem item)
    {
        int index = m_aItems.Find(item);
        if (index != -1)
            m_aItems.Remove(index);
    }

    int GetItemCount() { return m_aItems.Count(); }

    EdenContentItem GetItem(int index)
    {
        if (index >= 0 && index < m_aItems.Count())
            return m_aItems[index];
        return null;
    }
}
