//  File: fn_iconConfig.sqf
//	Description: Config file for the item icons
params [
	["_item","",[""]]
];
if (_item isEqualTo "") exitWith {""};

switch (_item) do {
	case "life_inv_oilu": {"images\icons\items\oil_unprocessed.paa"};
	case "life_inv_oilp": {"images\icons\items\oil_processed.paa"};
	case "life_inv_heroinu": {"images\icons\items\heroin_unprocessed.paa"};
	case "life_inv_heroinp": {"images\icons\items\heroin_processed.paa"};
	case "life_inv_cannabis": {"images\icons\items\cannabis.paa"};
	case "life_inv_marijuana": {"images\icons\items\marijuana.paa"};
	case "life_inv_apple": {"images\icons\items\apple.paa"};
	case "life_inv_rabbit": {"images\icons\items\meat.paa"};
	case "life_inv_salema": {"images\icons\items\fish.paa"};
	case "life_inv_ornate": {"images\icons\items\fish.paa"};
	case "life_inv_mackerel": {"images\icons\items\fish.paa"};
	case "life_inv_tuna": {"images\icons\items\fish.paa"};
	case "life_inv_mullet": {"images\icons\items\fish.paa"};
	case "life_inv_catshark": {"images\icons\items\fish.paa"};
	case "life_inv_turtle": {"images\icons\items\turtle.paa"};
	case "life_inv_fishingpoles": {"images\icons\items\fishingpoles.paa"};
	case "life_inv_water": {"images\icons\items\water.paa"};
	case "life_inv_coffee": {"images\icons\items\coffee.paa"};
	case "life_inv_turtlesoup": {"images\icons\items\turtle_soup.paa"};
	case "life_inv_donuts": {"images\icons\items\donut.paa"};
	case "life_inv_fuelE": {"images\icons\items\fuel_can.paa"};
	case "life_inv_fuelF": {"images\icons\items\fuel_can.paa"};
	case "life_inv_pickaxe": {"images\icons\items\pickaxe.paa"};
	case "life_inv_copperore": {"images\icons\items\copper_ore.paa"};
	case "life_inv_ironore": {"images\icons\items\iron_ore.paa"};
	case "life_inv_ironr": {"images\icons\items\iron.paa"};
	case "life_inv_copperr": {"images\icons\items\copper.paa"};
	case "life_inv_sand": {"images\icons\items\sand.paa"};
	case "life_inv_salt": {"images\icons\items\salt_unprocessed.paa"};
	case "life_inv_saltr": {"images\icons\items\salt.paa"};
	case "life_inv_glass": {"images\icons\items\glass.paa"};
	case "life_inv_diamond": {"images\icons\items\diamond_unprocessed.paa"};
	case "life_inv_diamondr": {"images\icons\items\diamond.paa"};
	case "life_inv_tbacon": {"images\icons\items\tbacon.paa"};
	case "life_inv_redgull": {"images\icons\items\redgull.paa"};
	case "life_inv_lollypop": {"images\icons\items\lollypop.paa"};
	case "life_inv_lockpick": {"images\icons\items\lockpick.paa"};
	case "life_inv_peach": {"images\icons\items\peach.paa"};
	case "life_inv_coke": {"images\icons\items\cocain_unprocessed.paa"};
	case "life_inv_cokep": {"images\icons\items\cocain_processed.paa"};
	case "life_inv_spikeStrip": {"images\icons\items\spikestrip.paa"};
	case "life_inv_rock": {"images\icons\items\rock.paa"};
	case "life_inv_cement": {"images\icons\items\cement.paa"};
	case "life_inv_goldbar": {"images\icons\items\goldbar.paa"};
	case "life_inv_moneybag": {"images\icons\items\money_bag_icon.paa"};
	case "life_inv_blastingcharge": {"images\icons\items\blastingcharge.paa"};
	case "life_inv_boltcutter": {"images\icons\items\boltcutter.paa"};
	case "life_inv_fireaxe": {"images\icons\items\fireaxe.paa"};
	case "life_inv_defusekit": {"images\icons\items\defusekit.paa"};
	case "life_inv_storagesmall": {"images\icons\items\storagesmall.paa"};
	case "life_inv_storagebig": {"images\icons\items\storagebig.paa"};
	case "life_inv_ziptie": {"images\icons\items\ziptie.paa"};
	case "life_inv_potato": {"images\icons\items\potato.paa"};
	case "life_inv_cream": {"images\icons\items\cream.paa"};
	case "life_inv_beer": {"images\icons\items\beer.paa"};
	case "life_inv_pepsi": {"images\icons\items\pepsi.paa"};
	case "life_inv_burger": {"images\icons\items\burger.paa"};
	case "life_inv_cupcake": {"images\icons\items\cupcake.paa"};
	case "life_inv_fireworks": {"images\icons\items\firework.paa"};
	case "life_inv_bloodbag": {"images\icons\items\bloodbag.paa"};
	case "life_inv_epiPen": {"images\icons\items\epiPen.paa"};
	case "life_inv_dopeShot": {"images\icons\items\defib.paa"};
	case "life_inv_hackingterminal": {"images\icons\items\hackingterminal.paa"};
	case "life_inv_takeoverterminal": {"images\icons\items\hackingterminal.paa"};
	case "life_inv_topaz": {"images\icons\items\topaz_unprocessed.paa"};
	case "life_inv_topazr": {"images\icons\items\topaz.paa"};
	case "life_inv_bananaSplit": {"images\icons\items\bananaSplit.paa"};
	case "life_inv_banana": {"images\icons\items\banana.paa"};
	case "life_inv_woodLog": {"images\icons\items\woodLog.paa"};
	case "life_inv_lumber": {"images\icons\items\lumber.paa"};
	case "life_inv_sugar": {"images\icons\items\sugar.paa"};
	case "life_inv_sugarp": {"images\icons\items\sugarp.paa"};
	case "life_inv_corn": {"images\icons\items\corn.paa"};
	case "life_inv_cocoau": {"images\icons\items\cocoau.paa"};
	case "life_inv_cocoap": {"images\icons\items\cocoap.paa"};
	case "life_inv_crystalmeth": {"images\icons\items\crystalmeth.paa"};
	case "life_inv_ephedra": {"images\icons\items\ephedra.paa"};
	case "life_inv_frog": {"images\icons\items\frog.paa"};
	case "life_inv_frogp": {"images\icons\items\frogp.paa"};
	case "life_inv_gpstracker": {"images\icons\items\gpstracker.paa"};
	case "life_inv_egpstracker": {"images\icons\items\enhanced_gps_icon.paa"};
	case "life_inv_gpsjammer": {"images\icons\items\gpsjammer.paa"};
	case "life_inv_heliTowHook": {"images\icons\items\heliTowHook.paa"};
	case "life_inv_lithium": {"images\icons\items\lithium.paa"};
	case "life_inv_mushroom": {"images\icons\items\mushroom.paa"};
	case "life_inv_mmushroom": {"images\icons\items\pmushroom.paa"};
	case "life_inv_mmushroomp": {"images\icons\items\mmushroomp.paa"};
	case "life_inv_moonshine": {"images\icons\items\moonshine.paa"};
	case "life_inv_rum": {"images\icons\items\moonshine.paa"};
	case "life_inv_phosphorous": {"images\icons\items\phosphorous.paa"};
	case "life_inv_platinum": {"images\icons\items\platinum.paa"};
	case "life_inv_platinumr": {"images\icons\items\platinumr.paa"};
	case "life_inv_silver": {"images\icons\items\silver.paa"};
	case "life_inv_silverr": {"images\icons\items\silverr.paa"};
	case "life_inv_speedbomb": {"images\icons\items\speedbomb.paa"};
	case "life_inv_yeast": {"images\icons\items\yeast.paa"};
	case "life_inv_blindfold": {"images\icons\items\blindfold.paa"};
	case "life_inv_panicButton": {"images\icons\items\panic_button.paa"};
	case "life_inv_wplPanicButton": {"images\icons\items\panic_button.paa"};
	case "life_inv_kidney": {"images\icons\items\kidney.paa"};
	case "life_inv_scalpel": {"images\icons\items\scalpel.paa"};
	case "life_inv_roadKit": {"images\icons\items\road_kit.paa"};
	case "life_inv_oilbarrel": {"images\icons\items\oilbarrel.paa"};
	case "life_inv_vehAmmo": {"images\icons\items\vehAmmo.paa"};
	case "life_inv_baitcar": {"images\icons\items\hackingterminal.paa"};
	case "life_inv_wpearl": {"images\icons\items\whitePearl.paa"};
	case "life_inv_bpearl": {"images\icons\items\bluePearl.paa"};
	case "life_inv_amethyst": {"images\icons\items\amethyst.paa"};
	case "life_inv_coin": {"images\icons\items\doubloon.paa"};
	case "life_inv_emerald": {"images\icons\items\emerald.paa"};
	case "life_inv_scrap": {"images\icons\items\scrap.paa"};
	case "life_inv_excavationtools": {"images\icons\items\excavation_tool_icon.paa"};
	case "life_inv_rubber": {"images\icons\items\rubber.paa"};
	case "life_inv_alumore": {"images\icons\items\alumore.paa"};
	case "life_inv_coal": {"images\icons\items\coal.paa"};
	case "life_inv_fibers": {"images\icons\items\fibers.paa"};
	case "life_inv_stire": {"images\icons\items\smalltire.paa"};
	case "life_inv_ltire": {"images\icons\items\largetire.paa"};
	case "life_inv_window": {"images\icons\items\window.paa"};
	case "life_inv_rglass": {"images\icons\items\rwindow.paa"};
	case "life_inv_vdoor": {"images\icons\items\vehicledoor.paa"};
	case "life_inv_electronics": {"images\icons\items\tronics.paa"};
	case "life_inv_smetal": {"images\icons\items\sheetmetal.paa"};
	case "life_inv_splating": {"images\icons\items\rmetal.paa"};
	case "life_inv_alumalloy": {"images\icons\items\alumignot.paa"};
	case "life_inv_hash": {"images\icons\items\hash.paa"};
	case "life_inv_acid": {"images\icons\items\acid.paa"};
	case "life_inv_mushroomu": {"images\icons\items\mmushroom.paa"};
	case "life_inv_pheroin": {"images\icons\items\pheroin.paa"};
	case "life_inv_painkillers": {"images\icons\items\painkillers.paa"};
	case "life_inv_crack": {"images\icons\items\crack.paa"};
	case "life_inv_bcremote": {"images\icons\items\bcremote.paa"};
	case "life_inv_paintingSm": {"images\icons\items\paintingSm.paa"};
	case "life_inv_paintingLg": {"images\icons\items\paintingLg.paa"};
	case "life_inv_lethalinjector": {"images\icons\items\lethalinjection.paa"};
	case "life_inv_gokart": {getText(configFile >> "CfgVehicles" >> "C_Kart_01_F" >> "picture");};
	default {""};
};
