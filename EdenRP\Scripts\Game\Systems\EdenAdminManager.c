//! Eden Admin Manager - Handles all administrative functions and staff tools
//! Converted from original admin systems

class EdenAdminManager
{
    protected ref map<string, int> m_AdminLevels; // Player ID -> Admin Level
    protected ref map<string, int> m_DeveloperLevels; // Player ID -> Developer Level
    protected ref map<string, bool> m_GodModeStates; // Player ID -> God Mode State
    protected ref map<string, bool> m_InvisibilityStates; // Player ID -> Invisibility State
    protected ref map<string, bool> m_SpectatorStates; // Player ID -> Spectator State
    protected ref array<ref EdenAdminLog> m_AdminLogs; // Admin action logs
    protected ref map<string, int> m_AdminCooldowns; // Player ID -> Cooldown time
    
    // Admin system configuration
    protected bool m_AdminSystemEnabled;
    protected int m_AdminCooldownTime;
    protected int m_MaxAdminLevel;
    protected int m_MaxDeveloperLevel;
    protected bool m_LogAllAdminActions;
    
    void EdenAdminManager()
    {
        m_AdminLevels = new map<string, int>();
        m_DeveloperLevels = new map<string, int>();
        m_GodModeStates = new map<string, bool>();
        m_InvisibilityStates = new map<string, bool>();
        m_SpectatorStates = new map<string, bool>();
        m_AdminLogs = new array<ref EdenAdminLog>();
        m_AdminCooldowns = new map<string, int>();
        
        m_AdminSystemEnabled = true;
        m_AdminCooldownTime = 1; // 1 second
        m_MaxAdminLevel = 5;
        m_MaxDeveloperLevel = 3;
        m_LogAllAdminActions = true;
        
        InitializeAdminSystem();
    }
    
    //! Initialize admin system
    void Initialize()
    {
        Print("[EdenAdminManager] Initializing admin management system...");
        
        LoadAdminLevels();
        LoadDeveloperLevels();
        
        // Set up periodic processing
        GetGame().GetCallqueue().CallLater(ProcessAdminCooldowns, 1000, true); // 1 second
        GetGame().GetCallqueue().CallLater(ProcessAdminMonitoring, 5000, true); // 5 seconds
        GetGame().GetCallqueue().CallLater(SaveAdminLogs, 60000, true); // 1 minute
        
        Print("[EdenAdminManager] Admin management system initialized");
    }
    
    //! Initialize admin system configuration
    protected void InitializeAdminSystem()
    {
        Print("[EdenAdminManager] Admin system configuration initialized");
    }
    
    //! Set player admin level
    bool SetAdminLevel(string playerId, int adminLevel)
    {
        if (playerId == "" || adminLevel < 0 || adminLevel > m_MaxAdminLevel)
            return false;
            
        m_AdminLevels.Set(playerId, adminLevel);
        
        // Update player component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (playerEntity)
        {
            EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
            if (playerComp)
            {
                playerComp.SetAdminLevel(adminLevel);
            }
        }
        
        // Log admin level change
        LogAdminAction("SET_ADMIN_LEVEL", playerId, "", string.Format("Admin level set to %1", adminLevel));
        
        Print(string.Format("[EdenAdminManager] Set admin level %1 for player %2", adminLevel, playerId));
        return true;
    }
    
    //! Set player developer level
    bool SetDeveloperLevel(string playerId, int developerLevel)
    {
        if (playerId == "" || developerLevel < 0 || developerLevel > m_MaxDeveloperLevel)
            return false;
            
        m_DeveloperLevels.Set(playerId, developerLevel);
        
        // Update player component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (playerEntity)
        {
            EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
            if (playerComp)
            {
                playerComp.SetDeveloperLevel(developerLevel);
            }
        }
        
        // Log developer level change
        LogAdminAction("SET_DEVELOPER_LEVEL", playerId, "", string.Format("Developer level set to %1", developerLevel));
        
        Print(string.Format("[EdenAdminManager] Set developer level %1 for player %2", developerLevel, playerId));
        return true;
    }
    
    //! Get player admin level
    int GetAdminLevel(string playerId)
    {
        if (playerId == "" || !m_AdminLevels.Contains(playerId))
            return 0;
            
        return m_AdminLevels.Get(playerId);
    }
    
    //! Get player developer level
    int GetDeveloperLevel(string playerId)
    {
        if (playerId == "" || !m_DeveloperLevels.Contains(playerId))
            return 0;
            
        return m_DeveloperLevels.Get(playerId);
    }
    
    //! Check if player has admin permissions
    bool HasAdminPermission(string playerId, int requiredLevel)
    {
        if (playerId == "")
            return false;
            
        int adminLevel = GetAdminLevel(playerId);
        int developerLevel = GetDeveloperLevel(playerId);
        
        return (adminLevel >= requiredLevel || developerLevel >= requiredLevel);
    }
    
    //! Toggle god mode for player
    bool ToggleGodMode(string adminId, string targetId = "")
    {
        if (!HasAdminPermission(adminId, 2))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        string targetPlayerId = (targetId == "") ? adminId : targetId;
        
        IEntity targetEntity = GetPlayerEntity(targetPlayerId);
        if (!targetEntity)
            return false;
            
        bool currentGodMode = m_GodModeStates.Get(targetPlayerId);
        bool newGodMode = !currentGodMode;
        
        // Toggle god mode
        if (newGodMode)
        {
            targetEntity.SetDamageAllowed(false);
        }
        else
        {
            targetEntity.SetDamageAllowed(true);
        }
        
        m_GodModeStates.Set(targetPlayerId, newGodMode);
        
        // Set cooldown
        SetPlayerCooldown(adminId);
        
        // Log action
        LogAdminAction("TOGGLE_GOD_MODE", adminId, targetPlayerId, 
            string.Format("God mode %1", newGodMode ? "enabled" : "disabled"));
        
        Print(string.Format("[EdenAdminManager] %1 %2 god mode for %3", 
            adminId, newGodMode ? "enabled" : "disabled", targetPlayerId));
        return true;
    }
    
    //! Toggle invisibility for player
    bool ToggleInvisibility(string adminId, string targetId = "")
    {
        if (!HasAdminPermission(adminId, 2))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        string targetPlayerId = (targetId == "") ? adminId : targetId;
        
        IEntity targetEntity = GetPlayerEntity(targetPlayerId);
        if (!targetEntity)
            return false;
            
        bool currentInvis = m_InvisibilityStates.Get(targetPlayerId);
        bool newInvis = !currentInvis;
        
        // Toggle invisibility
        SetPlayerVisibility(targetEntity, !newInvis);
        m_InvisibilityStates.Set(targetPlayerId, newInvis);
        
        // Set cooldown
        SetPlayerCooldown(adminId);
        
        // Log action
        LogAdminAction("TOGGLE_INVISIBILITY", adminId, targetPlayerId, 
            string.Format("Invisibility %1", newInvis ? "enabled" : "disabled"));
        
        Print(string.Format("[EdenAdminManager] %1 %2 invisibility for %3", 
            adminId, newInvis ? "enabled" : "disabled", targetPlayerId));
        return true;
    }
    
    //! Teleport player to position
    bool TeleportPlayer(string adminId, string targetId, vector position)
    {
        if (!HasAdminPermission(adminId, 2))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        IEntity targetEntity = GetPlayerEntity(targetId);
        if (!targetEntity)
            return false;
            
        // Store previous position for logging
        vector previousPos = targetEntity.GetOrigin();
        
        // Teleport player
        targetEntity.SetOrigin(position);
        
        // Set cooldown
        SetPlayerCooldown(adminId);
        
        // Log action
        LogAdminAction("TELEPORT_PLAYER", adminId, targetId, 
            string.Format("Teleported from %1 to %2", previousPos.ToString(), position.ToString()));
        
        Print(string.Format("[EdenAdminManager] %1 teleported %2 to %3", adminId, targetId, position.ToString()));
        return true;
    }
    
    //! Teleport player to another player
    bool TeleportPlayerToPlayer(string adminId, string targetId, string destinationId)
    {
        if (!HasAdminPermission(adminId, 2))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        IEntity targetEntity = GetPlayerEntity(targetId);
        IEntity destinationEntity = GetPlayerEntity(destinationId);
        if (!targetEntity || !destinationEntity)
            return false;
            
        vector destinationPos = destinationEntity.GetOrigin();
        return TeleportPlayer(adminId, targetId, destinationPos);
    }
    
    //! Heal player
    bool HealPlayer(string adminId, string targetId = "")
    {
        if (!HasAdminPermission(adminId, 1))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        string targetPlayerId = (targetId == "") ? adminId : targetId;
        
        IEntity targetEntity = GetPlayerEntity(targetPlayerId);
        if (!targetEntity)
            return false;
            
        // Heal player using medical manager
        EdenMedicalManager medicalManager = EdenGameMode.GetInstance().GetMedicalManager();
        if (medicalManager)
        {
            medicalManager.HealPlayer(targetPlayerId, true); // Full heal
        }
        
        // Set cooldown
        SetPlayerCooldown(adminId);
        
        // Log action
        LogAdminAction("HEAL_PLAYER", adminId, targetPlayerId, "Player healed");
        
        Print(string.Format("[EdenAdminManager] %1 healed %2", adminId, targetPlayerId));
        return true;
    }
    
    //! Revive player
    bool RevivePlayer(string adminId, string targetId)
    {
        if (!HasAdminPermission(adminId, 1))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        // Revive player using medical manager
        EdenMedicalManager medicalManager = EdenGameMode.GetInstance().GetMedicalManager();
        if (medicalManager)
        {
            bool success = medicalManager.RevivePlayer(targetId, adminId, true); // Admin revive
            if (success)
            {
                // Set cooldown
                SetPlayerCooldown(adminId);
                
                // Log action
                LogAdminAction("REVIVE_PLAYER", adminId, targetId, "Player revived");
                
                Print(string.Format("[EdenAdminManager] %1 revived %2", adminId, targetId));
                return true;
            }
        }
        
        return false;
    }
    
    //! Mass revive all players
    bool MassReviveAll(string adminId)
    {
        if (!HasAdminPermission(adminId, 3))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        // Get all players and revive them
        EdenMedicalManager medicalManager = EdenGameMode.GetInstance().GetMedicalManager();
        if (!medicalManager)
            return false;
            
        int revivedCount = 0;
        array<string> allPlayers = GetAllPlayerIds();
        
        foreach (string playerId : allPlayers)
        {
            if (medicalManager.IsPlayerDead(playerId))
            {
                if (medicalManager.RevivePlayer(playerId, adminId, true))
                {
                    revivedCount++;
                }
            }
        }
        
        // Set cooldown
        SetPlayerCooldown(adminId);
        
        // Log action
        LogAdminAction("MASS_REVIVE", adminId, "", string.Format("Mass revived %1 players", revivedCount));
        
        Print(string.Format("[EdenAdminManager] %1 mass revived %2 players", adminId, revivedCount));
        return true;
    }
    
    //! Restrain player
    bool RestrainPlayer(string adminId, string targetId)
    {
        if (!HasAdminPermission(adminId, 1))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        // Restrain player using restraint system
        EdenRestraintSystem restraintSystem = EdenGameMode.GetInstance().GetRestraintSystem();
        if (restraintSystem)
        {
            bool success = restraintSystem.RestrainPlayer(adminId, targetId, true); // Admin restraint
            if (success)
            {
                // Set cooldown
                SetPlayerCooldown(adminId);
                
                // Log action
                LogAdminAction("RESTRAIN_PLAYER", adminId, targetId, "Player restrained");
                
                Print(string.Format("[EdenAdminManager] %1 restrained %2", adminId, targetId));
                return true;
            }
        }
        
        return false;
    }
    
    //! Unrestrain player
    bool UnrestrainPlayer(string adminId, string targetId)
    {
        if (!HasAdminPermission(adminId, 1))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        // Unrestrain player using restraint system
        EdenRestraintSystem restraintSystem = EdenGameMode.GetInstance().GetRestraintSystem();
        if (restraintSystem)
        {
            bool success = restraintSystem.UnrestrainPlayer(adminId, targetId, true); // Admin unrestraint
            if (success)
            {
                // Set cooldown
                SetPlayerCooldown(adminId);
                
                // Log action
                LogAdminAction("UNRESTRAIN_PLAYER", adminId, targetId, "Player unrestrained");
                
                Print(string.Format("[EdenAdminManager] %1 unrestrained %2", adminId, targetId));
                return true;
            }
        }
        
        return false;
    }
    
    //! Give money to player
    bool GiveMoneyToPlayer(string adminId, string targetId, int amount)
    {
        if (!HasAdminPermission(adminId, 2))
            return false;
            
        if (IsPlayerOnCooldown(adminId))
            return false;
            
        if (amount <= 0)
            return false;
            
        IEntity targetEntity = GetPlayerEntity(targetId);
        if (!targetEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(targetEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Give money
        playerComp.AddCash(amount);
        
        // Set cooldown
        SetPlayerCooldown(adminId);
        
        // Log action
        LogAdminAction("GIVE_MONEY", adminId, targetId, string.Format("Gave $%1", amount));
        
        Print(string.Format("[EdenAdminManager] %1 gave $%2 to %3", adminId, amount, targetId));
        return true;
    }
    
    //! Log admin action
    void LogAdminAction(string actionType, string adminId, string targetId, string description)
    {
        if (!m_LogAllAdminActions)
            return;
            
        EdenAdminLog adminLog = new EdenAdminLog();
        adminLog.SetActionType(actionType);
        adminLog.SetAdminId(adminId);
        adminLog.SetTargetId(targetId);
        adminLog.SetDescription(description);
        adminLog.SetTimestamp(GetGame().GetWorld().GetWorldTime());
        
        // Get admin position
        IEntity adminEntity = GetPlayerEntity(adminId);
        if (adminEntity)
        {
            adminLog.SetPosition(adminEntity.GetOrigin());
        }
        
        m_AdminLogs.Insert(adminLog);
        
        // Keep only last 1000 logs in memory
        if (m_AdminLogs.Count() > 1000)
        {
            m_AdminLogs.RemoveOrdered(0);
        }
        
        Print(string.Format("[EdenAdminManager] Logged admin action: %1 by %2", actionType, adminId));
    }
    
    //! Get admin logs
    array<ref EdenAdminLog> GetAdminLogs(int maxLogs = 100)
    {
        array<ref EdenAdminLog> recentLogs = {};
        
        int startIndex = Math.Max(0, m_AdminLogs.Count() - maxLogs);
        for (int i = startIndex; i < m_AdminLogs.Count(); i++)
        {
            recentLogs.Insert(m_AdminLogs[i]);
        }
        
        return recentLogs;
    }
    
    //! Helper methods
    protected bool IsPlayerOnCooldown(string playerId)
    {
        if (!m_AdminCooldowns.Contains(playerId))
            return false;
            
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int cooldownTime = m_AdminCooldowns.Get(playerId);
        
        return currentTime < cooldownTime;
    }
    
    protected void SetPlayerCooldown(string playerId)
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int cooldownTime = currentTime + m_AdminCooldownTime;
        
        m_AdminCooldowns.Set(playerId, cooldownTime);
    }
    
    protected void ProcessAdminCooldowns()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        array<string> expiredCooldowns = {};
        
        foreach (string playerId, int cooldownTime : m_AdminCooldowns)
        {
            if (currentTime >= cooldownTime)
            {
                expiredCooldowns.Insert(playerId);
            }
        }
        
        foreach (string playerId : expiredCooldowns)
        {
            m_AdminCooldowns.Remove(playerId);
        }
    }
    
    protected void ProcessAdminMonitoring()
    {
        // Monitor for unauthorized admin actions
        array<string> allPlayers = GetAllPlayerIds();
        
        foreach (string playerId : allPlayers)
        {
            IEntity playerEntity = GetPlayerEntity(playerId);
            if (!playerEntity)
                continue;
                
            int adminLevel = GetAdminLevel(playerId);
            
            // Check for unauthorized god mode
            if (!playerEntity.GetDamageAllowed() && adminLevel < 2)
            {
                LogAdminAction("UNAUTHORIZED_GOD_MODE", playerId, "", "Non-staff god mode detected");
                // Could implement automatic punishment here
            }
            
            // Check for unauthorized invisibility
            if (IsPlayerInvisible(playerEntity) && adminLevel < 2)
            {
                LogAdminAction("UNAUTHORIZED_INVISIBILITY", playerId, "", "Non-staff invisibility detected");
                // Could implement automatic punishment here
            }
        }
    }
    
    protected void SaveAdminLogs()
    {
        // Implementation would save admin logs to file
        Print("[EdenAdminManager] Saving admin logs...");
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadAdminLevels() { }
    protected void LoadDeveloperLevels() { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    protected array<string> GetAllPlayerIds() { return {}; }
    protected void SetPlayerVisibility(IEntity playerEntity, bool visible) { }
    protected bool IsPlayerInvisible(IEntity playerEntity) { return false; }
    
    //! Configuration methods
    void SetAdminSystemEnabled(bool enabled) { m_AdminSystemEnabled = enabled; }
    bool IsAdminSystemEnabled() { return m_AdminSystemEnabled; }
    
    void SetAdminCooldownTime(int cooldown) { m_AdminCooldownTime = cooldown; }
    int GetAdminCooldownTime() { return m_AdminCooldownTime; }
    
    void SetMaxAdminLevel(int maxLevel) { m_MaxAdminLevel = maxLevel; }
    int GetMaxAdminLevel() { return m_MaxAdminLevel; }
    
    void SetMaxDeveloperLevel(int maxLevel) { m_MaxDeveloperLevel = maxLevel; }
    int GetMaxDeveloperLevel() { return m_MaxDeveloperLevel; }
    
    void SetLogAllAdminActions(bool logAll) { m_LogAllAdminActions = logAll; }
    bool ShouldLogAllAdminActions() { return m_LogAllAdminActions; }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenAdminManager] Cleaning up admin management system...");
        
        m_AdminLevels.Clear();
        m_DeveloperLevels.Clear();
        m_GodModeStates.Clear();
        m_InvisibilityStates.Clear();
        m_SpectatorStates.Clear();
        m_AdminLogs.Clear();
        m_AdminCooldowns.Clear();
    }
}
