"Language" "german" 
"Tokens" 
{ 
"SteamBootstrapper_UpdatingSteam"				"%appname% wird aktualisiert …"
"SteamBootstrapper_ElevationPrompt"			"%appname% wird vor dem Start automatisch aktualisiert."
"SteamBootstrapper_ReadOnlyInstallOSX"		"%appname% kann nicht in das Installationsverzeichnis schreiben. Vergewissern Sie sich, dass das Programm nicht vom Speicherabbild aus starten."
"SteamBootstrapper_RunFromDiskImageOSX"		"Bitte ziehen Sie %appname% vor dem Start in den Ordner „Programme“."
"SteamBootstrapper_ReadOnlyInstall"			"%appname% kann nicht in das Installationsverzeichnis schreiben."
"SteamBootstrapper_InstallDirNotASCII"			"%appname% kann nicht aus einem Pfad mit Nicht-ASCII-Zeichen ausgeführt werden. Bitte installieren Sie %appname% in das Standardverzeichnis neu."
"SteamBootstrapper_InstallDirIncompatible"		"%appname% kann unter dieser Version von Windows nicht aus einem Pfad mit Nicht-ASCII-Zeichen ausgeführt werden. Bitte installieren Sie %appname% in das Standardverzeichnis neu oder installieren Sie %appname% unter Windows 7 oder neuer."
"SteamBootstrapper_InsufficientDiskSpace"		"%appname% benötigt für Updates %nMegaBytes% MB freien Speicherplatz."
"SteamBootstrapper_FailedManifestLoad"		"Das Paketmanifest konnte nicht geladen werden."
"SteamBootstrapper_FailedManifestVerify"		"Das Paketmanifest konnte nicht überprüft werden."
"SteamBootstrapper_FailedManifestOSType"		"Die Version des Betriebssystems ist inkompatibel mit dem vom Paketmanifest verwendeten Betriebssystem."
"SteamBootstrapper_FailedManifestSave"		"Das Paketmanifest konnte nicht gespeichert werden."
"SteamBootstrapper_FailedPackageRead"			"Das Paket (%pkgName%) konnte nicht gelesen werden."
"SteamBootstrapper_CorruptPackageFile"		"Paketdatei (%pkgName%) ist beschädigt."
"SteamBootstrapper_FailedPackageUnzip"		"Das Paket (%pkgName%) konnte nicht entpackt werden."
"SteamBootstrapper_UpdateApplyFail"			"Update ist fehlgeschlagen; Wiederherstellung von …"
"SteamBootstrapper_UpdateCleanup"				"Bereinigen …"
"SteamBootstrapper_UpdateCleanupFail"			"Datenbereinigung nach Update ist fehlgeschlagen, fortfahren …"
"SteamBootstrapper_UpdateFinalizeFail"		"Beim Schreiben des Installationsmanifestes ist ein Fehler aufgetreten."
"SteamBootstrapper_UpdateComplete"			"Aktualisierung abgeschlossen, %appname% wird geladen …"
"SteamBootstrapper_UpdateChecking"			"Suche nach verfügbaren Updates …"
"SteamBootstrapper_UpdatePrepare"				"Update wird vorbereitet …"
"SteamBootstrapper_UpdateDownload"			"Update wird heruntergeladen …"
"SteamBootstrapper_UpdateDownloading"			"Update wird heruntergeladen (%bytes% von %size% KB) …"
"SteamBootstrapper_UpdateExtractingPackage"	"Paket wird extrahiert …"
"SteamBootstrapper_UpdateInstalling"			"Update wird installiert …"
"SteamBootstrapper_InstallVerify"				"Installation wird überprüft …"
"SteamBootstrapper_DownloadComplete"			"Download ist abgeschlossen."
"SteamBootstrapper_PercentComplete"			"%percent% %% abgeschlossen"
"SteamBootstrapper_NoNetwork"					"%appname% muss für die Durchführung von Updates mit dem Internet verbunden sein. Stellen Sie eine Verbindung zum Internet her, und versuchen Sie es erneut."
"SteamBootstrapper_FailedMkdir"				"Verzeichnis %dirname% konnte nicht erstellt werden, Fehler %errno%."
"SteamBootstrapper_NotWriteable"				"Verzeichnis %dirname% ist nicht beschreibbar."
"SteamBootstrapper_FailedDownloadHTTPError"	"Download ist fehlgeschlagen: HTTP-Fehler %errno%"
"SteamBootstrapper_AlreadyRunningError"		"%appname% wird auf diesem Gerät bereits unter einem anderen Benutzerkonto ausgeführt."
"SteamBootstrapper_MoveContentDir_DestExists" "%appname% speichert Spielinhalte nicht mehr unter „~/Documents/Steam Content“, sondern unter „~/Library/Application Support/Steam/SteamApps“. Es befinden sich noch Spieledateien im alten Speicherort, die von %appname% nicht verschoben werden konnten, da bereits Dateien im neuen Speicherort vorhanden sind. Sie müssen die Dateien daher ggf. manuell verschieben oder die alten Dateien löschen und die Spiele erneut herunterladen. Trotzdem fortfahren?"
"SteamBootstrapper_MoveContentDir_CopyError"  "%appname% speichert Spielinhalte nicht mehr unter „~/Documents/Steam Content“, sondern unter „~/Library/Application Support/Steam/SteamApps“. Es liegen noch Spieledateien im alten Speicherort, die von %appname% nicht verschoben werden konnten. Sie müssen die Dateien daher ggf. manuell verschieben oder die alten Dateien löschen und die Spiele erneut herunterladen. Trotzdem fortfahren?"
"SteamBootstrapper_OK"				"OK"
"SteamBootstrapper_Cancel"			"Abbrechen"
"SteamBootstrapper_WindowTitle"			"%appname%"
"SteamBootstrapper_Error_Title"			"%appname% – Kritischer Fehler"
"SteamBootstrapper_Warning_Title"			"%appname% – Warnung"
"SteamBootstrapper_OperatingSystemNoLongerSupported" "%appname% wird von Ihrem Betriebssystem nicht mehr unterstützt."
"SteamCmd_OperatingSystemNoLongerSupported" "Die Anwendung %appname% wird von der aktuellen Version Ihres Betriebssystems nicht länger unterstützt.\nSie können diese Überprüfung mithilfe von -overrideminos überschreiben.\nJedoch wird %appname% möglicherweise nicht richtig laufen und die Unterstützung für diese Überschreibung kann jederzeit entfernt werden.\nBitte aktualisieren Sie Ihr Betriebssystem, um uneingeschränkten Zugriff auf die Anwendung zu erhalten."
"SteamBootstrapper_OperatingSystemNoLongerSupportedOnBeta" "%appname% wird von Ihrem Betriebssystem nicht mehr unterstützt.\n\nKlicken Sie auf „OK“, um die ausgewählte Beta von %appname% zu verlassen."
"SteamBootstrapper_Starting"                  "Wird gestartet …"
}
