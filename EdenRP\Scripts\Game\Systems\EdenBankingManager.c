//! Eden Banking Manager - Handles all banking operations including ATMs, transfers, and accounts
//! Converted from original banking systems

class EdenBankingManager
{
    protected ref map<string, ref EdenBankAccount> m_BankAccounts;
    protected ref map<string, ref EdenTransactionRecord> m_TransactionHistory;
    protected ref array<ref EdenATMLocation> m_ATMLocations;
    protected ref map<string, bool> m_ATMStatus; // ATM robbery status
    
    // Banking configuration
    protected int m_MaxTransferAmount;
    protected float m_TransferTaxRate;
    protected int m_MinTransferForTax;
    protected int m_MaxWithdrawAmount;
    protected int m_MaxDepositAmount;
    protected int m_MinWithdrawForRichPlayers;
    protected int m_RichPlayerThreshold;
    
    void EdenBankingManager()
    {
        m_BankAccounts = new map<string, ref EdenBankAccount>();
        m_TransactionHistory = new map<string, ref EdenTransactionRecord>();
        m_ATMLocations = new array<ref EdenATMLocation>();
        m_ATMStatus = new map<string, bool>();
        
        m_MaxTransferAmount = 999999;
        m_TransferTaxRate = 0.05; // 5% tax on transfers
        m_MinTransferForTax = 1000;
        m_MaxWithdrawAmount = 999999;
        m_MaxDepositAmount = 999999;
        m_MinWithdrawForRichPlayers = 100;
        m_RichPlayerThreshold = ********;
        
        InitializeBankingSystem();
    }
    
    //! Initialize banking system
    void Initialize()
    {
        Print("[EdenBankingManager] Initializing banking system...");
        
        LoadBankAccounts();
        SetupATMLocations();
        LoadTransactionHistory();
        
        // Set up periodic maintenance
        GetGame().GetCallqueue().CallLater(ProcessDailyInterest, ********, true); // Daily
        GetGame().GetCallqueue().CallLater(CleanupOldTransactions, 3600000, true); // Hourly
        
        Print("[EdenBankingManager] Banking system initialized");
    }
    
    //! Initialize banking system configuration
    protected void InitializeBankingSystem()
    {
        // Set up ATM locations
        SetupATMLocations();
        
        Print("[EdenBankingManager] Banking system configuration initialized");
    }
    
    //! Setup ATM locations
    protected void SetupATMLocations()
    {
        // Main city ATMs
        EdenATMLocation kavalaATM = new EdenATMLocation();
        kavalaATM.SetPosition("0 0 0"); // Placeholder position
        kavalaATM.SetLocationName("Kavala Bank");
        kavalaATM.SetATMId("kavala_main");
        kavalaATM.SetIsActive(true);
        m_ATMLocations.Insert(kavalaATM);
        m_ATMStatus.Set("kavala_main", true);
        
        // Additional ATM locations would be added here
        Print(string.Format("[EdenBankingManager] Set up %1 ATM locations", m_ATMLocations.Count()));
    }
    
    //! Create bank account for new player
    bool CreateBankAccount(string playerId, string playerName)
    {
        if (playerId == "" || m_BankAccounts.Contains(playerId))
            return false;
            
        EdenBankAccount account = new EdenBankAccount();
        account.SetPlayerId(playerId);
        account.SetPlayerName(playerName);
        account.SetBalance(0);
        account.SetCreationTime(GetGame().GetWorld().GetWorldTime());
        account.SetLastAccessTime(GetGame().GetWorld().GetWorldTime());
        account.SetIsActive(true);
        
        m_BankAccounts.Set(playerId, account);
        
        // Save to database
        SaveBankAccountToDatabase(account);
        
        Print(string.Format("[EdenBankingManager] Created bank account for %1 (%2)", playerName, playerId));
        return true;
    }
    
    //! Deposit money to bank account
    bool DepositMoney(string playerId, int amount, bool depositAll = false)
    {
        if (playerId == "" || amount <= 0)
            return false;
            
        if (!m_BankAccounts.Contains(playerId))
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player has enough cash
        int playerCash = playerComp.GetCash();
        if (amount > playerCash)
            return false;
            
        // Validate deposit amount
        if (!depositAll && amount > m_MaxDepositAmount)
            return false;
            
        EdenBankAccount account = m_BankAccounts.Get(playerId);
        
        // Perform transaction
        playerComp.RemoveCash(amount);
        account.AddBalance(amount);
        account.SetLastAccessTime(GetGame().GetWorld().GetWorldTime());
        
        // Record transaction
        RecordTransaction(playerId, "DEPOSIT", amount, account.GetBalance());
        
        // Update database
        UpdateBankAccountInDatabase(account);
        
        // Log large transactions
        if (amount >= 1000)
        {
            LogTransaction("Bank Deposit", playerId, playerComp.GetPlayerName(), amount);
        }
        
        Print(string.Format("[EdenBankingManager] %1 deposited $%2", playerId, amount));
        return true;
    }
    
    //! Withdraw money from bank account
    bool WithdrawMoney(string playerId, int amount)
    {
        if (playerId == "" || amount <= 0)
            return false;
            
        if (!m_BankAccounts.Contains(playerId))
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        EdenBankAccount account = m_BankAccounts.Get(playerId);
        
        // Validate withdrawal amount
        if (amount > m_MaxWithdrawAmount)
            return false;
            
        if (amount > account.GetBalance())
            return false;
            
        // Check minimum withdrawal for rich players
        if (account.GetBalance() > m_RichPlayerThreshold && amount < m_MinWithdrawForRichPlayers)
            return false;
            
        // Perform transaction
        account.RemoveBalance(amount);
        playerComp.AddCash(amount);
        account.SetLastAccessTime(GetGame().GetWorld().GetWorldTime());
        
        // Record transaction
        RecordTransaction(playerId, "WITHDRAWAL", amount, account.GetBalance());
        
        // Update database
        UpdateBankAccountInDatabase(account);
        
        Print(string.Format("[EdenBankingManager] %1 withdrew $%2", playerId, amount));
        return true;
    }
    
    //! Transfer money between players
    bool TransferMoney(string fromPlayerId, string toPlayerId, int amount, int transferMode = 0)
    {
        if (fromPlayerId == "" || toPlayerId == "" || amount <= 0 || fromPlayerId == toPlayerId)
            return false;
            
        if (amount > m_MaxTransferAmount)
            return false;
            
        // Get sender account
        if (!m_BankAccounts.Contains(fromPlayerId))
            return false;
            
        EdenBankAccount fromAccount = m_BankAccounts.Get(fromPlayerId);
        
        // Calculate tax
        int tax = 0;
        if (transferMode == 0 && amount >= m_MinTransferForTax) // Money transfer with tax
        {
            tax = Math.Floor(amount * m_TransferTaxRate);
        }
        
        int totalCost = amount + tax;
        
        // Check sender funds
        if (transferMode == 0) // Bank transfer
        {
            if (totalCost > fromAccount.GetBalance())
                return false;
        }
        else if (transferMode == 2) // War points transfer
        {
            // Get sender player component for war points
            IEntity fromPlayerEntity = GetPlayerEntity(fromPlayerId);
            if (!fromPlayerEntity)
                return false;
                
            EdenPlayerComponent fromPlayerComp = EdenPlayerComponent.Cast(fromPlayerEntity.FindComponent(EdenPlayerComponent));
            if (!fromPlayerComp)
                return false;
                
            // Check war points (implementation would check war points)
            // For now, just check if player has enough "war points"
        }
        
        // Get recipient
        IEntity toPlayerEntity = GetPlayerEntity(toPlayerId);
        if (!toPlayerEntity)
            return false;
            
        EdenPlayerComponent toPlayerComp = EdenPlayerComponent.Cast(toPlayerEntity.FindComponent(EdenPlayerComponent));
        if (!toPlayerComp)
            return false;
            
        // Perform transfer
        if (transferMode == 0) // Money transfer
        {
            fromAccount.RemoveBalance(totalCost);
            
            // Create or get recipient account
            if (!m_BankAccounts.Contains(toPlayerId))
            {
                CreateBankAccount(toPlayerId, toPlayerComp.GetPlayerName());
            }
            
            EdenBankAccount toAccount = m_BankAccounts.Get(toPlayerId);
            toAccount.AddBalance(amount);
            
            // Update database
            UpdateBankAccountInDatabase(fromAccount);
            UpdateBankAccountInDatabase(toAccount);
            
            // Record transactions
            RecordTransaction(fromPlayerId, "TRANSFER_OUT", totalCost, fromAccount.GetBalance());
            RecordTransaction(toPlayerId, "TRANSFER_IN", amount, toAccount.GetBalance());
            
            // Log transaction
            LogTransfer("Money Transfer", fromPlayerId, toPlayerId, amount, tax);
        }
        else if (transferMode == 2) // War points transfer
        {
            // Implementation for war points transfer
            // This would interact with the gang war system
        }
        
        Print(string.Format("[EdenBankingManager] Transfer: %1 -> %2, Amount: $%3, Tax: $%4", fromPlayerId, toPlayerId, amount, tax));
        return true;
    }
    
    //! Check ATM availability
    bool IsATMAvailable(string atmId)
    {
        if (m_ATMStatus.Contains(atmId))
            return m_ATMStatus.Get(atmId);
        return false;
    }
    
    //! Rob ATM (disable it)
    bool RobATM(string atmId, string robberId)
    {
        if (atmId == "" || robberId == "")
            return false;
            
        if (!m_ATMStatus.Contains(atmId))
            return false;
            
        if (!m_ATMStatus.Get(atmId))
            return false; // Already robbed
            
        // Disable ATM
        m_ATMStatus.Set(atmId, false);
        
        // Set timer to re-enable ATM
        GetGame().GetCallqueue().CallLater(RestoreATM, 300000, false, atmId); // 5 minutes
        
        // Log robbery
        LogTransaction("ATM Robbery", robberId, "", 0);
        
        Print(string.Format("[EdenBankingManager] ATM %1 robbed by %2", atmId, robberId));
        return true;
    }
    
    //! Restore ATM after robbery
    protected void RestoreATM(string atmId)
    {
        if (m_ATMStatus.Contains(atmId))
        {
            m_ATMStatus.Set(atmId, true);
            Print(string.Format("[EdenBankingManager] ATM %1 restored", atmId));
        }
    }
    
    //! Record transaction
    protected void RecordTransaction(string playerId, string transactionType, int amount, int newBalance)
    {
        string transactionId = string.Format("%1_%2_%3", playerId, transactionType, GetGame().GetWorld().GetWorldTime());
        
        EdenTransactionRecord transaction = new EdenTransactionRecord();
        transaction.SetTransactionId(transactionId);
        transaction.SetPlayerId(playerId);
        transaction.SetTransactionType(transactionType);
        transaction.SetAmount(amount);
        transaction.SetNewBalance(newBalance);
        transaction.SetTimestamp(GetGame().GetWorld().GetWorldTime());
        
        m_TransactionHistory.Set(transactionId, transaction);
        
        // Save to database
        SaveTransactionToDatabase(transaction);
    }
    
    //! Process daily interest (if implemented)
    protected void ProcessDailyInterest()
    {
        // Implementation for daily interest on bank accounts
        // This could be a small percentage added to accounts over a certain threshold
        
        foreach (string playerId, EdenBankAccount account : m_BankAccounts)
        {
            if (account.GetBalance() > 1000000) // Only for accounts over 1M
            {
                int interest = Math.Floor(account.GetBalance() * 0.001); // 0.1% daily interest
                account.AddBalance(interest);
                
                RecordTransaction(playerId, "INTEREST", interest, account.GetBalance());
                UpdateBankAccountInDatabase(account);
            }
        }
        
        Print("[EdenBankingManager] Processed daily interest");
    }
    
    //! Clean up old transactions
    protected void CleanupOldTransactions()
    {
        array<string> toRemove = {};
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int maxAge = 2592000; // 30 days
        
        foreach (string transactionId, EdenTransactionRecord transaction : m_TransactionHistory)
        {
            if (currentTime - transaction.GetTimestamp() > maxAge)
            {
                toRemove.Insert(transactionId);
            }
        }
        
        foreach (string transactionId : toRemove)
        {
            m_TransactionHistory.Remove(transactionId);
        }
        
        if (toRemove.Count() > 0)
        {
            Print(string.Format("[EdenBankingManager] Cleaned up %1 old transactions", toRemove.Count()));
        }
    }
    
    //! Get bank account
    EdenBankAccount GetBankAccount(string playerId)
    {
        if (m_BankAccounts.Contains(playerId))
            return m_BankAccounts.Get(playerId);
        return null;
    }
    
    //! Get account balance
    int GetAccountBalance(string playerId)
    {
        if (m_BankAccounts.Contains(playerId))
            return m_BankAccounts.Get(playerId).GetBalance();
        return 0;
    }
    
    //! Get transaction history for player
    array<ref EdenTransactionRecord> GetPlayerTransactionHistory(string playerId, int maxRecords = 50)
    {
        array<ref EdenTransactionRecord> playerTransactions = {};
        
        foreach (string transactionId, EdenTransactionRecord transaction : m_TransactionHistory)
        {
            if (transaction.GetPlayerId() == playerId)
            {
                playerTransactions.Insert(transaction);
                
                if (playerTransactions.Count() >= maxRecords)
                    break;
            }
        }
        
        return playerTransactions;
    }
    
    //! Get ATM locations
    array<ref EdenATMLocation> GetATMLocations()
    {
        return m_ATMLocations;
    }
    
    //! Deposit box redemption
    bool RedeemDepositBox(string playerId, int amount, string message)
    {
        if (playerId == "" || amount <= 0)
            return false;
            
        // Create or get account
        if (!m_BankAccounts.Contains(playerId))
        {
            IEntity playerEntity = GetPlayerEntity(playerId);
            if (!playerEntity)
                return false;
                
            EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
            if (!playerComp)
                return false;
                
            CreateBankAccount(playerId, playerComp.GetPlayerName());
        }
        
        EdenBankAccount account = m_BankAccounts.Get(playerId);
        int oldBalance = account.GetBalance();
        
        account.AddBalance(amount);
        account.SetLastAccessTime(GetGame().GetWorld().GetWorldTime());
        
        // Record transaction
        RecordTransaction(playerId, "DEPOSIT_BOX", amount, account.GetBalance());
        
        // Update database
        UpdateBankAccountInDatabase(account);
        
        // Log transaction
        LogDepositBox(playerId, amount, oldBalance, account.GetBalance());
        
        Print(string.Format("[EdenBankingManager] Deposit box redeemed: %1, Amount: $%2", playerId, amount));
        return true;
    }
    
    //! Helper methods
    protected void LogTransaction(string eventType, string playerId, string playerName, int amount)
    {
        // Implementation would log to database or file
        Print(string.Format("[EdenBankingManager] %1: Player %2 (%3), Amount: $%4", eventType, playerName, playerId, amount));
    }
    
    protected void LogTransfer(string eventType, string fromPlayerId, string toPlayerId, int amount, int tax)
    {
        // Implementation would log to database or file
        Print(string.Format("[EdenBankingManager] %1: %2 -> %3, Amount: $%4, Tax: $%5", eventType, fromPlayerId, toPlayerId, amount, tax));
    }
    
    protected void LogDepositBox(string playerId, int amount, int oldBalance, int newBalance)
    {
        // Implementation would log to database or file
        Print(string.Format("[EdenBankingManager] Deposit Box: Player %1, Amount: $%2, Old Balance: $%3, New Balance: $%4", playerId, amount, oldBalance, newBalance));
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadBankAccounts() { }
    protected void LoadTransactionHistory() { }
    protected void SaveBankAccountToDatabase(EdenBankAccount account) { }
    protected void UpdateBankAccountInDatabase(EdenBankAccount account) { }
    protected void SaveTransactionToDatabase(EdenTransactionRecord transaction) { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenBankingManager] Cleaning up banking system...");
        
        m_BankAccounts.Clear();
        m_TransactionHistory.Clear();
        m_ATMLocations.Clear();
        m_ATMStatus.Clear();
    }
}
