@echo off
echo ========================================
echo     EDENRP WORKBENCH SETUP HELPER
echo ========================================
echo.
echo This script will:
echo 1. Create a proper directory for Workbench
echo 2. Copy EdenRP mod files to the new location
echo 3. Set up the project for Workbench import
echo.

set "SOURCE_DIR=%~dp0EdenRP"
set "TARGET_DIR=C:\ArmaReforgerMods\EdenRP"

echo Source: %SOURCE_DIR%
echo Target: %TARGET_DIR%
echo.

if not exist "%SOURCE_DIR%" (
    echo ERROR: EdenRP folder not found in current directory!
    echo Please run this script from the same folder as your EdenRP mod.
    pause
    exit /b 1
)

echo Creating target directory...
if not exist "C:\ArmaReforgerMods" mkdir "C:\ArmaReforgerMods"

echo Copying EdenRP files...
xcopy "%SOURCE_DIR%" "%TARGET_DIR%" /E /I /Y

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ SUCCESS! EdenRP mod copied to: %TARGET_DIR%
    echo.
    echo Next steps:
    echo 1. Launch Arma Reforger Tools from Steam
    echo 2. Open Workbench
    echo 3. File → Open Project
    echo 4. Navigate to: %TARGET_DIR%\addon.gproj
    echo 5. Select and Open
    echo.
    echo If that doesn't work, try:
    echo - File → New → Addon Project
    echo - Name: EdenRP
    echo - Location: C:\ArmaReforgerMods\
    echo - Then copy files manually
    echo.
) else (
    echo.
    echo ❌ ERROR: Failed to copy files!
    echo Try running this script as Administrator.
    echo.
)

pause
