//! Eden Configuration Manager - Handles all configuration data
//! Converted from original configuration.sqf and related config files

class EdenConfigManager
{
    protected int m_iServerId;
    protected int m_iPaycheckAmount;
    protected int m_iTotalCrimes;
    
    // Jail positions
    protected vector m_vJailPos1;
    protected vector m_vJailPos2;
    
    // Force names
    protected string m_sCopForce;
    protected string m_sCopHighway;
    
    // Weapon arrays
    protected ref array<string> m_aTaserWeapons;
    protected ref array<string> m_aIllegalGear;
    protected ref array<string> m_aIllegalVehicles;
    protected ref array<string> m_aBlackwaterVehiclesGround;
    protected ref array<string> m_aBlackwaterVehiclesAir;
    
    // Market configuration
    protected ref array<string> m_aMarketItems;
    
    // Spawn points
    protected ref array<string> m_aDropPoints;
    protected ref array<string> m_aMedicalPoints;
    
    // Buyable homes
    protected ref array<string> m_aBuyableHomes;
    
    // Zone polygons
    protected ref array<vector> m_aKavalaPoly;
    protected ref array<vector> m_aAthiraPoly;
    protected ref array<vector> m_aSofiaPoly;
    protected ref array<vector> m_aPyrgosPoly;
    protected ref array<vector> m_aWarzonePoly;
    protected ref array<vector> m_aFederalReservePoly;
    protected ref array<vector> m_aBlackwaterPoly;
    protected ref array<vector> m_aJailPoly;
    protected ref array<vector> m_aBankPoly;
    
    // HQ Polygons
    protected ref array<vector> m_aKavalaHQPoly;
    protected ref array<vector> m_aAthiraHQPoly;
    protected ref array<vector> m_aNeochoriHQPoly;
    protected ref array<vector> m_aAirHQPoly;
    protected ref array<vector> m_aBwHQPoly;
    protected ref array<vector> m_aPyrgosHQPoly;
    protected ref array<vector> m_aSofiaHQPoly;
    
    // Title colors (RGB values)
    protected ref array<ref array<int>> m_aAllowedColors;
    
    // Detection variables
    protected int m_iRandomCashVal;
    protected int m_iCacheCash;
    protected int m_iInventoryRandomVar;
    protected int m_iInventoryMonitor;
    protected int m_iAtmCash;
    protected int m_iCacheAtmCash;
    protected int m_iWarPtsCache;
    
    // Race variables
    protected bool m_bIsActiveRace;
    protected int m_iCurrentTriggerInc;
    
    // Other settings
    protected bool m_bAutorun;
    protected bool m_bAutoswim;
    protected bool m_bMedRedeploy;
    
    void EdenConfigManager()
    {
        InitializeArrays();
    }
    
    //! Initialize all configuration arrays
    protected void InitializeArrays()
    {
        m_aTaserWeapons = new array<string>();
        m_aIllegalGear = new array<string>();
        m_aIllegalVehicles = new array<string>();
        m_aBlackwaterVehiclesGround = new array<string>();
        m_aBlackwaterVehiclesAir = new array<string>();
        m_aMarketItems = new array<string>();
        m_aDropPoints = new array<string>();
        m_aMedicalPoints = new array<string>();
        m_aBuyableHomes = new array<string>();
        
        m_aKavalaPoly = new array<vector>();
        m_aAthiraPoly = new array<vector>();
        m_aSofiaPoly = new array<vector>();
        m_aPyrgosPoly = new array<vector>();
        m_aWarzonePoly = new array<vector>();
        m_aFederalReservePoly = new array<vector>();
        m_aBlackwaterPoly = new array<vector>();
        m_aJailPoly = new array<vector>();
        m_aBankPoly = new array<vector>();
        
        m_aKavalaHQPoly = new array<vector>();
        m_aAthiraHQPoly = new array<vector>();
        m_aNeochoriHQPoly = new array<vector>();
        m_aAirHQPoly = new array<vector>();
        m_aBwHQPoly = new array<vector>();
        m_aPyrgosHQPoly = new array<vector>();
        m_aSofiaHQPoly = new array<vector>();
        
        m_aAllowedColors = new array<ref array<int>>();
    }
    
    //! Initialize configuration with server-specific settings
    void Initialize(int serverId)
    {
        m_iServerId = serverId;
        
        Print(string.Format("[EdenConfigManager] Initializing configuration for server %1", serverId));
        
        LoadDefaultConfiguration();
        LoadServerSpecificConfiguration();
        
        Print("[EdenConfigManager] Configuration loaded successfully");
    }
    
    //! Load default configuration values
    protected void LoadDefaultConfiguration()
    {
        // Basic settings
        m_iPaycheckAmount = 450;
        m_iTotalCrimes = 75;
        
        // Jail positions (converted from original coordinates)
        m_vJailPos1 = "16697.6 15.5 13614.7";
        m_vJailPos2 = "16697.6 0 13614.7";
        
        // Force names
        m_sCopForce = "APD";
        m_sCopHighway = "AHP";
        
        // Load weapon arrays
        LoadTaserWeapons();
        LoadIllegalGear();
        LoadIllegalVehicles();
        LoadBlackwaterVehicles();
        
        // Load market items
        LoadMarketItems();
        
        // Load spawn points
        LoadSpawnPoints();
        
        // Load buyable homes
        LoadBuyableHomes();
        
        // Load zone polygons
        LoadZonePolygons();
        
        // Load HQ polygons
        LoadHQPolygons();
        
        // Load allowed colors
        LoadAllowedColors();
        
        // Initialize detection variables
        InitializeDetectionVariables();
        
        // Initialize other settings
        m_bIsActiveRace = false;
        m_iCurrentTriggerInc = 0;
        m_bAutorun = false;
        m_bAutoswim = false;
        m_bMedRedeploy = false;
    }
    
    //! Load server-specific configuration
    protected void LoadServerSpecificConfiguration()
    {
        // Server-specific overrides can be loaded here
        // For now, using default values for all servers
    }
    
    //! Load taser weapons array
    protected void LoadTaserWeapons()
    {
        // Note: These are Arma 3 weapon class names - will need Reforger equivalents
        m_aTaserWeapons.Insert("arifle_ARX_blk_F");
        m_aTaserWeapons.Insert("arifle_SPAR_01_GL_snd_F");
        m_aTaserWeapons.Insert("srifle_DMR_07_blk_F");
        m_aTaserWeapons.Insert("hgun_P07_F");
        m_aTaserWeapons.Insert("SMG_02_F");
        // Add more weapons as needed - convert to Reforger equivalents
    }
    
    //! Load illegal gear array
    protected void LoadIllegalGear()
    {
        // Note: These are Arma 3 gear class names - will need Reforger equivalents
        m_aIllegalGear.Insert("U_Rangemaster");
        m_aIllegalGear.Insert("U_C_Man_casual_1_F");
        m_aIllegalGear.Insert("V_TacVest_blk_POLICE");
        // Add more gear as needed - convert to Reforger equivalents
    }
    
    //! Load illegal vehicles array
    protected void LoadIllegalVehicles()
    {
        // Note: These are Arma 3 vehicle class names - will need Reforger equivalents
        m_aIllegalVehicles.Insert("O_T_LSV_02_armed_F");
        m_aIllegalVehicles.Insert("I_C_Offroad_02_LMG_F");
        m_aIllegalVehicles.Insert("B_T_LSV_01_armed_F");
        // Add more vehicles as needed - convert to Reforger equivalents
    }
    
    //! Load blackwater vehicles
    protected void LoadBlackwaterVehicles()
    {
        // Ground vehicles
        m_aBlackwaterVehiclesGround.Insert("I_G_Offroad_01_AT_F");
        m_aBlackwaterVehiclesGround.Insert("B_T_LSV_01_armed_F");
        m_aBlackwaterVehiclesGround.Insert("O_T_LSV_02_armed_F");
        
        // Air vehicles
        m_aBlackwaterVehiclesAir.Insert("B_Heli_Transport_03_black_F");
        m_aBlackwaterVehiclesAir.Insert("B_Heli_Transport_01_camo_F");
    }
    
    //! Load market items
    protected void LoadMarketItems()
    {
        m_aMarketItems.Insert("apple");
        m_aMarketItems.Insert("peach");
        m_aMarketItems.Insert("salema");
        m_aMarketItems.Insert("marijuana");
        m_aMarketItems.Insert("heroinp");
        m_aMarketItems.Insert("cocainep");
        m_aMarketItems.Insert("diamondc");
        m_aMarketItems.Insert("goldbar");
        // Add all market items from original configuration
    }
    
    //! Load spawn points
    protected void LoadSpawnPoints()
    {
        // Drop points
        for (int i = 1; i <= 25; i++)
        {
            m_aDropPoints.Insert(string.Format("dp_%1", i));
        }
        
        // Medical points
        m_aMedicalPoints.Insert("Kavala_APD");
        m_aMedicalPoints.Insert("Neochori_APD");
        m_aMedicalPoints.Insert("Athira_APD");
        m_aMedicalPoints.Insert("Pyrgos_APD");
        m_aMedicalPoints.Insert("Air_APD");
        m_aMedicalPoints.Insert("BW_APD");
        m_aMedicalPoints.Insert("Sofia_Rescue");
        m_aMedicalPoints.Insert("Kavala_Rescue");
        m_aMedicalPoints.Insert("Air_Rescue");
        m_aMedicalPoints.Insert("Pyrgos_Rescue");
        m_aMedicalPoints.Insert("Neochori_Rescue");
    }
    
    //! Load buyable homes
    protected void LoadBuyableHomes()
    {
        // Note: These are Arma 3 building class names - will need Reforger equivalents
        m_aBuyableHomes.Insert("Land_i_House_Big_02_V1_F");
        m_aBuyableHomes.Insert("Land_i_House_Big_02_V2_F");
        m_aBuyableHomes.Insert("Land_i_House_Big_01_V1_F");
        m_aBuyableHomes.Insert("Land_i_Garage_V1_F");
        m_aBuyableHomes.Insert("Land_i_House_Small_01_V1_F");
        // Add all buyable home types
    }
    
    //! Load zone polygons
    protected void LoadZonePolygons()
    {
        // Kavala polygon
        m_aKavalaPoly.Insert("3870.55 0 13969.3");
        m_aKavalaPoly.Insert("4322.84 0 13610.1");
        m_aKavalaPoly.Insert("3946.07 0 12581.5");
        m_aKavalaPoly.Insert("3099.49 0 13101.9");
        
        // Athira polygon
        m_aAthiraPoly.Insert("13995.9 0 19019.9");
        m_aAthiraPoly.Insert("14459.4 0 18705.9");
        m_aAthiraPoly.Insert("14064.8 0 18359.5");
        m_aAthiraPoly.Insert("13599 0 18647.9");
        
        // Add other zone polygons...
    }
    
    //! Load HQ polygons
    protected void LoadHQPolygons()
    {
        // Kavala HQ polygon (simplified - full polygon has many points)
        m_aKavalaHQPoly.Insert("3238.41 0 12968.9");
        m_aKavalaHQPoly.Insert("3227.36 0 12969.3");
        m_aKavalaHQPoly.Insert("3225.16 0 12954.1");
        // Add remaining points...
        
        // Add other HQ polygons...
    }
    
    //! Load allowed title colors
    protected void LoadAllowedColors()
    {
        ref array<int> color;
        
        // Default
        color = {217, 217, 217};
        m_aAllowedColors.Insert(color);
        
        // Supporter ($15) (Brown)
        color = {76, 50, 26};
        m_aAllowedColors.Insert(color);
        
        // MVP ($30) (Yellow)
        color = {255, 255, 0};
        m_aAllowedColors.Insert(color);
        
        // Add all color definitions...
    }
    
    //! Initialize detection variables
    protected void InitializeDetectionVariables()
    {
        m_iRandomCashVal = 794524 + Math.RandomInt(0, 548486);
        m_iCacheCash = m_iRandomCashVal;
        m_iInventoryRandomVar = 15632 + Math.RandomInt(0, 43532);
        m_iInventoryMonitor = m_iInventoryRandomVar;
        m_iAtmCash = 50000;
        m_iCacheAtmCash = 50000 + m_iRandomCashVal;
        m_iWarPtsCache = 50000 + m_iRandomCashVal;
    }
    
    //! Getters for configuration values
    int GetServerId() { return m_iServerId; }
    int GetPaycheckAmount() { return m_iPaycheckAmount; }
    int GetTotalCrimes() { return m_iTotalCrimes; }
    
    vector GetJailPos1() { return m_vJailPos1; }
    vector GetJailPos2() { return m_vJailPos2; }
    
    string GetCopForce() { return m_sCopForce; }
    string GetCopHighway() { return m_sCopHighway; }
    
    array<string> GetTaserWeapons() { return m_aTaserWeapons; }
    array<string> GetIllegalGear() { return m_aIllegalGear; }
    array<string> GetIllegalVehicles() { return m_aIllegalVehicles; }
    array<string> GetMarketItems() { return m_aMarketItems; }
    array<string> GetBuyableHomes() { return m_aBuyableHomes; }
    
    array<vector> GetKavalaPoly() { return m_aKavalaPoly; }
    array<vector> GetAthiraPoly() { return m_aAthiraPoly; }
    array<vector> GetWarzonePoly() { return m_aWarzonePoly; }
    array<vector> GetJailPoly() { return m_aJailPoly; }
    
    array<ref array<int>> GetAllowedColors() { return m_aAllowedColors; }
    
    //! Check if weapon is a taser weapon
    bool IsTaserWeapon(string weaponClass)
    {
        return m_aTaserWeapons.Find(weaponClass) != -1;
    }
    
    //! Check if gear is illegal
    bool IsIllegalGear(string gearClass)
    {
        return m_aIllegalGear.Find(gearClass) != -1;
    }
    
    //! Check if vehicle is illegal
    bool IsIllegalVehicle(string vehicleClass)
    {
        return m_aIllegalVehicles.Find(vehicleClass) != -1;
    }
    
    //! Check if point is inside polygon
    bool IsPointInPolygon(vector point, array<vector> polygon)
    {
        if (polygon.Count() < 3)
            return false;
            
        bool inside = false;
        int j = polygon.Count() - 1;
        
        for (int i = 0; i < polygon.Count(); i++)
        {
            vector pi = polygon[i];
            vector pj = polygon[j];
            
            if (((pi[2] > point[2]) != (pj[2] > point[2])) &&
                (point[0] < (pj[0] - pi[0]) * (point[2] - pi[2]) / (pj[2] - pi[2]) + pi[0]))
            {
                inside = !inside;
            }
            j = i;
        }
        
        return inside;
    }
}
