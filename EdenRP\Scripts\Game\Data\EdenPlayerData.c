//! Eden Player Data - Stores all player information
//! Converted from MySQL players table structure

class EdenPlayerData
{
    // Core player information
    protected string m_PlayerId;
    protected string m_PlayerName;
    protected int m_Cash;
    protected int m_BankAccount;
    protected int m_DepositBox;
    
    // Faction levels and permissions
    protected int m_CopLevel;
    protected int m_MedicLevel;
    protected int m_AdminLevel;
    protected int m_DonatorLevel;
    protected int m_NewsLevel;
    protected int m_SupportTeamLevel;
    protected int m_CivCouncilLevel;
    protected int m_DesignerLevel;
    protected int m_DeveloperLevel;
    protected int m_RestrictionsLevel;
    
    // Licenses (stored as arrays of license names)
    protected ref array<string> m_CopLicenses;
    protected ref array<string> m_CivLicenses;
    protected ref array<string> m_MedLicenses;
    
    // Gear loadouts for each faction
    protected ref EdenGearLoadout m_CopGear;
    protected ref EdenGearLoadout m_MedGear;
    protected ref EdenGearLoadout m_CivGear;
    protected ref EdenGearLoadout m_ConqGear;
    
    // Position data
    protected ref EdenPlayerPosition m_Position;
    protected ref EdenPlayerPosition m_TanoaPosition;
    protected ref EdenPlayerPosition m_MaldenPosition;
    
    // Player statistics
    protected ref array<int> m_PlayerStats;
    
    // Wanted system
    protected ref array<ref EdenWantedEntry> m_WantedEntries;
    
    // Gang and war data
    protected int m_WarPoints;
    protected int m_WarKills;
    protected int m_WarDeaths;
    protected int m_VigilanteArrests;
    protected int m_VigilanteArrestsStored;
    
    // Misc data
    protected bool m_IsBlacklisted;
    protected string m_Aliases;
    protected string m_ArrestedData;
    protected string m_LastSide;
    protected int m_LastServer;
    protected string m_CurrentTitle;
    protected int m_RealtorCash;
    protected float m_NewDonor;
    protected string m_HexIcon;
    protected int m_HexIconRedemptions;
    protected int m_MutedUntil;
    
    // Timestamps
    protected int m_LastActive;
    protected int m_JoinedTimestamp;
    
    void EdenPlayerData()
    {
        m_CopLicenses = new array<string>();
        m_CivLicenses = new array<string>();
        m_MedLicenses = new array<string>();
        m_PlayerStats = new array<int>();
        m_WantedEntries = new array<ref EdenWantedEntry>();
        
        m_CopGear = new EdenGearLoadout();
        m_MedGear = new EdenGearLoadout();
        m_CivGear = new EdenGearLoadout();
        m_ConqGear = new EdenGearLoadout();
        
        m_Position = new EdenPlayerPosition();
        m_TanoaPosition = new EdenPlayerPosition();
        m_MaldenPosition = new EdenPlayerPosition();
    }
    
    //! Initialize with default values (equivalent to MySQL defaults)
    void InitializeDefaults()
    {
        m_Cash = 0;
        m_BankAccount = ********; // Default starting money
        m_DepositBox = 0;
        
        m_CopLevel = 0;
        m_MedicLevel = 7; // Default medic level
        m_AdminLevel = 4; // Default admin level
        m_DonatorLevel = 0;
        m_NewsLevel = 0;
        m_SupportTeamLevel = 0;
        m_CivCouncilLevel = 0;
        m_DesignerLevel = 0;
        m_DeveloperLevel = 0;
        m_RestrictionsLevel = 0;
        
        m_WarPoints = 0;
        m_WarKills = 0;
        m_WarDeaths = 0;
        m_VigilanteArrests = 0;
        m_VigilanteArrestsStored = 0;
        
        m_IsBlacklisted = false;
        m_LastSide = "civ";
        m_LastServer = 1;
        m_RealtorCash = 0;
        m_NewDonor = 0.0;
        m_HexIconRedemptions = 5;
        m_MutedUntil = 0;
        
        // Initialize stats array with 78 zeros (matching original system)
        m_PlayerStats.Clear();
        for (int i = 0; i < 78; i++)
        {
            m_PlayerStats.Insert(0);
        }
        
        m_LastActive = GetGame().GetWorld().GetWorldTime();
        m_JoinedTimestamp = GetGame().GetWorld().GetWorldTime();
    }
    
    //! Getters and Setters
    string GetPlayerId() { return m_PlayerId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    
    string GetPlayerName() { return m_PlayerName; }
    void SetPlayerName(string name) { m_PlayerName = name; }
    
    int GetCash() { return m_Cash; }
    void SetCash(int cash) { m_Cash = cash; }
    void AddCash(int amount) { m_Cash += amount; }
    bool RemoveCash(int amount) 
    { 
        if (m_Cash >= amount) 
        { 
            m_Cash -= amount; 
            return true; 
        } 
        return false; 
    }
    
    int GetBankAccount() { return m_BankAccount; }
    void SetBankAccount(int amount) { m_BankAccount = amount; }
    void AddBankMoney(int amount) { m_BankAccount += amount; }
    bool RemoveBankMoney(int amount) 
    { 
        if (m_BankAccount >= amount) 
        { 
            m_BankAccount -= amount; 
            return true; 
        } 
        return false; 
    }
    
    int GetDepositBox() { return m_DepositBox; }
    void SetDepositBox(int amount) { m_DepositBox = amount; }
    
    // Faction level getters/setters
    int GetCopLevel() { return m_CopLevel; }
    void SetCopLevel(int level) { m_CopLevel = level; }
    
    int GetMedicLevel() { return m_MedicLevel; }
    void SetMedicLevel(int level) { m_MedicLevel = level; }
    
    int GetAdminLevel() { return m_AdminLevel; }
    void SetAdminLevel(int level) { m_AdminLevel = level; }
    
    int GetDonatorLevel() { return m_DonatorLevel; }
    void SetDonatorLevel(int level) { m_DonatorLevel = level; }
    
    // License management
    array<string> GetCopLicenses() { return m_CopLicenses; }
    array<string> GetCivLicenses() { return m_CivLicenses; }
    array<string> GetMedLicenses() { return m_MedLicenses; }
    
    bool HasCopLicense(string license) { return m_CopLicenses.Find(license) != -1; }
    bool HasCivLicense(string license) { return m_CivLicenses.Find(license) != -1; }
    bool HasMedLicense(string license) { return m_MedLicenses.Find(license) != -1; }
    
    void AddCopLicense(string license) 
    { 
        if (!HasCopLicense(license)) 
            m_CopLicenses.Insert(license); 
    }
    void AddCivLicense(string license) 
    { 
        if (!HasCivLicense(license)) 
            m_CivLicenses.Insert(license); 
    }
    void AddMedLicense(string license) 
    { 
        if (!HasMedLicense(license)) 
            m_MedLicenses.Insert(license); 
    }
    
    void RemoveCopLicense(string license) { m_CopLicenses.RemoveItem(license); }
    void RemoveCivLicense(string license) { m_CivLicenses.RemoveItem(license); }
    void RemoveMedLicense(string license) { m_MedLicenses.RemoveItem(license); }
    
    // Gear loadouts
    EdenGearLoadout GetCopGear() { return m_CopGear; }
    EdenGearLoadout GetMedGear() { return m_MedGear; }
    EdenGearLoadout GetCivGear() { return m_CivGear; }
    EdenGearLoadout GetConqGear() { return m_ConqGear; }
    
    // Position data
    EdenPlayerPosition GetPosition() { return m_Position; }
    void SetPosition(vector pos, vector dir) 
    { 
        m_Position.SetPosition(pos); 
        m_Position.SetDirection(dir); 
    }
    
    // Statistics
    array<int> GetPlayerStats() { return m_PlayerStats; }
    int GetStat(int index) 
    { 
        if (index >= 0 && index < m_PlayerStats.Count()) 
            return m_PlayerStats[index]; 
        return 0; 
    }
    void SetStat(int index, int value) 
    { 
        if (index >= 0 && index < m_PlayerStats.Count()) 
            m_PlayerStats[index] = value; 
    }
    void AddStat(int index, int value) 
    { 
        if (index >= 0 && index < m_PlayerStats.Count()) 
            m_PlayerStats[index] += value; 
    }
    
    // War data
    int GetWarPoints() { return m_WarPoints; }
    void SetWarPoints(int points) { m_WarPoints = points; }
    void AddWarPoints(int points) { m_WarPoints += points; }
    
    int GetWarKills() { return m_WarKills; }
    void AddWarKill() { m_WarKills++; }
    
    int GetWarDeaths() { return m_WarDeaths; }
    void AddWarDeath() { m_WarDeaths++; }
    
    // Wanted system
    array<ref EdenWantedEntry> GetWantedEntries() { return m_WantedEntries; }
    void AddWantedEntry(EdenWantedEntry entry) { m_WantedEntries.Insert(entry); }
    void ClearWantedEntries() { m_WantedEntries.Clear(); }
    
    // Misc getters/setters
    bool IsBlacklisted() { return m_IsBlacklisted; }
    void SetBlacklisted(bool blacklisted) { m_IsBlacklisted = blacklisted; }
    
    string GetLastSide() { return m_LastSide; }
    void SetLastSide(string side) { m_LastSide = side; }
    
    string GetCurrentTitle() { return m_CurrentTitle; }
    void SetCurrentTitle(string title) { m_CurrentTitle = title; }
    
    //! Update last active timestamp
    void UpdateLastActive()
    {
        m_LastActive = GetGame().GetWorld().GetWorldTime();
    }
    
    //! Save to JSON string
    string SaveToJson()
    {
        // Implementation for JSON serialization
        return "{}"; // Placeholder
    }
    
    //! Load from JSON string
    bool LoadFromJson(string jsonContent)
    {
        // Implementation for JSON deserialization
        return true; // Placeholder
    }
}
