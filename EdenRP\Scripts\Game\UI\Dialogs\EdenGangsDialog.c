//! Gangs dialog for Eden Reforger
//! Converted from original yMenuGangs.hpp
//! Manages gang membership, operations, and territory

class EdenGangsDialog : EdenBaseDialog
{
    protected Widget m_GangInfoPanel;
    protected Widget m_GangMembersList;
    protected Widget m_GangNameLabel;
    protected Widget m_GangLevelLabel;
    protected Widget m_GangBankLabel;
    protected Widget m_GangTerritoryLabel;
    protected Widget m_PlayerRankLabel;
    protected Widget m_InvitePlayerButton;
    protected Widget m_KickMemberButton;
    protected Widget m_PromoteMemberButton;
    protected Widget m_DemoteMemberButton;
    protected Widget m_LeaveGangButton;
    protected Widget m_DisbandGangButton;
    protected Widget m_ManageBankButton;
    protected Widget m_RefreshButton;
    
    protected ref EdenGangData m_CurrentGang;
    protected ref array<ref EdenGangMember> m_GangMembers;
    protected int m_SelectedMemberIndex;
    protected bool m_InGang;
    protected bool m_IsGangLeader;
    protected int m_PlayerRank;
    
    //! Constructor
    void EdenGangsDialog()
    {
        m_CurrentGang = null;
        m_GangMembers = new array<ref EdenGangMember>();
        m_SelectedMemberIndex = -1;
        m_InGang = false;
        m_IsGangLeader = false;
        m_PlayerRank = 0;
    }
    
    //! Initialize the dialog
    override void OnCreate()
    {
        super.OnCreate();
        
        // Get widget references
        m_GangInfoPanel = m_Root.FindAnyWidget("GangInfoPanel");
        m_GangMembersList = m_Root.FindAnyWidget("GangMembersList");
        m_GangNameLabel = m_Root.FindAnyWidget("GangNameLabel");
        m_GangLevelLabel = m_Root.FindAnyWidget("GangLevelLabel");
        m_GangBankLabel = m_Root.FindAnyWidget("GangBankLabel");
        m_GangTerritoryLabel = m_Root.FindAnyWidget("GangTerritoryLabel");
        m_PlayerRankLabel = m_Root.FindAnyWidget("PlayerRankLabel");
        m_InvitePlayerButton = m_Root.FindAnyWidget("InvitePlayerButton");
        m_KickMemberButton = m_Root.FindAnyWidget("KickMemberButton");
        m_PromoteMemberButton = m_Root.FindAnyWidget("PromoteMemberButton");
        m_DemoteMemberButton = m_Root.FindAnyWidget("DemoteMemberButton");
        m_LeaveGangButton = m_Root.FindAnyWidget("LeaveGangButton");
        m_DisbandGangButton = m_Root.FindAnyWidget("DisbandGangButton");
        m_ManageBankButton = m_Root.FindAnyWidget("ManageBankButton");
        m_RefreshButton = m_Root.FindAnyWidget("RefreshButton");
        
        // Initialize display
        LoadGangData();
        RefreshDisplay();
        UpdateButtonStates();
    }
    
    //! Get dialog title
    override string GetDialogTitle()
    {
        return "Gangs";
    }
    
    //! Get active tab ID
    override int GetActiveTabId()
    {
        return 8; // Gangs tab
    }
    
    //! Load gang data
    void LoadGangData()
    {
        EdenGameMode gameMode = EdenGameMode.Cast(GetGame().GetGameMode());
        if (!gameMode)
            return;
            
        EdenDataManager dataManager = gameMode.GetDataManager();
        if (!dataManager)
            return;
            
        IEntity player = GetGame().GetPlayerController().GetControlledEntity();
        if (!player)
            return;
            
        string playerId = player.GetPrefabData().GetPrefabName(); // Simplified ID
        
        // TODO: Load actual gang data from data manager
        // For now, create sample data
        CreateSampleGangData();
    }
    
    //! Create sample gang data for testing
    void CreateSampleGangData()
    {
        // Simulate being in a gang
        m_InGang = true;
        m_IsGangLeader = false;
        m_PlayerRank = 2; // Member rank
        
        m_CurrentGang = new EdenGangData();
        m_CurrentGang.SetGangName("The Olympians");
        m_CurrentGang.SetGangLevel(5);
        m_CurrentGang.SetGangBank(150000);
        m_CurrentGang.SetMaxMembers(8);
        m_CurrentGang.SetIsActive(true);
        
        // Create sample gang members
        m_GangMembers.Clear();
        
        EdenGangMember leader = new EdenGangMember();
        leader.SetPlayerId("leader_001");
        leader.SetPlayerName("GangLeader");
        leader.SetRank(5); // Leader
        leader.SetJoinTime(GetGame().GetWorld().GetWorldTime() - 86400 * 30); // 30 days ago
        leader.SetIsOnline(true);
        m_GangMembers.Insert(leader);
        
        EdenGangMember officer = new EdenGangMember();
        officer.SetPlayerId("officer_001");
        officer.SetPlayerName("GangOfficer");
        officer.SetRank(4); // Officer
        officer.SetJoinTime(GetGame().GetWorld().GetWorldTime() - 86400 * 20); // 20 days ago
        officer.SetIsOnline(false);
        m_GangMembers.Insert(officer);
        
        EdenGangMember currentPlayer = new EdenGangMember();
        currentPlayer.SetPlayerId("current_player");
        currentPlayer.SetPlayerName("CurrentPlayer");
        currentPlayer.SetRank(2); // Member
        currentPlayer.SetJoinTime(GetGame().GetWorld().GetWorldTime() - 86400 * 10); // 10 days ago
        currentPlayer.SetIsOnline(true);
        m_GangMembers.Insert(currentPlayer);
        
        EdenGangMember recruit = new EdenGangMember();
        recruit.SetPlayerId("recruit_001");
        recruit.SetPlayerName("NewRecruit");
        recruit.SetRank(1); // Recruit
        recruit.SetJoinTime(GetGame().GetWorld().GetWorldTime() - 86400 * 2); // 2 days ago
        recruit.SetIsOnline(true);
        m_GangMembers.Insert(recruit);
    }
    
    //! Refresh the display
    void RefreshDisplay()
    {
        UpdateGangInfo();
        RefreshGangMembersList();
    }
    
    //! Update gang information display
    void UpdateGangInfo()
    {
        if (!m_InGang || !m_CurrentGang)
        {
            if (m_GangNameLabel) m_GangNameLabel.SetText("Not in a gang");
            if (m_GangLevelLabel) m_GangLevelLabel.SetText("Level: N/A");
            if (m_GangBankLabel) m_GangBankLabel.SetText("Bank: N/A");
            if (m_GangTerritoryLabel) m_GangTerritoryLabel.SetText("Territory: None");
            if (m_PlayerRankLabel) m_PlayerRankLabel.SetText("Rank: None");
            return;
        }
        
        if (m_GangNameLabel)
            m_GangNameLabel.SetText(string.Format("Gang: %1", m_CurrentGang.GetGangName()));
            
        if (m_GangLevelLabel)
            m_GangLevelLabel.SetText(string.Format("Level: %1", m_CurrentGang.GetGangLevel()));
            
        if (m_GangBankLabel)
            m_GangBankLabel.SetText(string.Format("Bank: $%1", FormatMoney(m_CurrentGang.GetGangBank())));
            
        if (m_GangTerritoryLabel)
            m_GangTerritoryLabel.SetText("Territory: Drug Lab Alpha"); // Sample territory
            
        if (m_PlayerRankLabel)
            m_PlayerRankLabel.SetText(string.Format("Your Rank: %1", GetRankName(m_PlayerRank)));
    }
    
    //! Refresh gang members list
    void RefreshGangMembersList()
    {
        if (!m_GangMembersList)
            return;
            
        // Clear existing items
        Widget child = m_GangMembersList.GetChildren();
        while (child)
        {
            Widget next = child.GetSibling();
            child.RemoveFromHierarchy();
            child = next;
        }
        
        // Add gang members
        for (int i = 0; i < m_GangMembers.Count(); i++)
        {
            EdenGangMember member = m_GangMembers[i];
            if (!member)
                continue;
                
            string memberText = FormatMemberDisplay(member, i);
            
            // Create list item widget
            Widget memberItem = GetGame().GetWorkspace().CreateWidgets("{2EFEA2AF1F38E7F0}UI/layouts/Menus/ContentBrowser/ContentBrowserEntry.layout", m_GangMembersList);
            if (memberItem)
            {
                TextWidget memberLabel = TextWidget.Cast(memberItem.FindAnyWidget("Name"));
                if (memberLabel)
                    memberLabel.SetText(memberText);
            }
        }
    }
    
    //! Format member display text
    string FormatMemberDisplay(EdenGangMember member, int index)
    {
        string rankName = GetRankName(member.GetRank());
        string onlineStatus = member.IsOnline() ? "Online" : "Offline";
        
        return string.Format("[%1] %2 - %3 (%4)", 
            index + 1, 
            member.GetPlayerName(), 
            rankName, 
            onlineStatus);
    }
    
    //! Get rank name from rank number
    string GetRankName(int rank)
    {
        switch (rank)
        {
            case 5: return "Leader";
            case 4: return "Officer";
            case 3: return "Sergeant";
            case 2: return "Member";
            case 1: return "Recruit";
            default: return "Unknown";
        }
    }
    
    //! Format money display
    string FormatMoney(int amount)
    {
        if (amount >= 1000000)
            return string.Format("%.1fM", amount / 1000000.0);
        else if (amount >= 1000)
            return string.Format("%.1fK", amount / 1000.0);
        else
            return amount.ToString();
    }
    
    //! Update button states based on current situation
    void UpdateButtonStates()
    {
        bool canManageMembers = m_InGang && (m_PlayerRank >= 4); // Officer or higher
        bool canManageBank = m_InGang && (m_PlayerRank >= 3); // Sergeant or higher
        
        if (m_InvitePlayerButton)
            m_InvitePlayerButton.SetEnabled(canManageMembers);
            
        if (m_KickMemberButton)
            m_KickMemberButton.SetEnabled(canManageMembers && m_SelectedMemberIndex >= 0);
            
        if (m_PromoteMemberButton)
            m_PromoteMemberButton.SetEnabled(canManageMembers && m_SelectedMemberIndex >= 0);
            
        if (m_DemoteMemberButton)
            m_DemoteMemberButton.SetEnabled(canManageMembers && m_SelectedMemberIndex >= 0);
            
        if (m_LeaveGangButton)
            m_LeaveGangButton.SetEnabled(m_InGang);
            
        if (m_DisbandGangButton)
            m_DisbandGangButton.SetEnabled(m_InGang && m_IsGangLeader);
            
        if (m_ManageBankButton)
            m_ManageBankButton.SetEnabled(canManageBank);
    }
    
    //! Handle widget click events
    override bool OnClick(Widget w, int x, int y, int button)
    {
        if (super.OnClick(w, x, y, button))
            return true;
            
        if (w == m_InvitePlayerButton)
        {
            OnInvitePlayerButtonClick();
            return true;
        }
        else if (w == m_KickMemberButton)
        {
            OnKickMemberButtonClick();
            return true;
        }
        else if (w == m_PromoteMemberButton)
        {
            OnPromoteMemberButtonClick();
            return true;
        }
        else if (w == m_DemoteMemberButton)
        {
            OnDemoteMemberButtonClick();
            return true;
        }
        else if (w == m_LeaveGangButton)
        {
            OnLeaveGangButtonClick();
            return true;
        }
        else if (w == m_DisbandGangButton)
        {
            OnDisbandGangButtonClick();
            return true;
        }
        else if (w == m_ManageBankButton)
        {
            OnManageBankButtonClick();
            return true;
        }
        else if (w == m_RefreshButton)
        {
            OnRefreshButtonClick();
            return true;
        }
        
        return false;
    }
    
    //! Handle invite player button click
    void OnInvitePlayerButtonClick()
    {
        // TODO: Open player selection dialog for gang invitation
        Print("EdenGangsDialog: Gang invitation functionality not yet implemented");
    }
    
    //! Handle kick member button click
    void OnKickMemberButtonClick()
    {
        if (m_SelectedMemberIndex >= 0 && m_SelectedMemberIndex < m_GangMembers.Count())
        {
            EdenGangMember member = m_GangMembers[m_SelectedMemberIndex];
            Print(string.Format("EdenGangsDialog: Kicking %1 from gang", member.GetPlayerName()));
            // TODO: Implement gang member kicking
        }
    }
    
    //! Handle promote member button click
    void OnPromoteMemberButtonClick()
    {
        if (m_SelectedMemberIndex >= 0 && m_SelectedMemberIndex < m_GangMembers.Count())
        {
            EdenGangMember member = m_GangMembers[m_SelectedMemberIndex];
            Print(string.Format("EdenGangsDialog: Promoting %1", member.GetPlayerName()));
            // TODO: Implement gang member promotion
        }
    }
    
    //! Handle demote member button click
    void OnDemoteMemberButtonClick()
    {
        if (m_SelectedMemberIndex >= 0 && m_SelectedMemberIndex < m_GangMembers.Count())
        {
            EdenGangMember member = m_GangMembers[m_SelectedMemberIndex];
            Print(string.Format("EdenGangsDialog: Demoting %1", member.GetPlayerName()));
            // TODO: Implement gang member demotion
        }
    }
    
    //! Handle leave gang button click
    void OnLeaveGangButtonClick()
    {
        Print("EdenGangsDialog: Leaving gang");
        // TODO: Implement gang leaving functionality
    }
    
    //! Handle disband gang button click
    void OnDisbandGangButtonClick()
    {
        Print("EdenGangsDialog: Disbanding gang");
        // TODO: Implement gang disbanding functionality
    }
    
    //! Handle manage bank button click
    void OnManageBankButtonClick()
    {
        Print("EdenGangsDialog: Opening gang bank management");
        // TODO: Open gang bank management dialog
    }
    
    //! Handle refresh button click
    void OnRefreshButtonClick()
    {
        LoadGangData();
        RefreshDisplay();
        UpdateButtonStates();
    }
}
