//! Wanted dialog for Eden Reforger
//! Converted from original yMenuWanted.hpp
//! Displays wanted list and bounty information

class EdenWantedDialog : EdenBaseDialog
{
    protected Widget m_WantedPlayersList;
    protected Widget m_PlayerDetailsPanel;
    protected Widget m_SelectedPlayerLabel;
    protected Widget m_BountyAmountLabel;
    protected Widget m_CrimesLabel;
    protected Widget m_WantedLevelLabel;
    protected Widget m_LastSeenLabel;
    protected Widget m_SortComboBox;
    protected Widget m_RefreshButton;
    protected Widget m_CollectBountyButton;
    
    protected ref array<ref EdenWantedPlayer> m_WantedPlayers;
    protected int m_SelectedPlayerIndex;
    protected string m_CurrentSortMode;
    
    //! Constructor
    void EdenWantedDialog()
    {
        m_WantedPlayers = new array<ref EdenWantedPlayer>();
        m_SelectedPlayerIndex = -1;
        m_CurrentSortMode = "Bounty";
    }
    
    //! Initialize the dialog
    override void OnCreate()
    {
        super.OnCreate();
        
        // Get widget references
        m_WantedPlayersList = m_Root.FindAnyWidget("WantedPlayersList");
        m_PlayerDetailsPanel = m_Root.FindAnyWidget("PlayerDetailsPanel");
        m_SelectedPlayerLabel = m_Root.FindAnyWidget("SelectedPlayerLabel");
        m_BountyAmountLabel = m_Root.FindAnyWidget("BountyAmountLabel");
        m_CrimesLabel = m_Root.FindAnyWidget("CrimesLabel");
        m_WantedLevelLabel = m_Root.FindAnyWidget("WantedLevelLabel");
        m_LastSeenLabel = m_Root.FindAnyWidget("LastSeenLabel");
        m_SortComboBox = m_Root.FindAnyWidget("SortComboBox");
        m_RefreshButton = m_Root.FindAnyWidget("RefreshButton");
        m_CollectBountyButton = m_Root.FindAnyWidget("CollectBountyButton");
        
        // Initialize display
        LoadWantedData();
        RefreshWantedPlayersList();
        UpdatePlayerDetails();
    }
    
    //! Get dialog title
    override string GetDialogTitle()
    {
        return "Wanted List";
    }
    
    //! Get active tab ID
    override int GetActiveTabId()
    {
        return 10; // Wanted tab
    }
    
    //! Load wanted data
    void LoadWantedData()
    {
        EdenGameMode gameMode = EdenGameMode.Cast(GetGame().GetGameMode());
        if (!gameMode)
            return;
            
        EdenDataManager dataManager = gameMode.GetDataManager();
        if (!dataManager)
            return;
            
        // TODO: Load actual wanted data from data manager
        // For now, create sample data
        CreateSampleWantedData();
    }
    
    //! Create sample wanted data for testing
    void CreateSampleWantedData()
    {
        m_WantedPlayers.Clear();
        
        // High bounty criminals
        CreateWantedPlayer("CriminalMastermind", 250000, 5, {"Murder", "Bank Robbery", "Drug Trafficking"}, "Kavala Bank");
        CreateWantedPlayer("DrugLord", 180000, 4, {"Drug Manufacturing", "Assault", "Reckless Driving"}, "Drug Lab Alpha");
        CreateWantedPlayer("BankRobber", 120000, 3, {"Armed Robbery", "Vehicle Theft", "Speeding"}, "Athira");
        
        // Medium bounty criminals
        CreateWantedPlayer("CarThief", 75000, 3, {"Vehicle Theft", "Reckless Driving", "Fleeing Police"}, "Pyrgos Garage");
        CreateWantedPlayer("Smuggler", 65000, 2, {"Drug Possession", "Illegal Weapons", "Speeding"}, "Checkpoint Charlie");
        CreateWantedPlayer("Vandal", 45000, 2, {"Property Damage", "Disorderly Conduct", "Trespassing"}, "Kavala Square");
        
        // Low bounty criminals
        CreateWantedPlayer("Speedster", 25000, 1, {"Speeding", "Reckless Driving"}, "Highway 1");
        CreateWantedPlayer("Pickpocket", 15000, 1, {"Theft", "Trespassing"}, "Market District");
        CreateWantedPlayer("Jaywalker", 5000, 1, {"Public Disturbance"}, "Kavala Streets");
    }
    
    //! Create a wanted player with specified parameters
    void CreateWantedPlayer(string playerName, int bounty, int wantedLevel, array<string> crimes, string lastSeen)
    {
        EdenWantedPlayer wantedPlayer = new EdenWantedPlayer();
        wantedPlayer.SetPlayerName(playerName);
        wantedPlayer.SetBountyAmount(bounty);
        wantedPlayer.SetWantedLevel(wantedLevel);
        wantedPlayer.SetCrimes(crimes);
        wantedPlayer.SetLastSeenLocation(lastSeen);
        wantedPlayer.SetLastSeenTime(GetGame().GetWorld().GetWorldTime() - Math.RandomInt(300, 3600)); // 5 min to 1 hour ago
        wantedPlayer.SetIsActive(true);
        
        m_WantedPlayers.Insert(wantedPlayer);
    }
    
    //! Refresh wanted players list
    void RefreshWantedPlayersList()
    {
        if (!m_WantedPlayersList)
            return;
            
        // Clear existing items
        Widget child = m_WantedPlayersList.GetChildren();
        while (child)
        {
            Widget next = child.GetSibling();
            child.RemoveFromHierarchy();
            child = next;
        }
        
        // Sort players based on current sort mode
        ref array<ref EdenWantedPlayer> sortedPlayers = SortWantedPlayers();
        
        // Add wanted players
        for (int i = 0; i < sortedPlayers.Count(); i++)
        {
            EdenWantedPlayer player = sortedPlayers[i];
            if (!player)
                continue;
                
            string playerText = FormatWantedPlayerDisplay(player, i);
            
            // Create list item widget
            Widget playerItem = GetGame().GetWorkspace().CreateWidgets("{2EFEA2AF1F38E7F0}UI/layouts/Menus/ContentBrowser/ContentBrowserEntry.layout", m_WantedPlayersList);
            if (playerItem)
            {
                TextWidget playerLabel = TextWidget.Cast(playerItem.FindAnyWidget("Name"));
                if (playerLabel)
                    playerLabel.SetText(playerText);
            }
        }
    }
    
    //! Sort wanted players based on current sort mode
    ref array<ref EdenWantedPlayer> SortWantedPlayers()
    {
        ref array<ref EdenWantedPlayer> sortedPlayers = new array<ref EdenWantedPlayer>();
        
        // Copy all players
        for (int i = 0; i < m_WantedPlayers.Count(); i++)
        {
            sortedPlayers.Insert(m_WantedPlayers[i]);
        }
        
        // Sort by bounty (highest first) - simple bubble sort for now
        if (m_CurrentSortMode == "Bounty")
        {
            for (int i = 0; i < sortedPlayers.Count() - 1; i++)
            {
                for (int j = 0; j < sortedPlayers.Count() - 1 - i; j++)
                {
                    if (sortedPlayers[j].GetBountyAmount() < sortedPlayers[j + 1].GetBountyAmount())
                    {
                        EdenWantedPlayer temp = sortedPlayers[j];
                        sortedPlayers[j] = sortedPlayers[j + 1];
                        sortedPlayers[j + 1] = temp;
                    }
                }
            }
        }
        else if (m_CurrentSortMode == "Level")
        {
            // Sort by wanted level (highest first)
            for (int i = 0; i < sortedPlayers.Count() - 1; i++)
            {
                for (int j = 0; j < sortedPlayers.Count() - 1 - i; j++)
                {
                    if (sortedPlayers[j].GetWantedLevel() < sortedPlayers[j + 1].GetWantedLevel())
                    {
                        EdenWantedPlayer temp = sortedPlayers[j];
                        sortedPlayers[j] = sortedPlayers[j + 1];
                        sortedPlayers[j + 1] = temp;
                    }
                }
            }
        }
        
        return sortedPlayers;
    }
    
    //! Format wanted player display text
    string FormatWantedPlayerDisplay(EdenWantedPlayer player, int index)
    {
        string wantedLevelText = GetWantedLevelText(player.GetWantedLevel());
        
        return string.Format("[%1] %2 - $%3 (%4)", 
            index + 1, 
            player.GetPlayerName(), 
            FormatMoney(player.GetBountyAmount()), 
            wantedLevelText);
    }
    
    //! Get wanted level text
    string GetWantedLevelText(int level)
    {
        switch (level)
        {
            case 5: return "★★★★★";
            case 4: return "★★★★☆";
            case 3: return "★★★☆☆";
            case 2: return "★★☆☆☆";
            case 1: return "★☆☆☆☆";
            default: return "☆☆☆☆☆";
        }
    }
    
    //! Format money display
    string FormatMoney(int amount)
    {
        if (amount >= 1000000)
            return string.Format("%.1fM", amount / 1000000.0);
        else if (amount >= 1000)
            return string.Format("%.1fK", amount / 1000.0);
        else
            return amount.ToString();
    }
    
    //! Update player details panel
    void UpdatePlayerDetails()
    {
        if (m_SelectedPlayerIndex < 0 || m_SelectedPlayerIndex >= m_WantedPlayers.Count())
        {
            if (m_SelectedPlayerLabel) m_SelectedPlayerLabel.SetText("No player selected");
            if (m_BountyAmountLabel) m_BountyAmountLabel.SetText("Bounty: N/A");
            if (m_CrimesLabel) m_CrimesLabel.SetText("Crimes: None");
            if (m_WantedLevelLabel) m_WantedLevelLabel.SetText("Level: N/A");
            if (m_LastSeenLabel) m_LastSeenLabel.SetText("Last Seen: Unknown");
            return;
        }
        
        EdenWantedPlayer player = m_WantedPlayers[m_SelectedPlayerIndex];
        if (!player)
            return;
            
        if (m_SelectedPlayerLabel)
            m_SelectedPlayerLabel.SetText(string.Format("Suspect: %1", player.GetPlayerName()));
            
        if (m_BountyAmountLabel)
            m_BountyAmountLabel.SetText(string.Format("Bounty: $%1", FormatMoney(player.GetBountyAmount())));
            
        if (m_CrimesLabel)
        {
            array<string> crimes = player.GetCrimes();
            string crimesText = "Crimes: ";
            for (int i = 0; i < crimes.Count(); i++)
            {
                if (i > 0) crimesText += ", ";
                crimesText += crimes[i];
            }
            m_CrimesLabel.SetText(crimesText);
        }
        
        if (m_WantedLevelLabel)
            m_WantedLevelLabel.SetText(string.Format("Wanted Level: %1 %2", 
                player.GetWantedLevel(), 
                GetWantedLevelText(player.GetWantedLevel())));
                
        if (m_LastSeenLabel)
        {
            int timeSince = GetGame().GetWorld().GetWorldTime() - player.GetLastSeenTime();
            string timeText = FormatTimeSince(timeSince);
            m_LastSeenLabel.SetText(string.Format("Last Seen: %1 (%2 ago)", 
                player.GetLastSeenLocation(), 
                timeText));
        }
    }
    
    //! Format time since last seen
    string FormatTimeSince(int seconds)
    {
        if (seconds < 60)
            return string.Format("%1s", seconds);
        else if (seconds < 3600)
            return string.Format("%1m", seconds / 60);
        else
            return string.Format("%1h", seconds / 3600);
    }
    
    //! Handle widget click events
    override bool OnClick(Widget w, int x, int y, int button)
    {
        if (super.OnClick(w, x, y, button))
            return true;
            
        if (w == m_SortComboBox)
        {
            OnSortModeChanged();
            return true;
        }
        else if (w == m_RefreshButton)
        {
            OnRefreshButtonClick();
            return true;
        }
        else if (w == m_CollectBountyButton)
        {
            OnCollectBountyButtonClick();
            return true;
        }
        
        return false;
    }
    
    //! Handle sort mode change
    void OnSortModeChanged()
    {
        // TODO: Get selected sort mode from combobox
        // Toggle between Bounty and Level for now
        if (m_CurrentSortMode == "Bounty")
            m_CurrentSortMode = "Level";
        else
            m_CurrentSortMode = "Bounty";
            
        RefreshWantedPlayersList();
    }
    
    //! Handle refresh button click
    void OnRefreshButtonClick()
    {
        LoadWantedData();
        RefreshWantedPlayersList();
        UpdatePlayerDetails();
        Print("EdenWantedDialog: Wanted list refreshed");
    }
    
    //! Handle collect bounty button click
    void OnCollectBountyButtonClick()
    {
        if (m_SelectedPlayerIndex >= 0 && m_SelectedPlayerIndex < m_WantedPlayers.Count())
        {
            EdenWantedPlayer player = m_WantedPlayers[m_SelectedPlayerIndex];
            Print(string.Format("EdenWantedDialog: Attempting to collect bounty for %1 ($%2)", 
                player.GetPlayerName(), 
                FormatMoney(player.GetBountyAmount())));
            // TODO: Implement bounty collection logic
        }
        else
        {
            Print("EdenWantedDialog: No wanted player selected");
        }
    }
    
    //! Update dialog content
    override void OnUpdate()
    {
        super.OnUpdate();
        
        // Update last seen times
        UpdatePlayerDetails();
    }
}
