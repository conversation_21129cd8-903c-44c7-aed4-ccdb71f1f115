//! Eden Civilian Equipment System - Handles civilian tools, items, and loadouts
//! Converted from original civilian equipment systems

class EdenCivilianEquipment
{
    protected ref map<string, ref EdenCivilianLoadout> m_CivilianLoadouts;
    protected ref map<string, ref EdenToolData> m_ToolConfigs;
    protected ref map<string, int> m_ItemPrices;
    protected ref array<string> m_IllegalItems;
    
    void EdenCivilianEquipment()
    {
        m_CivilianLoadouts = new map<string, ref EdenCivilianLoadout>();
        m_ToolConfigs = new map<string, ref EdenToolData>();
        m_ItemPrices = new map<string, int>();
        m_IllegalItems = new array<string>();
        
        InitializeLoadouts();
        InitializeTools();
        InitializeItems();
    }
    
    //! Initialize civilian equipment system
    void Initialize()
    {
        Print("[EdenCivilianEquipment] Initializing civilian equipment system...");
        
        SetupDefaultLoadouts();
        SetupToolConfigurations();
        SetupItemPricing();
        
        Print("[EdenCivilianEquipment] Civilian equipment system initialized");
    }
    
    //! Initialize civilian loadouts
    protected void InitializeLoadouts()
    {
        // Basic civilian loadout
        EdenCivilianLoadout basicLoadout = new EdenCivilianLoadout();
        basicLoadout.SetLoadoutName("Basic Civilian");
        basicLoadout.SetLoadoutType("civilian");
        basicLoadout.AddUniformOption("U_C_Poloshirt_stripped");
        basicLoadout.AddUniformOption("U_C_Poloshirt_tricolour");
        basicLoadout.AddUniformOption("U_C_Poloshirt_salmon");
        basicLoadout.AddUniformOption("U_C_man_sport_1_F");
        basicLoadout.AddUniformOption("U_C_man_sport_2_F");
        basicLoadout.AddUniformOption("U_C_man_sport_3_F");
        basicLoadout.AddUniformOption("U_C_Man_casual_4_F");
        basicLoadout.AddUniformOption("U_C_Man_casual_5_F");
        basicLoadout.AddUniformOption("U_C_Man_casual_6_F");
        basicLoadout.AddItem("FirstAidKit", 1);
        basicLoadout.AddItem("ItemMap", 1);
        basicLoadout.AddItem("ItemCompass", 1);
        basicLoadout.AddItem("ItemWatch", 1);
        m_CivilianLoadouts.Set("basic", basicLoadout);
        
        // News team loadout
        EdenCivilianLoadout newsLoadout = new EdenCivilianLoadout();
        newsLoadout.SetLoadoutName("News Team");
        newsLoadout.SetLoadoutType("news");
        newsLoadout.SetRequiredLevel(1);
        newsLoadout.AddUniformByLevel(1, "U_C_Journalist");
        newsLoadout.AddUniformByLevel(2, "U_C_Poor_2");
        newsLoadout.AddUniformByLevel(3, "U_IG_Guerilla2_1");
        newsLoadout.AddUniformByLevel(4, "U_Marshal");
        newsLoadout.AddVestByLevel(1, "V_Rangemaster_belt");
        newsLoadout.AddItem("FirstAidKit", 2);
        newsLoadout.AddItem("ItemMap", 1);
        newsLoadout.AddItem("ItemCompass", 1);
        newsLoadout.AddItem("ItemWatch", 1);
        newsLoadout.AddItem("ItemRadio", 1);
        m_CivilianLoadouts.Set("news", newsLoadout);
        
        // Worker loadout
        EdenCivilianLoadout workerLoadout = new EdenCivilianLoadout();
        workerLoadout.SetLoadoutName("Worker");
        workerLoadout.SetLoadoutType("worker");
        workerLoadout.AddUniformOption("U_C_WorkerCoveralls");
        workerLoadout.AddUniformOption("U_C_Mechanic_01");
        workerLoadout.AddVestOption("V_Rangemaster_belt");
        workerLoadout.AddBackpackOption("B_Kitbag_mcamo");
        workerLoadout.AddItem("FirstAidKit", 2);
        workerLoadout.AddItem("ToolKit", 1);
        workerLoadout.AddItem("ItemMap", 1);
        workerLoadout.AddItem("ItemCompass", 1);
        workerLoadout.AddItem("ItemWatch", 1);
        m_CivilianLoadouts.Set("worker", workerLoadout);
    }
    
    //! Initialize tool configurations
    protected void InitializeTools()
    {
        // Pickaxe
        EdenToolData pickaxe = new EdenToolData();
        pickaxe.SetToolName("Pickaxe");
        pickaxe.SetToolType("mining");
        pickaxe.SetItemClass("pickaxe");
        pickaxe.SetUsageTime(3.0);
        pickaxe.SetDurability(100);
        pickaxe.SetPrice(150);
        pickaxe.AddCompatibleResource("copperore");
        pickaxe.AddCompatibleResource("ironore");
        pickaxe.AddCompatibleResource("salt");
        pickaxe.AddCompatibleResource("sand");
        pickaxe.AddCompatibleResource("diamond");
        pickaxe.AddCompatibleResource("oilu");
        pickaxe.AddCompatibleResource("rock");
        m_ToolConfigs.Set("pickaxe", pickaxe);
        
        // Fishing rod
        EdenToolData fishingRod = new EdenToolData();
        fishingRod.SetToolName("Fishing Rod");
        fishingRod.SetToolType("fishing");
        fishingRod.SetItemClass("fishingrod");
        fishingRod.SetUsageTime(2.5);
        fishingRod.SetDurability(75);
        fishingRod.SetPrice(100);
        fishingRod.AddCompatibleResource("salema");
        fishingRod.AddCompatibleResource("ornate");
        fishingRod.AddCompatibleResource("mackerel");
        fishingRod.AddCompatibleResource("tuna");
        fishingRod.AddCompatibleResource("mullet");
        fishingRod.AddCompatibleResource("catshark");
        m_ToolConfigs.Set("fishingrod", fishingRod);
        
        // Lockpick
        EdenToolData lockpick = new EdenToolData();
        lockpick.SetToolName("Lockpick");
        lockpick.SetToolType("illegal");
        lockpick.SetItemClass("lockpick");
        lockpick.SetUsageTime(5.0);
        lockpick.SetDurability(25);
        lockpick.SetPrice(500);
        lockpick.SetIllegal(true);
        m_ToolConfigs.Set("lockpick", lockpick);
        
        // Bolt cutter
        EdenToolData boltCutter = new EdenToolData();
        boltCutter.SetToolName("Bolt Cutter");
        boltCutter.SetToolType("illegal");
        boltCutter.SetItemClass("boltcutter");
        boltCutter.SetUsageTime(3.0);
        boltCutter.SetDurability(50);
        boltCutter.SetPrice(750);
        boltCutter.SetIllegal(true);
        m_ToolConfigs.Set("boltcutter", boltCutter);
    }
    
    //! Initialize item pricing and categories
    protected void InitializeItems()
    {
        // Basic items
        m_ItemPrices.Set("FirstAidKit", 50);
        m_ItemPrices.Set("ToolKit", 100);
        m_ItemPrices.Set("ItemMap", 25);
        m_ItemPrices.Set("ItemCompass", 25);
        m_ItemPrices.Set("ItemWatch", 50);
        m_ItemPrices.Set("ItemRadio", 150);
        
        // Tools
        m_ItemPrices.Set("pickaxe", 150);
        m_ItemPrices.Set("fishingrod", 100);
        m_ItemPrices.Set("lockpick", 500);
        m_ItemPrices.Set("boltcutter", 750);
        
        // Illegal items
        m_IllegalItems.Insert("lockpick");
        m_IllegalItems.Insert("boltcutter");
        m_IllegalItems.Insert("speedbomb");
        m_IllegalItems.Insert("blastingcharge");
        m_IllegalItems.Insert("goldbar");
        m_IllegalItems.Insert("hackingterminal");
        m_IllegalItems.Insert("takeoverterminal");
        
        // Processed materials
        m_ItemPrices.Set("copperore", 25);
        m_ItemPrices.Set("copperr", 75);
        m_ItemPrices.Set("ironore", 30);
        m_ItemPrices.Set("ironr", 85);
        m_ItemPrices.Set("salt", 15);
        m_ItemPrices.Set("saltr", 45);
        m_ItemPrices.Set("sand", 20);
        m_ItemPrices.Set("glass", 60);
        m_ItemPrices.Set("diamond", 500);
        m_ItemPrices.Set("oilu", 100);
        m_ItemPrices.Set("oilp", 300);
        m_ItemPrices.Set("rock", 10);
        m_ItemPrices.Set("cement", 35);
        
        // Fish
        m_ItemPrices.Set("salema", 45);
        m_ItemPrices.Set("ornate", 40);
        m_ItemPrices.Set("mackerel", 50);
        m_ItemPrices.Set("tuna", 85);
        m_ItemPrices.Set("mullet", 35);
        m_ItemPrices.Set("catshark", 125);
    }
    
    //! Give civilian loadout to player
    bool GiveCivilianLoadout(IEntity playerEntity, string loadoutType, int level = 1)
    {
        if (!playerEntity || loadoutType == "")
            return false;
            
        if (!m_CivilianLoadouts.Contains(loadoutType))
            return false;
            
        EdenCivilianLoadout loadout = m_CivilianLoadouts.Get(loadoutType);
        
        // Strip player first
        StripPlayerEquipment(playerEntity);
        
        // Apply uniform
        string uniform = loadout.GetUniformForLevel(level);
        if (uniform != "")
        {
            ApplyUniform(playerEntity, uniform);
        }
        
        // Apply vest
        string vest = loadout.GetVestForLevel(level);
        if (vest != "")
        {
            ApplyVest(playerEntity, vest);
        }
        
        // Apply helmet
        string helmet = loadout.GetHelmetForLevel(level);
        if (helmet != "")
        {
            ApplyHelmet(playerEntity, helmet);
        }
        
        // Apply backpack
        string backpack = loadout.GetBackpackForLevel(level);
        if (backpack != "")
        {
            ApplyBackpack(playerEntity, backpack);
        }
        
        // Add items
        map<string, int> items = loadout.GetItems();
        foreach (string itemName, int quantity : items)
        {
            AddItemToPlayer(playerEntity, itemName, quantity);
        }
        
        // Add weapons
        array<string> weapons = loadout.GetWeapons();
        foreach (string weaponName : weapons)
        {
            AddWeaponToPlayer(playerEntity, weaponName);
        }
        
        Print(string.Format("[EdenCivilianEquipment] Applied %1 loadout to player", loadoutType));
        return true;
    }
    
    //! Check if player has tool
    bool PlayerHasTool(IEntity playerEntity, string toolType)
    {
        if (!playerEntity || toolType == "")
            return false;
            
        if (!m_ToolConfigs.Contains(toolType))
            return false;
            
        EdenToolData toolData = m_ToolConfigs.Get(toolType);
        return PlayerHasItem(playerEntity, toolData.GetItemClass());
    }
    
    //! Use tool
    bool UseTool(IEntity playerEntity, string toolType)
    {
        if (!PlayerHasTool(playerEntity, toolType))
            return false;
            
        EdenToolData toolData = m_ToolConfigs.Get(toolType);
        
        // Check durability and potentially break tool
        if (Math.RandomFloat01() < 0.05) // 5% chance to break
        {
            RemoveItemFromPlayer(playerEntity, toolData.GetItemClass(), 1);
            Print(string.Format("[EdenCivilianEquipment] Tool %1 broke from use", toolType));
            return false;
        }
        
        return true;
    }
    
    //! Get tool data
    EdenToolData GetToolData(string toolType)
    {
        if (m_ToolConfigs.Contains(toolType))
            return m_ToolConfigs.Get(toolType);
        return null;
    }
    
    //! Check if item is illegal
    bool IsItemIllegal(string itemName)
    {
        return m_IllegalItems.Find(itemName) != -1;
    }
    
    //! Get item price
    int GetItemPrice(string itemName)
    {
        if (m_ItemPrices.Contains(itemName))
            return m_ItemPrices.Get(itemName);
        return 0;
    }
    
    //! Remove illegal items from player
    void RemoveIllegalItems(IEntity playerEntity)
    {
        if (!playerEntity)
            return;
            
        foreach (string illegalItem : m_IllegalItems)
        {
            if (PlayerHasItem(playerEntity, illegalItem))
            {
                RemoveItemFromPlayer(playerEntity, illegalItem, -1); // Remove all
                Print(string.Format("[EdenCivilianEquipment] Removed illegal item %1 from player", illegalItem));
            }
        }
    }
    
    //! Setup methods
    protected void SetupDefaultLoadouts()
    {
        // Additional setup for default loadouts
    }
    
    protected void SetupToolConfigurations()
    {
        // Additional setup for tool configurations
    }
    
    protected void SetupItemPricing()
    {
        // Additional setup for item pricing
    }
    
    //! Helper methods (placeholders for actual implementation)
    protected void StripPlayerEquipment(IEntity playerEntity)
    {
        // Remove all equipment from player
    }
    
    protected void ApplyUniform(IEntity playerEntity, string uniform)
    {
        // Apply uniform to player
    }
    
    protected void ApplyVest(IEntity playerEntity, string vest)
    {
        // Apply vest to player
    }
    
    protected void ApplyHelmet(IEntity playerEntity, string helmet)
    {
        // Apply helmet to player
    }
    
    protected void ApplyBackpack(IEntity playerEntity, string backpack)
    {
        // Apply backpack to player
    }
    
    protected void AddItemToPlayer(IEntity playerEntity, string itemName, int quantity)
    {
        // Add item to player inventory
    }
    
    protected void AddWeaponToPlayer(IEntity playerEntity, string weaponName)
    {
        // Add weapon to player
    }
    
    protected bool PlayerHasItem(IEntity playerEntity, string itemName)
    {
        // Check if player has item
        return true; // Placeholder
    }
    
    protected void RemoveItemFromPlayer(IEntity playerEntity, string itemName, int quantity)
    {
        // Remove item from player inventory
    }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenCivilianEquipment] Cleaning up civilian equipment system...");
        
        m_CivilianLoadouts.Clear();
        m_ToolConfigs.Clear();
        m_ItemPrices.Clear();
        m_IllegalItems.Clear();
    }
}
