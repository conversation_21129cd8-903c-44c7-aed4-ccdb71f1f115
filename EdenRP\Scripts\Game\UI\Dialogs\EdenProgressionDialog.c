//! Progression Dialog for Eden Reforger
//! Converted from original Eden Altis Life progression systems
//! Displays player statistics, titles, ranks, and achievements

class EdenProgressionDialog : EdenBaseDialog
{
    protected Widget m_wStatsPanel;
    protected Widget m_wTitlesPanel;
    protected Widget m_wAchievementsPanel;
    protected Widget m_wRanksPanel;
    
    // Statistics widgets
    protected TextWidget m_wCivLevelText;
    protected TextWidget m_wCopLevelText;
    protected TextWidget m_wMedicLevelText;
    protected TextWidget m_wCivExpText;
    protected TextWidget m_wCopExpText;
    protected TextWidget m_wMedicExpText;
    protected TextWidget m_wKillsText;
    protected TextWidget m_wDeathsText;
    protected TextWidget m_wArrestsText;
    protected TextWidget m_wRevivesText;
    protected TextWidget m_wPlaytimeText;
    protected TextWidget m_wMoneyEarnedText;
    protected TextWidget m_wMoneySpentText;
    protected TextWidget m_wDistanceFootText;
    protected TextWidget m_wDistanceVehicleText;
    
    // Titles widgets
    protected ScrollLayoutWidget m_wTitlesList;
    protected TextWidget m_wCurrentTitleText;
    protected ButtonWidget m_wSetTitleButton;
    
    // Achievements widgets
    protected ScrollLayoutWidget m_wAchievementsList;
    protected TextWidget m_wAchievementPointsText;
    
    // Ranks widgets
    protected TextWidget m_wCivRankText;
    protected TextWidget m_wCopRankText;
    protected TextWidget m_wMedicRankText;
    protected ProgressBarWidget m_wCivProgressBar;
    protected ProgressBarWidget m_wCopProgressBar;
    protected ProgressBarWidget m_wMedicProgressBar;
    
    // Tab buttons
    protected ButtonWidget m_wStatsTabButton;
    protected ButtonWidget m_wTitlesTabButton;
    protected ButtonWidget m_wAchievementsTabButton;
    protected ButtonWidget m_wRanksTabButton;
    
    protected string m_sSelectedTitle;
    protected EdenPlayerStats m_PlayerStats;
    protected EdenProgressionManager m_ProgressionManager;
    
    //! Constructor
    void EdenProgressionDialog()
    {
        m_sSelectedTitle = "";
    }
    
    //! Initialize dialog
    override void OnMenuOpen()
    {
        super.OnMenuOpen();
        
        // Get progression manager
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
            m_ProgressionManager = gameMode.GetProgressionManager();
        
        // Initialize widgets
        InitializeWidgets();
        
        // Load player data
        LoadPlayerData();
        
        // Show statistics tab by default
        ShowStatsTab();
    }
    
    //! Initialize all widgets
    protected void InitializeWidgets()
    {
        // Get main panels
        m_wStatsPanel = m_wRoot.FindAnyWidget("StatsPanel");
        m_wTitlesPanel = m_wRoot.FindAnyWidget("TitlesPanel");
        m_wAchievementsPanel = m_wRoot.FindAnyWidget("AchievementsPanel");
        m_wRanksPanel = m_wRoot.FindAnyWidget("RanksPanel");
        
        // Statistics widgets
        m_wCivLevelText = TextWidget.Cast(m_wRoot.FindAnyWidget("CivLevelText"));
        m_wCopLevelText = TextWidget.Cast(m_wRoot.FindAnyWidget("CopLevelText"));
        m_wMedicLevelText = TextWidget.Cast(m_wRoot.FindAnyWidget("MedicLevelText"));
        m_wCivExpText = TextWidget.Cast(m_wRoot.FindAnyWidget("CivExpText"));
        m_wCopExpText = TextWidget.Cast(m_wRoot.FindAnyWidget("CopExpText"));
        m_wMedicExpText = TextWidget.Cast(m_wRoot.FindAnyWidget("MedicExpText"));
        m_wKillsText = TextWidget.Cast(m_wRoot.FindAnyWidget("KillsText"));
        m_wDeathsText = TextWidget.Cast(m_wRoot.FindAnyWidget("DeathsText"));
        m_wArrestsText = TextWidget.Cast(m_wRoot.FindAnyWidget("ArrestsText"));
        m_wRevivesText = TextWidget.Cast(m_wRoot.FindAnyWidget("RevivesText"));
        m_wPlaytimeText = TextWidget.Cast(m_wRoot.FindAnyWidget("PlaytimeText"));
        m_wMoneyEarnedText = TextWidget.Cast(m_wRoot.FindAnyWidget("MoneyEarnedText"));
        m_wMoneySpentText = TextWidget.Cast(m_wRoot.FindAnyWidget("MoneySpentText"));
        m_wDistanceFootText = TextWidget.Cast(m_wRoot.FindAnyWidget("DistanceFootText"));
        m_wDistanceVehicleText = TextWidget.Cast(m_wRoot.FindAnyWidget("DistanceVehicleText"));
        
        // Titles widgets
        m_wTitlesList = ScrollLayoutWidget.Cast(m_wRoot.FindAnyWidget("TitlesList"));
        m_wCurrentTitleText = TextWidget.Cast(m_wRoot.FindAnyWidget("CurrentTitleText"));
        m_wSetTitleButton = ButtonWidget.Cast(m_wRoot.FindAnyWidget("SetTitleButton"));
        
        // Achievements widgets
        m_wAchievementsList = ScrollLayoutWidget.Cast(m_wRoot.FindAnyWidget("AchievementsList"));
        m_wAchievementPointsText = TextWidget.Cast(m_wRoot.FindAnyWidget("AchievementPointsText"));
        
        // Ranks widgets
        m_wCivRankText = TextWidget.Cast(m_wRoot.FindAnyWidget("CivRankText"));
        m_wCopRankText = TextWidget.Cast(m_wRoot.FindAnyWidget("CopRankText"));
        m_wMedicRankText = TextWidget.Cast(m_wRoot.FindAnyWidget("MedicRankText"));
        m_wCivProgressBar = ProgressBarWidget.Cast(m_wRoot.FindAnyWidget("CivProgressBar"));
        m_wCopProgressBar = ProgressBarWidget.Cast(m_wRoot.FindAnyWidget("CopProgressBar"));
        m_wMedicProgressBar = ProgressBarWidget.Cast(m_wRoot.FindAnyWidget("MedicProgressBar"));
        
        // Tab buttons
        m_wStatsTabButton = ButtonWidget.Cast(m_wRoot.FindAnyWidget("StatsTabButton"));
        m_wTitlesTabButton = ButtonWidget.Cast(m_wRoot.FindAnyWidget("TitlesTabButton"));
        m_wAchievementsTabButton = ButtonWidget.Cast(m_wRoot.FindAnyWidget("AchievementsTabButton"));
        m_wRanksTabButton = ButtonWidget.Cast(m_wRoot.FindAnyWidget("RanksTabButton"));
        
        // Set up button handlers
        if (m_wStatsTabButton)
            m_wStatsTabButton.SetHandler(this);
        if (m_wTitlesTabButton)
            m_wTitlesTabButton.SetHandler(this);
        if (m_wAchievementsTabButton)
            m_wAchievementsTabButton.SetHandler(this);
        if (m_wRanksTabButton)
            m_wRanksTabButton.SetHandler(this);
        if (m_wSetTitleButton)
            m_wSetTitleButton.SetHandler(this);
    }
    
    //! Load player data
    protected void LoadPlayerData()
    {
        if (!m_ProgressionManager)
            return;
            
        // Get player ID
        string playerId = GetGame().GetPlayerManager().GetPlayerController(0).GetPlayerId();
        
        // Get player statistics
        m_PlayerStats = m_ProgressionManager.GetComponent().GetPlayerStats(playerId);
        
        // Update all displays
        UpdateStatisticsDisplay();
        UpdateTitlesDisplay();
        UpdateAchievementsDisplay();
        UpdateRanksDisplay();
    }
    
    //! Update statistics display
    protected void UpdateStatisticsDisplay()
    {
        if (!m_PlayerStats)
            return;
            
        // Update level displays
        if (m_wCivLevelText)
        {
            int civLevel = m_ProgressionManager.GetComponent().CalculateLevel(m_PlayerStats.GetCivExperience());
            m_wCivLevelText.SetText("Level " + civLevel);
        }
        
        if (m_wCopLevelText)
        {
            int copLevel = m_ProgressionManager.GetComponent().CalculateLevel(m_PlayerStats.GetCopExperience());
            m_wCopLevelText.SetText("Level " + copLevel);
        }
        
        if (m_wMedicLevelText)
        {
            int medicLevel = m_ProgressionManager.GetComponent().CalculateLevel(m_PlayerStats.GetMedicExperience());
            m_wMedicLevelText.SetText("Level " + medicLevel);
        }
        
        // Update experience displays
        if (m_wCivExpText)
        {
            int civExp = m_PlayerStats.GetCivExperience();
            int civLevel = m_ProgressionManager.GetComponent().CalculateLevel(civExp);
            int nextLevelExp = m_ProgressionManager.GetComponent().CalculateXPForNextLevel(civLevel);
            m_wCivExpText.SetText(civExp + " / " + nextLevelExp + " XP");
        }
        
        if (m_wCopExpText)
        {
            int copExp = m_PlayerStats.GetCopExperience();
            int copLevel = m_ProgressionManager.GetComponent().CalculateLevel(copExp);
            int nextLevelExp = m_ProgressionManager.GetComponent().CalculateXPForNextLevel(copLevel);
            m_wCopExpText.SetText(copExp + " / " + nextLevelExp + " XP");
        }
        
        if (m_wMedicExpText)
        {
            int medicExp = m_PlayerStats.GetMedicExperience();
            int medicLevel = m_ProgressionManager.GetComponent().CalculateLevel(medicExp);
            int nextLevelExp = m_ProgressionManager.GetComponent().CalculateXPForNextLevel(medicLevel);
            m_wMedicExpText.SetText(medicExp + " / " + nextLevelExp + " XP");
        }
        
        // Update statistics
        if (m_wKillsText)
            m_wKillsText.SetText(m_PlayerStats.GetKills().ToString());
        if (m_wDeathsText)
            m_wDeathsText.SetText(m_PlayerStats.GetDeaths().ToString());
        if (m_wArrestsText)
            m_wArrestsText.SetText(m_PlayerStats.GetArrestsMade().ToString());
        if (m_wRevivesText)
            m_wRevivesText.SetText(m_PlayerStats.GetRevives().ToString());
        if (m_wPlaytimeText)
            m_wPlaytimeText.SetText(FormatPlaytime(m_PlayerStats.GetPlaytime()));
        if (m_wMoneyEarnedText)
            m_wMoneyEarnedText.SetText("$" + FormatMoney(m_PlayerStats.GetTotalMoneyEarned()));
        if (m_wMoneySpentText)
            m_wMoneySpentText.SetText("$" + FormatMoney(m_PlayerStats.GetTotalMoneySpent()));
        if (m_wDistanceFootText)
            m_wDistanceFootText.SetText(FormatDistance(m_PlayerStats.GetDistanceFoot()));
        if (m_wDistanceVehicleText)
            m_wDistanceVehicleText.SetText(FormatDistance(m_PlayerStats.GetDistanceVehicle()));
    }
    
    //! Update titles display
    protected void UpdateTitlesDisplay()
    {
        if (!m_ProgressionManager || !m_wTitlesList)
            return;
            
        // Clear existing titles
        Widget child = m_wTitlesList.GetChildren();
        while (child)
        {
            Widget nextChild = child.GetSibling();
            child.RemoveFromHierarchy();
            child = nextChild;
        }
        
        // Get player ID
        string playerId = GetGame().GetPlayerManager().GetPlayerController(0).GetPlayerId();
        
        // Get player titles
        array<string> playerTitles = m_ProgressionManager.GetComponent().GetPlayerTitles(playerId);
        
        // Add title entries
        foreach (string title : playerTitles)
        {
            CreateTitleEntry(title);
        }
        
        // Update current title display
        if (m_wCurrentTitleText)
        {
            // TODO: Get current selected title from player data
            m_wCurrentTitleText.SetText("No Title Selected");
        }
    }
    
    //! Create title entry
    protected void CreateTitleEntry(string titleName)
    {
        // TODO: Create title entry widget
        // This would create a clickable title entry in the titles list
    }
    
    //! Update achievements display
    protected void UpdateAchievementsDisplay()
    {
        if (!m_wAchievementsList)
            return;
            
        // TODO: Implement achievements display
        // This would show completed and in-progress achievements
        
        if (m_wAchievementPointsText)
        {
            // TODO: Calculate total achievement points
            m_wAchievementPointsText.SetText("Achievement Points: 0");
        }
    }
    
    //! Update ranks display
    protected void UpdateRanksDisplay()
    {
        if (!m_PlayerStats || !m_ProgressionManager)
            return;
            
        // Update civilian rank
        if (m_wCivRankText)
        {
            int civLevel = m_ProgressionManager.GetComponent().CalculateLevel(m_PlayerStats.GetCivExperience());
            string civRank = m_ProgressionManager.GetComponent().GetRankName("civilian", civLevel);
            m_wCivRankText.SetText(civRank);
        }
        
        // Update police rank
        if (m_wCopRankText)
        {
            int copLevel = m_ProgressionManager.GetComponent().CalculateLevel(m_PlayerStats.GetCopExperience());
            string copRank = m_ProgressionManager.GetComponent().GetRankName("police", copLevel);
            m_wCopRankText.SetText(copRank);
        }
        
        // Update medical rank
        if (m_wMedicRankText)
        {
            int medicLevel = m_ProgressionManager.GetComponent().CalculateLevel(m_PlayerStats.GetMedicExperience());
            string medicRank = m_ProgressionManager.GetComponent().GetRankName("medical", medicLevel);
            m_wMedicRankText.SetText(medicRank);
        }
        
        // Update progress bars
        UpdateProgressBars();
    }
    
    //! Update progress bars
    protected void UpdateProgressBars()
    {
        if (!m_PlayerStats || !m_ProgressionManager)
            return;
            
        // Civilian progress
        if (m_wCivProgressBar)
        {
            int civExp = m_PlayerStats.GetCivExperience();
            int civLevel = m_ProgressionManager.GetComponent().CalculateLevel(civExp);
            int nextLevelExp = m_ProgressionManager.GetComponent().CalculateXPForNextLevel(civLevel);
            int currentLevelExp = m_ProgressionManager.GetComponent().CalculateXPForNextLevel(civLevel - 1);
            
            float progress = 0.0;
            if (nextLevelExp > currentLevelExp)
            {
                progress = (float)(civExp - currentLevelExp) / (float)(nextLevelExp - currentLevelExp);
            }
            m_wCivProgressBar.SetCurrent(progress);
        }
        
        // Police progress
        if (m_wCopProgressBar)
        {
            int copExp = m_PlayerStats.GetCopExperience();
            int copLevel = m_ProgressionManager.GetComponent().CalculateLevel(copExp);
            int nextLevelExp = m_ProgressionManager.GetComponent().CalculateXPForNextLevel(copLevel);
            int currentLevelExp = m_ProgressionManager.GetComponent().CalculateXPForNextLevel(copLevel - 1);
            
            float progress = 0.0;
            if (nextLevelExp > currentLevelExp)
            {
                progress = (float)(copExp - currentLevelExp) / (float)(nextLevelExp - currentLevelExp);
            }
            m_wCopProgressBar.SetCurrent(progress);
        }
        
        // Medical progress
        if (m_wMedicProgressBar)
        {
            int medicExp = m_PlayerStats.GetMedicExperience();
            int medicLevel = m_ProgressionManager.GetComponent().CalculateLevel(medicExp);
            int nextLevelExp = m_ProgressionManager.GetComponent().CalculateXPForNextLevel(medicLevel);
            int currentLevelExp = m_ProgressionManager.GetComponent().CalculateXPForNextLevel(medicLevel - 1);
            
            float progress = 0.0;
            if (nextLevelExp > currentLevelExp)
            {
                progress = (float)(medicExp - currentLevelExp) / (float)(nextLevelExp - currentLevelExp);
            }
            m_wMedicProgressBar.SetCurrent(progress);
        }
    }
    
    //! Show statistics tab
    protected void ShowStatsTab()
    {
        if (m_wStatsPanel) m_wStatsPanel.SetVisible(true);
        if (m_wTitlesPanel) m_wTitlesPanel.SetVisible(false);
        if (m_wAchievementsPanel) m_wAchievementsPanel.SetVisible(false);
        if (m_wRanksPanel) m_wRanksPanel.SetVisible(false);
    }
    
    //! Show titles tab
    protected void ShowTitlesTab()
    {
        if (m_wStatsPanel) m_wStatsPanel.SetVisible(false);
        if (m_wTitlesPanel) m_wTitlesPanel.SetVisible(true);
        if (m_wAchievementsPanel) m_wAchievementsPanel.SetVisible(false);
        if (m_wRanksPanel) m_wRanksPanel.SetVisible(false);
    }
    
    //! Show achievements tab
    protected void ShowAchievementsTab()
    {
        if (m_wStatsPanel) m_wStatsPanel.SetVisible(false);
        if (m_wTitlesPanel) m_wTitlesPanel.SetVisible(false);
        if (m_wAchievementsPanel) m_wAchievementsPanel.SetVisible(true);
        if (m_wRanksPanel) m_wRanksPanel.SetVisible(false);
    }
    
    //! Show ranks tab
    protected void ShowRanksTab()
    {
        if (m_wStatsPanel) m_wStatsPanel.SetVisible(false);
        if (m_wTitlesPanel) m_wTitlesPanel.SetVisible(false);
        if (m_wAchievementsPanel) m_wAchievementsPanel.SetVisible(false);
        if (m_wRanksPanel) m_wRanksPanel.SetVisible(true);
    }
    
    //! Handle button clicks
    override bool OnClick(Widget w, int x, int y, int button)
    {
        if (w == m_wStatsTabButton)
        {
            ShowStatsTab();
            return true;
        }
        else if (w == m_wTitlesTabButton)
        {
            ShowTitlesTab();
            return true;
        }
        else if (w == m_wAchievementsTabButton)
        {
            ShowAchievementsTab();
            return true;
        }
        else if (w == m_wRanksTabButton)
        {
            ShowRanksTab();
            return true;
        }
        else if (w == m_wSetTitleButton)
        {
            SetPlayerTitle();
            return true;
        }
        
        return super.OnClick(w, x, y, button);
    }
    
    //! Set player title
    protected void SetPlayerTitle()
    {
        if (m_sSelectedTitle == "")
            return;
            
        // TODO: Implement title setting
        Print("[EdenProgressionDialog] Setting player title to: " + m_sSelectedTitle);
    }
    
    //! Format playtime
    protected string FormatPlaytime(int minutes)
    {
        int hours = minutes / 60;
        int remainingMinutes = minutes % 60;
        return hours + "h " + remainingMinutes + "m";
    }
    
    //! Format money
    protected string FormatMoney(int amount)
    {
        // TODO: Implement proper money formatting with commas
        return amount.ToString();
    }
    
    //! Format distance
    protected string FormatDistance(int meters)
    {
        if (meters >= 1000)
        {
            float km = (float)meters / 1000.0;
            return km.ToString() + " km";
        }
        return meters.ToString() + " m";
    }
}
