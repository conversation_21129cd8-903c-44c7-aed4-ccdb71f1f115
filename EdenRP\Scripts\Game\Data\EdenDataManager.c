//! Eden Data Manager - Handles all data persistence for the Eden Reforger framework
//! Converts MySQL-based storage to JSON file-based storage system

class EdenDataManager
{
    protected static EdenDataManager s_Instance;
    protected ref map<string, ref EdenPlayerData> m_PlayerDataCache;
    protected ref map<int, ref EdenVehicleData> m_VehicleDataCache;
    protected ref map<int, ref EdenGangData> m_GangDataCache;
    protected ref map<int, ref EdenHouseData> m_HouseDataCache;
    protected ref map<string, ref EdenMarketData> m_MarketDataCache;
    
    protected string m_DataPath = "$profile:EdenData/";
    protected bool m_IsInitialized = false;
    
    //! Get singleton instance
    static EdenDataManager GetInstance()
    {
        if (!s_Instance)
            s_Instance = new EdenDataManager();
        return s_Instance;
    }
    
    //! Initialize the data manager
    void Initialize()
    {
        if (m_IsInitialized)
            return;
            
        Print("[EdenDataManager] Initializing data storage system...");
        
        // Initialize data caches
        m_PlayerDataCache = new map<string, ref EdenPlayerData>();
        m_VehicleDataCache = new map<int, ref EdenVehicleData>();
        m_GangDataCache = new map<int, ref EdenGangData>();
        m_HouseDataCache = new map<int, ref EdenHouseData>();
        m_MarketDataCache = new map<string, ref EdenMarketData>();
        
        // Create data directories
        CreateDataDirectories();
        
        // Load existing data
        LoadAllData();
        
        m_IsInitialized = true;
        Print("[EdenDataManager] Data manager initialized successfully");
    }
    
    //! Create necessary data directories
    protected void CreateDataDirectories()
    {
        FileIO.MakeDirectory(m_DataPath);
        FileIO.MakeDirectory(m_DataPath + "Players/");
        FileIO.MakeDirectory(m_DataPath + "Vehicles/");
        FileIO.MakeDirectory(m_DataPath + "Gangs/");
        FileIO.MakeDirectory(m_DataPath + "Houses/");
        FileIO.MakeDirectory(m_DataPath + "Market/");
        FileIO.MakeDirectory(m_DataPath + "Config/");
        FileIO.MakeDirectory(m_DataPath + "Logs/");
    }
    
    //! Load all data from storage
    protected void LoadAllData()
    {
        LoadPlayerData();
        LoadVehicleData();
        LoadGangData();
        LoadHouseData();
        LoadMarketData();
    }
    
    //! Save all data to storage
    void SaveAllData()
    {
        SavePlayerData();
        SaveVehicleData();
        SaveGangData();
        SaveHouseData();
        SaveMarketData();
    }
    
    //! Get player data by player ID
    EdenPlayerData GetPlayerData(string playerId)
    {
        if (!m_PlayerDataCache.Contains(playerId))
        {
            // Try to load from file
            EdenPlayerData playerData = LoadPlayerDataFromFile(playerId);
            if (!playerData)
            {
                // Create new player data
                playerData = new EdenPlayerData();
                playerData.SetPlayerId(playerId);
                playerData.InitializeDefaults();
            }
            m_PlayerDataCache.Set(playerId, playerData);
        }
        
        return m_PlayerDataCache.Get(playerId);
    }
    
    //! Save player data
    void SavePlayerData(string playerId = "")
    {
        if (playerId != "")
        {
            // Save specific player
            EdenPlayerData playerData = m_PlayerDataCache.Get(playerId);
            if (playerData)
                SavePlayerDataToFile(playerData);
        }
        else
        {
            // Save all players
            foreach (string pid, EdenPlayerData data : m_PlayerDataCache)
            {
                SavePlayerDataToFile(data);
            }
        }
    }
    
    //! Load player data from file
    protected EdenPlayerData LoadPlayerDataFromFile(string playerId)
    {
        string filePath = m_DataPath + "Players/" + playerId + ".json";
        
        if (!FileIO.FileExists(filePath))
            return null;
            
        FileHandle file = FileIO.OpenFile(filePath, FileMode.READ);
        if (!file)
            return null;
            
        string jsonContent = "";
        string line;
        while (FGets(file, line) >= 0)
        {
            jsonContent += line;
        }
        FClose(file);
        
        EdenPlayerData playerData = new EdenPlayerData();
        if (playerData.LoadFromJson(jsonContent))
            return playerData;
            
        return null;
    }
    
    //! Save player data to file
    protected void SavePlayerDataToFile(EdenPlayerData playerData)
    {
        if (!playerData)
            return;
            
        string filePath = m_DataPath + "Players/" + playerData.GetPlayerId() + ".json";
        string jsonContent = playerData.SaveToJson();
        
        FileHandle file = FileIO.OpenFile(filePath, FileMode.WRITE);
        if (!file)
        {
            Print("[EdenDataManager] ERROR: Could not save player data for " + playerData.GetPlayerId());
            return;
        }
        
        FPuts(file, jsonContent);
        FClose(file);
    }
    
    //! Get vehicle data by vehicle ID
    EdenVehicleData GetVehicleData(int vehicleId)
    {
        return m_VehicleDataCache.Get(vehicleId);
    }
    
    //! Add new vehicle data
    int AddVehicleData(EdenVehicleData vehicleData)
    {
        int newId = GetNextVehicleId();
        vehicleData.SetId(newId);
        m_VehicleDataCache.Set(newId, vehicleData);
        SaveVehicleDataToFile(vehicleData);
        return newId;
    }
    
    //! Get gang data by gang ID
    EdenGangData GetGangData(int gangId)
    {
        return m_GangDataCache.Get(gangId);
    }
    
    //! Get house data by house ID
    EdenHouseData GetHouseData(int houseId)
    {
        return m_HouseDataCache.Get(houseId);
    }
    
    //! Get market data by item name
    EdenMarketData GetMarketData(string itemName)
    {
        return m_MarketDataCache.Get(itemName);
    }
    
    //! Load vehicle data (placeholder - implement similar to player data)
    protected void LoadVehicleData()
    {
        // Implementation similar to LoadPlayerData
        Print("[EdenDataManager] Loading vehicle data...");
    }
    
    //! Save vehicle data (placeholder - implement similar to player data)
    protected void SaveVehicleData()
    {
        // Implementation similar to SavePlayerData
        Print("[EdenDataManager] Saving vehicle data...");
    }
    
    //! Save vehicle data to file
    protected void SaveVehicleDataToFile(EdenVehicleData vehicleData)
    {
        // Implementation similar to SavePlayerDataToFile
    }
    
    //! Load gang data (placeholder)
    protected void LoadGangData()
    {
        Print("[EdenDataManager] Loading gang data...");
    }
    
    //! Save gang data (placeholder)
    protected void SaveGangData()
    {
        Print("[EdenDataManager] Saving gang data...");
    }
    
    //! Load house data (placeholder)
    protected void LoadHouseData()
    {
        Print("[EdenDataManager] Loading house data...");
    }
    
    //! Save house data (placeholder)
    protected void SaveHouseData()
    {
        Print("[EdenDataManager] Saving house data...");
    }
    
    //! Load market data (placeholder)
    protected void LoadMarketData()
    {
        Print("[EdenDataManager] Loading market data...");
    }
    
    //! Save market data (placeholder)
    protected void SaveMarketData()
    {
        Print("[EdenDataManager] Saving market data...");
    }
    
    //! Get next available vehicle ID
    protected int GetNextVehicleId()
    {
        int maxId = 0;
        foreach (int id, EdenVehicleData data : m_VehicleDataCache)
        {
            if (id > maxId)
                maxId = id;
        }
        return maxId + 1;
    }
    
    //! Cleanup - called on server shutdown
    void Cleanup()
    {
        Print("[EdenDataManager] Performing final data save...");
        SaveAllData();
        Print("[EdenDataManager] Data manager cleanup complete");
    }
}
