---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-37-31\console.log started at 2025-07-27 20:37:31 (2025-07-28 00:37:31 UTC)

20:37:31.455 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-37-31' to filesystem under name logs
20:37:31.456 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
20:37:31.458 ENGINE       : Initializing engine, version 184702
20:37:31.458 ENGINE       : CLI Params: -config ..\..\..\server.json -profile ..\..\..\saves -addonsDir addons -logLevel 3 -logStats 
20:37:31.461 ENGINE       : Addon dirs:
20:37:31.461  ENGINE       : dir: 'addons'
20:37:31.461  ENGINE       : dir: './addons'
20:37:31.461  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
20:37:31.461 ENGINE       : Available addons:
20:37:31.462  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
20:37:31.462  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
20:37:31.521 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
20:37:31.523 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
20:37:31.523 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
20:37:31.669 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
20:37:31.671 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
20:37:31.671 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-37-31/resourceDatabase.rdb)
20:37:31.774 ENGINE       : GameProject load
20:37:31.775  ENGINE       : Loaded addons:
20:37:31.775   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
20:37:31.775   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
20:37:31.779 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
20:37:31.779 ENGINE       : No Enfusion settings file loaded. Continuing with defaults
20:37:31.780 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
20:37:31.780 RENDER       : Basic screen setup:
20:37:31.780 RENDER       :   Back buffer width x height: 0x0
20:37:31.780 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
20:37:31.781 RENDER       :   MSAA                      : none
20:37:31.781 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
20:37:31.781 RENDER       : Detected output devices:
20:37:31.781 RENDER       :  * 1 device/s connected to Dummy:
20:37:31.781 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
20:37:31.781 RENDER       :   Concurrent frames limit 2
20:37:31.781 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
20:37:31.840 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
20:37:31.844 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
20:37:31.844 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
20:37:31.844 PROFILING    : Settings changed took: 0.019900 ms
20:37:31.848 ENGINE       : Initializing inputs.
20:37:31.856 NETWORK      : Initializing networking.
20:37:31.856 SCRIPT       : SCRIPT       : Initializing scripts
20:37:31.856  SCRIPT       : SCRIPT       : ScriptProjectManager init
20:37:31.857  PROFILING    : ScriptProjectManager init took: 0.031500 ms
20:37:31.874 PROFILING    : Initializing scripts took: 18.308700 ms
20:37:31.874 ENGINE       : Enfusion engine successfully created.
20:37:31.881 GUI          : Using default language (en_us)
20:37:31.881 GUI          : Loading 'en_us' localization file.
20:37:31.881 INIT         : INIT         : Loading StringTable
20:37:31.905 PROFILING    : Loading StringTable took: 23.899400 ms
20:37:31.905 SCRIPT       : SCRIPT       : Compiling GameLib scripts
20:37:31.930  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
20:37:31.933 PROFILING    : Compiling GameLib scripts took: 27.668500 ms
20:37:31.933 SCRIPT       : SCRIPT       : Compiling Game scripts
20:37:32.457 SCRIPT       : Compiling Game scripts
20:37:32.458  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
20:37:32.495  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
20:37:32.701  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:37:32.702  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:37:32.702  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:37:32.718  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
20:37:32.748  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
20:37:32.824  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
20:37:32.882  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:37:32.924  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
20:37:32.924  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
20:37:32.991  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
20:37:32.992  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:37:32.992  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:37:32.992  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:37:32.993  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:37:32.993  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:37:33.028  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:37:33.028  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:37:33.029  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:37:33.029  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:37:33.029  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:37:33.029  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:37:33.043  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:37:33.054  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:37:33.054  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:37:33.054  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:37:33.078  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
20:37:33.079  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
20:37:33.083  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:37:33.083  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:37:33.084  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:37:33.085  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
20:37:33.085  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:37:33.130  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
20:37:33.872 PROFILING    : Compiling Game scripts took: 1939.196400 ms
20:37:33.874 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
20:37:33.890 BACKEND      : Loading dedicated server config.
20:37:33.890  BACKEND      : Server config loaded.
20:37:33.890 BACKEND      : Loading dedicated server config.
20:37:33.890  BACKEND      : JSON Schema Validation:
20:37:33.890   BACKEND      : additionalProperties error:
20:37:33.890    BACKEND   (E): Param "#/gameHostBindPort" is not allowed and must be removed.
20:37:33.890   BACKEND   (E): JSON is invalid!
20:37:33.890  BACKEND   (E): There are errors in server config!
20:37:33.891 BACKEND   (E): Unable to continue with a broken DS config! Shutdown!
20:37:33.891 ENGINE    (E): Error while initializing game.
20:37:34.066 ENGINE    (E): Unable to initialize the game
20:37:34.085 ENGINE       : Game destroyed.
20:37:34.100 RPL          : Pip::Destroy
20:37:34.106 RESOURCES (E): ==== Resource leaks ====
20:37:34.106 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
