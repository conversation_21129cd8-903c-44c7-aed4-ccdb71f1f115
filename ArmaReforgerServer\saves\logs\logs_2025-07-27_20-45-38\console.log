---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-45-38\console.log started at 2025-07-27 20:45:38 (2025-07-28 00:45:38 UTC)

20:45:38.160 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-45-38' to filesystem under name logs
20:45:38.160 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
20:45:38.162 ENGINE       : Initializing engine, version 184702
20:45:38.162 ENGINE       : CLI Params: -config C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\server.json -profile C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves -addonsDir addons -logLevel 3 -logStats 
20:45:38.165 ENGINE       : Addon dirs:
20:45:38.165  ENGINE       : dir: 'addons'
20:45:38.165  ENGINE       : dir: './addons'
20:45:38.165  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
20:45:38.165 ENGINE       : Available addons:
20:45:38.165  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
20:45:38.165  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
20:45:38.218 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
20:45:38.220 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
20:45:38.220 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
20:45:38.362 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
20:45:38.364 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
20:45:38.364 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-45-38/resourceDatabase.rdb)
20:45:38.471 ENGINE       : GameProject load
20:45:38.472  ENGINE       : Loaded addons:
20:45:38.472   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
20:45:38.472   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
20:45:38.473 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
20:45:38.474 ENGINE       : No Enfusion settings file loaded. Continuing with defaults
20:45:38.474 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
20:45:38.474 RENDER       : Basic screen setup:
20:45:38.474 RENDER       :   Back buffer width x height: 0x0
20:45:38.475 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
20:45:38.475 RENDER       :   MSAA                      : none
20:45:38.475 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
20:45:38.475 RENDER       : Detected output devices:
20:45:38.475 RENDER       :  * 1 device/s connected to Dummy:
20:45:38.475 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
20:45:38.475 RENDER       :   Concurrent frames limit 2
20:45:38.475 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
20:45:38.530 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
20:45:38.535 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
20:45:38.536 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
20:45:38.536 PROFILING    : Settings changed took: 0.022600 ms
20:45:38.539 ENGINE       : Initializing inputs.
20:45:38.545 NETWORK      : Initializing networking.
20:45:38.545 SCRIPT       : SCRIPT       : Initializing scripts
20:45:38.546  SCRIPT       : SCRIPT       : ScriptProjectManager init
20:45:38.546  PROFILING    : ScriptProjectManager init took: 0.024000 ms
20:45:38.562 PROFILING    : Initializing scripts took: 16.315400 ms
20:45:38.562 ENGINE       : Enfusion engine successfully created.
20:45:38.569 GUI          : Using default language (en_us)
20:45:38.569 GUI          : Loading 'en_us' localization file.
20:45:38.569 INIT         : INIT         : Loading StringTable
20:45:38.592 PROFILING    : Loading StringTable took: 23.135300 ms
20:45:38.592 SCRIPT       : SCRIPT       : Compiling GameLib scripts
20:45:38.614  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
20:45:38.618 PROFILING    : Compiling GameLib scripts took: 24.815400 ms
20:45:38.618 SCRIPT       : SCRIPT       : Compiling Game scripts
20:45:39.104 SCRIPT       : Compiling Game scripts
20:45:39.105  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
20:45:39.141  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
20:45:39.340  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:45:39.341  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:45:39.341  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:45:39.358  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
20:45:39.388  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
20:45:39.463  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
20:45:39.520  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:45:39.560  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
20:45:39.560  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
20:45:39.628  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
20:45:39.629  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:45:39.629  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:45:39.630  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:45:39.630  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:45:39.630  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:45:39.664  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:45:39.664  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:45:39.665  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:45:39.665  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:45:39.665  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:45:39.665  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:45:39.679  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:45:39.690  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:45:39.690  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:45:39.690  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:45:39.715  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
20:45:39.715  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
20:45:39.718  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:45:39.719  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:45:39.720  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:45:39.720  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
20:45:39.720  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:45:39.769  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
20:45:40.471 PROFILING    : Compiling Game scripts took: 1852.864700 ms
20:45:40.473 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
20:45:40.488 BACKEND      : Loading dedicated server config.
20:45:40.488  BACKEND      : Server config loaded.
20:45:40.488 BACKEND      : Loading dedicated server config.
20:45:40.488  BACKEND      : JSON Schema Validation:
20:45:40.488   BACKEND      : pattern error:
20:45:40.488    BACKEND   (E): Value of "#/game/scenarioId" does not match the required pattern. Value: "{F8B2E3A1-4C5D-6E7F-8A9B-0C1D2E3F4A5B}Missions/EdenRP.conf"
20:45:40.488    BACKEND   (E): RegEx Pattern: "^\{[0-9A-F]{16}\}[a-zA-Z0-9_./ -]+$"
20:45:40.488    BACKEND   (E): Pattern Description: "Param must start with scenarioId enclosed in brackets: {ECC61978EDCC2B5A}"
20:45:40.489    BACKEND   (E): Reference in schema: "#/properties/game/properties/scenarioId"
20:45:40.489   BACKEND   (E): JSON is invalid!
20:45:40.489  BACKEND   (E): There are errors in server config!
20:45:40.489 BACKEND   (E): Unable to continue with a broken DS config! Shutdown!
20:45:40.489 ENGINE    (E): Error while initializing game.
20:45:40.653 ENGINE    (E): Unable to initialize the game
20:45:40.672 ENGINE       : Game destroyed.
20:45:40.688 RPL          : Pip::Destroy
20:45:40.693 RESOURCES (E): ==== Resource leaks ====
20:45:40.693 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
