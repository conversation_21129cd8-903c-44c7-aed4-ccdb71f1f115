//! Eden Vehicle Modification System - Handles vehicle modifications, insurance, and customization
//! Converted from original mod shop systems

class EdenVehicleModificationSystem
{
    protected ref map<string, ref EdenVehicleModConfig> m_ModConfigurations;
    protected ref map<string, ref EdenVehicleColorConfig> m_ColorConfigurations;
    protected ref array<ref EdenModShopLocation> m_ModShopLocations;
    
    // Modification prices
    protected int m_TurboPrice;
    protected int m_StorageUpgradePrice;
    protected int m_SecurityUpgradePrice;
    protected int m_BasicInsurancePrice;
    protected int m_FullInsurancePrice;
    protected int m_PaintJobBasePrice;
    
    void EdenVehicleModificationSystem()
    {
        m_ModConfigurations = new map<string, ref EdenVehicleModConfig>();
        m_ColorConfigurations = new map<string, ref EdenVehicleColorConfig>();
        m_ModShopLocations = new array<ref EdenModShopLocation>();
        
        m_TurboPrice = 50000;
        m_StorageUpgradePrice = 35000;
        m_SecurityUpgradePrice = 25000;
        m_BasicInsurancePrice = 15000;
        m_FullInsurancePrice = 35000;
        m_PaintJobBasePrice = 1000;
        
        InitializeModificationSystem();
    }
    
    //! Initialize modification system
    void Initialize()
    {
        Print("[EdenVehicleModificationSystem] Initializing vehicle modification system...");
        
        SetupModConfigurations();
        SetupColorConfigurations();
        SetupModShopLocations();
        
        Print("[EdenVehicleModificationSystem] Vehicle modification system initialized");
    }
    
    //! Initialize modification system configuration
    protected void InitializeModificationSystem()
    {
        Print("[EdenVehicleModificationSystem] Modification system configuration initialized");
    }
    
    //! Setup modification configurations
    protected void SetupModConfigurations()
    {
        // Turbo modifications
        SetupModConfig("turbo_level_1", "Turbo Level 1", 0, 25000, 1.15);
        SetupModConfig("turbo_level_2", "Turbo Level 2", 1, 50000, 1.30);
        SetupModConfig("turbo_level_3", "Turbo Level 3", 2, 75000, 1.45);
        SetupModConfig("turbo_level_4", "Turbo Level 4", 3, 100000, 1.60);
        
        // Storage modifications
        SetupModConfig("storage_level_1", "Storage Level 1", 0, 15000, 1.25);
        SetupModConfig("storage_level_2", "Storage Level 2", 1, 30000, 1.50);
        SetupModConfig("storage_level_3", "Storage Level 3", 2, 45000, 1.75);
        SetupModConfig("storage_level_4", "Storage Level 4", 3, 60000, 2.00);
        
        // Security modifications
        SetupModConfig("security_level_1", "Security Level 1", 0, 10000, 1.0);
        SetupModConfig("security_level_2", "Security Level 2", 1, 20000, 1.0);
        SetupModConfig("security_level_3", "Security Level 3", 2, 30000, 1.0);
        SetupModConfig("security_level_4", "Security Level 4", 3, 40000, 1.0);
        
        Print(string.Format("[EdenVehicleModificationSystem] Set up %1 modification configurations", m_ModConfigurations.Count()));
    }
    
    //! Setup individual mod configuration
    protected void SetupModConfig(string modId, string modName, int level, int price, float multiplier)
    {
        EdenVehicleModConfig config = new EdenVehicleModConfig();
        config.SetModId(modId);
        config.SetModName(modName);
        config.SetLevel(level);
        config.SetPrice(price);
        config.SetMultiplier(multiplier);
        config.SetIsAvailable(true);
        
        m_ModConfigurations.Set(modId, config);
    }
    
    //! Setup color configurations
    protected void SetupColorConfigurations()
    {
        // Standard colors
        SetupColorConfig("default", "Default", "[\"Default\",0]", 0);
        SetupColorConfig("red", "Red", "[\"Red\",1]", 1000);
        SetupColorConfig("blue", "Blue", "[\"Blue\",2]", 1000);
        SetupColorConfig("green", "Green", "[\"Green\",3]", 1000);
        SetupColorConfig("yellow", "Yellow", "[\"Yellow\",4]", 1000);
        SetupColorConfig("orange", "Orange", "[\"Orange\",5]", 1500);
        SetupColorConfig("purple", "Purple", "[\"Purple\",6]", 1500);
        SetupColorConfig("black", "Black", "[\"Black\",7]", 2000);
        SetupColorConfig("white", "White", "[\"White\",8]", 2000);
        
        // Special colors
        SetupColorConfig("chrome", "Chrome", "[\"Chrome\",9]", 5000);
        SetupColorConfig("gold", "Gold", "[\"Gold\",10]", 10000);
        SetupColorConfig("rainbow", "Rainbow", "[\"Rainbow\",11]", 15000);
        
        Print(string.Format("[EdenVehicleModificationSystem] Set up %1 color configurations", m_ColorConfigurations.Count()));
    }
    
    //! Setup individual color configuration
    protected void SetupColorConfig(string colorId, string colorName, string colorData, int price)
    {
        EdenVehicleColorConfig config = new EdenVehicleColorConfig();
        config.SetColorId(colorId);
        config.SetColorName(colorName);
        config.SetColorData(colorData);
        config.SetPrice(price);
        config.SetIsAvailable(true);
        
        m_ColorConfigurations.Set(colorId, config);
    }
    
    //! Setup mod shop locations
    protected void SetupModShopLocations()
    {
        SetupModShop("kavala_mod_shop", "Kavala Mod Shop", "0 0 0", true);
        SetupModShop("athira_mod_shop", "Athira Mod Shop", "0 0 0", true);
        
        Print(string.Format("[EdenVehicleModificationSystem] Set up %1 mod shop locations", m_ModShopLocations.Count()));
    }
    
    //! Setup individual mod shop
    protected void SetupModShop(string shopId, string shopName, string position, bool isActive)
    {
        EdenModShopLocation shop = new EdenModShopLocation();
        shop.SetShopId(shopId);
        shop.SetShopName(shopName);
        shop.SetPosition(position.ToVector());
        shop.SetIsActive(isActive);
        shop.SetInteractionRadius(15.0);
        
        m_ModShopLocations.Insert(shop);
    }
    
    //! Apply vehicle modifications
    bool ApplyVehicleModifications(string playerId, IEntity vehicleEntity, string shopId, EdenVehicleModificationRequest modRequest)
    {
        if (playerId == "" || !vehicleEntity || shopId == "" || !modRequest)
            return false;
            
        // Check if shop exists and is active
        EdenModShopLocation shop = GetModShopById(shopId);
        if (!shop || !shop.IsActive())
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player is in range
        if (vector.Distance(playerEntity.GetOrigin(), shop.GetPosition()) > shop.GetInteractionRadius())
            return false;
            
        // Get vehicle data
        EdenVehicleData vehicleData = GetVehicleDataFromEntity(vehicleEntity);
        if (!vehicleData)
            return false;
            
        // Check ownership
        if (vehicleData.GetOwnerId() != playerId)
            return false;
            
        // Calculate total cost
        int totalCost = CalculateModificationCost(modRequest);
        
        // Check if player has enough money
        int playerMoney = playerComp.GetCash() + playerComp.GetBankAccount();
        if (totalCost > playerMoney)
            return false;
            
        // Process payment
        if (playerComp.GetCash() >= totalCost)
        {
            playerComp.RemoveCash(totalCost);
        }
        else
        {
            int remainingCost = totalCost - playerComp.GetCash();
            playerComp.RemoveCash(playerComp.GetCash());
            playerComp.RemoveBankMoney(remainingCost);
        }
        
        // Apply modifications
        ApplyModificationsToVehicle(vehicleEntity, vehicleData, modRequest);
        
        // Update vehicle data
        UpdateVehicleModificationData(vehicleData, modRequest);
        
        // Save to database
        UpdateVehicleInDatabase(vehicleData);
        
        // Repair vehicle if needed
        RepairVehicle(vehicleEntity);
        
        Print(string.Format("[EdenVehicleModificationSystem] %1 applied modifications for $%2", playerId, totalCost));
        return true;
    }
    
    //! Calculate modification cost
    protected int CalculateModificationCost(EdenVehicleModificationRequest modRequest)
    {
        int totalCost = 0;
        
        // Turbo cost
        if (modRequest.GetTurboLevel() > 0)
        {
            totalCost += m_TurboPrice * modRequest.GetTurboLevel();
        }
        
        // Storage cost
        if (modRequest.GetStorageLevel() > 0)
        {
            totalCost += m_StorageUpgradePrice * modRequest.GetStorageLevel();
        }
        
        // Security cost
        if (modRequest.GetSecurityLevel() > 0)
        {
            totalCost += m_SecurityUpgradePrice * modRequest.GetSecurityLevel();
        }
        
        // Insurance cost
        switch (modRequest.GetInsuranceLevel())
        {
            case 1: totalCost += m_BasicInsurancePrice; break;
            case 2: totalCost += m_FullInsurancePrice; break;
        }
        
        // Paint job cost
        string colorId = modRequest.GetColorId();
        if (colorId != "" && m_ColorConfigurations.Contains(colorId))
        {
            EdenVehicleColorConfig colorConfig = m_ColorConfigurations.Get(colorId);
            totalCost += colorConfig.GetPrice();
        }
        
        return totalCost;
    }
    
    //! Apply modifications to vehicle entity
    protected void ApplyModificationsToVehicle(IEntity vehicleEntity, EdenVehicleData vehicleData, EdenVehicleModificationRequest modRequest)
    {
        // Apply turbo modification
        if (modRequest.GetTurboLevel() > 0)
        {
            ApplyTurboModification(vehicleEntity, modRequest.GetTurboLevel());
        }
        
        // Apply storage modification
        if (modRequest.GetStorageLevel() > 0)
        {
            ApplyStorageModification(vehicleEntity, modRequest.GetStorageLevel());
        }
        
        // Apply security modification
        if (modRequest.GetSecurityLevel() > 0)
        {
            ApplySecurityModification(vehicleEntity, modRequest.GetSecurityLevel());
        }
        
        // Apply color modification
        string colorId = modRequest.GetColorId();
        if (colorId != "" && m_ColorConfigurations.Contains(colorId))
        {
            EdenVehicleColorConfig colorConfig = m_ColorConfigurations.Get(colorId);
            ApplyColorModification(vehicleEntity, colorConfig.GetColorData());
        }
    }
    
    //! Apply turbo modification
    protected void ApplyTurboModification(IEntity vehicleEntity, int turboLevel)
    {
        // Implementation would modify vehicle performance
        // This would involve adjusting engine power, acceleration, etc.
        Print(string.Format("[EdenVehicleModificationSystem] Applied turbo level %1", turboLevel));
    }
    
    //! Apply storage modification
    protected void ApplyStorageModification(IEntity vehicleEntity, int storageLevel)
    {
        // Implementation would modify vehicle storage capacity
        // This would involve adjusting trunk/inventory space
        Print(string.Format("[EdenVehicleModificationSystem] Applied storage level %1", storageLevel));
    }
    
    //! Apply security modification
    protected void ApplySecurityModification(IEntity vehicleEntity, int securityLevel)
    {
        // Implementation would modify vehicle security
        // This would involve lockpicking difficulty, alarm systems, etc.
        Print(string.Format("[EdenVehicleModificationSystem] Applied security level %1", securityLevel));
    }
    
    //! Apply color modification
    protected void ApplyColorModification(IEntity vehicleEntity, string colorData)
    {
        // Implementation would change vehicle color/skin
        // This would involve material/texture changes
        Print(string.Format("[EdenVehicleModificationSystem] Applied color: %1", colorData));
    }
    
    //! Update vehicle modification data
    protected void UpdateVehicleModificationData(EdenVehicleData vehicleData, EdenVehicleModificationRequest modRequest)
    {
        // Create modifications array [turbo, storage, security, 0, 0, 0, 0, 0]
        string modifications = string.Format("[%1,%2,%3,0,0,0,0,0]", 
            modRequest.GetTurboLevel(),
            modRequest.GetStorageLevel(),
            modRequest.GetSecurityLevel());
            
        vehicleData.SetModifications(modifications);
        vehicleData.SetInsuranceLevel(modRequest.GetInsuranceLevel());
        
        // Update color if specified
        string colorId = modRequest.GetColorId();
        if (colorId != "" && m_ColorConfigurations.Contains(colorId))
        {
            EdenVehicleColorConfig colorConfig = m_ColorConfigurations.Get(colorId);
            vehicleData.SetColor(colorConfig.GetColorData());
        }
    }
    
    //! Repair vehicle
    protected void RepairVehicle(IEntity vehicleEntity)
    {
        // Implementation would repair vehicle damage
        // This would involve resetting damage states, fixing components, etc.
        Print("[EdenVehicleModificationSystem] Vehicle repaired");
    }
    
    //! Get available modifications for vehicle
    EdenVehicleModificationOptions GetAvailableModifications(string vehicleClass)
    {
        EdenVehicleModificationOptions options = new EdenVehicleModificationOptions();
        
        // Set available turbo levels
        options.SetMaxTurboLevel(4);
        options.SetMaxStorageLevel(4);
        options.SetMaxSecurityLevel(4);
        options.SetInsuranceAvailable(true);
        
        // Set available colors based on vehicle class
        array<string> availableColors = GetAvailableColorsForVehicle(vehicleClass);
        options.SetAvailableColors(availableColors);
        
        return options;
    }
    
    //! Get available colors for vehicle
    protected array<string> GetAvailableColorsForVehicle(string vehicleClass)
    {
        array<string> availableColors = {};
        
        // All vehicles get basic colors
        availableColors.Insert("default");
        availableColors.Insert("red");
        availableColors.Insert("blue");
        availableColors.Insert("green");
        availableColors.Insert("yellow");
        availableColors.Insert("black");
        availableColors.Insert("white");
        
        // Special vehicles get premium colors
        if (IsSpecialVehicle(vehicleClass))
        {
            availableColors.Insert("chrome");
            availableColors.Insert("gold");
            availableColors.Insert("rainbow");
        }
        
        return availableColors;
    }
    
    //! Check if vehicle is special (gets premium colors)
    protected bool IsSpecialVehicle(string vehicleClass)
    {
        array<string> specialVehicles = {
            "B_Heli_Light_01_F",
            "O_Heli_Light_02_unarmed_F",
            "B_MRAP_01_F",
            "I_MRAP_03_F"
        };
        
        return specialVehicles.Find(vehicleClass) != -1;
    }
    
    //! Get mod shop by ID
    protected EdenModShopLocation GetModShopById(string shopId)
    {
        foreach (EdenModShopLocation shop : m_ModShopLocations)
        {
            if (shop.GetShopId() == shopId)
                return shop;
        }
        return null;
    }
    
    //! Get modification prices
    EdenModificationPrices GetModificationPrices()
    {
        EdenModificationPrices prices = new EdenModificationPrices();
        prices.SetTurboPrice(m_TurboPrice);
        prices.SetStoragePrice(m_StorageUpgradePrice);
        prices.SetSecurityPrice(m_SecurityUpgradePrice);
        prices.SetBasicInsurancePrice(m_BasicInsurancePrice);
        prices.SetFullInsurancePrice(m_FullInsurancePrice);
        prices.SetPaintJobBasePrice(m_PaintJobBasePrice);
        
        return prices;
    }
    
    //! Get color configurations
    map<string, ref EdenVehicleColorConfig> GetColorConfigurations()
    {
        return m_ColorConfigurations;
    }
    
    //! Get mod shop locations
    array<ref EdenModShopLocation> GetModShopLocations()
    {
        return m_ModShopLocations;
    }
    
    //! Set modification prices
    void SetModificationPrices(int turboPrice, int storagePrice, int securityPrice, int basicInsurance, int fullInsurance)
    {
        m_TurboPrice = turboPrice;
        m_StorageUpgradePrice = storagePrice;
        m_SecurityUpgradePrice = securityPrice;
        m_BasicInsurancePrice = basicInsurance;
        m_FullInsurancePrice = fullInsurance;
    }
    
    //! Placeholder methods for actual implementation
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    protected EdenVehicleData GetVehicleDataFromEntity(IEntity vehicleEntity) { return null; }
    protected void UpdateVehicleInDatabase(EdenVehicleData vehicleData) { }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenVehicleModificationSystem] Cleaning up vehicle modification system...");
        
        m_ModConfigurations.Clear();
        m_ColorConfigurations.Clear();
        m_ModShopLocations.Clear();
    }
}
