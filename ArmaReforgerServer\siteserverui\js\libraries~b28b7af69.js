(self.webpackChunksiteserverui=self.webpackChunksiteserverui||[]).push([[8997],{1735:(e,t,r)=>{e.exports=r(8681)},1238:(e,t,r)=>{"use strict";var n=r(1362),o=r(5072),i=r(4438),a=r(3724),s=r(7429),l=r(3862),u=r(4508),g=r(8466),c=r(5407),p=r(9461),d=r(7962);e.exports=function(e){return new Promise((function(t,r){var f,h=e.data,y=e.headers,b=e.responseType;function m(){e.cancelToken&&e.cancelToken.unsubscribe(f),e.signal&&e.signal.removeEventListener("abort",f)}n.isFormData(h)&&n.isStandardBrowserEnv()&&delete y["Content-Type"];var _=new XMLHttpRequest;if(e.auth){var v=e.auth.username||"",S=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";y.Authorization="Basic "+btoa(v+":"+S)}var E=s(e.baseURL,e.url);function T(){if(_){var n="getAllResponseHeaders"in _?l(_.getAllResponseHeaders()):null,i={data:b&&"text"!==b&&"json"!==b?_.response:_.responseText,status:_.status,statusText:_.statusText,headers:n,config:e,request:_};o((function(e){t(e),m()}),(function(e){r(e),m()}),i),_=null}}if(_.open(e.method.toUpperCase(),a(E,e.params,e.paramsSerializer),!0),_.timeout=e.timeout,"onloadend"in _?_.onloadend=T:_.onreadystatechange=function(){_&&4===_.readyState&&(0!==_.status||_.responseURL&&0===_.responseURL.indexOf("file:"))&&setTimeout(T)},_.onabort=function(){_&&(r(new c("Request aborted",c.ECONNABORTED,e,_)),_=null)},_.onerror=function(){r(new c("Network Error",c.ERR_NETWORK,e,_,_)),_=null},_.ontimeout=function(){var t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||g;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),r(new c(t,n.clarifyTimeoutError?c.ETIMEDOUT:c.ECONNABORTED,e,_)),_=null},n.isStandardBrowserEnv()){var A=(e.withCredentials||u(E))&&e.xsrfCookieName?i.read(e.xsrfCookieName):void 0;A&&(y[e.xsrfHeaderName]=A)}"setRequestHeader"in _&&n.forEach(y,(function(e,t){void 0===h&&"content-type"===t.toLowerCase()?delete y[t]:_.setRequestHeader(t,e)})),n.isUndefined(e.withCredentials)||(_.withCredentials=!!e.withCredentials),b&&"json"!==b&&(_.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&_.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&_.upload&&_.upload.addEventListener("progress",e.onUploadProgress),(e.cancelToken||e.signal)&&(f=function(e){_&&(r(!e||e&&e.type?new p:e),_.abort(),_=null)},e.cancelToken&&e.cancelToken.subscribe(f),e.signal&&(e.signal.aborted?f():e.signal.addEventListener("abort",f))),h||(h=null);var w=d(E);w&&-1===["http","https","file"].indexOf(w)?r(new c("Unsupported protocol "+w+":",c.ERR_BAD_REQUEST,e)):_.send(h)}))}},8681:(e,t,r)=>{"use strict";var n=r(1362),o=r(2046),i=r(2393),a=r(481);var s=function e(t){var r=new i(t),s=o(i.prototype.request,r);return n.extend(s,i.prototype,r),n.extend(s,r),s.create=function(r){return e(a(t,r))},s}(r(5338));s.Axios=i,s.CanceledError=r(9461),s.CancelToken=r(2509),s.isCancel=r(1898),s.VERSION=r(3179).version,s.toFormData=r(6034),s.AxiosError=r(5407),s.Cancel=s.CanceledError,s.all=function(e){return Promise.all(e)},s.spread=r(8038),s.isAxiosError=r(8617),e.exports=s,e.exports.default=s},2509:(e,t,r)=>{"use strict";var n=r(9461);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var r=this;this.promise.then((function(e){if(r._listeners){var t,n=r._listeners.length;for(t=0;t<n;t++)r._listeners[t](e);r._listeners=null}})),this.promise.then=function(e){var t,n=new Promise((function(e){r.subscribe(e),t=e})).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e((function(e){r.reason||(r.reason=new n(e),t(r.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.prototype.subscribe=function(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]},o.prototype.unsubscribe=function(e){if(this._listeners){var t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},9461:(e,t,r)=>{"use strict";var n=r(5407);function o(e){n.call(this,null==e?"canceled":e,n.ERR_CANCELED),this.name="CanceledError"}r(1362).inherits(o,n,{__CANCEL__:!0}),e.exports=o},1898:e=>{"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},2393:(e,t,r)=>{"use strict";var n=r(1362),o=r(3724),i=r(8265),a=r(1728),s=r(481),l=r(7429),u=r(5963),g=u.validators;function c(e){this.defaults=e,this.interceptors={request:new i,response:new i}}c.prototype.request=function(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},(t=s(this.defaults,t)).method?t.method=t.method.toLowerCase():this.defaults.method?t.method=this.defaults.method.toLowerCase():t.method="get";var r=t.transitional;void 0!==r&&u.assertOptions(r,{silentJSONParsing:g.transitional(g.boolean),forcedJSONParsing:g.transitional(g.boolean),clarifyTimeoutError:g.transitional(g.boolean)},!1);var n=[],o=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(o=o&&e.synchronous,n.unshift(e.fulfilled,e.rejected))}));var i,l=[];if(this.interceptors.response.forEach((function(e){l.push(e.fulfilled,e.rejected)})),!o){var c=[a,void 0];for(Array.prototype.unshift.apply(c,n),c=c.concat(l),i=Promise.resolve(t);c.length;)i=i.then(c.shift(),c.shift());return i}for(var p=t;n.length;){var d=n.shift(),f=n.shift();try{p=d(p)}catch(e){f(e);break}}try{i=a(p)}catch(e){return Promise.reject(e)}for(;l.length;)i=i.then(l.shift(),l.shift());return i},c.prototype.getUri=function(e){e=s(this.defaults,e);var t=l(e.baseURL,e.url);return o(t,e.params,e.paramsSerializer)},n.forEach(["delete","get","head","options"],(function(e){c.prototype[e]=function(t,r){return this.request(s(r||{},{method:e,url:t,data:(r||{}).data}))}})),n.forEach(["post","put","patch"],(function(e){function t(t){return function(r,n,o){return this.request(s(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}c.prototype[e]=t(),c.prototype[e+"Form"]=t(!0)})),e.exports=c},5407:(e,t,r)=>{"use strict";var n=r(1362);function o(e,t,r,n,o){Error.call(this),this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),o&&(this.response=o)}n.inherits(o,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var i=o.prototype,a={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED"].forEach((function(e){a[e]={value:e}})),Object.defineProperties(o,a),Object.defineProperty(i,"isAxiosError",{value:!0}),o.from=function(e,t,r,a,s,l){var u=Object.create(i);return n.toFlatObject(e,u,(function(e){return e!==Error.prototype})),o.call(u,e.message,t,r,a,s),u.name=e.name,l&&Object.assign(u,l),u},e.exports=o},8265:(e,t,r)=>{"use strict";var n=r(1362);function o(){this.handlers=[]}o.prototype.use=function(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){n.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},7429:(e,t,r)=>{"use strict";var n=r(8051),o=r(2118);e.exports=function(e,t){return e&&!n(t)?o(e,t):t}},1728:(e,t,r)=>{"use strict";var n=r(1362),o=r(671),i=r(1898),a=r(5338),s=r(9461);function l(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new s}e.exports=function(e){return l(e),e.headers=e.headers||{},e.data=o.call(e,e.data,e.headers,e.transformRequest),e.headers=n.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),n.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||a.adapter)(e).then((function(t){return l(e),t.data=o.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(l(e),t&&t.response&&(t.response.data=o.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},481:(e,t,r)=>{"use strict";var n=r(1362);e.exports=function(e,t){t=t||{};var r={};function o(e,t){return n.isPlainObject(e)&&n.isPlainObject(t)?n.merge(e,t):n.isPlainObject(t)?n.merge({},t):n.isArray(t)?t.slice():t}function i(r){return n.isUndefined(t[r])?n.isUndefined(e[r])?void 0:o(void 0,e[r]):o(e[r],t[r])}function a(e){if(!n.isUndefined(t[e]))return o(void 0,t[e])}function s(r){return n.isUndefined(t[r])?n.isUndefined(e[r])?void 0:o(void 0,e[r]):o(void 0,t[r])}function l(r){return r in t?o(e[r],t[r]):r in e?o(void 0,e[r]):void 0}var u={url:a,method:a,data:a,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:l};return n.forEach(Object.keys(e).concat(Object.keys(t)),(function(e){var t=u[e]||i,o=t(e);n.isUndefined(o)&&t!==l||(r[e]=o)})),r}},5072:(e,t,r)=>{"use strict";var n=r(5407);e.exports=function(e,t,r){var o=r.config.validateStatus;r.status&&o&&!o(r.status)?t(new n("Request failed with status code "+r.status,[n.ERR_BAD_REQUEST,n.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r)):e(r)}},671:(e,t,r)=>{"use strict";var n=r(1362),o=r(5338);e.exports=function(e,t,r){var i=this||o;return n.forEach(r,(function(r){e=r.call(i,e,t)})),e}},5338:(e,t,r)=>{"use strict";var n=r(1362),o=r(24),i=r(5407),a=r(8466),s=r(6034),l={"Content-Type":"application/x-www-form-urlencoded"};function u(e,t){!n.isUndefined(e)&&n.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var g,c={transitional:a,adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(g=r(1238)),g),transformRequest:[function(e,t){if(o(t,"Accept"),o(t,"Content-Type"),n.isFormData(e)||n.isArrayBuffer(e)||n.isBuffer(e)||n.isStream(e)||n.isFile(e)||n.isBlob(e))return e;if(n.isArrayBufferView(e))return e.buffer;if(n.isURLSearchParams(e))return u(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString();var r,i=n.isObject(e),a=t&&t["Content-Type"];if((r=n.isFileList(e))||i&&"multipart/form-data"===a){var l=this.env&&this.env.FormData;return s(r?{"files[]":e}:e,l&&new l)}return i||"application/json"===a?(u(t,"application/json"),function(e,t,r){if(n.isString(e))try{return(t||JSON.parse)(e),n.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(r||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional||c.transitional,r=t&&t.silentJSONParsing,o=t&&t.forcedJSONParsing,a=!r&&"json"===this.responseType;if(a||o&&n.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(a){if("SyntaxError"===e.name)throw i.from(e,i.ERR_BAD_RESPONSE,this,null,this.response);throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:r(892)},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};n.forEach(["delete","get","head"],(function(e){c.headers[e]={}})),n.forEach(["post","put","patch"],(function(e){c.headers[e]=n.merge(l)})),e.exports=c},8466:e=>{"use strict";e.exports={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1}},3179:e=>{e.exports={version:"0.27.2"}},2046:e=>{"use strict";e.exports=function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}}},3724:(e,t,r)=>{"use strict";var n=r(1362);function o(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,r){if(!t)return e;var i;if(r)i=r(t);else if(n.isURLSearchParams(t))i=t.toString();else{var a=[];n.forEach(t,(function(e,t){null!=e&&(n.isArray(e)?t+="[]":e=[e],n.forEach(e,(function(e){n.isDate(e)?e=e.toISOString():n.isObject(e)&&(e=JSON.stringify(e)),a.push(o(t)+"="+o(e))})))})),i=a.join("&")}if(i){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}},2118:e=>{"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},4438:(e,t,r)=>{"use strict";var n=r(1362);e.exports=n.isStandardBrowserEnv()?{write:function(e,t,r,o,i,a){var s=[];s.push(e+"="+encodeURIComponent(t)),n.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),n.isString(o)&&s.push("path="+o),n.isString(i)&&s.push("domain="+i),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},8051:e=>{"use strict";e.exports=function(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}},8617:(e,t,r)=>{"use strict";var n=r(1362);e.exports=function(e){return n.isObject(e)&&!0===e.isAxiosError}},4508:(e,t,r)=>{"use strict";var n=r(1362);e.exports=n.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function o(e){var n=e;return t&&(r.setAttribute("href",n),n=r.href),r.setAttribute("href",n),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return e=o(window.location.href),function(t){var r=n.isString(t)?o(t):t;return r.protocol===e.protocol&&r.host===e.host}}():function(){return!0}},24:(e,t,r)=>{"use strict";var n=r(1362);e.exports=function(e,t){n.forEach(e,(function(r,n){n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[n])}))}},892:e=>{e.exports=null},3862:(e,t,r)=>{"use strict";var n=r(1362),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,r,i,a={};return e?(n.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=n.trim(e.substr(0,i)).toLowerCase(),r=n.trim(e.substr(i+1)),t){if(a[t]&&o.indexOf(t)>=0)return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([r]):a[t]?a[t]+", "+r:r}})),a):a}},7962:e=>{"use strict";e.exports=function(e){var t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}},8038:e=>{"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},6034:(e,t,r)=>{"use strict";var n=r(1362);e.exports=function(e,t){t=t||new FormData;var r=[];function o(e){return null===e?"":n.isDate(e)?e.toISOString():n.isArrayBuffer(e)||n.isTypedArray(e)?"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}return function e(i,a){if(n.isPlainObject(i)||n.isArray(i)){if(-1!==r.indexOf(i))throw Error("Circular reference detected in "+a);r.push(i),n.forEach(i,(function(r,i){if(!n.isUndefined(r)){var s,l=a?a+"."+i:i;if(r&&!a&&"object"==typeof r)if(n.endsWith(i,"{}"))r=JSON.stringify(r);else if(n.endsWith(i,"[]")&&(s=n.toArray(r)))return void s.forEach((function(e){!n.isUndefined(e)&&t.append(l,o(e))}));e(r,l)}})),r.pop()}else t.append(a,o(i))}(e),t}},5963:(e,t,r)=>{"use strict";var n=r(3179).version,o=r(5407),i={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){i[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}}));var a={};i.transitional=function(e,t,r){function i(e,t){return"[Axios v"+n+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return function(r,n,s){if(!1===e)throw new o(i(n," has been removed"+(t?" in "+t:"")),o.ERR_DEPRECATED);return t&&!a[n]&&(a[n]=!0,console.warn(i(n," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,n,s)}},e.exports={assertOptions:function(e,t,r){if("object"!=typeof e)throw new o("options must be an object",o.ERR_BAD_OPTION_VALUE);for(var n=Object.keys(e),i=n.length;i-- >0;){var a=n[i],s=t[a];if(s){var l=e[a],u=void 0===l||s(l,a,e);if(!0!==u)throw new o("option "+a+" must be "+u,o.ERR_BAD_OPTION_VALUE)}else if(!0!==r)throw new o("Unknown option "+a,o.ERR_BAD_OPTION)}},validators:i}},1362:(e,t,r)=>{"use strict";var n,o=r(2046),i=Object.prototype.toString,a=(n=Object.create(null),function(e){var t=i.call(e);return n[t]||(n[t]=t.slice(8,-1).toLowerCase())});function s(e){return e=e.toLowerCase(),function(t){return a(t)===e}}function l(e){return Array.isArray(e)}function u(e){return void 0===e}var g=s("ArrayBuffer");function c(e){return null!==e&&"object"==typeof e}function p(e){if("object"!==a(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}var d=s("Date"),f=s("File"),h=s("Blob"),y=s("FileList");function b(e){return"[object Function]"===i.call(e)}var m=s("URLSearchParams");function _(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),l(e))for(var r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}var v,S=(v="undefined"!=typeof Uint8Array&&Object.getPrototypeOf(Uint8Array),function(e){return v&&e instanceof v});e.exports={isArray:l,isArrayBuffer:g,isBuffer:function(e){return null!==e&&!u(e)&&null!==e.constructor&&!u(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){var t="[object FormData]";return e&&("function"==typeof FormData&&e instanceof FormData||i.call(e)===t||b(e.toString)&&e.toString()===t)},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&g(e.buffer)},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:c,isPlainObject:p,isUndefined:u,isDate:d,isFile:f,isBlob:h,isFunction:b,isStream:function(e){return c(e)&&b(e.pipe)},isURLSearchParams:m,isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:_,merge:function e(){var t={};function r(r,n){p(t[n])&&p(r)?t[n]=e(t[n],r):p(r)?t[n]=e({},r):l(r)?t[n]=r.slice():t[n]=r}for(var n=0,o=arguments.length;n<o;n++)_(arguments[n],r);return t},extend:function(e,t,r){return _(t,(function(t,n){e[n]=r&&"function"==typeof t?o(t,r):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e},inherits:function(e,t,r,n){e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,r&&Object.assign(e.prototype,r)},toFlatObject:function(e,t,r){var n,o,i,a={};t=t||{};do{for(o=(n=Object.getOwnPropertyNames(e)).length;o-- >0;)a[i=n[o]]||(t[i]=e[i],a[i]=!0);e=Object.getPrototypeOf(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},kindOf:a,kindOfTest:s,endsWith:function(e,t,r){e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;var n=e.indexOf(t,r);return-1!==n&&n===r},toArray:function(e){if(!e)return null;var t=e.length;if(u(t))return null;for(var r=new Array(t);t-- >0;)r[t]=e[t];return r},isTypedArray:S,isFileList:y}},1944:(e,t)=>{"use strict";t.iI=function(e){for(var t,n=e.length,o=n%3,i=[],a=16383,s=0,u=n-o;s<u;s+=a)i.push(l(e,s,s+a>u?u:s+a));1===o?(t=e[n-1],i.push(r[t>>2]+r[t<<4&63]+"==")):2===o&&(t=(e[n-2]<<8)+e[n-1],i.push(r[t>>10]+r[t>>4&63]+r[t<<2&63]+"="));return i.join("")};for(var r=[],n=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0;a<64;++a)r[a]=i[a],n[i.charCodeAt(a)]=a;function s(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function l(e,t,n){for(var o,i,a=[],s=t;s<n;s+=3)o=(e[s]<<16&16711680)+(e[s+1]<<8&65280)+(255&e[s+2]),a.push(r[(i=o)>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return a.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},613:function(module,exports){var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.findInternal=function(e,t,r){e instanceof String&&(e=String(e));for(var n=e.length,o=0;o<n;o++){var i=e[o];if(t.call(r,i,o,e))return{i:o,v:i}}return{i:-1,v:void 0}},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.SIMPLE_FROUND_POLYFILL=!1,$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(e,t,r){e!=Array.prototype&&e!=Object.prototype&&(e[t]=r.value)},$jscomp.getGlobal=function(e){return"undefined"!=typeof window&&window===e?e:"undefined"!=typeof global&&null!=global?global:e},$jscomp.global=$jscomp.getGlobal(this),$jscomp.polyfill=function(e,t,r,n){if(t){for(r=$jscomp.global,e=e.split("."),n=0;n<e.length-1;n++){var o=e[n];o in r||(r[o]={}),r=r[o]}(t=t(n=r[e=e[e.length-1]]))!=n&&null!=t&&$jscomp.defineProperty(r,e,{configurable:!0,writable:!0,value:t})}},$jscomp.polyfill("Array.prototype.findIndex",(function(e){return e||function(e,t){return $jscomp.findInternal(this,e,t).i}}),"es6","es3"),$jscomp.checkStringArgs=function(e,t,r){if(null==e)throw new TypeError("The 'this' value for String.prototype."+r+" must not be null or undefined");if(t instanceof RegExp)throw new TypeError("First argument to String.prototype."+r+" must not be a regular expression");return e+""},$jscomp.polyfill("String.prototype.endsWith",(function(e){return e||function(e,t){var r=$jscomp.checkStringArgs(this,e,"endsWith");e+="",void 0===t&&(t=r.length),t=Math.max(0,Math.min(0|t,r.length));for(var n=e.length;0<n&&0<t;)if(r[--t]!=e[--n])return!1;return 0>=n}}),"es6","es3"),$jscomp.polyfill("Array.prototype.find",(function(e){return e||function(e,t){return $jscomp.findInternal(this,e,t).v}}),"es6","es3"),$jscomp.polyfill("String.prototype.startsWith",(function(e){return e||function(e,t){var r=$jscomp.checkStringArgs(this,e,"startsWith");e+="";var n=r.length,o=e.length;t=Math.max(0,Math.min(0|t,r.length));for(var i=0;i<o&&t<n;)if(r[t++]!=e[i++])return!1;return i>=o}}),"es6","es3"),$jscomp.polyfill("String.prototype.repeat",(function(e){return e||function(e){var t=$jscomp.checkStringArgs(this,null,"repeat");if(0>e||1342177279<e)throw new RangeError("Invalid count value");e|=0;for(var r="";e;)1&e&&(r+=t),(e>>>=1)&&(t+=t);return r}}),"es6","es3");var COMPILED=!0,goog=goog||{};goog.global=this||self,goog.exportPath_=function(e,t,r){e=e.split("."),r=r||goog.global,e[0]in r||void 0===r.execScript||r.execScript("var "+e[0]);for(var n;e.length&&(n=e.shift());)e.length||void 0===t?r=r[n]&&r[n]!==Object.prototype[n]?r[n]:r[n]={}:r[n]=t},goog.define=function(e,t){if(!COMPILED){var r=goog.global.CLOSURE_UNCOMPILED_DEFINES,n=goog.global.CLOSURE_DEFINES;r&&void 0===r.nodeType&&Object.prototype.hasOwnProperty.call(r,e)?t=r[e]:n&&void 0===n.nodeType&&Object.prototype.hasOwnProperty.call(n,e)&&(t=n[e])}return t},goog.FEATURESET_YEAR=2012,goog.DEBUG=!0,goog.LOCALE="en",goog.TRUSTED_SITE=!0,goog.STRICT_MODE_COMPATIBLE=!1,goog.DISALLOW_TEST_ONLY_CODE=COMPILED&&!goog.DEBUG,goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING=!1,goog.provide=function(e){if(goog.isInModuleLoader_())throw Error("goog.provide cannot be used within a module.");if(!COMPILED&&goog.isProvided_(e))throw Error('Namespace "'+e+'" already declared.');goog.constructNamespace_(e)},goog.constructNamespace_=function(e,t){if(!COMPILED){delete goog.implicitNamespaces_[e];for(var r=e;(r=r.substring(0,r.lastIndexOf(".")))&&!goog.getObjectByName(r);)goog.implicitNamespaces_[r]=!0}goog.exportPath_(e,t)},goog.getScriptNonce=function(e){return e&&e!=goog.global?goog.getScriptNonce_(e.document):(null===goog.cspNonce_&&(goog.cspNonce_=goog.getScriptNonce_(goog.global.document)),goog.cspNonce_)},goog.NONCE_PATTERN_=/^[\w+/_-]+[=]{0,2}$/,goog.cspNonce_=null,goog.getScriptNonce_=function(e){return(e=e.querySelector&&e.querySelector("script[nonce]"))&&(e=e.nonce||e.getAttribute("nonce"))&&goog.NONCE_PATTERN_.test(e)?e:""},goog.VALID_MODULE_RE_=/^[a-zA-Z_$][a-zA-Z0-9._$]*$/,goog.module=function(e){if("string"!=typeof e||!e||-1==e.search(goog.VALID_MODULE_RE_))throw Error("Invalid module identifier");if(!goog.isInGoogModuleLoader_())throw Error("Module "+e+" has been loaded incorrectly. Note, modules cannot be loaded as normal scripts. They require some kind of pre-processing step. You're likely trying to load a module via a script tag or as a part of a concatenated bundle without rewriting the module. For more info see: https://github.com/google/closure-library/wiki/goog.module:-an-ES6-module-like-alternative-to-goog.provide.");if(goog.moduleLoaderState_.moduleName)throw Error("goog.module may only be called once per module.");if(goog.moduleLoaderState_.moduleName=e,!COMPILED){if(goog.isProvided_(e))throw Error('Namespace "'+e+'" already declared.');delete goog.implicitNamespaces_[e]}},goog.module.get=function(e){return goog.module.getInternal_(e)},goog.module.getInternal_=function(e){if(!COMPILED){if(e in goog.loadedModules_)return goog.loadedModules_[e].exports;if(!goog.implicitNamespaces_[e])return null!=(e=goog.getObjectByName(e))?e:null}return null},goog.ModuleType={ES6:"es6",GOOG:"goog"},goog.moduleLoaderState_=null,goog.isInModuleLoader_=function(){return goog.isInGoogModuleLoader_()||goog.isInEs6ModuleLoader_()},goog.isInGoogModuleLoader_=function(){return!!goog.moduleLoaderState_&&goog.moduleLoaderState_.type==goog.ModuleType.GOOG},goog.isInEs6ModuleLoader_=function(){if(goog.moduleLoaderState_&&goog.moduleLoaderState_.type==goog.ModuleType.ES6)return!0;var e=goog.global.$jscomp;return!!e&&("function"==typeof e.getCurrentModulePath&&!!e.getCurrentModulePath())},goog.module.declareLegacyNamespace=function(){if(!COMPILED&&!goog.isInGoogModuleLoader_())throw Error("goog.module.declareLegacyNamespace must be called from within a goog.module");if(!COMPILED&&!goog.moduleLoaderState_.moduleName)throw Error("goog.module must be called prior to goog.module.declareLegacyNamespace.");goog.moduleLoaderState_.declareLegacyNamespace=!0},goog.declareModuleId=function(e){if(!COMPILED){if(!goog.isInEs6ModuleLoader_())throw Error("goog.declareModuleId may only be called from within an ES6 module");if(goog.moduleLoaderState_&&goog.moduleLoaderState_.moduleName)throw Error("goog.declareModuleId may only be called once per module.");if(e in goog.loadedModules_)throw Error('Module with namespace "'+e+'" already exists.')}if(goog.moduleLoaderState_)goog.moduleLoaderState_.moduleName=e;else{var t=goog.global.$jscomp;if(!t||"function"!=typeof t.getCurrentModulePath)throw Error('Module with namespace "'+e+'" has been loaded incorrectly.');t=t.require(t.getCurrentModulePath()),goog.loadedModules_[e]={exports:t,type:goog.ModuleType.ES6,moduleId:e}}},goog.setTestOnly=function(e){if(goog.DISALLOW_TEST_ONLY_CODE)throw e=e||"",Error("Importing test-only code into non-debug environment"+(e?": "+e:"."))},goog.forwardDeclare=function(e){},COMPILED||(goog.isProvided_=function(e){return e in goog.loadedModules_||!goog.implicitNamespaces_[e]&&null!=goog.getObjectByName(e)},goog.implicitNamespaces_={"goog.module":!0}),goog.getObjectByName=function(e,t){e=e.split("."),t=t||goog.global;for(var r=0;r<e.length;r++)if(null==(t=t[e[r]]))return null;return t},goog.globalize=function(e,t){for(var r in t=t||goog.global,e)t[r]=e[r]},goog.addDependency=function(e,t,r,n){!COMPILED&&goog.DEPENDENCIES_ENABLED&&goog.debugLoader_.addDependency(e,t,r,n)},goog.ENABLE_DEBUG_LOADER=!0,goog.logToConsole_=function(e){goog.global.console&&goog.global.console.error(e)},goog.require=function(e){if(!COMPILED){if(goog.ENABLE_DEBUG_LOADER&&goog.debugLoader_.requested(e),goog.isProvided_(e)){if(goog.isInModuleLoader_())return goog.module.getInternal_(e)}else if(goog.ENABLE_DEBUG_LOADER){var t=goog.moduleLoaderState_;goog.moduleLoaderState_=null;try{goog.debugLoader_.load_(e)}finally{goog.moduleLoaderState_=t}}return null}},goog.requireType=function(e){return{}},goog.basePath="",goog.nullFunction=function(){},goog.abstractMethod=function(){throw Error("unimplemented abstract method")},goog.addSingletonGetter=function(e){e.instance_=void 0,e.getInstance=function(){return e.instance_?e.instance_:(goog.DEBUG&&(goog.instantiatedSingletons_[goog.instantiatedSingletons_.length]=e),e.instance_=new e)}},goog.instantiatedSingletons_=[],goog.LOAD_MODULE_USING_EVAL=!0,goog.SEAL_MODULE_EXPORTS=goog.DEBUG,goog.loadedModules_={},goog.DEPENDENCIES_ENABLED=!COMPILED&&goog.ENABLE_DEBUG_LOADER,goog.TRANSPILE="detect",goog.ASSUME_ES_MODULES_TRANSPILED=!1,goog.TRANSPILE_TO_LANGUAGE="",goog.TRANSPILER="transpile.js",goog.hasBadLetScoping=null,goog.useSafari10Workaround=function(){if(null==goog.hasBadLetScoping){try{var a=!eval('"use strict";let x = 1; function f() { return typeof x; };f() == "number";')}catch(e){a=!1}goog.hasBadLetScoping=a}return goog.hasBadLetScoping},goog.workaroundSafari10EvalBug=function(e){return"(function(){"+e+"\n;})();\n"},goog.loadModule=function(e){var t=goog.moduleLoaderState_;try{if(goog.moduleLoaderState_={moduleName:"",declareLegacyNamespace:!1,type:goog.ModuleType.GOOG},goog.isFunction(e))var r=e.call(void 0,{});else{if("string"!=typeof e)throw Error("Invalid module definition");goog.useSafari10Workaround()&&(e=goog.workaroundSafari10EvalBug(e)),r=goog.loadModuleFromSource_.call(void 0,e)}var n=goog.moduleLoaderState_.moduleName;if("string"!=typeof n||!n)throw Error('Invalid module name "'+n+'"');goog.moduleLoaderState_.declareLegacyNamespace?goog.constructNamespace_(n,r):goog.SEAL_MODULE_EXPORTS&&Object.seal&&"object"==typeof r&&null!=r&&Object.seal(r),goog.loadedModules_[n]={exports:r,type:goog.ModuleType.GOOG,moduleId:goog.moduleLoaderState_.moduleName}}finally{goog.moduleLoaderState_=t}},goog.loadModuleFromSource_=function(a){return eval(a),{}},goog.normalizePath_=function(e){e=e.split("/");for(var t=0;t<e.length;)"."==e[t]?e.splice(t,1):t&&".."==e[t]&&e[t-1]&&".."!=e[t-1]?e.splice(--t,2):t++;return e.join("/")},goog.loadFileSync_=function(e){if(goog.global.CLOSURE_LOAD_FILE_SYNC)return goog.global.CLOSURE_LOAD_FILE_SYNC(e);try{var t=new goog.global.XMLHttpRequest;return t.open("get",e,!1),t.send(),0==t.status||200==t.status?t.responseText:null}catch(e){return null}},goog.transpile_=function(e,t,r){var n=goog.global.$jscomp;n||(goog.global.$jscomp=n={});var o=n.transpile;if(!o){var i=goog.basePath+goog.TRANSPILER,a=goog.loadFileSync_(i);if(a){if(function(){(0,eval)(a+"\n//# sourceURL="+i)}.call(goog.global),goog.global.$gwtExport&&goog.global.$gwtExport.$jscomp&&!goog.global.$gwtExport.$jscomp.transpile)throw Error('The transpiler did not properly export the "transpile" method. $gwtExport: '+JSON.stringify(goog.global.$gwtExport));goog.global.$jscomp.transpile=goog.global.$gwtExport.$jscomp.transpile,o=(n=goog.global.$jscomp).transpile}}return o||(o=n.transpile=function(e,t){return goog.logToConsole_(t+" requires transpilation but no transpiler was found."),e}),o(e,t,r)},goog.typeOf=function(e){var t=typeof e;if("object"==t){if(!e)return"null";if(e instanceof Array)return"array";if(e instanceof Object)return t;var r=Object.prototype.toString.call(e);if("[object Window]"==r)return"object";if("[object Array]"==r||"number"==typeof e.length&&void 0!==e.splice&&void 0!==e.propertyIsEnumerable&&!e.propertyIsEnumerable("splice"))return"array";if("[object Function]"==r||void 0!==e.call&&void 0!==e.propertyIsEnumerable&&!e.propertyIsEnumerable("call"))return"function"}else if("function"==t&&void 0===e.call)return"object";return t},goog.isArray=function(e){return"array"==goog.typeOf(e)},goog.isArrayLike=function(e){var t=goog.typeOf(e);return"array"==t||"object"==t&&"number"==typeof e.length},goog.isDateLike=function(e){return goog.isObject(e)&&"function"==typeof e.getFullYear},goog.isFunction=function(e){return"function"==goog.typeOf(e)},goog.isObject=function(e){var t=typeof e;return"object"==t&&null!=e||"function"==t},goog.getUid=function(e){return Object.prototype.hasOwnProperty.call(e,goog.UID_PROPERTY_)&&e[goog.UID_PROPERTY_]||(e[goog.UID_PROPERTY_]=++goog.uidCounter_)},goog.hasUid=function(e){return!!e[goog.UID_PROPERTY_]},goog.removeUid=function(e){null!==e&&"removeAttribute"in e&&e.removeAttribute(goog.UID_PROPERTY_);try{delete e[goog.UID_PROPERTY_]}catch(e){}},goog.UID_PROPERTY_="closure_uid_"+(1e9*Math.random()>>>0),goog.uidCounter_=0,goog.getHashCode=goog.getUid,goog.removeHashCode=goog.removeUid,goog.cloneObject=function(e){var t=goog.typeOf(e);if("object"==t||"array"==t){if("function"==typeof e.clone)return e.clone();for(var r in t="array"==t?[]:{},e)t[r]=goog.cloneObject(e[r]);return t}return e},goog.bindNative_=function(e,t,r){return e.call.apply(e.bind,arguments)},goog.bindJs_=function(e,t,r){if(!e)throw Error();if(2<arguments.length){var n=Array.prototype.slice.call(arguments,2);return function(){var r=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(r,n),e.apply(t,r)}}return function(){return e.apply(t,arguments)}},goog.bind=function(e,t,r){return Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?goog.bind=goog.bindNative_:goog.bind=goog.bindJs_,goog.bind.apply(null,arguments)},goog.partial=function(e,t){var r=Array.prototype.slice.call(arguments,1);return function(){var t=r.slice();return t.push.apply(t,arguments),e.apply(this,t)}},goog.mixin=function(e,t){for(var r in t)e[r]=t[r]},goog.now=goog.TRUSTED_SITE&&Date.now||function(){return+new Date},goog.globalEval=function(e){if(goog.global.execScript)goog.global.execScript(e,"JavaScript");else{if(!goog.global.eval)throw Error("goog.globalEval not available");if(null==goog.evalWorksForGlobals_){try{goog.global.eval("var _evalTest_ = 1;")}catch(e){}if(void 0!==goog.global._evalTest_){try{delete goog.global._evalTest_}catch(e){}goog.evalWorksForGlobals_=!0}else goog.evalWorksForGlobals_=!1}if(goog.evalWorksForGlobals_)goog.global.eval(e);else{var t=goog.global.document,r=t.createElement("script");r.type="text/javascript",r.defer=!1,r.appendChild(t.createTextNode(e)),t.head.appendChild(r),t.head.removeChild(r)}}},goog.evalWorksForGlobals_=null,goog.getCssName=function(e,t){if("."==String(e).charAt(0))throw Error('className passed in goog.getCssName must not start with ".". You passed: '+e);var r=function(e){return goog.cssNameMapping_[e]||e},n=function(e){e=e.split("-");for(var t=[],n=0;n<e.length;n++)t.push(r(e[n]));return t.join("-")};return n=goog.cssNameMapping_?"BY_WHOLE"==goog.cssNameMappingStyle_?r:n:function(e){return e},e=t?e+"-"+n(t):n(e),goog.global.CLOSURE_CSS_NAME_MAP_FN?goog.global.CLOSURE_CSS_NAME_MAP_FN(e):e},goog.setCssNameMapping=function(e,t){goog.cssNameMapping_=e,goog.cssNameMappingStyle_=t},!COMPILED&&goog.global.CLOSURE_CSS_NAME_MAPPING&&(goog.cssNameMapping_=goog.global.CLOSURE_CSS_NAME_MAPPING),goog.getMsg=function(e,t,r){return r&&r.html&&(e=e.replace(/</g,"&lt;")),t&&(e=e.replace(/\{\$([^}]+)}/g,(function(e,r){return null!=t&&r in t?t[r]:e}))),e},goog.getMsgWithFallback=function(e,t){return e},goog.exportSymbol=function(e,t,r){goog.exportPath_(e,t,r)},goog.exportProperty=function(e,t,r){e[t]=r},goog.inherits=function(e,t){function r(){}r.prototype=t.prototype,e.superClass_=t.prototype,e.prototype=new r,e.prototype.constructor=e,e.base=function(e,r,n){for(var o=Array(arguments.length-2),i=2;i<arguments.length;i++)o[i-2]=arguments[i];return t.prototype[r].apply(e,o)}},goog.scope=function(e){if(goog.isInModuleLoader_())throw Error("goog.scope is not supported within a module.");e.call(goog.global)},COMPILED||(goog.global.COMPILED=COMPILED),goog.defineClass=function(e,t){var r=t.constructor,n=t.statics;return r&&r!=Object.prototype.constructor||(r=function(){throw Error("cannot instantiate an interface (no constructor defined).")}),r=goog.defineClass.createSealingConstructor_(r,e),e&&goog.inherits(r,e),delete t.constructor,delete t.statics,goog.defineClass.applyProperties_(r.prototype,t),null!=n&&(n instanceof Function?n(r):goog.defineClass.applyProperties_(r,n)),r},goog.defineClass.SEAL_CLASS_INSTANCES=goog.DEBUG,goog.defineClass.createSealingConstructor_=function(e,t){if(!goog.defineClass.SEAL_CLASS_INSTANCES)return e;var r=!goog.defineClass.isUnsealable_(t),n=function(){var t=e.apply(this,arguments)||this;return t[goog.UID_PROPERTY_]=t[goog.UID_PROPERTY_],this.constructor===n&&r&&Object.seal instanceof Function&&Object.seal(t),t};return n},goog.defineClass.isUnsealable_=function(e){return e&&e.prototype&&e.prototype[goog.UNSEALABLE_CONSTRUCTOR_PROPERTY_]},goog.defineClass.OBJECT_PROTOTYPE_FIELDS_="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "),goog.defineClass.applyProperties_=function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);for(var n=0;n<goog.defineClass.OBJECT_PROTOTYPE_FIELDS_.length;n++)r=goog.defineClass.OBJECT_PROTOTYPE_FIELDS_[n],Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},goog.tagUnsealableClass=function(e){!COMPILED&&goog.defineClass.SEAL_CLASS_INSTANCES&&(e.prototype[goog.UNSEALABLE_CONSTRUCTOR_PROPERTY_]=!0)},goog.UNSEALABLE_CONSTRUCTOR_PROPERTY_="goog_defineClass_legacy_unsealable",!COMPILED&&goog.DEPENDENCIES_ENABLED&&(goog.inHtmlDocument_=function(){var e=goog.global.document;return null!=e&&"write"in e},goog.isDocumentLoading_=function(){var e=goog.global.document;return e.attachEvent?"complete"!=e.readyState:"loading"==e.readyState},goog.findBasePath_=function(){if(null!=goog.global.CLOSURE_BASE_PATH&&"string"==typeof goog.global.CLOSURE_BASE_PATH)goog.basePath=goog.global.CLOSURE_BASE_PATH;else if(goog.inHtmlDocument_()){var e=goog.global.document,t=e.currentScript;for(t=(e=t?[t]:e.getElementsByTagName("SCRIPT")).length-1;0<=t;--t){var r=e[t].src,n=r.lastIndexOf("?");if(n=-1==n?r.length:n,"base.js"==r.substr(n-7,7)){goog.basePath=r.substr(0,n-7);break}}}},goog.findBasePath_(),goog.Transpiler=function(){this.requiresTranspilation_=null,this.transpilationTarget_=goog.TRANSPILE_TO_LANGUAGE},goog.Transpiler.prototype.createRequiresTranspilation_=function(){function a(t,r){e?d[t]=!0:r()?(c=t,d[t]=!1):e=d[t]=!0}function b(a){try{return!!eval(a)}catch(e){return!1}}var c="es3",d={es3:!1},e=!1,f=goog.global.navigator&&goog.global.navigator.userAgent?goog.global.navigator.userAgent:"";return a("es5",(function(){return b("[1,].length==1")})),a("es6",(function(){return!f.match(/Edge\/(\d+)(\.\d)*/i)&&b('(()=>{"use strict";class X{constructor(){if(new.target!=String)throw 1;this.x=42}}let q=Reflect.construct(X,[],String);if(q.x!=42||!(q instanceof String))throw 1;for(const a of[2,3]){if(a==2)continue;function f(z={a}){let a=0;return z.a}{function f(){return 0;}}return f()==3}})()')})),a("es7",(function(){return b("2 ** 2 == 4")})),a("es8",(function(){return b("async () => 1, true")})),a("es9",(function(){return b("({...rest} = {}), true")})),a("es_next",(function(){return!1})),{target:c,map:d}},goog.Transpiler.prototype.needsTranspile=function(e,t){if("always"==goog.TRANSPILE)return!0;if("never"==goog.TRANSPILE)return!1;if(!this.requiresTranspilation_){var r=this.createRequiresTranspilation_();this.requiresTranspilation_=r.map,this.transpilationTarget_=this.transpilationTarget_||r.target}if(e in this.requiresTranspilation_)return!!this.requiresTranspilation_[e]||!(!goog.inHtmlDocument_()||"es6"!=t||"noModule"in goog.global.document.createElement("script"));throw Error("Unknown language mode: "+e)},goog.Transpiler.prototype.transpile=function(e,t){return goog.transpile_(e,t,this.transpilationTarget_)},goog.transpiler_=new goog.Transpiler,goog.protectScriptTag_=function(e){return e.replace(/<\/(SCRIPT)/gi,"\\x3c/$1")},goog.DebugLoader_=function(){this.dependencies_={},this.idToPath_={},this.written_={},this.loadingDeps_=[],this.depsToLoad_=[],this.paused_=!1,this.factory_=new goog.DependencyFactory(goog.transpiler_),this.deferredCallbacks_={},this.deferredQueue_=[]},goog.DebugLoader_.prototype.bootstrap=function(e,t){function r(){n&&(goog.global.setTimeout(n,0),n=null)}var n=t;if(e.length){t=[];for(var o=0;o<e.length;o++){var i=this.getPathFromDeps_(e[o]);if(!i)throw Error("Unregonized namespace: "+e[o]);t.push(this.dependencies_[i])}i=goog.require;var a=0;for(o=0;o<e.length;o++)i(e[o]),t[o].onLoad((function(){++a==e.length&&r()}))}else r()},goog.DebugLoader_.prototype.loadClosureDeps=function(){this.depsToLoad_.push(this.factory_.createDependency(goog.normalizePath_(goog.basePath+"deps.js"),"deps.js",[],[],{},!1)),this.loadDeps_()},goog.DebugLoader_.prototype.requested=function(e,t){(e=this.getPathFromDeps_(e))&&(t||this.areDepsLoaded_(this.dependencies_[e].requires))&&(t=this.deferredCallbacks_[e])&&(delete this.deferredCallbacks_[e],t())},goog.DebugLoader_.prototype.setDependencyFactory=function(e){this.factory_=e},goog.DebugLoader_.prototype.load_=function(e){if(!this.getPathFromDeps_(e))throw e="goog.require could not find: "+e,goog.logToConsole_(e),Error(e);var t=this,r=[],n=function(e){var o=t.getPathFromDeps_(e);if(!o)throw Error("Bad dependency path or symbol: "+e);if(!t.written_[o]){for(t.written_[o]=!0,e=t.dependencies_[o],o=0;o<e.requires.length;o++)goog.isProvided_(e.requires[o])||n(e.requires[o]);r.push(e)}};n(e),e=!!this.depsToLoad_.length,this.depsToLoad_=this.depsToLoad_.concat(r),this.paused_||e||this.loadDeps_()},goog.DebugLoader_.prototype.loadDeps_=function(){for(var e=this,t=this.paused_;this.depsToLoad_.length&&!t;)!function(){var r=!1,n=e.depsToLoad_.shift(),o=!1;e.loading_(n);var i={pause:function(){if(r)throw Error("Cannot call pause after the call to load.");t=!0},resume:function(){r?e.resume_():t=!1},loaded:function(){if(o)throw Error("Double call to loaded.");o=!0,e.loaded_(n)},pending:function(){for(var t=[],r=0;r<e.loadingDeps_.length;r++)t.push(e.loadingDeps_[r]);return t},setModuleState:function(e){goog.moduleLoaderState_={type:e,moduleName:"",declareLegacyNamespace:!1}},registerEs6ModuleExports:function(e,t,r){r&&(goog.loadedModules_[r]={exports:t,type:goog.ModuleType.ES6,moduleId:r||""})},registerGoogModuleExports:function(e,t){goog.loadedModules_[e]={exports:t,type:goog.ModuleType.GOOG,moduleId:e}},clearModuleState:function(){goog.moduleLoaderState_=null},defer:function(t){if(r)throw Error("Cannot register with defer after the call to load.");e.defer_(n,t)},areDepsLoaded:function(){return e.areDepsLoaded_(n.requires)}};try{n.load(i)}finally{r=!0}}();t&&this.pause_()},goog.DebugLoader_.prototype.pause_=function(){this.paused_=!0},goog.DebugLoader_.prototype.resume_=function(){this.paused_&&(this.paused_=!1,this.loadDeps_())},goog.DebugLoader_.prototype.loading_=function(e){this.loadingDeps_.push(e)},goog.DebugLoader_.prototype.loaded_=function(e){for(var t=0;t<this.loadingDeps_.length;t++)if(this.loadingDeps_[t]==e){this.loadingDeps_.splice(t,1);break}for(t=0;t<this.deferredQueue_.length;t++)if(this.deferredQueue_[t]==e.path){this.deferredQueue_.splice(t,1);break}if(this.loadingDeps_.length==this.deferredQueue_.length&&!this.depsToLoad_.length)for(;this.deferredQueue_.length;)this.requested(this.deferredQueue_.shift(),!0);e.loaded()},goog.DebugLoader_.prototype.areDepsLoaded_=function(e){for(var t=0;t<e.length;t++){var r=this.getPathFromDeps_(e[t]);if(!r||!(r in this.deferredCallbacks_)&&!goog.isProvided_(e[t]))return!1}return!0},goog.DebugLoader_.prototype.getPathFromDeps_=function(e){return e in this.idToPath_?this.idToPath_[e]:e in this.dependencies_?e:null},goog.DebugLoader_.prototype.defer_=function(e,t){this.deferredCallbacks_[e.path]=t,this.deferredQueue_.push(e.path)},goog.LoadController=function(){},goog.LoadController.prototype.pause=function(){},goog.LoadController.prototype.resume=function(){},goog.LoadController.prototype.loaded=function(){},goog.LoadController.prototype.pending=function(){},goog.LoadController.prototype.registerEs6ModuleExports=function(e,t,r){},goog.LoadController.prototype.setModuleState=function(e){},goog.LoadController.prototype.clearModuleState=function(){},goog.LoadController.prototype.defer=function(e){},goog.LoadController.prototype.areDepsLoaded=function(){},goog.Dependency=function(e,t,r,n,o){this.path=e,this.relativePath=t,this.provides=r,this.requires=n,this.loadFlags=o,this.loaded_=!1,this.loadCallbacks_=[]},goog.Dependency.prototype.getPathName=function(){var e=this.path,t=e.indexOf("://");return 0<=t&&(0<=(t=(e=e.substring(t+3)).indexOf("/"))&&(e=e.substring(t+1))),e},goog.Dependency.prototype.onLoad=function(e){this.loaded_?e():this.loadCallbacks_.push(e)},goog.Dependency.prototype.loaded=function(){this.loaded_=!0;var e=this.loadCallbacks_;this.loadCallbacks_=[];for(var t=0;t<e.length;t++)e[t]()},goog.Dependency.defer_=!1,goog.Dependency.callbackMap_={},goog.Dependency.registerCallback_=function(e){var t=Math.random().toString(32);return goog.Dependency.callbackMap_[t]=e,t},goog.Dependency.unregisterCallback_=function(e){delete goog.Dependency.callbackMap_[e]},goog.Dependency.callback_=function(e,t){if(!(e in goog.Dependency.callbackMap_))throw Error("Callback key "+e+" does not exist (was base.js loaded more than once?).");for(var r=goog.Dependency.callbackMap_[e],n=[],o=1;o<arguments.length;o++)n.push(arguments[o]);r.apply(void 0,n)},goog.Dependency.prototype.load=function(e){if(goog.global.CLOSURE_IMPORT_SCRIPT)goog.global.CLOSURE_IMPORT_SCRIPT(this.path)?e.loaded():e.pause();else if(goog.inHtmlDocument_()){var t=goog.global.document;if("complete"==t.readyState&&!goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING){if(/\bdeps.js$/.test(this.path))return void e.loaded();throw Error('Cannot write "'+this.path+'" after document load')}if(!goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING&&goog.isDocumentLoading_()){var r=goog.Dependency.registerCallback_((function(t){goog.DebugLoader_.IS_OLD_IE_&&"complete"!=t.readyState||(goog.Dependency.unregisterCallback_(r),e.loaded())})),n=!goog.DebugLoader_.IS_OLD_IE_&&goog.getScriptNonce()?' nonce="'+goog.getScriptNonce()+'"':"";n='<script src="'+this.path+'" '+(goog.DebugLoader_.IS_OLD_IE_?"onreadystatechange":"onload")+"=\"goog.Dependency.callback_('"+r+'\', this)" type="text/javascript" '+(goog.Dependency.defer_?"defer":"")+n+"><\/script>",t.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(n):n)}else{var o=t.createElement("script");o.defer=goog.Dependency.defer_,o.async=!1,o.type="text/javascript",(n=goog.getScriptNonce())&&o.setAttribute("nonce",n),goog.DebugLoader_.IS_OLD_IE_?(e.pause(),o.onreadystatechange=function(){"loaded"!=o.readyState&&"complete"!=o.readyState||(e.loaded(),e.resume())}):o.onload=function(){o.onload=null,e.loaded()},o.src=goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createScriptURL(this.path):this.path,t.head.appendChild(o)}}else goog.logToConsole_("Cannot use default debug loader outside of HTML documents."),"deps.js"==this.relativePath?(goog.logToConsole_("Consider setting CLOSURE_IMPORT_SCRIPT before loading base.js, or setting CLOSURE_NO_DEPS to true."),e.loaded()):e.pause()},goog.Es6ModuleDependency=function(e,t,r,n,o){goog.Dependency.call(this,e,t,r,n,o)},goog.inherits(goog.Es6ModuleDependency,goog.Dependency),goog.Es6ModuleDependency.prototype.load=function(e){if(goog.global.CLOSURE_IMPORT_SCRIPT)goog.global.CLOSURE_IMPORT_SCRIPT(this.path)?e.loaded():e.pause();else if(goog.inHtmlDocument_()){var t=goog.global.document,r=this;if(goog.isDocumentLoading_()){var n=function(e,r){e=r?'<script type="module" crossorigin>'+r+"<\/script>":'<script type="module" crossorigin src="'+e+'"><\/script>',t.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(e):e)};goog.Dependency.defer_=!0}else n=function(e,r){var n=t.createElement("script");n.defer=!0,n.async=!1,n.type="module",n.setAttribute("crossorigin",!0);var o=goog.getScriptNonce();o&&n.setAttribute("nonce",o),r?n.textContent=goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createScript(r):r:n.src=goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createScriptURL(e):e,t.head.appendChild(n)};var o=goog.Dependency.registerCallback_((function(){goog.Dependency.unregisterCallback_(o),e.setModuleState(goog.ModuleType.ES6)}));n(void 0,'goog.Dependency.callback_("'+o+'")'),n(this.path,void 0);var i=goog.Dependency.registerCallback_((function(t){goog.Dependency.unregisterCallback_(i),e.registerEs6ModuleExports(r.path,t,goog.moduleLoaderState_.moduleName)}));n(void 0,'import * as m from "'+this.path+'"; goog.Dependency.callback_("'+i+'", m)');var a=goog.Dependency.registerCallback_((function(){goog.Dependency.unregisterCallback_(a),e.clearModuleState(),e.loaded()}));n(void 0,'goog.Dependency.callback_("'+a+'")')}else goog.logToConsole_("Cannot use default debug loader outside of HTML documents."),e.pause()},goog.TransformedDependency=function(e,t,r,n,o){goog.Dependency.call(this,e,t,r,n,o),this.contents_=null,this.lazyFetch_=!goog.inHtmlDocument_()||!("noModule"in goog.global.document.createElement("script"))},goog.inherits(goog.TransformedDependency,goog.Dependency),goog.TransformedDependency.prototype.load=function(e){function t(){n.contents_=goog.loadFileSync_(n.path),n.contents_&&(n.contents_=n.transform(n.contents_),n.contents_&&(n.contents_+="\n//# sourceURL="+n.path))}function r(){if(n.lazyFetch_&&t(),n.contents_){o&&e.setModuleState(goog.ModuleType.ES6);try{var r=n.contents_;if(n.contents_=null,goog.globalEval(r),o)var i=goog.moduleLoaderState_.moduleName}finally{o&&e.clearModuleState()}o&&goog.global.$jscomp.require.ensure([n.getPathName()],(function(){e.registerEs6ModuleExports(n.path,goog.global.$jscomp.require(n.getPathName()),i)})),e.loaded()}}var n=this;if(goog.global.CLOSURE_IMPORT_SCRIPT)t(),this.contents_&&goog.global.CLOSURE_IMPORT_SCRIPT("",this.contents_)?(this.contents_=null,e.loaded()):e.pause();else{var o=this.loadFlags.module==goog.ModuleType.ES6;this.lazyFetch_||t();var i=1<e.pending().length,a=i&&goog.DebugLoader_.IS_OLD_IE_;if(i=goog.Dependency.defer_&&(i||goog.isDocumentLoading_()),a||i)e.defer((function(){r()}));else{var s=goog.global.document;if(a=goog.inHtmlDocument_()&&"ActiveXObject"in goog.global,o&&goog.inHtmlDocument_()&&goog.isDocumentLoading_()&&!a){goog.Dependency.defer_=!0,e.pause();var l=s.onreadystatechange;s.onreadystatechange=function(){"interactive"==s.readyState&&(s.onreadystatechange=l,r(),e.resume()),goog.isFunction(l)&&l.apply(void 0,arguments)}}else!goog.DebugLoader_.IS_OLD_IE_&&goog.inHtmlDocument_()&&goog.isDocumentLoading_()?function(){var e=goog.global.document,t=goog.Dependency.registerCallback_((function(){goog.Dependency.unregisterCallback_(t),r()})),n='<script type="text/javascript">'+goog.protectScriptTag_('goog.Dependency.callback_("'+t+'");')+"<\/script>";e.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(n):n)}():r()}}},goog.TransformedDependency.prototype.transform=function(e){},goog.TranspiledDependency=function(e,t,r,n,o,i){goog.TransformedDependency.call(this,e,t,r,n,o),this.transpiler=i},goog.inherits(goog.TranspiledDependency,goog.TransformedDependency),goog.TranspiledDependency.prototype.transform=function(e){return this.transpiler.transpile(e,this.getPathName())},goog.PreTranspiledEs6ModuleDependency=function(e,t,r,n,o){goog.TransformedDependency.call(this,e,t,r,n,o)},goog.inherits(goog.PreTranspiledEs6ModuleDependency,goog.TransformedDependency),goog.PreTranspiledEs6ModuleDependency.prototype.transform=function(e){return e},goog.GoogModuleDependency=function(e,t,r,n,o,i,a){goog.TransformedDependency.call(this,e,t,r,n,o),this.needsTranspile_=i,this.transpiler_=a},goog.inherits(goog.GoogModuleDependency,goog.TransformedDependency),goog.GoogModuleDependency.prototype.transform=function(e){return this.needsTranspile_&&(e=this.transpiler_.transpile(e,this.getPathName())),goog.LOAD_MODULE_USING_EVAL&&void 0!==goog.global.JSON?"goog.loadModule("+goog.global.JSON.stringify(e+"\n//# sourceURL="+this.path+"\n")+");":'goog.loadModule(function(exports) {"use strict";'+e+"\n;return exports});\n//# sourceURL="+this.path+"\n"},goog.DebugLoader_.IS_OLD_IE_=!(goog.global.atob||!goog.global.document||!goog.global.document.all),goog.DebugLoader_.prototype.addDependency=function(e,t,r,n){t=t||[],e=e.replace(/\\/g,"/");var o=goog.normalizePath_(goog.basePath+e);for(n&&"boolean"!=typeof n||(n=n?{module:goog.ModuleType.GOOG}:{}),r=this.factory_.createDependency(o,e,t,r,n,goog.transpiler_.needsTranspile(n.lang||"es3",n.module)),this.dependencies_[o]=r,r=0;r<t.length;r++)this.idToPath_[t[r]]=o;this.idToPath_[e]=o},goog.DependencyFactory=function(e){this.transpiler=e},goog.DependencyFactory.prototype.createDependency=function(e,t,r,n,o,i){return o.module==goog.ModuleType.GOOG?new goog.GoogModuleDependency(e,t,r,n,o,i,this.transpiler):i?new goog.TranspiledDependency(e,t,r,n,o,this.transpiler):o.module==goog.ModuleType.ES6?"never"==goog.TRANSPILE&&goog.ASSUME_ES_MODULES_TRANSPILED?new goog.PreTranspiledEs6ModuleDependency(e,t,r,n,o):new goog.Es6ModuleDependency(e,t,r,n,o):new goog.Dependency(e,t,r,n,o)},goog.debugLoader_=new goog.DebugLoader_,goog.loadClosureDeps=function(){goog.debugLoader_.loadClosureDeps()},goog.setDependencyFactory=function(e){goog.debugLoader_.setDependencyFactory(e)},goog.global.CLOSURE_NO_DEPS||goog.debugLoader_.loadClosureDeps(),goog.bootstrap=function(e,t){goog.debugLoader_.bootstrap(e,t)}),goog.TRUSTED_TYPES_POLICY_NAME="",goog.identity_=function(e){return e},goog.createTrustedTypesPolicy=function(e){var t=null,r=goog.global.trustedTypes||goog.global.TrustedTypes;if(!r||!r.createPolicy)return t;try{t=r.createPolicy(e,{createHTML:goog.identity_,createScript:goog.identity_,createScriptURL:goog.identity_,createURL:goog.identity_})}catch(e){goog.logToConsole_(e.message)}return t},goog.TRUSTED_TYPES_POLICY_=goog.TRUSTED_TYPES_POLICY_NAME?goog.createTrustedTypesPolicy(goog.TRUSTED_TYPES_POLICY_NAME+"#base"):null,goog.object={},goog.object.is=function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t},goog.object.forEach=function(e,t,r){for(var n in e)t.call(r,e[n],n,e)},goog.object.filter=function(e,t,r){var n,o={};for(n in e)t.call(r,e[n],n,e)&&(o[n]=e[n]);return o},goog.object.map=function(e,t,r){var n,o={};for(n in e)o[n]=t.call(r,e[n],n,e);return o},goog.object.some=function(e,t,r){for(var n in e)if(t.call(r,e[n],n,e))return!0;return!1},goog.object.every=function(e,t,r){for(var n in e)if(!t.call(r,e[n],n,e))return!1;return!0},goog.object.getCount=function(e){var t,r=0;for(t in e)r++;return r},goog.object.getAnyKey=function(e){for(var t in e)return t},goog.object.getAnyValue=function(e){for(var t in e)return e[t]},goog.object.contains=function(e,t){return goog.object.containsValue(e,t)},goog.object.getValues=function(e){var t,r=[],n=0;for(t in e)r[n++]=e[t];return r},goog.object.getKeys=function(e){var t,r=[],n=0;for(t in e)r[n++]=t;return r},goog.object.getValueByKeys=function(e,t){var r=goog.isArrayLike(t),n=r?t:arguments;for(r=r?0:1;r<n.length;r++){if(null==e)return;e=e[n[r]]}return e},goog.object.containsKey=function(e,t){return null!==e&&t in e},goog.object.containsValue=function(e,t){for(var r in e)if(e[r]==t)return!0;return!1},goog.object.findKey=function(e,t,r){for(var n in e)if(t.call(r,e[n],n,e))return n},goog.object.findValue=function(e,t,r){return(t=goog.object.findKey(e,t,r))&&e[t]},goog.object.isEmpty=function(e){for(var t in e)return!1;return!0},goog.object.clear=function(e){for(var t in e)delete e[t]},goog.object.remove=function(e,t){var r;return(r=t in e)&&delete e[t],r},goog.object.add=function(e,t,r){if(null!==e&&t in e)throw Error('The object already contains the key "'+t+'"');goog.object.set(e,t,r)},goog.object.get=function(e,t,r){return null!==e&&t in e?e[t]:r},goog.object.set=function(e,t,r){e[t]=r},goog.object.setIfUndefined=function(e,t,r){return t in e?e[t]:e[t]=r},goog.object.setWithReturnValueIfNotSet=function(e,t,r){return t in e?e[t]:(r=r(),e[t]=r)},goog.object.equals=function(e,t){for(var r in e)if(!(r in t)||e[r]!==t[r])return!1;for(var n in t)if(!(n in e))return!1;return!0},goog.object.clone=function(e){var t,r={};for(t in e)r[t]=e[t];return r},goog.object.unsafeClone=function(e){var t=goog.typeOf(e);if("object"==t||"array"==t){if(goog.isFunction(e.clone))return e.clone();for(var r in t="array"==t?[]:{},e)t[r]=goog.object.unsafeClone(e[r]);return t}return e},goog.object.transpose=function(e){var t,r={};for(t in e)r[e[t]]=t;return r},goog.object.PROTOTYPE_FIELDS_="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "),goog.object.extend=function(e,t){for(var r,n,o=1;o<arguments.length;o++){for(r in n=arguments[o])e[r]=n[r];for(var i=0;i<goog.object.PROTOTYPE_FIELDS_.length;i++)r=goog.object.PROTOTYPE_FIELDS_[i],Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}},goog.object.create=function(e){var t=arguments.length;if(1==t&&Array.isArray(arguments[0]))return goog.object.create.apply(null,arguments[0]);if(t%2)throw Error("Uneven number of arguments");for(var r={},n=0;n<t;n+=2)r[arguments[n]]=arguments[n+1];return r},goog.object.createSet=function(e){var t=arguments.length;if(1==t&&Array.isArray(arguments[0]))return goog.object.createSet.apply(null,arguments[0]);for(var r={},n=0;n<t;n++)r[arguments[n]]=!0;return r},goog.object.createImmutableView=function(e){var t=e;return Object.isFrozen&&!Object.isFrozen(e)&&(t=Object.create(e),Object.freeze(t)),t},goog.object.isImmutableView=function(e){return!!Object.isFrozen&&Object.isFrozen(e)},goog.object.getAllPropertyNames=function(e,t,r){if(!e)return[];if(!Object.getOwnPropertyNames||!Object.getPrototypeOf)return goog.object.getKeys(e);for(var n={};e&&(e!==Object.prototype||t)&&(e!==Function.prototype||r);){for(var o=Object.getOwnPropertyNames(e),i=0;i<o.length;i++)n[o[i]]=!0;e=Object.getPrototypeOf(e)}return goog.object.getKeys(n)},goog.object.getSuperClass=function(e){return(e=Object.getPrototypeOf(e.prototype))&&e.constructor};var jspb={asserts:{}};jspb.asserts.doAssertFailure=function(e,t,r,n){var o="Assertion failed";if(r){o+=": "+r;var i=n}else e&&(o+=": "+e,i=t);throw Error(""+o,i||[])},jspb.asserts.assert=function(e,t,r){for(var n=[],o=2;o<arguments.length;++o)n[o-2]=arguments[o];return e||jspb.asserts.doAssertFailure("",null,t,n),e},jspb.asserts.assertString=function(e,t,r){for(var n=[],o=2;o<arguments.length;++o)n[o-2]=arguments[o];return"string"!=typeof e&&jspb.asserts.doAssertFailure("Expected string but got %s: %s.",[goog.typeOf(e),e],t,n),e},jspb.asserts.assertArray=function(e,t,r){for(var n=[],o=2;o<arguments.length;++o)n[o-2]=arguments[o];return Array.isArray(e)||jspb.asserts.doAssertFailure("Expected array but got %s: %s.",[goog.typeOf(e),e],t,n),e},jspb.asserts.fail=function(e,t){for(var r=[],n=1;n<arguments.length;++n)r[n-1]=arguments[n];throw Error("Failure"+(e?": "+e:""),r)},jspb.asserts.assertInstanceof=function(e,t,r,n){for(var o=[],i=3;i<arguments.length;++i)o[i-3]=arguments[i];return e instanceof t||jspb.asserts.doAssertFailure("Expected instanceof %s but got %s.",[jspb.asserts.getType(t),jspb.asserts.getType(e)],r,o),e},jspb.asserts.getType=function(e){return e instanceof Function?e.displayName||e.name||"unknown type name":e instanceof Object?e.constructor.displayName||e.constructor.name||Object.prototype.toString.call(e):null===e?"null":typeof e},jspb.BinaryConstants={},jspb.ConstBinaryMessage=function(){},jspb.BinaryMessage=function(){},jspb.BinaryConstants.FieldType={INVALID:-1,DOUBLE:1,FLOAT:2,INT64:3,UINT64:4,INT32:5,FIXED64:6,FIXED32:7,BOOL:8,STRING:9,GROUP:10,MESSAGE:11,BYTES:12,UINT32:13,ENUM:14,SFIXED32:15,SFIXED64:16,SINT32:17,SINT64:18,FHASH64:30,VHASH64:31},jspb.BinaryConstants.WireType={INVALID:-1,VARINT:0,FIXED64:1,DELIMITED:2,START_GROUP:3,END_GROUP:4,FIXED32:5},jspb.BinaryConstants.FieldTypeToWireType=function(e){var t=jspb.BinaryConstants.FieldType,r=jspb.BinaryConstants.WireType;switch(e){case t.INT32:case t.INT64:case t.UINT32:case t.UINT64:case t.SINT32:case t.SINT64:case t.BOOL:case t.ENUM:case t.VHASH64:return r.VARINT;case t.DOUBLE:case t.FIXED64:case t.SFIXED64:case t.FHASH64:return r.FIXED64;case t.STRING:case t.MESSAGE:case t.BYTES:return r.DELIMITED;case t.FLOAT:case t.FIXED32:case t.SFIXED32:return r.FIXED32;default:return r.INVALID}},jspb.BinaryConstants.INVALID_FIELD_NUMBER=-1,jspb.BinaryConstants.FLOAT32_EPS=1401298464324817e-60,jspb.BinaryConstants.FLOAT32_MIN=11754943508222875e-54,jspb.BinaryConstants.FLOAT32_MAX=34028234663852886e22,jspb.BinaryConstants.FLOAT64_EPS=5e-324,jspb.BinaryConstants.FLOAT64_MIN=22250738585072014e-324,jspb.BinaryConstants.FLOAT64_MAX=17976931348623157e292,jspb.BinaryConstants.TWO_TO_20=1048576,jspb.BinaryConstants.TWO_TO_23=8388608,jspb.BinaryConstants.TWO_TO_31=2147483648,jspb.BinaryConstants.TWO_TO_32=4294967296,jspb.BinaryConstants.TWO_TO_52=4503599627370496,jspb.BinaryConstants.TWO_TO_63=0x8000000000000000,jspb.BinaryConstants.TWO_TO_64=0x10000000000000000,jspb.BinaryConstants.ZERO_HASH="\0\0\0\0\0\0\0\0",goog.debug={},goog.debug.Error=function(e){if(Error.captureStackTrace)Error.captureStackTrace(this,goog.debug.Error);else{var t=Error().stack;t&&(this.stack=t)}e&&(this.message=String(e)),this.reportErrorToServer=!0},goog.inherits(goog.debug.Error,Error),goog.debug.Error.prototype.name="CustomError",goog.dom={},goog.dom.NodeType={ELEMENT:1,ATTRIBUTE:2,TEXT:3,CDATA_SECTION:4,ENTITY_REFERENCE:5,ENTITY:6,PROCESSING_INSTRUCTION:7,COMMENT:8,DOCUMENT:9,DOCUMENT_TYPE:10,DOCUMENT_FRAGMENT:11,NOTATION:12},goog.asserts={},goog.asserts.ENABLE_ASSERTS=goog.DEBUG,goog.asserts.AssertionError=function(e,t){goog.debug.Error.call(this,goog.asserts.subs_(e,t)),this.messagePattern=e},goog.inherits(goog.asserts.AssertionError,goog.debug.Error),goog.asserts.AssertionError.prototype.name="AssertionError",goog.asserts.DEFAULT_ERROR_HANDLER=function(e){throw e},goog.asserts.errorHandler_=goog.asserts.DEFAULT_ERROR_HANDLER,goog.asserts.subs_=function(e,t){for(var r="",n=(e=e.split("%s")).length-1,o=0;o<n;o++)r+=e[o]+(o<t.length?t[o]:"%s");return r+e[n]},goog.asserts.doAssertFailure_=function(e,t,r,n){var o="Assertion failed";if(r){o+=": "+r;var i=n}else e&&(o+=": "+e,i=t);e=new goog.asserts.AssertionError(""+o,i||[]),goog.asserts.errorHandler_(e)},goog.asserts.setErrorHandler=function(e){goog.asserts.ENABLE_ASSERTS&&(goog.asserts.errorHandler_=e)},goog.asserts.assert=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!e&&goog.asserts.doAssertFailure_("",null,t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertExists=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&null==e&&goog.asserts.doAssertFailure_("Expected to exist: %s.",[e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.fail=function(e,t){goog.asserts.ENABLE_ASSERTS&&goog.asserts.errorHandler_(new goog.asserts.AssertionError("Failure"+(e?": "+e:""),Array.prototype.slice.call(arguments,1)))},goog.asserts.assertNumber=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&"number"!=typeof e&&goog.asserts.doAssertFailure_("Expected number but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertString=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&"string"!=typeof e&&goog.asserts.doAssertFailure_("Expected string but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertFunction=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!goog.isFunction(e)&&goog.asserts.doAssertFailure_("Expected function but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertObject=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!goog.isObject(e)&&goog.asserts.doAssertFailure_("Expected object but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertArray=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!Array.isArray(e)&&goog.asserts.doAssertFailure_("Expected array but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertBoolean=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&"boolean"!=typeof e&&goog.asserts.doAssertFailure_("Expected boolean but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertElement=function(e,t,r){return!goog.asserts.ENABLE_ASSERTS||goog.isObject(e)&&e.nodeType==goog.dom.NodeType.ELEMENT||goog.asserts.doAssertFailure_("Expected Element but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertInstanceof=function(e,t,r,n){return!goog.asserts.ENABLE_ASSERTS||e instanceof t||goog.asserts.doAssertFailure_("Expected instanceof %s but got %s.",[goog.asserts.getType_(t),goog.asserts.getType_(e)],r,Array.prototype.slice.call(arguments,3)),e},goog.asserts.assertFinite=function(e,t,r){return!goog.asserts.ENABLE_ASSERTS||"number"==typeof e&&isFinite(e)||goog.asserts.doAssertFailure_("Expected %s to be a finite number but it is not.",[e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertObjectPrototypeIsIntact=function(){for(var e in Object.prototype)goog.asserts.fail(e+" should not be enumerable in Object.prototype.")},goog.asserts.getType_=function(e){return e instanceof Function?e.displayName||e.name||"unknown type name":e instanceof Object?e.constructor.displayName||e.constructor.name||Object.prototype.toString.call(e):null===e?"null":typeof e},goog.array={},goog.NATIVE_ARRAY_PROTOTYPES=goog.TRUSTED_SITE,goog.array.ASSUME_NATIVE_FUNCTIONS=2012<goog.FEATURESET_YEAR,goog.array.peek=function(e){return e[e.length-1]},goog.array.last=goog.array.peek,goog.array.indexOf=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.indexOf)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.indexOf.call(e,t,r)}:function(e,t,r){if(r=null==r?0:0>r?Math.max(0,e.length+r):r,"string"==typeof e)return"string"!=typeof t||1!=t.length?-1:e.indexOf(t,r);for(;r<e.length;r++)if(r in e&&e[r]===t)return r;return-1},goog.array.lastIndexOf=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.lastIndexOf)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.lastIndexOf.call(e,t,null==r?e.length-1:r)}:function(e,t,r){if(0>(r=null==r?e.length-1:r)&&(r=Math.max(0,e.length+r)),"string"==typeof e)return"string"!=typeof t||1!=t.length?-1:e.lastIndexOf(t,r);for(;0<=r;r--)if(r in e&&e[r]===t)return r;return-1},goog.array.forEach=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.forEach)?function(e,t,r){goog.asserts.assert(null!=e.length),Array.prototype.forEach.call(e,t,r)}:function(e,t,r){for(var n=e.length,o="string"==typeof e?e.split(""):e,i=0;i<n;i++)i in o&&t.call(r,o[i],i,e)},goog.array.forEachRight=function(e,t,r){var n=e.length,o="string"==typeof e?e.split(""):e;for(--n;0<=n;--n)n in o&&t.call(r,o[n],n,e)},goog.array.filter=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.filter)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.filter.call(e,t,r)}:function(e,t,r){for(var n=e.length,o=[],i=0,a="string"==typeof e?e.split(""):e,s=0;s<n;s++)if(s in a){var l=a[s];t.call(r,l,s,e)&&(o[i++]=l)}return o},goog.array.map=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.map)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.map.call(e,t,r)}:function(e,t,r){for(var n=e.length,o=Array(n),i="string"==typeof e?e.split(""):e,a=0;a<n;a++)a in i&&(o[a]=t.call(r,i[a],a,e));return o},goog.array.reduce=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.reduce)?function(e,t,r,n){return goog.asserts.assert(null!=e.length),n&&(t=goog.bind(t,n)),Array.prototype.reduce.call(e,t,r)}:function(e,t,r,n){var o=r;return goog.array.forEach(e,(function(r,i){o=t.call(n,o,r,i,e)})),o},goog.array.reduceRight=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.reduceRight)?function(e,t,r,n){return goog.asserts.assert(null!=e.length),goog.asserts.assert(null!=t),n&&(t=goog.bind(t,n)),Array.prototype.reduceRight.call(e,t,r)}:function(e,t,r,n){var o=r;return goog.array.forEachRight(e,(function(r,i){o=t.call(n,o,r,i,e)})),o},goog.array.some=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.some)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.some.call(e,t,r)}:function(e,t,r){for(var n=e.length,o="string"==typeof e?e.split(""):e,i=0;i<n;i++)if(i in o&&t.call(r,o[i],i,e))return!0;return!1},goog.array.every=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.every)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.every.call(e,t,r)}:function(e,t,r){for(var n=e.length,o="string"==typeof e?e.split(""):e,i=0;i<n;i++)if(i in o&&!t.call(r,o[i],i,e))return!1;return!0},goog.array.count=function(e,t,r){var n=0;return goog.array.forEach(e,(function(e,o,i){t.call(r,e,o,i)&&++n}),r),n},goog.array.find=function(e,t,r){return 0>(t=goog.array.findIndex(e,t,r))?null:"string"==typeof e?e.charAt(t):e[t]},goog.array.findIndex=function(e,t,r){for(var n=e.length,o="string"==typeof e?e.split(""):e,i=0;i<n;i++)if(i in o&&t.call(r,o[i],i,e))return i;return-1},goog.array.findRight=function(e,t,r){return 0>(t=goog.array.findIndexRight(e,t,r))?null:"string"==typeof e?e.charAt(t):e[t]},goog.array.findIndexRight=function(e,t,r){var n=e.length,o="string"==typeof e?e.split(""):e;for(--n;0<=n;n--)if(n in o&&t.call(r,o[n],n,e))return n;return-1},goog.array.contains=function(e,t){return 0<=goog.array.indexOf(e,t)},goog.array.isEmpty=function(e){return 0==e.length},goog.array.clear=function(e){if(!Array.isArray(e))for(var t=e.length-1;0<=t;t--)delete e[t];e.length=0},goog.array.insert=function(e,t){goog.array.contains(e,t)||e.push(t)},goog.array.insertAt=function(e,t,r){goog.array.splice(e,r,0,t)},goog.array.insertArrayAt=function(e,t,r){goog.partial(goog.array.splice,e,r,0).apply(null,t)},goog.array.insertBefore=function(e,t,r){var n;2==arguments.length||0>(n=goog.array.indexOf(e,r))?e.push(t):goog.array.insertAt(e,t,n)},goog.array.remove=function(e,t){var r;return(r=0<=(t=goog.array.indexOf(e,t)))&&goog.array.removeAt(e,t),r},goog.array.removeLast=function(e,t){return 0<=(t=goog.array.lastIndexOf(e,t))&&(goog.array.removeAt(e,t),!0)},goog.array.removeAt=function(e,t){return goog.asserts.assert(null!=e.length),1==Array.prototype.splice.call(e,t,1).length},goog.array.removeIf=function(e,t,r){return 0<=(t=goog.array.findIndex(e,t,r))&&(goog.array.removeAt(e,t),!0)},goog.array.removeAllIf=function(e,t,r){var n=0;return goog.array.forEachRight(e,(function(o,i){t.call(r,o,i,e)&&goog.array.removeAt(e,i)&&n++})),n},goog.array.concat=function(e){return Array.prototype.concat.apply([],arguments)},goog.array.join=function(e){return Array.prototype.concat.apply([],arguments)},goog.array.toArray=function(e){var t=e.length;if(0<t){for(var r=Array(t),n=0;n<t;n++)r[n]=e[n];return r}return[]},goog.array.clone=goog.array.toArray,goog.array.extend=function(e,t){for(var r=1;r<arguments.length;r++){var n=arguments[r];if(goog.isArrayLike(n)){var o=e.length||0,i=n.length||0;e.length=o+i;for(var a=0;a<i;a++)e[o+a]=n[a]}else e.push(n)}},goog.array.splice=function(e,t,r,n){return goog.asserts.assert(null!=e.length),Array.prototype.splice.apply(e,goog.array.slice(arguments,1))},goog.array.slice=function(e,t,r){return goog.asserts.assert(null!=e.length),2>=arguments.length?Array.prototype.slice.call(e,t):Array.prototype.slice.call(e,t,r)},goog.array.removeDuplicates=function(e,t,r){t=t||e;var n=function(e){return goog.isObject(e)?"o"+goog.getUid(e):(typeof e).charAt(0)+e};r=r||n,n={};for(var o=0,i=0;i<e.length;){var a=e[i++],s=r(a);Object.prototype.hasOwnProperty.call(n,s)||(n[s]=!0,t[o++]=a)}t.length=o},goog.array.binarySearch=function(e,t,r){return goog.array.binarySearch_(e,r||goog.array.defaultCompare,!1,t)},goog.array.binarySelect=function(e,t,r){return goog.array.binarySearch_(e,t,!0,void 0,r)},goog.array.binarySearch_=function(e,t,r,n,o){for(var i,a=0,s=e.length;a<s;){var l=a+(s-a>>>1),u=r?t.call(o,e[l],l,e):t(n,e[l]);0<u?a=l+1:(s=l,i=!u)}return i?a:-a-1},goog.array.sort=function(e,t){e.sort(t||goog.array.defaultCompare)},goog.array.stableSort=function(e,t){for(var r=Array(e.length),n=0;n<e.length;n++)r[n]={index:n,value:e[n]};var o=t||goog.array.defaultCompare;for(goog.array.sort(r,(function(e,t){return o(e.value,t.value)||e.index-t.index})),n=0;n<e.length;n++)e[n]=r[n].value},goog.array.sortByKey=function(e,t,r){var n=r||goog.array.defaultCompare;goog.array.sort(e,(function(e,r){return n(t(e),t(r))}))},goog.array.sortObjectsByKey=function(e,t,r){goog.array.sortByKey(e,(function(e){return e[t]}),r)},goog.array.isSorted=function(e,t,r){t=t||goog.array.defaultCompare;for(var n=1;n<e.length;n++){var o=t(e[n-1],e[n]);if(0<o||0==o&&r)return!1}return!0},goog.array.equals=function(e,t,r){if(!goog.isArrayLike(e)||!goog.isArrayLike(t)||e.length!=t.length)return!1;var n=e.length;r=r||goog.array.defaultCompareEquality;for(var o=0;o<n;o++)if(!r(e[o],t[o]))return!1;return!0},goog.array.compare3=function(e,t,r){r=r||goog.array.defaultCompare;for(var n=Math.min(e.length,t.length),o=0;o<n;o++){var i=r(e[o],t[o]);if(0!=i)return i}return goog.array.defaultCompare(e.length,t.length)},goog.array.defaultCompare=function(e,t){return e>t?1:e<t?-1:0},goog.array.inverseDefaultCompare=function(e,t){return-goog.array.defaultCompare(e,t)},goog.array.defaultCompareEquality=function(e,t){return e===t},goog.array.binaryInsert=function(e,t,r){return 0>(r=goog.array.binarySearch(e,t,r))&&(goog.array.insertAt(e,t,-(r+1)),!0)},goog.array.binaryRemove=function(e,t,r){return 0<=(t=goog.array.binarySearch(e,t,r))&&goog.array.removeAt(e,t)},goog.array.bucket=function(e,t,r){for(var n={},o=0;o<e.length;o++){var i=e[o],a=t.call(r,i,o,e);void 0!==a&&(n[a]||(n[a]=[])).push(i)}return n},goog.array.toObject=function(e,t,r){var n={};return goog.array.forEach(e,(function(o,i){n[t.call(r,o,i,e)]=o})),n},goog.array.range=function(e,t,r){var n=[],o=0,i=e;if(void 0!==t&&(o=e,i=t),0>(r=r||1)*(i-o))return[];if(0<r)for(e=o;e<i;e+=r)n.push(e);else for(e=o;e>i;e+=r)n.push(e);return n},goog.array.repeat=function(e,t){for(var r=[],n=0;n<t;n++)r[n]=e;return r},goog.array.flatten=function(e){for(var t=[],r=0;r<arguments.length;r++){var n=arguments[r];if(Array.isArray(n))for(var o=0;o<n.length;o+=8192){var i=goog.array.slice(n,o,o+8192);i=goog.array.flatten.apply(null,i);for(var a=0;a<i.length;a++)t.push(i[a])}else t.push(n)}return t},goog.array.rotate=function(e,t){return goog.asserts.assert(null!=e.length),e.length&&(0<(t%=e.length)?Array.prototype.unshift.apply(e,e.splice(-t,t)):0>t&&Array.prototype.push.apply(e,e.splice(0,-t))),e},goog.array.moveItem=function(e,t,r){goog.asserts.assert(0<=t&&t<e.length),goog.asserts.assert(0<=r&&r<e.length),t=Array.prototype.splice.call(e,t,1),Array.prototype.splice.call(e,r,0,t[0])},goog.array.zip=function(e){if(!arguments.length)return[];for(var t=[],r=arguments[0].length,n=1;n<arguments.length;n++)arguments[n].length<r&&(r=arguments[n].length);for(n=0;n<r;n++){for(var o=[],i=0;i<arguments.length;i++)o.push(arguments[i][n]);t.push(o)}return t},goog.array.shuffle=function(e,t){t=t||Math.random;for(var r=e.length-1;0<r;r--){var n=Math.floor(t()*(r+1)),o=e[r];e[r]=e[n],e[n]=o}},goog.array.copyByIndex=function(e,t){var r=[];return goog.array.forEach(t,(function(t){r.push(e[t])})),r},goog.array.concatMap=function(e,t,r){return goog.array.concat.apply([],goog.array.map(e,t,r))},goog.crypt={},goog.crypt.stringToByteArray=function(e){for(var t=[],r=0,n=0;n<e.length;n++){var o=e.charCodeAt(n);255<o&&(t[r++]=255&o,o>>=8),t[r++]=o}return t},goog.crypt.byteArrayToString=function(e){if(8192>=e.length)return String.fromCharCode.apply(null,e);for(var t="",r=0;r<e.length;r+=8192){var n=goog.array.slice(e,r,r+8192);t+=String.fromCharCode.apply(null,n)}return t},goog.crypt.byteArrayToHex=function(e,t){return goog.array.map(e,(function(e){return 1<(e=e.toString(16)).length?e:"0"+e})).join(t||"")},goog.crypt.hexToByteArray=function(e){goog.asserts.assert(0==e.length%2,"Key string length must be multiple of 2");for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substring(r,r+2),16));return t},goog.crypt.stringToUtf8ByteArray=function(e){for(var t=[],r=0,n=0;n<e.length;n++){var o=e.charCodeAt(n);128>o?t[r++]=o:(2048>o?t[r++]=o>>6|192:(55296==(64512&o)&&n+1<e.length&&56320==(64512&e.charCodeAt(n+1))?(o=65536+((1023&o)<<10)+(1023&e.charCodeAt(++n)),t[r++]=o>>18|240,t[r++]=o>>12&63|128):t[r++]=o>>12|224,t[r++]=o>>6&63|128),t[r++]=63&o|128)}return t},goog.crypt.utf8ByteArrayToString=function(e){for(var t=[],r=0,n=0;r<e.length;){var o=e[r++];if(128>o)t[n++]=String.fromCharCode(o);else if(191<o&&224>o){var i=e[r++];t[n++]=String.fromCharCode((31&o)<<6|63&i)}else if(239<o&&365>o){i=e[r++];var a=e[r++];o=((7&o)<<18|(63&i)<<12|(63&a)<<6|63&e[r++])-65536,t[n++]=String.fromCharCode(55296+(o>>10)),t[n++]=String.fromCharCode(56320+(1023&o))}else i=e[r++],a=e[r++],t[n++]=String.fromCharCode((15&o)<<12|(63&i)<<6|63&a)}return t.join("")},goog.crypt.xorByteArray=function(e,t){goog.asserts.assert(e.length==t.length,"XOR array lengths must match");for(var r=[],n=0;n<e.length;n++)r.push(e[n]^t[n]);return r},goog.dom.asserts={},goog.dom.asserts.assertIsLocation=function(e){if(goog.asserts.ENABLE_ASSERTS){var t=goog.dom.asserts.getWindow_(e);t&&(!e||!(e instanceof t.Location)&&e instanceof t.Element)&&goog.asserts.fail("Argument is not a Location (or a non-Element mock); got: %s",goog.dom.asserts.debugStringForType_(e))}return e},goog.dom.asserts.assertIsElementType_=function(e,t){if(goog.asserts.ENABLE_ASSERTS){var r=goog.dom.asserts.getWindow_(e);r&&void 0!==r[t]&&(e&&(e instanceof r[t]||!(e instanceof r.Location||e instanceof r.Element))||goog.asserts.fail("Argument is not a %s (or a non-Element, non-Location mock); got: %s",t,goog.dom.asserts.debugStringForType_(e)))}return e},goog.dom.asserts.assertIsHTMLAnchorElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLAnchorElement")},goog.dom.asserts.assertIsHTMLButtonElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLButtonElement")},goog.dom.asserts.assertIsHTMLLinkElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLLinkElement")},goog.dom.asserts.assertIsHTMLImageElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLImageElement")},goog.dom.asserts.assertIsHTMLAudioElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLAudioElement")},goog.dom.asserts.assertIsHTMLVideoElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLVideoElement")},goog.dom.asserts.assertIsHTMLInputElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLInputElement")},goog.dom.asserts.assertIsHTMLTextAreaElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLTextAreaElement")},goog.dom.asserts.assertIsHTMLCanvasElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLCanvasElement")},goog.dom.asserts.assertIsHTMLEmbedElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLEmbedElement")},goog.dom.asserts.assertIsHTMLFormElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLFormElement")},goog.dom.asserts.assertIsHTMLFrameElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLFrameElement")},goog.dom.asserts.assertIsHTMLIFrameElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLIFrameElement")},goog.dom.asserts.assertIsHTMLObjectElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLObjectElement")},goog.dom.asserts.assertIsHTMLScriptElement=function(e){return goog.dom.asserts.assertIsElementType_(e,"HTMLScriptElement")},goog.dom.asserts.debugStringForType_=function(e){if(!goog.isObject(e))return void 0===e?"undefined":null===e?"null":typeof e;try{return e.constructor.displayName||e.constructor.name||Object.prototype.toString.call(e)}catch(e){return"<object could not be stringified>"}},goog.dom.asserts.getWindow_=function(e){try{var t=e&&e.ownerDocument,r=t&&(t.defaultView||t.parentWindow);if((r=r||goog.global).Element&&r.Location)return r}catch(e){}return null},goog.functions={},goog.functions.constant=function(e){return function(){return e}},goog.functions.FALSE=function(){return!1},goog.functions.TRUE=function(){return!0},goog.functions.NULL=function(){return null},goog.functions.identity=function(e,t){return e},goog.functions.error=function(e){return function(){throw Error(e)}},goog.functions.fail=function(e){return function(){throw e}},goog.functions.lock=function(e,t){return t=t||0,function(){return e.apply(this,Array.prototype.slice.call(arguments,0,t))}},goog.functions.nth=function(e){return function(){return arguments[e]}},goog.functions.partialRight=function(e,t){var r=Array.prototype.slice.call(arguments,1);return function(){var t=Array.prototype.slice.call(arguments);return t.push.apply(t,r),e.apply(this,t)}},goog.functions.withReturnValue=function(e,t){return goog.functions.sequence(e,goog.functions.constant(t))},goog.functions.equalTo=function(e,t){return function(r){return t?e==r:e===r}},goog.functions.compose=function(e,t){var r=arguments,n=r.length;return function(){var e;n&&(e=r[n-1].apply(this,arguments));for(var t=n-2;0<=t;t--)e=r[t].call(this,e);return e}},goog.functions.sequence=function(e){var t=arguments,r=t.length;return function(){for(var e,n=0;n<r;n++)e=t[n].apply(this,arguments);return e}},goog.functions.and=function(e){var t=arguments,r=t.length;return function(){for(var e=0;e<r;e++)if(!t[e].apply(this,arguments))return!1;return!0}},goog.functions.or=function(e){var t=arguments,r=t.length;return function(){for(var e=0;e<r;e++)if(t[e].apply(this,arguments))return!0;return!1}},goog.functions.not=function(e){return function(){return!e.apply(this,arguments)}},goog.functions.create=function(e,t){var r=function(){};return r.prototype=e.prototype,r=new r,e.apply(r,Array.prototype.slice.call(arguments,1)),r},goog.functions.CACHE_RETURN_VALUE=!0,goog.functions.cacheReturnValue=function(e){var t,r=!1;return function(){return goog.functions.CACHE_RETURN_VALUE?(r||(t=e(),r=!0),t):e()}},goog.functions.once=function(e){var t=e;return function(){if(t){var e=t;t=null,e()}}},goog.functions.debounce=function(e,t,r){var n=0;return function(o){goog.global.clearTimeout(n);var i=arguments;n=goog.global.setTimeout((function(){e.apply(r,i)}),t)}},goog.functions.throttle=function(e,t,r){var n=0,o=!1,i=[],a=function(){n=0,o&&(o=!1,s())},s=function(){n=goog.global.setTimeout(a,t),e.apply(r,i)};return function(e){i=arguments,n?o=!0:s()}},goog.functions.rateLimit=function(e,t,r){var n=0,o=function(){n=0};return function(i){n||(n=goog.global.setTimeout(o,t),e.apply(r,arguments))}},goog.dom.HtmlElement=function(){},goog.dom.TagName=function(e){this.tagName_=e},goog.dom.TagName.prototype.toString=function(){return this.tagName_},goog.dom.TagName.A=new goog.dom.TagName("A"),goog.dom.TagName.ABBR=new goog.dom.TagName("ABBR"),goog.dom.TagName.ACRONYM=new goog.dom.TagName("ACRONYM"),goog.dom.TagName.ADDRESS=new goog.dom.TagName("ADDRESS"),goog.dom.TagName.APPLET=new goog.dom.TagName("APPLET"),goog.dom.TagName.AREA=new goog.dom.TagName("AREA"),goog.dom.TagName.ARTICLE=new goog.dom.TagName("ARTICLE"),goog.dom.TagName.ASIDE=new goog.dom.TagName("ASIDE"),goog.dom.TagName.AUDIO=new goog.dom.TagName("AUDIO"),goog.dom.TagName.B=new goog.dom.TagName("B"),goog.dom.TagName.BASE=new goog.dom.TagName("BASE"),goog.dom.TagName.BASEFONT=new goog.dom.TagName("BASEFONT"),goog.dom.TagName.BDI=new goog.dom.TagName("BDI"),goog.dom.TagName.BDO=new goog.dom.TagName("BDO"),goog.dom.TagName.BIG=new goog.dom.TagName("BIG"),goog.dom.TagName.BLOCKQUOTE=new goog.dom.TagName("BLOCKQUOTE"),goog.dom.TagName.BODY=new goog.dom.TagName("BODY"),goog.dom.TagName.BR=new goog.dom.TagName("BR"),goog.dom.TagName.BUTTON=new goog.dom.TagName("BUTTON"),goog.dom.TagName.CANVAS=new goog.dom.TagName("CANVAS"),goog.dom.TagName.CAPTION=new goog.dom.TagName("CAPTION"),goog.dom.TagName.CENTER=new goog.dom.TagName("CENTER"),goog.dom.TagName.CITE=new goog.dom.TagName("CITE"),goog.dom.TagName.CODE=new goog.dom.TagName("CODE"),goog.dom.TagName.COL=new goog.dom.TagName("COL"),goog.dom.TagName.COLGROUP=new goog.dom.TagName("COLGROUP"),goog.dom.TagName.COMMAND=new goog.dom.TagName("COMMAND"),goog.dom.TagName.DATA=new goog.dom.TagName("DATA"),goog.dom.TagName.DATALIST=new goog.dom.TagName("DATALIST"),goog.dom.TagName.DD=new goog.dom.TagName("DD"),goog.dom.TagName.DEL=new goog.dom.TagName("DEL"),goog.dom.TagName.DETAILS=new goog.dom.TagName("DETAILS"),goog.dom.TagName.DFN=new goog.dom.TagName("DFN"),goog.dom.TagName.DIALOG=new goog.dom.TagName("DIALOG"),goog.dom.TagName.DIR=new goog.dom.TagName("DIR"),goog.dom.TagName.DIV=new goog.dom.TagName("DIV"),goog.dom.TagName.DL=new goog.dom.TagName("DL"),goog.dom.TagName.DT=new goog.dom.TagName("DT"),goog.dom.TagName.EM=new goog.dom.TagName("EM"),goog.dom.TagName.EMBED=new goog.dom.TagName("EMBED"),goog.dom.TagName.FIELDSET=new goog.dom.TagName("FIELDSET"),goog.dom.TagName.FIGCAPTION=new goog.dom.TagName("FIGCAPTION"),goog.dom.TagName.FIGURE=new goog.dom.TagName("FIGURE"),goog.dom.TagName.FONT=new goog.dom.TagName("FONT"),goog.dom.TagName.FOOTER=new goog.dom.TagName("FOOTER"),goog.dom.TagName.FORM=new goog.dom.TagName("FORM"),goog.dom.TagName.FRAME=new goog.dom.TagName("FRAME"),goog.dom.TagName.FRAMESET=new goog.dom.TagName("FRAMESET"),goog.dom.TagName.H1=new goog.dom.TagName("H1"),goog.dom.TagName.H2=new goog.dom.TagName("H2"),goog.dom.TagName.H3=new goog.dom.TagName("H3"),goog.dom.TagName.H4=new goog.dom.TagName("H4"),goog.dom.TagName.H5=new goog.dom.TagName("H5"),goog.dom.TagName.H6=new goog.dom.TagName("H6"),goog.dom.TagName.HEAD=new goog.dom.TagName("HEAD"),goog.dom.TagName.HEADER=new goog.dom.TagName("HEADER"),goog.dom.TagName.HGROUP=new goog.dom.TagName("HGROUP"),goog.dom.TagName.HR=new goog.dom.TagName("HR"),goog.dom.TagName.HTML=new goog.dom.TagName("HTML"),goog.dom.TagName.I=new goog.dom.TagName("I"),goog.dom.TagName.IFRAME=new goog.dom.TagName("IFRAME"),goog.dom.TagName.IMG=new goog.dom.TagName("IMG"),goog.dom.TagName.INPUT=new goog.dom.TagName("INPUT"),goog.dom.TagName.INS=new goog.dom.TagName("INS"),goog.dom.TagName.ISINDEX=new goog.dom.TagName("ISINDEX"),goog.dom.TagName.KBD=new goog.dom.TagName("KBD"),goog.dom.TagName.KEYGEN=new goog.dom.TagName("KEYGEN"),goog.dom.TagName.LABEL=new goog.dom.TagName("LABEL"),goog.dom.TagName.LEGEND=new goog.dom.TagName("LEGEND"),goog.dom.TagName.LI=new goog.dom.TagName("LI"),goog.dom.TagName.LINK=new goog.dom.TagName("LINK"),goog.dom.TagName.MAIN=new goog.dom.TagName("MAIN"),goog.dom.TagName.MAP=new goog.dom.TagName("MAP"),goog.dom.TagName.MARK=new goog.dom.TagName("MARK"),goog.dom.TagName.MATH=new goog.dom.TagName("MATH"),goog.dom.TagName.MENU=new goog.dom.TagName("MENU"),goog.dom.TagName.MENUITEM=new goog.dom.TagName("MENUITEM"),goog.dom.TagName.META=new goog.dom.TagName("META"),goog.dom.TagName.METER=new goog.dom.TagName("METER"),goog.dom.TagName.NAV=new goog.dom.TagName("NAV"),goog.dom.TagName.NOFRAMES=new goog.dom.TagName("NOFRAMES"),goog.dom.TagName.NOSCRIPT=new goog.dom.TagName("NOSCRIPT"),goog.dom.TagName.OBJECT=new goog.dom.TagName("OBJECT"),goog.dom.TagName.OL=new goog.dom.TagName("OL"),goog.dom.TagName.OPTGROUP=new goog.dom.TagName("OPTGROUP"),goog.dom.TagName.OPTION=new goog.dom.TagName("OPTION"),goog.dom.TagName.OUTPUT=new goog.dom.TagName("OUTPUT"),goog.dom.TagName.P=new goog.dom.TagName("P"),goog.dom.TagName.PARAM=new goog.dom.TagName("PARAM"),goog.dom.TagName.PICTURE=new goog.dom.TagName("PICTURE"),goog.dom.TagName.PRE=new goog.dom.TagName("PRE"),goog.dom.TagName.PROGRESS=new goog.dom.TagName("PROGRESS"),goog.dom.TagName.Q=new goog.dom.TagName("Q"),goog.dom.TagName.RP=new goog.dom.TagName("RP"),goog.dom.TagName.RT=new goog.dom.TagName("RT"),goog.dom.TagName.RTC=new goog.dom.TagName("RTC"),goog.dom.TagName.RUBY=new goog.dom.TagName("RUBY"),goog.dom.TagName.S=new goog.dom.TagName("S"),goog.dom.TagName.SAMP=new goog.dom.TagName("SAMP"),goog.dom.TagName.SCRIPT=new goog.dom.TagName("SCRIPT"),goog.dom.TagName.SECTION=new goog.dom.TagName("SECTION"),goog.dom.TagName.SELECT=new goog.dom.TagName("SELECT"),goog.dom.TagName.SMALL=new goog.dom.TagName("SMALL"),goog.dom.TagName.SOURCE=new goog.dom.TagName("SOURCE"),goog.dom.TagName.SPAN=new goog.dom.TagName("SPAN"),goog.dom.TagName.STRIKE=new goog.dom.TagName("STRIKE"),goog.dom.TagName.STRONG=new goog.dom.TagName("STRONG"),goog.dom.TagName.STYLE=new goog.dom.TagName("STYLE"),goog.dom.TagName.SUB=new goog.dom.TagName("SUB"),goog.dom.TagName.SUMMARY=new goog.dom.TagName("SUMMARY"),goog.dom.TagName.SUP=new goog.dom.TagName("SUP"),goog.dom.TagName.SVG=new goog.dom.TagName("SVG"),goog.dom.TagName.TABLE=new goog.dom.TagName("TABLE"),goog.dom.TagName.TBODY=new goog.dom.TagName("TBODY"),goog.dom.TagName.TD=new goog.dom.TagName("TD"),goog.dom.TagName.TEMPLATE=new goog.dom.TagName("TEMPLATE"),goog.dom.TagName.TEXTAREA=new goog.dom.TagName("TEXTAREA"),goog.dom.TagName.TFOOT=new goog.dom.TagName("TFOOT"),goog.dom.TagName.TH=new goog.dom.TagName("TH"),goog.dom.TagName.THEAD=new goog.dom.TagName("THEAD"),goog.dom.TagName.TIME=new goog.dom.TagName("TIME"),goog.dom.TagName.TITLE=new goog.dom.TagName("TITLE"),goog.dom.TagName.TR=new goog.dom.TagName("TR"),goog.dom.TagName.TRACK=new goog.dom.TagName("TRACK"),goog.dom.TagName.TT=new goog.dom.TagName("TT"),goog.dom.TagName.U=new goog.dom.TagName("U"),goog.dom.TagName.UL=new goog.dom.TagName("UL"),goog.dom.TagName.VAR=new goog.dom.TagName("VAR"),goog.dom.TagName.VIDEO=new goog.dom.TagName("VIDEO"),goog.dom.TagName.WBR=new goog.dom.TagName("WBR"),goog.dom.tags={},goog.dom.tags.VOID_TAGS_={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},goog.dom.tags.isVoidTag=function(e){return!0===goog.dom.tags.VOID_TAGS_[e]},goog.html={},goog.html.trustedtypes={},goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY=goog.TRUSTED_TYPES_POLICY_NAME?goog.createTrustedTypesPolicy(goog.TRUSTED_TYPES_POLICY_NAME+"#html"):null,goog.string={},goog.string.TypedString=function(){},goog.string.Const=function(e,t){this.stringConstValueWithSecurityContract__googStringSecurityPrivate_=e===goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_&&t||"",this.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_=goog.string.Const.TYPE_MARKER_},goog.string.Const.prototype.implementsGoogStringTypedString=!0,goog.string.Const.prototype.getTypedStringValue=function(){return this.stringConstValueWithSecurityContract__googStringSecurityPrivate_},goog.DEBUG&&(goog.string.Const.prototype.toString=function(){return"Const{"+this.stringConstValueWithSecurityContract__googStringSecurityPrivate_+"}"}),goog.string.Const.unwrap=function(e){return e instanceof goog.string.Const&&e.constructor===goog.string.Const&&e.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_===goog.string.Const.TYPE_MARKER_?e.stringConstValueWithSecurityContract__googStringSecurityPrivate_:(goog.asserts.fail("expected object of type Const, got '"+e+"'"),"type_error:Const")},goog.string.Const.from=function(e){return new goog.string.Const(goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_,e)},goog.string.Const.TYPE_MARKER_={},goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_={},goog.string.Const.EMPTY=goog.string.Const.from(""),goog.html.SafeScript=function(){this.privateDoNotAccessOrElseSafeScriptWrappedValue_="",this.SAFE_SCRIPT_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeScript.prototype.implementsGoogStringTypedString=!0,goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeScript.fromConstant=function(e){return 0===(e=goog.string.Const.unwrap(e)).length?goog.html.SafeScript.EMPTY:goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(e)},goog.html.SafeScript.fromConstantAndArgs=function(e,t){for(var r=[],n=1;n<arguments.length;n++)r.push(goog.html.SafeScript.stringify_(arguments[n]));return goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse("("+goog.string.Const.unwrap(e)+")("+r.join(", ")+");")},goog.html.SafeScript.fromJson=function(e){return goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(goog.html.SafeScript.stringify_(e))},goog.html.SafeScript.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeScriptWrappedValue_.toString()},goog.DEBUG&&(goog.html.SafeScript.prototype.toString=function(){return"SafeScript{"+this.privateDoNotAccessOrElseSafeScriptWrappedValue_+"}"}),goog.html.SafeScript.unwrap=function(e){return goog.html.SafeScript.unwrapTrustedScript(e).toString()},goog.html.SafeScript.unwrapTrustedScript=function(e){return e instanceof goog.html.SafeScript&&e.constructor===goog.html.SafeScript&&e.SAFE_SCRIPT_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?e.privateDoNotAccessOrElseSafeScriptWrappedValue_:(goog.asserts.fail("expected object of type SafeScript, got '"+e+"' of type "+goog.typeOf(e)),"type_error:SafeScript")},goog.html.SafeScript.stringify_=function(e){return JSON.stringify(e).replace(/</g,"\\x3c")},goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse=function(e){return(new goog.html.SafeScript).initSecurityPrivateDoNotAccessOrElse_(e)},goog.html.SafeScript.prototype.initSecurityPrivateDoNotAccessOrElse_=function(e){return this.privateDoNotAccessOrElseSafeScriptWrappedValue_=goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY?goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY.createScript(e):e,this},goog.html.SafeScript.EMPTY=goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(""),goog.fs={},goog.fs.url={},goog.fs.url.createObjectUrl=function(e){return goog.fs.url.getUrlObject_().createObjectURL(e)},goog.fs.url.revokeObjectUrl=function(e){goog.fs.url.getUrlObject_().revokeObjectURL(e)},goog.fs.url.UrlObject_=function(){},goog.fs.url.UrlObject_.prototype.createObjectURL=function(e){},goog.fs.url.UrlObject_.prototype.revokeObjectURL=function(e){},goog.fs.url.getUrlObject_=function(){var e=goog.fs.url.findUrlObject_();if(null!=e)return e;throw Error("This browser doesn't seem to support blob URLs")},goog.fs.url.findUrlObject_=function(){return void 0!==goog.global.URL&&void 0!==goog.global.URL.createObjectURL?goog.global.URL:void 0!==goog.global.webkitURL&&void 0!==goog.global.webkitURL.createObjectURL?goog.global.webkitURL:void 0!==goog.global.createObjectURL?goog.global:null},goog.fs.url.browserSupportsObjectUrls=function(){return null!=goog.fs.url.findUrlObject_()},goog.fs.blob={},goog.fs.blob.getBlob=function(e){var t=goog.global.BlobBuilder||goog.global.WebKitBlobBuilder;if(void 0!==t){t=new t;for(var r=0;r<arguments.length;r++)t.append(arguments[r]);return t.getBlob()}return goog.fs.blob.getBlobWithProperties(goog.array.toArray(arguments))},goog.fs.blob.getBlobWithProperties=function(e,t,r){var n=goog.global.BlobBuilder||goog.global.WebKitBlobBuilder;if(void 0!==n){n=new n;for(var o=0;o<e.length;o++)n.append(e[o],r);return n.getBlob(t)}if(void 0!==goog.global.Blob)return n={},t&&(n.type=t),r&&(n.endings=r),new Blob(e,n);throw Error("This browser doesn't seem to support creating Blobs")},goog.i18n={},goog.i18n.bidi={},goog.i18n.bidi.FORCE_RTL=!1,goog.i18n.bidi.IS_RTL=goog.i18n.bidi.FORCE_RTL||("ar"==goog.LOCALE.substring(0,2).toLowerCase()||"fa"==goog.LOCALE.substring(0,2).toLowerCase()||"he"==goog.LOCALE.substring(0,2).toLowerCase()||"iw"==goog.LOCALE.substring(0,2).toLowerCase()||"ps"==goog.LOCALE.substring(0,2).toLowerCase()||"sd"==goog.LOCALE.substring(0,2).toLowerCase()||"ug"==goog.LOCALE.substring(0,2).toLowerCase()||"ur"==goog.LOCALE.substring(0,2).toLowerCase()||"yi"==goog.LOCALE.substring(0,2).toLowerCase())&&(2==goog.LOCALE.length||"-"==goog.LOCALE.substring(2,3)||"_"==goog.LOCALE.substring(2,3))||3<=goog.LOCALE.length&&"ckb"==goog.LOCALE.substring(0,3).toLowerCase()&&(3==goog.LOCALE.length||"-"==goog.LOCALE.substring(3,4)||"_"==goog.LOCALE.substring(3,4))||7<=goog.LOCALE.length&&("-"==goog.LOCALE.substring(2,3)||"_"==goog.LOCALE.substring(2,3))&&("adlm"==goog.LOCALE.substring(3,7).toLowerCase()||"arab"==goog.LOCALE.substring(3,7).toLowerCase()||"hebr"==goog.LOCALE.substring(3,7).toLowerCase()||"nkoo"==goog.LOCALE.substring(3,7).toLowerCase()||"rohg"==goog.LOCALE.substring(3,7).toLowerCase()||"thaa"==goog.LOCALE.substring(3,7).toLowerCase())||8<=goog.LOCALE.length&&("-"==goog.LOCALE.substring(3,4)||"_"==goog.LOCALE.substring(3,4))&&("adlm"==goog.LOCALE.substring(4,8).toLowerCase()||"arab"==goog.LOCALE.substring(4,8).toLowerCase()||"hebr"==goog.LOCALE.substring(4,8).toLowerCase()||"nkoo"==goog.LOCALE.substring(4,8).toLowerCase()||"rohg"==goog.LOCALE.substring(4,8).toLowerCase()||"thaa"==goog.LOCALE.substring(4,8).toLowerCase()),goog.i18n.bidi.Format={LRE:"‪",RLE:"‫",PDF:"‬",LRM:"‎",RLM:"‏"},goog.i18n.bidi.Dir={LTR:1,RTL:-1,NEUTRAL:0},goog.i18n.bidi.RIGHT="right",goog.i18n.bidi.LEFT="left",goog.i18n.bidi.I18N_RIGHT=goog.i18n.bidi.IS_RTL?goog.i18n.bidi.LEFT:goog.i18n.bidi.RIGHT,goog.i18n.bidi.I18N_LEFT=goog.i18n.bidi.IS_RTL?goog.i18n.bidi.RIGHT:goog.i18n.bidi.LEFT,goog.i18n.bidi.toDir=function(e,t){return"number"==typeof e?0<e?goog.i18n.bidi.Dir.LTR:0>e?goog.i18n.bidi.Dir.RTL:t?null:goog.i18n.bidi.Dir.NEUTRAL:null==e?null:e?goog.i18n.bidi.Dir.RTL:goog.i18n.bidi.Dir.LTR},goog.i18n.bidi.ltrChars_="A-Za-zÀ-ÖØ-öø-ʸ̀-֐ऀ-῿‎Ⰰ-\ud801\ud804-\ud839\ud83c-\udbff豈-﬜︀-﹯﻽-￿",goog.i18n.bidi.rtlChars_="֑-ۯۺ-ࣿ‏\ud802-\ud803\ud83a-\ud83bיִ-﷿ﹰ-ﻼ",goog.i18n.bidi.htmlSkipReg_=/<[^>]*>|&[^;]+;/g,goog.i18n.bidi.stripHtmlIfNeeded_=function(e,t){return t?e.replace(goog.i18n.bidi.htmlSkipReg_,""):e},goog.i18n.bidi.rtlCharReg_=new RegExp("["+goog.i18n.bidi.rtlChars_+"]"),goog.i18n.bidi.ltrCharReg_=new RegExp("["+goog.i18n.bidi.ltrChars_+"]"),goog.i18n.bidi.hasAnyRtl=function(e,t){return goog.i18n.bidi.rtlCharReg_.test(goog.i18n.bidi.stripHtmlIfNeeded_(e,t))},goog.i18n.bidi.hasRtlChar=goog.i18n.bidi.hasAnyRtl,goog.i18n.bidi.hasAnyLtr=function(e,t){return goog.i18n.bidi.ltrCharReg_.test(goog.i18n.bidi.stripHtmlIfNeeded_(e,t))},goog.i18n.bidi.ltrRe_=new RegExp("^["+goog.i18n.bidi.ltrChars_+"]"),goog.i18n.bidi.rtlRe_=new RegExp("^["+goog.i18n.bidi.rtlChars_+"]"),goog.i18n.bidi.isRtlChar=function(e){return goog.i18n.bidi.rtlRe_.test(e)},goog.i18n.bidi.isLtrChar=function(e){return goog.i18n.bidi.ltrRe_.test(e)},goog.i18n.bidi.isNeutralChar=function(e){return!goog.i18n.bidi.isLtrChar(e)&&!goog.i18n.bidi.isRtlChar(e)},goog.i18n.bidi.ltrDirCheckRe_=new RegExp("^[^"+goog.i18n.bidi.rtlChars_+"]*["+goog.i18n.bidi.ltrChars_+"]"),goog.i18n.bidi.rtlDirCheckRe_=new RegExp("^[^"+goog.i18n.bidi.ltrChars_+"]*["+goog.i18n.bidi.rtlChars_+"]"),goog.i18n.bidi.startsWithRtl=function(e,t){return goog.i18n.bidi.rtlDirCheckRe_.test(goog.i18n.bidi.stripHtmlIfNeeded_(e,t))},goog.i18n.bidi.isRtlText=goog.i18n.bidi.startsWithRtl,goog.i18n.bidi.startsWithLtr=function(e,t){return goog.i18n.bidi.ltrDirCheckRe_.test(goog.i18n.bidi.stripHtmlIfNeeded_(e,t))},goog.i18n.bidi.isLtrText=goog.i18n.bidi.startsWithLtr,goog.i18n.bidi.isRequiredLtrRe_=/^http:\/\/.*/,goog.i18n.bidi.isNeutralText=function(e,t){return e=goog.i18n.bidi.stripHtmlIfNeeded_(e,t),goog.i18n.bidi.isRequiredLtrRe_.test(e)||!goog.i18n.bidi.hasAnyLtr(e)&&!goog.i18n.bidi.hasAnyRtl(e)},goog.i18n.bidi.ltrExitDirCheckRe_=new RegExp("["+goog.i18n.bidi.ltrChars_+"][^"+goog.i18n.bidi.rtlChars_+"]*$"),goog.i18n.bidi.rtlExitDirCheckRe_=new RegExp("["+goog.i18n.bidi.rtlChars_+"][^"+goog.i18n.bidi.ltrChars_+"]*$"),goog.i18n.bidi.endsWithLtr=function(e,t){return goog.i18n.bidi.ltrExitDirCheckRe_.test(goog.i18n.bidi.stripHtmlIfNeeded_(e,t))},goog.i18n.bidi.isLtrExitText=goog.i18n.bidi.endsWithLtr,goog.i18n.bidi.endsWithRtl=function(e,t){return goog.i18n.bidi.rtlExitDirCheckRe_.test(goog.i18n.bidi.stripHtmlIfNeeded_(e,t))},goog.i18n.bidi.isRtlExitText=goog.i18n.bidi.endsWithRtl,goog.i18n.bidi.rtlLocalesRe_=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i,goog.i18n.bidi.isRtlLanguage=function(e){return goog.i18n.bidi.rtlLocalesRe_.test(e)},goog.i18n.bidi.bracketGuardTextRe_=/(\(.*?\)+)|(\[.*?\]+)|(\{.*?\}+)|(<.*?>+)/g,goog.i18n.bidi.guardBracketInText=function(e,t){return t=(void 0===t?goog.i18n.bidi.hasAnyRtl(e):t)?goog.i18n.bidi.Format.RLM:goog.i18n.bidi.Format.LRM,e.replace(goog.i18n.bidi.bracketGuardTextRe_,t+"$&"+t)},goog.i18n.bidi.enforceRtlInHtml=function(e){return"<"==e.charAt(0)?e.replace(/<\w+/,"$& dir=rtl"):"\n<span dir=rtl>"+e+"</span>"},goog.i18n.bidi.enforceRtlInText=function(e){return goog.i18n.bidi.Format.RLE+e+goog.i18n.bidi.Format.PDF},goog.i18n.bidi.enforceLtrInHtml=function(e){return"<"==e.charAt(0)?e.replace(/<\w+/,"$& dir=ltr"):"\n<span dir=ltr>"+e+"</span>"},goog.i18n.bidi.enforceLtrInText=function(e){return goog.i18n.bidi.Format.LRE+e+goog.i18n.bidi.Format.PDF},goog.i18n.bidi.dimensionsRe_=/:\s*([.\d][.\w]*)\s+([.\d][.\w]*)\s+([.\d][.\w]*)\s+([.\d][.\w]*)/g,goog.i18n.bidi.leftRe_=/left/gi,goog.i18n.bidi.rightRe_=/right/gi,goog.i18n.bidi.tempRe_=/%%%%/g,goog.i18n.bidi.mirrorCSS=function(e){return e.replace(goog.i18n.bidi.dimensionsRe_,":$1 $4 $3 $2").replace(goog.i18n.bidi.leftRe_,"%%%%").replace(goog.i18n.bidi.rightRe_,goog.i18n.bidi.LEFT).replace(goog.i18n.bidi.tempRe_,goog.i18n.bidi.RIGHT)},goog.i18n.bidi.doubleQuoteSubstituteRe_=/([\u0591-\u05f2])"/g,goog.i18n.bidi.singleQuoteSubstituteRe_=/([\u0591-\u05f2])'/g,goog.i18n.bidi.normalizeHebrewQuote=function(e){return e.replace(goog.i18n.bidi.doubleQuoteSubstituteRe_,"$1״").replace(goog.i18n.bidi.singleQuoteSubstituteRe_,"$1׳")},goog.i18n.bidi.wordSeparatorRe_=/\s+/,goog.i18n.bidi.hasNumeralsRe_=/[\d\u06f0-\u06f9]/,goog.i18n.bidi.rtlDetectionThreshold_=.4,goog.i18n.bidi.estimateDirection=function(e,t){var r=0,n=0,o=!1;for(e=goog.i18n.bidi.stripHtmlIfNeeded_(e,t).split(goog.i18n.bidi.wordSeparatorRe_),t=0;t<e.length;t++){var i=e[t];goog.i18n.bidi.startsWithRtl(i)?(r++,n++):goog.i18n.bidi.isRequiredLtrRe_.test(i)?o=!0:goog.i18n.bidi.hasAnyLtr(i)?n++:goog.i18n.bidi.hasNumeralsRe_.test(i)&&(o=!0)}return 0==n?o?goog.i18n.bidi.Dir.LTR:goog.i18n.bidi.Dir.NEUTRAL:r/n>goog.i18n.bidi.rtlDetectionThreshold_?goog.i18n.bidi.Dir.RTL:goog.i18n.bidi.Dir.LTR},goog.i18n.bidi.detectRtlDirectionality=function(e,t){return goog.i18n.bidi.estimateDirection(e,t)==goog.i18n.bidi.Dir.RTL},goog.i18n.bidi.setElementDirAndAlign=function(e,t){e&&(t=goog.i18n.bidi.toDir(t))&&(e.style.textAlign=t==goog.i18n.bidi.Dir.RTL?goog.i18n.bidi.RIGHT:goog.i18n.bidi.LEFT,e.dir=t==goog.i18n.bidi.Dir.RTL?"rtl":"ltr")},goog.i18n.bidi.setElementDirByTextDirectionality=function(e,t){switch(goog.i18n.bidi.estimateDirection(t)){case goog.i18n.bidi.Dir.LTR:e.dir="ltr";break;case goog.i18n.bidi.Dir.RTL:e.dir="rtl";break;default:e.removeAttribute("dir")}},goog.i18n.bidi.DirectionalString=function(){},goog.html.TrustedResourceUrl=function(e,t){this.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_=e===goog.html.TrustedResourceUrl.CONSTRUCTOR_TOKEN_PRIVATE_&&t||"",this.TRUSTED_RESOURCE_URL_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.TrustedResourceUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.TrustedResourceUrl.prototype.implementsGoogStringTypedString=!0,goog.html.TrustedResourceUrl.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_.toString()},goog.html.TrustedResourceUrl.prototype.implementsGoogI18nBidiDirectionalString=!0,goog.html.TrustedResourceUrl.prototype.getDirection=function(){return goog.i18n.bidi.Dir.LTR},goog.html.TrustedResourceUrl.prototype.cloneWithParams=function(e,t){var r=goog.html.TrustedResourceUrl.unwrap(this),n=(r=goog.html.TrustedResourceUrl.URL_PARAM_PARSER_.exec(r))[3]||"";return goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(r[1]+goog.html.TrustedResourceUrl.stringifyParams_("?",r[2]||"",e)+goog.html.TrustedResourceUrl.stringifyParams_("#",n,t))},goog.DEBUG&&(goog.html.TrustedResourceUrl.prototype.toString=function(){return"TrustedResourceUrl{"+this.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_+"}"}),goog.html.TrustedResourceUrl.unwrap=function(e){return goog.html.TrustedResourceUrl.unwrapTrustedScriptURL(e).toString()},goog.html.TrustedResourceUrl.unwrapTrustedScriptURL=function(e){return e instanceof goog.html.TrustedResourceUrl&&e.constructor===goog.html.TrustedResourceUrl&&e.TRUSTED_RESOURCE_URL_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.TrustedResourceUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?e.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_:(goog.asserts.fail("expected object of type TrustedResourceUrl, got '"+e+"' of type "+goog.typeOf(e)),"type_error:TrustedResourceUrl")},goog.html.TrustedResourceUrl.format=function(e,t){var r=goog.string.Const.unwrap(e);if(!goog.html.TrustedResourceUrl.BASE_URL_.test(r))throw Error("Invalid TrustedResourceUrl format: "+r);return e=r.replace(goog.html.TrustedResourceUrl.FORMAT_MARKER_,(function(e,n){if(!Object.prototype.hasOwnProperty.call(t,n))throw Error('Found marker, "'+n+'", in format string, "'+r+'", but no valid label mapping found in args: '+JSON.stringify(t));return(e=t[n])instanceof goog.string.Const?goog.string.Const.unwrap(e):encodeURIComponent(String(e))})),goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.TrustedResourceUrl.FORMAT_MARKER_=/%{(\w+)}/g,goog.html.TrustedResourceUrl.BASE_URL_=/^((https:)?\/\/[0-9a-z.:[\]-]+\/|\/[^/\\]|[^:/\\%]+\/|[^:/\\%]*[?#]|about:blank#)/i,goog.html.TrustedResourceUrl.URL_PARAM_PARSER_=/^([^?#]*)(\?[^#]*)?(#[\s\S]*)?/,goog.html.TrustedResourceUrl.formatWithParams=function(e,t,r,n){return goog.html.TrustedResourceUrl.format(e,t).cloneWithParams(r,n)},goog.html.TrustedResourceUrl.fromConstant=function(e){return goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(goog.string.Const.unwrap(e))},goog.html.TrustedResourceUrl.fromConstants=function(e){for(var t="",r=0;r<e.length;r++)t+=goog.string.Const.unwrap(e[r]);return goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(t)},goog.html.TrustedResourceUrl.fromSafeScript=function(e){return e=goog.fs.blob.getBlobWithProperties([goog.html.SafeScript.unwrap(e)],"text/javascript"),e=goog.fs.url.createObjectUrl(e),goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.TrustedResourceUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse=function(e){return e=goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY?goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY.createScriptURL(e):e,new goog.html.TrustedResourceUrl(goog.html.TrustedResourceUrl.CONSTRUCTOR_TOKEN_PRIVATE_,e)},goog.html.TrustedResourceUrl.stringifyParams_=function(e,t,r){if(null==r)return t;if("string"==typeof r)return r?e+encodeURIComponent(r):"";for(var n in r){var o=r[n];o=Array.isArray(o)?o:[o];for(var i=0;i<o.length;i++){var a=o[i];null!=a&&(t||(t=e),t+=(t.length>e.length?"&":"")+encodeURIComponent(n)+"="+encodeURIComponent(String(a)))}}return t},goog.html.TrustedResourceUrl.CONSTRUCTOR_TOKEN_PRIVATE_={},goog.string.internal={},goog.string.internal.startsWith=function(e,t){return 0==e.lastIndexOf(t,0)},goog.string.internal.endsWith=function(e,t){var r=e.length-t.length;return 0<=r&&e.indexOf(t,r)==r},goog.string.internal.caseInsensitiveStartsWith=function(e,t){return 0==goog.string.internal.caseInsensitiveCompare(t,e.substr(0,t.length))},goog.string.internal.caseInsensitiveEndsWith=function(e,t){return 0==goog.string.internal.caseInsensitiveCompare(t,e.substr(e.length-t.length,t.length))},goog.string.internal.caseInsensitiveEquals=function(e,t){return e.toLowerCase()==t.toLowerCase()},goog.string.internal.isEmptyOrWhitespace=function(e){return/^[\s\xa0]*$/.test(e)},goog.string.internal.trim=goog.TRUSTED_SITE&&String.prototype.trim?function(e){return e.trim()}:function(e){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(e)[1]},goog.string.internal.caseInsensitiveCompare=function(e,t){return(e=String(e).toLowerCase())<(t=String(t).toLowerCase())?-1:e==t?0:1},goog.string.internal.newLineToBr=function(e,t){return e.replace(/(\r\n|\r|\n)/g,t?"<br />":"<br>")},goog.string.internal.htmlEscape=function(e,t){if(t)e=e.replace(goog.string.internal.AMP_RE_,"&amp;").replace(goog.string.internal.LT_RE_,"&lt;").replace(goog.string.internal.GT_RE_,"&gt;").replace(goog.string.internal.QUOT_RE_,"&quot;").replace(goog.string.internal.SINGLE_QUOTE_RE_,"&#39;").replace(goog.string.internal.NULL_RE_,"&#0;");else{if(!goog.string.internal.ALL_RE_.test(e))return e;-1!=e.indexOf("&")&&(e=e.replace(goog.string.internal.AMP_RE_,"&amp;")),-1!=e.indexOf("<")&&(e=e.replace(goog.string.internal.LT_RE_,"&lt;")),-1!=e.indexOf(">")&&(e=e.replace(goog.string.internal.GT_RE_,"&gt;")),-1!=e.indexOf('"')&&(e=e.replace(goog.string.internal.QUOT_RE_,"&quot;")),-1!=e.indexOf("'")&&(e=e.replace(goog.string.internal.SINGLE_QUOTE_RE_,"&#39;")),-1!=e.indexOf("\0")&&(e=e.replace(goog.string.internal.NULL_RE_,"&#0;"))}return e},goog.string.internal.AMP_RE_=/&/g,goog.string.internal.LT_RE_=/</g,goog.string.internal.GT_RE_=/>/g,goog.string.internal.QUOT_RE_=/"/g,goog.string.internal.SINGLE_QUOTE_RE_=/'/g,goog.string.internal.NULL_RE_=/\x00/g,goog.string.internal.ALL_RE_=/[\x00&<>"']/,goog.string.internal.whitespaceEscape=function(e,t){return goog.string.internal.newLineToBr(e.replace(/  /g," &#160;"),t)},goog.string.internal.contains=function(e,t){return-1!=e.indexOf(t)},goog.string.internal.caseInsensitiveContains=function(e,t){return goog.string.internal.contains(e.toLowerCase(),t.toLowerCase())},goog.string.internal.compareVersions=function(e,t){var r=0;e=goog.string.internal.trim(String(e)).split("."),t=goog.string.internal.trim(String(t)).split(".");for(var n=Math.max(e.length,t.length),o=0;0==r&&o<n;o++){var i=e[o]||"",a=t[o]||"";do{if(i=/(\d*)(\D*)(.*)/.exec(i)||["","","",""],a=/(\d*)(\D*)(.*)/.exec(a)||["","","",""],0==i[0].length&&0==a[0].length)break;r=0==i[1].length?0:parseInt(i[1],10);var s=0==a[1].length?0:parseInt(a[1],10);r=goog.string.internal.compareElements_(r,s)||goog.string.internal.compareElements_(0==i[2].length,0==a[2].length)||goog.string.internal.compareElements_(i[2],a[2]),i=i[3],a=a[3]}while(0==r)}return r},goog.string.internal.compareElements_=function(e,t){return e<t?-1:e>t?1:0},goog.html.SafeUrl=function(e,t){this.privateDoNotAccessOrElseSafeUrlWrappedValue_=e===goog.html.SafeUrl.CONSTRUCTOR_TOKEN_PRIVATE_&&t||"",this.SAFE_URL_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeUrl.INNOCUOUS_STRING="about:invalid#zClosurez",goog.html.SafeUrl.prototype.implementsGoogStringTypedString=!0,goog.html.SafeUrl.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeUrlWrappedValue_.toString()},goog.html.SafeUrl.prototype.implementsGoogI18nBidiDirectionalString=!0,goog.html.SafeUrl.prototype.getDirection=function(){return goog.i18n.bidi.Dir.LTR},goog.DEBUG&&(goog.html.SafeUrl.prototype.toString=function(){return"SafeUrl{"+this.privateDoNotAccessOrElseSafeUrlWrappedValue_+"}"}),goog.html.SafeUrl.unwrap=function(e){return e instanceof goog.html.SafeUrl&&e.constructor===goog.html.SafeUrl&&e.SAFE_URL_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?e.privateDoNotAccessOrElseSafeUrlWrappedValue_:(goog.asserts.fail("expected object of type SafeUrl, got '"+e+"' of type "+goog.typeOf(e)),"type_error:SafeUrl")},goog.html.SafeUrl.fromConstant=function(e){return goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(goog.string.Const.unwrap(e))},goog.html.SAFE_MIME_TYPE_PATTERN_=/^(?:audio\/(?:3gpp2|3gpp|aac|L16|midi|mp3|mp4|mpeg|oga|ogg|opus|x-m4a|x-matroska|x-wav|wav|webm)|image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp|x-icon)|text\/csv|video\/(?:mpeg|mp4|ogg|webm|quicktime|x-matroska))(?:;\w+=(?:\w+|"[\w;,= ]+"))*$/i,goog.html.SafeUrl.isSafeMimeType=function(e){return goog.html.SAFE_MIME_TYPE_PATTERN_.test(e)},goog.html.SafeUrl.fromBlob=function(e){return e=goog.html.SafeUrl.isSafeMimeType(e.type)?goog.fs.url.createObjectUrl(e):goog.html.SafeUrl.INNOCUOUS_STRING,goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.SafeUrl.fromMediaSource=function(e){return goog.asserts.assert("MediaSource"in goog.global,"No support for MediaSource"),e=e instanceof MediaSource?goog.fs.url.createObjectUrl(e):goog.html.SafeUrl.INNOCUOUS_STRING,goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.DATA_URL_PATTERN_=/^data:(.*);base64,[a-z0-9+\/]+=*$/i,goog.html.SafeUrl.fromDataUrl=function(e){var t=(e=e.replace(/(%0A|%0D)/g,"")).match(goog.html.DATA_URL_PATTERN_);return t=t&&goog.html.SafeUrl.isSafeMimeType(t[1]),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(t?e:goog.html.SafeUrl.INNOCUOUS_STRING)},goog.html.SafeUrl.fromTelUrl=function(e){return goog.string.internal.caseInsensitiveStartsWith(e,"tel:")||(e=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.SIP_URL_PATTERN_=/^sip[s]?:[+a-z0-9_.!$%&'*\/=^`{|}~-]+@([a-z0-9-]+\.)+[a-z0-9]{2,63}$/i,goog.html.SafeUrl.fromSipUrl=function(e){return goog.html.SIP_URL_PATTERN_.test(decodeURIComponent(e))||(e=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.SafeUrl.fromFacebookMessengerUrl=function(e){return goog.string.internal.caseInsensitiveStartsWith(e,"fb-messenger://share")||(e=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.SafeUrl.fromWhatsAppUrl=function(e){return goog.string.internal.caseInsensitiveStartsWith(e,"whatsapp://send")||(e=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.SafeUrl.fromSmsUrl=function(e){return goog.string.internal.caseInsensitiveStartsWith(e,"sms:")&&goog.html.SafeUrl.isSmsUrlBodyValid_(e)||(e=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.SafeUrl.isSmsUrlBodyValid_=function(e){var t=e.indexOf("#");if(0<t&&(e=e.substring(0,t)),!(t=e.match(/[?&]body=/gi)))return!0;if(1<t.length)return!1;if(!(e=e.match(/[?&]body=([^&]*)/)[1]))return!0;try{decodeURIComponent(e)}catch(e){return!1}return/^(?:[a-z0-9\-_.~]|%[0-9a-f]{2})+$/i.test(e)},goog.html.SafeUrl.fromSshUrl=function(e){return goog.string.internal.caseInsensitiveStartsWith(e,"ssh://")||(e=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e)},goog.html.SafeUrl.sanitizeChromeExtensionUrl=function(e,t){return goog.html.SafeUrl.sanitizeExtensionUrl_(/^chrome-extension:\/\/([^\/]+)\//,e,t)},goog.html.SafeUrl.sanitizeFirefoxExtensionUrl=function(e,t){return goog.html.SafeUrl.sanitizeExtensionUrl_(/^moz-extension:\/\/([^\/]+)\//,e,t)},goog.html.SafeUrl.sanitizeEdgeExtensionUrl=function(e,t){return goog.html.SafeUrl.sanitizeExtensionUrl_(/^ms-browser-extension:\/\/([^\/]+)\//,e,t)},goog.html.SafeUrl.sanitizeExtensionUrl_=function(e,t,r){return(e=e.exec(t))?(e=e[1],-1==(r instanceof goog.string.Const?[goog.string.Const.unwrap(r)]:r.map((function(e){return goog.string.Const.unwrap(e)}))).indexOf(e)&&(t=goog.html.SafeUrl.INNOCUOUS_STRING)):t=goog.html.SafeUrl.INNOCUOUS_STRING,goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(t)},goog.html.SafeUrl.fromTrustedResourceUrl=function(e){return goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(goog.html.TrustedResourceUrl.unwrap(e))},goog.html.SAFE_URL_PATTERN_=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i,goog.html.SafeUrl.SAFE_URL_PATTERN=goog.html.SAFE_URL_PATTERN_,goog.html.SafeUrl.sanitize=function(e){return e instanceof goog.html.SafeUrl?e:(e="object"==typeof e&&e.implementsGoogStringTypedString?e.getTypedStringValue():String(e),goog.html.SAFE_URL_PATTERN_.test(e)||(e=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e))},goog.html.SafeUrl.sanitizeAssertUnchanged=function(e,t){return e instanceof goog.html.SafeUrl?e:(e="object"==typeof e&&e.implementsGoogStringTypedString?e.getTypedStringValue():String(e),t&&/^data:/i.test(e)&&(t=goog.html.SafeUrl.fromDataUrl(e)).getTypedStringValue()==e?t:(goog.asserts.assert(goog.html.SAFE_URL_PATTERN_.test(e),"%s does not match the safe URL pattern",e)||(e=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(e)))},goog.html.SafeUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse=function(e){return new goog.html.SafeUrl(goog.html.SafeUrl.CONSTRUCTOR_TOKEN_PRIVATE_,e)},goog.html.SafeUrl.ABOUT_BLANK=goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse("about:blank"),goog.html.SafeUrl.CONSTRUCTOR_TOKEN_PRIVATE_={},goog.html.SafeStyle=function(){this.privateDoNotAccessOrElseSafeStyleWrappedValue_="",this.SAFE_STYLE_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeStyle.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeStyle.prototype.implementsGoogStringTypedString=!0,goog.html.SafeStyle.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeStyle.fromConstant=function(e){return 0===(e=goog.string.Const.unwrap(e)).length?goog.html.SafeStyle.EMPTY:(goog.asserts.assert(goog.string.internal.endsWith(e,";"),"Last character of style string is not ';': "+e),goog.asserts.assert(goog.string.internal.contains(e,":"),"Style string must contain at least one ':', to specify a \"name: value\" pair: "+e),goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(e))},goog.html.SafeStyle.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeStyleWrappedValue_},goog.DEBUG&&(goog.html.SafeStyle.prototype.toString=function(){return"SafeStyle{"+this.privateDoNotAccessOrElseSafeStyleWrappedValue_+"}"}),goog.html.SafeStyle.unwrap=function(e){return e instanceof goog.html.SafeStyle&&e.constructor===goog.html.SafeStyle&&e.SAFE_STYLE_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeStyle.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?e.privateDoNotAccessOrElseSafeStyleWrappedValue_:(goog.asserts.fail("expected object of type SafeStyle, got '"+e+"' of type "+goog.typeOf(e)),"type_error:SafeStyle")},goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse=function(e){return(new goog.html.SafeStyle).initSecurityPrivateDoNotAccessOrElse_(e)},goog.html.SafeStyle.prototype.initSecurityPrivateDoNotAccessOrElse_=function(e){return this.privateDoNotAccessOrElseSafeStyleWrappedValue_=e,this},goog.html.SafeStyle.EMPTY=goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(""),goog.html.SafeStyle.INNOCUOUS_STRING="zClosurez",goog.html.SafeStyle.create=function(e){var t,r="";for(t in e){if(!/^[-_a-zA-Z0-9]+$/.test(t))throw Error("Name allows only [-_a-zA-Z0-9], got: "+t);var n=e[t];null!=n&&(r+=t+":"+(n=Array.isArray(n)?goog.array.map(n,goog.html.SafeStyle.sanitizePropertyValue_).join(" "):goog.html.SafeStyle.sanitizePropertyValue_(n))+";")}return r?goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(r):goog.html.SafeStyle.EMPTY},goog.html.SafeStyle.sanitizePropertyValue_=function(e){if(e instanceof goog.html.SafeUrl)return'url("'+goog.html.SafeUrl.unwrap(e).replace(/</g,"%3c").replace(/[\\"]/g,"\\$&")+'")';if(e=e instanceof goog.string.Const?goog.string.Const.unwrap(e):goog.html.SafeStyle.sanitizePropertyValueString_(String(e)),/[{;}]/.test(e))throw new goog.asserts.AssertionError("Value does not allow [{;}], got: %s.",[e]);return e},goog.html.SafeStyle.sanitizePropertyValueString_=function(e){var t=e.replace(goog.html.SafeStyle.FUNCTIONS_RE_,"$1").replace(goog.html.SafeStyle.FUNCTIONS_RE_,"$1").replace(goog.html.SafeStyle.URL_RE_,"url");return goog.html.SafeStyle.VALUE_RE_.test(t)?goog.html.SafeStyle.COMMENT_RE_.test(e)?(goog.asserts.fail("String value disallows comments, got: "+e),goog.html.SafeStyle.INNOCUOUS_STRING):goog.html.SafeStyle.hasBalancedQuotes_(e)?goog.html.SafeStyle.hasBalancedSquareBrackets_(e)?goog.html.SafeStyle.sanitizeUrl_(e):(goog.asserts.fail("String value requires balanced square brackets and one identifier per pair of brackets, got: "+e),goog.html.SafeStyle.INNOCUOUS_STRING):(goog.asserts.fail("String value requires balanced quotes, got: "+e),goog.html.SafeStyle.INNOCUOUS_STRING):(goog.asserts.fail("String value allows only "+goog.html.SafeStyle.VALUE_ALLOWED_CHARS_+" and simple functions, got: "+e),goog.html.SafeStyle.INNOCUOUS_STRING)},goog.html.SafeStyle.hasBalancedQuotes_=function(e){for(var t=!0,r=!0,n=0;n<e.length;n++){var o=e.charAt(n);"'"==o&&r?t=!t:'"'==o&&t&&(r=!r)}return t&&r},goog.html.SafeStyle.hasBalancedSquareBrackets_=function(e){for(var t=!0,r=/^[-_a-zA-Z0-9]$/,n=0;n<e.length;n++){var o=e.charAt(n);if("]"==o){if(t)return!1;t=!0}else if("["==o){if(!t)return!1;t=!1}else if(!t&&!r.test(o))return!1}return t},goog.html.SafeStyle.VALUE_ALLOWED_CHARS_="[-,.\"'%_!# a-zA-Z0-9\\[\\]]",goog.html.SafeStyle.VALUE_RE_=new RegExp("^"+goog.html.SafeStyle.VALUE_ALLOWED_CHARS_+"+$"),goog.html.SafeStyle.URL_RE_=/\b(url\([ \t\n]*)('[ -&(-\[\]-~]*'|"[ !#-\[\]-~]*"|[!#-&*-\[\]-~]*)([ \t\n]*\))/g,goog.html.SafeStyle.ALLOWED_FUNCTIONS_="calc cubic-bezier fit-content hsl hsla linear-gradient matrix minmax repeat rgb rgba (rotate|scale|translate)(X|Y|Z|3d)?".split(" "),goog.html.SafeStyle.FUNCTIONS_RE_=new RegExp("\\b("+goog.html.SafeStyle.ALLOWED_FUNCTIONS_.join("|")+")\\([-+*/0-9a-z.%\\[\\], ]+\\)","g"),goog.html.SafeStyle.COMMENT_RE_=/\/\*/,goog.html.SafeStyle.sanitizeUrl_=function(e){return e.replace(goog.html.SafeStyle.URL_RE_,(function(e,t,r,n){var o="";return r=r.replace(/^(['"])(.*)\1$/,(function(e,t,r){return o=t,r})),e=goog.html.SafeUrl.sanitize(r).getTypedStringValue(),t+o+e+o+n}))},goog.html.SafeStyle.concat=function(e){var t="",r=function(e){Array.isArray(e)?goog.array.forEach(e,r):t+=goog.html.SafeStyle.unwrap(e)};return goog.array.forEach(arguments,r),t?goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(t):goog.html.SafeStyle.EMPTY},goog.html.SafeStyleSheet=function(){this.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_="",this.SAFE_STYLE_SHEET_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeStyleSheet.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeStyleSheet.prototype.implementsGoogStringTypedString=!0,goog.html.SafeStyleSheet.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeStyleSheet.createRule=function(e,t){if(goog.string.internal.contains(e,"<"))throw Error("Selector does not allow '<', got: "+e);var r=e.replace(/('|")((?!\1)[^\r\n\f\\]|\\[\s\S])*\1/g,"");if(!/^[-_a-zA-Z0-9#.:* ,>+~[\]()=^$|]+$/.test(r))throw Error("Selector allows only [-_a-zA-Z0-9#.:* ,>+~[\\]()=^$|] and strings, got: "+e);if(!goog.html.SafeStyleSheet.hasBalancedBrackets_(r))throw Error("() and [] in selector must be balanced, got: "+e);return t instanceof goog.html.SafeStyle||(t=goog.html.SafeStyle.create(t)),e=e+"{"+goog.html.SafeStyle.unwrap(t).replace(/</g,"\\3C ")+"}",goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(e)},goog.html.SafeStyleSheet.hasBalancedBrackets_=function(e){for(var t={"(":")","[":"]"},r=[],n=0;n<e.length;n++){var o=e[n];if(t[o])r.push(t[o]);else if(goog.object.contains(t,o)&&r.pop()!=o)return!1}return 0==r.length},goog.html.SafeStyleSheet.concat=function(e){var t="",r=function(e){Array.isArray(e)?goog.array.forEach(e,r):t+=goog.html.SafeStyleSheet.unwrap(e)};return goog.array.forEach(arguments,r),goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(t)},goog.html.SafeStyleSheet.fromConstant=function(e){return 0===(e=goog.string.Const.unwrap(e)).length?goog.html.SafeStyleSheet.EMPTY:(goog.asserts.assert(!goog.string.internal.contains(e,"<"),"Forbidden '<' character in style sheet string: "+e),goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(e))},goog.html.SafeStyleSheet.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_},goog.DEBUG&&(goog.html.SafeStyleSheet.prototype.toString=function(){return"SafeStyleSheet{"+this.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_+"}"}),goog.html.SafeStyleSheet.unwrap=function(e){return e instanceof goog.html.SafeStyleSheet&&e.constructor===goog.html.SafeStyleSheet&&e.SAFE_STYLE_SHEET_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeStyleSheet.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?e.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_:(goog.asserts.fail("expected object of type SafeStyleSheet, got '"+e+"' of type "+goog.typeOf(e)),"type_error:SafeStyleSheet")},goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse=function(e){return(new goog.html.SafeStyleSheet).initSecurityPrivateDoNotAccessOrElse_(e)},goog.html.SafeStyleSheet.prototype.initSecurityPrivateDoNotAccessOrElse_=function(e){return this.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_=e,this},goog.html.SafeStyleSheet.EMPTY=goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(""),goog.labs={},goog.labs.userAgent={},goog.labs.userAgent.util={},goog.labs.userAgent.util.getNativeUserAgentString_=function(){var e=goog.labs.userAgent.util.getNavigator_();return e&&(e=e.userAgent)?e:""},goog.labs.userAgent.util.getNavigator_=function(){return goog.global.navigator},goog.labs.userAgent.util.userAgent_=goog.labs.userAgent.util.getNativeUserAgentString_(),goog.labs.userAgent.util.setUserAgent=function(e){goog.labs.userAgent.util.userAgent_=e||goog.labs.userAgent.util.getNativeUserAgentString_()},goog.labs.userAgent.util.getUserAgent=function(){return goog.labs.userAgent.util.userAgent_},goog.labs.userAgent.util.matchUserAgent=function(e){var t=goog.labs.userAgent.util.getUserAgent();return goog.string.internal.contains(t,e)},goog.labs.userAgent.util.matchUserAgentIgnoreCase=function(e){var t=goog.labs.userAgent.util.getUserAgent();return goog.string.internal.caseInsensitiveContains(t,e)},goog.labs.userAgent.util.extractVersionTuples=function(e){for(var t,r=/(\w[\w ]+)\/([^\s]+)\s*(?:\((.*?)\))?/g,n=[];t=r.exec(e);)n.push([t[1],t[2],t[3]||void 0]);return n},goog.labs.userAgent.browser={},goog.labs.userAgent.browser.matchOpera_=function(){return goog.labs.userAgent.util.matchUserAgent("Opera")},goog.labs.userAgent.browser.matchIE_=function(){return goog.labs.userAgent.util.matchUserAgent("Trident")||goog.labs.userAgent.util.matchUserAgent("MSIE")},goog.labs.userAgent.browser.matchEdgeHtml_=function(){return goog.labs.userAgent.util.matchUserAgent("Edge")},goog.labs.userAgent.browser.matchEdgeChromium_=function(){return goog.labs.userAgent.util.matchUserAgent("Edg/")},goog.labs.userAgent.browser.matchOperaChromium_=function(){return goog.labs.userAgent.util.matchUserAgent("OPR")},goog.labs.userAgent.browser.matchFirefox_=function(){return goog.labs.userAgent.util.matchUserAgent("Firefox")||goog.labs.userAgent.util.matchUserAgent("FxiOS")},goog.labs.userAgent.browser.matchSafari_=function(){return goog.labs.userAgent.util.matchUserAgent("Safari")&&!(goog.labs.userAgent.browser.matchChrome_()||goog.labs.userAgent.browser.matchCoast_()||goog.labs.userAgent.browser.matchOpera_()||goog.labs.userAgent.browser.matchEdgeHtml_()||goog.labs.userAgent.browser.matchEdgeChromium_()||goog.labs.userAgent.browser.matchOperaChromium_()||goog.labs.userAgent.browser.matchFirefox_()||goog.labs.userAgent.browser.isSilk()||goog.labs.userAgent.util.matchUserAgent("Android"))},goog.labs.userAgent.browser.matchCoast_=function(){return goog.labs.userAgent.util.matchUserAgent("Coast")},goog.labs.userAgent.browser.matchIosWebview_=function(){return(goog.labs.userAgent.util.matchUserAgent("iPad")||goog.labs.userAgent.util.matchUserAgent("iPhone"))&&!goog.labs.userAgent.browser.matchSafari_()&&!goog.labs.userAgent.browser.matchChrome_()&&!goog.labs.userAgent.browser.matchCoast_()&&!goog.labs.userAgent.browser.matchFirefox_()&&goog.labs.userAgent.util.matchUserAgent("AppleWebKit")},goog.labs.userAgent.browser.matchChrome_=function(){return(goog.labs.userAgent.util.matchUserAgent("Chrome")||goog.labs.userAgent.util.matchUserAgent("CriOS"))&&!goog.labs.userAgent.browser.matchEdgeHtml_()},goog.labs.userAgent.browser.matchAndroidBrowser_=function(){return goog.labs.userAgent.util.matchUserAgent("Android")&&!(goog.labs.userAgent.browser.isChrome()||goog.labs.userAgent.browser.isFirefox()||goog.labs.userAgent.browser.isOpera()||goog.labs.userAgent.browser.isSilk())},goog.labs.userAgent.browser.isOpera=goog.labs.userAgent.browser.matchOpera_,goog.labs.userAgent.browser.isIE=goog.labs.userAgent.browser.matchIE_,goog.labs.userAgent.browser.isEdge=goog.labs.userAgent.browser.matchEdgeHtml_,goog.labs.userAgent.browser.isEdgeChromium=goog.labs.userAgent.browser.matchEdgeChromium_,goog.labs.userAgent.browser.isOperaChromium=goog.labs.userAgent.browser.matchOperaChromium_,goog.labs.userAgent.browser.isFirefox=goog.labs.userAgent.browser.matchFirefox_,goog.labs.userAgent.browser.isSafari=goog.labs.userAgent.browser.matchSafari_,goog.labs.userAgent.browser.isCoast=goog.labs.userAgent.browser.matchCoast_,goog.labs.userAgent.browser.isIosWebview=goog.labs.userAgent.browser.matchIosWebview_,goog.labs.userAgent.browser.isChrome=goog.labs.userAgent.browser.matchChrome_,goog.labs.userAgent.browser.isAndroidBrowser=goog.labs.userAgent.browser.matchAndroidBrowser_,goog.labs.userAgent.browser.isSilk=function(){return goog.labs.userAgent.util.matchUserAgent("Silk")},goog.labs.userAgent.browser.getVersion=function(){function e(e){return e=goog.array.find(e,n),r[e]||""}var t=goog.labs.userAgent.util.getUserAgent();if(goog.labs.userAgent.browser.isIE())return goog.labs.userAgent.browser.getIEVersion_(t);t=goog.labs.userAgent.util.extractVersionTuples(t);var r={};goog.array.forEach(t,(function(e){r[e[0]]=e[1]}));var n=goog.partial(goog.object.containsKey,r);return goog.labs.userAgent.browser.isOpera()?e(["Version","Opera"]):goog.labs.userAgent.browser.isEdge()?e(["Edge"]):goog.labs.userAgent.browser.isEdgeChromium()?e(["Edg"]):goog.labs.userAgent.browser.isChrome()?e(["Chrome","CriOS","HeadlessChrome"]):(t=t[2])&&t[1]||""},goog.labs.userAgent.browser.isVersionOrHigher=function(e){return 0<=goog.string.internal.compareVersions(goog.labs.userAgent.browser.getVersion(),e)},goog.labs.userAgent.browser.getIEVersion_=function(e){var t=/rv: *([\d\.]*)/.exec(e);if(t&&t[1])return t[1];t="";var r=/MSIE +([\d\.]+)/.exec(e);if(r&&r[1])if(e=/Trident\/(\d.\d)/.exec(e),"7.0"==r[1])if(e&&e[1])switch(e[1]){case"4.0":t="8.0";break;case"5.0":t="9.0";break;case"6.0":t="10.0";break;case"7.0":t="11.0"}else t="7.0";else t=r[1];return t},goog.html.SafeHtml=function(){this.privateDoNotAccessOrElseSafeHtmlWrappedValue_="",this.SAFE_HTML_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeHtml.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_,this.dir_=null},goog.html.SafeHtml.ENABLE_ERROR_MESSAGES=goog.DEBUG,goog.html.SafeHtml.SUPPORT_STYLE_ATTRIBUTE=!0,goog.html.SafeHtml.prototype.implementsGoogI18nBidiDirectionalString=!0,goog.html.SafeHtml.prototype.getDirection=function(){return this.dir_},goog.html.SafeHtml.prototype.implementsGoogStringTypedString=!0,goog.html.SafeHtml.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeHtmlWrappedValue_.toString()},goog.DEBUG&&(goog.html.SafeHtml.prototype.toString=function(){return"SafeHtml{"+this.privateDoNotAccessOrElseSafeHtmlWrappedValue_+"}"}),goog.html.SafeHtml.unwrap=function(e){return goog.html.SafeHtml.unwrapTrustedHTML(e).toString()},goog.html.SafeHtml.unwrapTrustedHTML=function(e){return e instanceof goog.html.SafeHtml&&e.constructor===goog.html.SafeHtml&&e.SAFE_HTML_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeHtml.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?e.privateDoNotAccessOrElseSafeHtmlWrappedValue_:(goog.asserts.fail("expected object of type SafeHtml, got '"+e+"' of type "+goog.typeOf(e)),"type_error:SafeHtml")},goog.html.SafeHtml.htmlEscape=function(e){if(e instanceof goog.html.SafeHtml)return e;var t="object"==typeof e,r=null;return t&&e.implementsGoogI18nBidiDirectionalString&&(r=e.getDirection()),e=t&&e.implementsGoogStringTypedString?e.getTypedStringValue():String(e),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(goog.string.internal.htmlEscape(e),r)},goog.html.SafeHtml.htmlEscapePreservingNewlines=function(e){return e instanceof goog.html.SafeHtml?e:(e=goog.html.SafeHtml.htmlEscape(e),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(goog.string.internal.newLineToBr(goog.html.SafeHtml.unwrap(e)),e.getDirection()))},goog.html.SafeHtml.htmlEscapePreservingNewlinesAndSpaces=function(e){return e instanceof goog.html.SafeHtml?e:(e=goog.html.SafeHtml.htmlEscape(e),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(goog.string.internal.whitespaceEscape(goog.html.SafeHtml.unwrap(e)),e.getDirection()))},goog.html.SafeHtml.from=goog.html.SafeHtml.htmlEscape,goog.html.SafeHtml.comment=function(e){return goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse("\x3c!--"+goog.string.internal.htmlEscape(e)+"--\x3e",null)},goog.html.SafeHtml.VALID_NAMES_IN_TAG_=/^[a-zA-Z0-9-]+$/,goog.html.SafeHtml.URL_ATTRIBUTES_={action:!0,cite:!0,data:!0,formaction:!0,href:!0,manifest:!0,poster:!0,src:!0},goog.html.SafeHtml.NOT_ALLOWED_TAG_NAMES_={APPLET:!0,BASE:!0,EMBED:!0,IFRAME:!0,LINK:!0,MATH:!0,META:!0,OBJECT:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0},goog.html.SafeHtml.create=function(e,t,r){return goog.html.SafeHtml.verifyTagName(String(e)),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse(String(e),t,r)},goog.html.SafeHtml.verifyTagName=function(e){if(!goog.html.SafeHtml.VALID_NAMES_IN_TAG_.test(e))throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?"Invalid tag name <"+e+">.":"");if(e.toUpperCase()in goog.html.SafeHtml.NOT_ALLOWED_TAG_NAMES_)throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?"Tag name <"+e+"> is not allowed for SafeHtml.":"")},goog.html.SafeHtml.createIframe=function(e,t,r,n){e&&goog.html.TrustedResourceUrl.unwrap(e);var o={};return o.src=e||null,o.srcdoc=t&&goog.html.SafeHtml.unwrap(t),e=goog.html.SafeHtml.combineAttributes(o,{sandbox:""},r),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("iframe",e,n)},goog.html.SafeHtml.createSandboxIframe=function(e,t,r,n){if(!goog.html.SafeHtml.canUseSandboxIframe())throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?"The browser does not support sandboxed iframes.":"");var o={};return o.src=e?goog.html.SafeUrl.unwrap(goog.html.SafeUrl.sanitize(e)):null,o.srcdoc=t||null,o.sandbox="",e=goog.html.SafeHtml.combineAttributes(o,{},r),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("iframe",e,n)},goog.html.SafeHtml.canUseSandboxIframe=function(){return goog.global.HTMLIFrameElement&&"sandbox"in goog.global.HTMLIFrameElement.prototype},goog.html.SafeHtml.createScriptSrc=function(e,t){return goog.html.TrustedResourceUrl.unwrap(e),e=goog.html.SafeHtml.combineAttributes({src:e},{},t),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("script",e)},goog.html.SafeHtml.createScript=function(e,t){for(var r in t){var n=r.toLowerCase();if("language"==n||"src"==n||"text"==n||"type"==n)throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Cannot set "'+n+'" attribute':"")}for(r="",e=goog.array.concat(e),n=0;n<e.length;n++)r+=goog.html.SafeScript.unwrap(e[n]);return e=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(r,goog.i18n.bidi.Dir.NEUTRAL),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("script",t,e)},goog.html.SafeHtml.createStyle=function(e,t){t=goog.html.SafeHtml.combineAttributes({type:"text/css"},{},t);var r="";e=goog.array.concat(e);for(var n=0;n<e.length;n++)r+=goog.html.SafeStyleSheet.unwrap(e[n]);return e=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(r,goog.i18n.bidi.Dir.NEUTRAL),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("style",t,e)},goog.html.SafeHtml.createMetaRefresh=function(e,t){return e=goog.html.SafeUrl.unwrap(goog.html.SafeUrl.sanitize(e)),(goog.labs.userAgent.browser.isIE()||goog.labs.userAgent.browser.isEdge())&&goog.string.internal.contains(e,";")&&(e="'"+e.replace(/'/g,"%27")+"'"),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("meta",{"http-equiv":"refresh",content:(t||0)+"; url="+e})},goog.html.SafeHtml.getAttrNameAndValue_=function(e,t,r){if(r instanceof goog.string.Const)r=goog.string.Const.unwrap(r);else if("style"==t.toLowerCase()){if(!goog.html.SafeHtml.SUPPORT_STYLE_ATTRIBUTE)throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Attribute "style" not supported.':"");r=goog.html.SafeHtml.getStyleValue_(r)}else{if(/^on/i.test(t))throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Attribute "'+t+'" requires goog.string.Const value, "'+r+'" given.':"");if(t.toLowerCase()in goog.html.SafeHtml.URL_ATTRIBUTES_)if(r instanceof goog.html.TrustedResourceUrl)r=goog.html.TrustedResourceUrl.unwrap(r);else if(r instanceof goog.html.SafeUrl)r=goog.html.SafeUrl.unwrap(r);else{if("string"!=typeof r)throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Attribute "'+t+'" on tag "'+e+'" requires goog.html.SafeUrl, goog.string.Const, or string, value "'+r+'" given.':"");r=goog.html.SafeUrl.sanitize(r).getTypedStringValue()}}return r.implementsGoogStringTypedString&&(r=r.getTypedStringValue()),goog.asserts.assert("string"==typeof r||"number"==typeof r,"String or number value expected, got "+typeof r+" with value: "+r),t+'="'+goog.string.internal.htmlEscape(String(r))+'"'},goog.html.SafeHtml.getStyleValue_=function(e){if(!goog.isObject(e))throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'The "style" attribute requires goog.html.SafeStyle or map of style properties, '+typeof e+" given: "+e:"");return e instanceof goog.html.SafeStyle||(e=goog.html.SafeStyle.create(e)),goog.html.SafeStyle.unwrap(e)},goog.html.SafeHtml.createWithDir=function(e,t,r,n){return(t=goog.html.SafeHtml.create(t,r,n)).dir_=e,t},goog.html.SafeHtml.join=function(e,t){var r=(e=goog.html.SafeHtml.htmlEscape(e)).getDirection(),n=[],o=function(e){Array.isArray(e)?goog.array.forEach(e,o):(e=goog.html.SafeHtml.htmlEscape(e),n.push(goog.html.SafeHtml.unwrap(e)),e=e.getDirection(),r==goog.i18n.bidi.Dir.NEUTRAL?r=e:e!=goog.i18n.bidi.Dir.NEUTRAL&&r!=e&&(r=null))};return goog.array.forEach(t,o),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(n.join(goog.html.SafeHtml.unwrap(e)),r)},goog.html.SafeHtml.concat=function(e){return goog.html.SafeHtml.join(goog.html.SafeHtml.EMPTY,Array.prototype.slice.call(arguments))},goog.html.SafeHtml.concatWithDir=function(e,t){var r=goog.html.SafeHtml.concat(goog.array.slice(arguments,1));return r.dir_=e,r},goog.html.SafeHtml.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse=function(e,t){return(new goog.html.SafeHtml).initSecurityPrivateDoNotAccessOrElse_(e,t)},goog.html.SafeHtml.prototype.initSecurityPrivateDoNotAccessOrElse_=function(e,t){return this.privateDoNotAccessOrElseSafeHtmlWrappedValue_=goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY?goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY.createHTML(e):e,this.dir_=t,this},goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse=function(e,t,r){var n=null,o="<"+e+goog.html.SafeHtml.stringifyAttributes(e,t);return null==r?r=[]:Array.isArray(r)||(r=[r]),goog.dom.tags.isVoidTag(e.toLowerCase())?(goog.asserts.assert(!r.length,"Void tag <"+e+"> does not allow content."),o+=">"):(n=goog.html.SafeHtml.concat(r),o+=">"+goog.html.SafeHtml.unwrap(n)+"</"+e+">",n=n.getDirection()),(e=t&&t.dir)&&(n=/^(ltr|rtl|auto)$/i.test(e)?goog.i18n.bidi.Dir.NEUTRAL:null),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(o,n)},goog.html.SafeHtml.stringifyAttributes=function(e,t){var r="";if(t)for(var n in t){if(!goog.html.SafeHtml.VALID_NAMES_IN_TAG_.test(n))throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Invalid attribute name "'+n+'".':"");var o=t[n];null!=o&&(r+=" "+goog.html.SafeHtml.getAttrNameAndValue_(e,n,o))}return r},goog.html.SafeHtml.combineAttributes=function(e,t,r){var n,o={};for(n in e)goog.asserts.assert(n.toLowerCase()==n,"Must be lower case"),o[n]=e[n];for(n in t)goog.asserts.assert(n.toLowerCase()==n,"Must be lower case"),o[n]=t[n];if(r)for(n in r){var i=n.toLowerCase();if(i in e)throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Cannot override "'+i+'" attribute, got "'+n+'" with value "'+r[n]+'"':"");i in t&&delete o[i],o[n]=r[n]}return o},goog.html.SafeHtml.DOCTYPE_HTML=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse("<!DOCTYPE html>",goog.i18n.bidi.Dir.NEUTRAL),goog.html.SafeHtml.EMPTY=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse("",goog.i18n.bidi.Dir.NEUTRAL),goog.html.SafeHtml.BR=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse("<br>",goog.i18n.bidi.Dir.NEUTRAL),goog.html.uncheckedconversions={},goog.html.uncheckedconversions.safeHtmlFromStringKnownToSatisfyTypeContract=function(e,t,r){return goog.asserts.assertString(goog.string.Const.unwrap(e),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(e)),"must provide non-empty justification"),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(t,r||null)},goog.html.uncheckedconversions.safeScriptFromStringKnownToSatisfyTypeContract=function(e,t){return goog.asserts.assertString(goog.string.Const.unwrap(e),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(e)),"must provide non-empty justification"),goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(t)},goog.html.uncheckedconversions.safeStyleFromStringKnownToSatisfyTypeContract=function(e,t){return goog.asserts.assertString(goog.string.Const.unwrap(e),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(e)),"must provide non-empty justification"),goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(t)},goog.html.uncheckedconversions.safeStyleSheetFromStringKnownToSatisfyTypeContract=function(e,t){return goog.asserts.assertString(goog.string.Const.unwrap(e),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(e)),"must provide non-empty justification"),goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(t)},goog.html.uncheckedconversions.safeUrlFromStringKnownToSatisfyTypeContract=function(e,t){return goog.asserts.assertString(goog.string.Const.unwrap(e),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(e)),"must provide non-empty justification"),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(t)},goog.html.uncheckedconversions.trustedResourceUrlFromStringKnownToSatisfyTypeContract=function(e,t){return goog.asserts.assertString(goog.string.Const.unwrap(e),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(e)),"must provide non-empty justification"),goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(t)},goog.dom.safe={},goog.dom.safe.InsertAdjacentHtmlPosition={AFTERBEGIN:"afterbegin",AFTEREND:"afterend",BEFOREBEGIN:"beforebegin",BEFOREEND:"beforeend"},goog.dom.safe.insertAdjacentHtml=function(e,t,r){e.insertAdjacentHTML(t,goog.html.SafeHtml.unwrapTrustedHTML(r))},goog.dom.safe.SET_INNER_HTML_DISALLOWED_TAGS_={MATH:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0},goog.dom.safe.isInnerHtmlCleanupRecursive_=goog.functions.cacheReturnValue((function(){if(goog.DEBUG&&"undefined"==typeof document)return!1;var e=document.createElement("div"),t=document.createElement("div");return t.appendChild(document.createElement("div")),e.appendChild(t),!(goog.DEBUG&&!e.firstChild)&&(t=e.firstChild.firstChild,e.innerHTML=goog.html.SafeHtml.unwrapTrustedHTML(goog.html.SafeHtml.EMPTY),!t.parentElement)})),goog.dom.safe.unsafeSetInnerHtmlDoNotUseOrElse=function(e,t){if(goog.dom.safe.isInnerHtmlCleanupRecursive_())for(;e.lastChild;)e.removeChild(e.lastChild);e.innerHTML=goog.html.SafeHtml.unwrapTrustedHTML(t)},goog.dom.safe.setInnerHtml=function(e,t){if(goog.asserts.ENABLE_ASSERTS){var r=e.tagName.toUpperCase();if(goog.dom.safe.SET_INNER_HTML_DISALLOWED_TAGS_[r])throw Error("goog.dom.safe.setInnerHtml cannot be used to set content of "+e.tagName+".")}goog.dom.safe.unsafeSetInnerHtmlDoNotUseOrElse(e,t)},goog.dom.safe.setOuterHtml=function(e,t){e.outerHTML=goog.html.SafeHtml.unwrapTrustedHTML(t)},goog.dom.safe.setFormElementAction=function(e,t){t=t instanceof goog.html.SafeUrl?t:goog.html.SafeUrl.sanitizeAssertUnchanged(t),goog.dom.asserts.assertIsHTMLFormElement(e).action=goog.html.SafeUrl.unwrap(t)},goog.dom.safe.setButtonFormAction=function(e,t){t=t instanceof goog.html.SafeUrl?t:goog.html.SafeUrl.sanitizeAssertUnchanged(t),goog.dom.asserts.assertIsHTMLButtonElement(e).formAction=goog.html.SafeUrl.unwrap(t)},goog.dom.safe.setInputFormAction=function(e,t){t=t instanceof goog.html.SafeUrl?t:goog.html.SafeUrl.sanitizeAssertUnchanged(t),goog.dom.asserts.assertIsHTMLInputElement(e).formAction=goog.html.SafeUrl.unwrap(t)},goog.dom.safe.setStyle=function(e,t){e.style.cssText=goog.html.SafeStyle.unwrap(t)},goog.dom.safe.documentWrite=function(e,t){e.write(goog.html.SafeHtml.unwrapTrustedHTML(t))},goog.dom.safe.setAnchorHref=function(e,t){goog.dom.asserts.assertIsHTMLAnchorElement(e),t=t instanceof goog.html.SafeUrl?t:goog.html.SafeUrl.sanitizeAssertUnchanged(t),e.href=goog.html.SafeUrl.unwrap(t)},goog.dom.safe.setImageSrc=function(e,t){if(goog.dom.asserts.assertIsHTMLImageElement(e),!(t instanceof goog.html.SafeUrl)){var r=/^data:image\//i.test(t);t=goog.html.SafeUrl.sanitizeAssertUnchanged(t,r)}e.src=goog.html.SafeUrl.unwrap(t)},goog.dom.safe.setAudioSrc=function(e,t){if(goog.dom.asserts.assertIsHTMLAudioElement(e),!(t instanceof goog.html.SafeUrl)){var r=/^data:audio\//i.test(t);t=goog.html.SafeUrl.sanitizeAssertUnchanged(t,r)}e.src=goog.html.SafeUrl.unwrap(t)},goog.dom.safe.setVideoSrc=function(e,t){if(goog.dom.asserts.assertIsHTMLVideoElement(e),!(t instanceof goog.html.SafeUrl)){var r=/^data:video\//i.test(t);t=goog.html.SafeUrl.sanitizeAssertUnchanged(t,r)}e.src=goog.html.SafeUrl.unwrap(t)},goog.dom.safe.setEmbedSrc=function(e,t){goog.dom.asserts.assertIsHTMLEmbedElement(e),e.src=goog.html.TrustedResourceUrl.unwrapTrustedScriptURL(t)},goog.dom.safe.setFrameSrc=function(e,t){goog.dom.asserts.assertIsHTMLFrameElement(e),e.src=goog.html.TrustedResourceUrl.unwrap(t)},goog.dom.safe.setIframeSrc=function(e,t){goog.dom.asserts.assertIsHTMLIFrameElement(e),e.src=goog.html.TrustedResourceUrl.unwrap(t)},goog.dom.safe.setIframeSrcdoc=function(e,t){goog.dom.asserts.assertIsHTMLIFrameElement(e),e.srcdoc=goog.html.SafeHtml.unwrapTrustedHTML(t)},goog.dom.safe.setLinkHrefAndRel=function(e,t,r){goog.dom.asserts.assertIsHTMLLinkElement(e),e.rel=r,goog.string.internal.caseInsensitiveContains(r,"stylesheet")?(goog.asserts.assert(t instanceof goog.html.TrustedResourceUrl,'URL must be TrustedResourceUrl because "rel" contains "stylesheet"'),e.href=goog.html.TrustedResourceUrl.unwrap(t)):e.href=t instanceof goog.html.TrustedResourceUrl?goog.html.TrustedResourceUrl.unwrap(t):t instanceof goog.html.SafeUrl?goog.html.SafeUrl.unwrap(t):goog.html.SafeUrl.unwrap(goog.html.SafeUrl.sanitizeAssertUnchanged(t))},goog.dom.safe.setObjectData=function(e,t){goog.dom.asserts.assertIsHTMLObjectElement(e),e.data=goog.html.TrustedResourceUrl.unwrapTrustedScriptURL(t)},goog.dom.safe.setScriptSrc=function(e,t){goog.dom.asserts.assertIsHTMLScriptElement(e),e.src=goog.html.TrustedResourceUrl.unwrapTrustedScriptURL(t),(t=goog.getScriptNonce())&&e.setAttribute("nonce",t)},goog.dom.safe.setScriptContent=function(e,t){goog.dom.asserts.assertIsHTMLScriptElement(e),e.text=goog.html.SafeScript.unwrapTrustedScript(t),(t=goog.getScriptNonce())&&e.setAttribute("nonce",t)},goog.dom.safe.setLocationHref=function(e,t){goog.dom.asserts.assertIsLocation(e),t=t instanceof goog.html.SafeUrl?t:goog.html.SafeUrl.sanitizeAssertUnchanged(t),e.href=goog.html.SafeUrl.unwrap(t)},goog.dom.safe.assignLocation=function(e,t){goog.dom.asserts.assertIsLocation(e),t=t instanceof goog.html.SafeUrl?t:goog.html.SafeUrl.sanitizeAssertUnchanged(t),e.assign(goog.html.SafeUrl.unwrap(t))},goog.dom.safe.replaceLocation=function(e,t){t=t instanceof goog.html.SafeUrl?t:goog.html.SafeUrl.sanitizeAssertUnchanged(t),e.replace(goog.html.SafeUrl.unwrap(t))},goog.dom.safe.openInWindow=function(e,t,r,n,o){return e=e instanceof goog.html.SafeUrl?e:goog.html.SafeUrl.sanitizeAssertUnchanged(e),t=t||goog.global,r=r instanceof goog.string.Const?goog.string.Const.unwrap(r):r||"",t.open(goog.html.SafeUrl.unwrap(e),r,n,o)},goog.dom.safe.parseFromStringHtml=function(e,t){return goog.dom.safe.parseFromString(e,t,"text/html")},goog.dom.safe.parseFromString=function(e,t,r){return e.parseFromString(goog.html.SafeHtml.unwrapTrustedHTML(t),r)},goog.dom.safe.createImageFromBlob=function(e){if(!/^image\/.*/g.test(e.type))throw Error("goog.dom.safe.createImageFromBlob only accepts MIME type image/.*.");var t=goog.global.URL.createObjectURL(e);return(e=new goog.global.Image).onload=function(){goog.global.URL.revokeObjectURL(t)},goog.dom.safe.setImageSrc(e,goog.html.uncheckedconversions.safeUrlFromStringKnownToSatisfyTypeContract(goog.string.Const.from("Image blob URL."),t)),e},goog.string.DETECT_DOUBLE_ESCAPING=!1,goog.string.FORCE_NON_DOM_HTML_UNESCAPING=!1,goog.string.Unicode={NBSP:" "},goog.string.startsWith=goog.string.internal.startsWith,goog.string.endsWith=goog.string.internal.endsWith,goog.string.caseInsensitiveStartsWith=goog.string.internal.caseInsensitiveStartsWith,goog.string.caseInsensitiveEndsWith=goog.string.internal.caseInsensitiveEndsWith,goog.string.caseInsensitiveEquals=goog.string.internal.caseInsensitiveEquals,goog.string.subs=function(e,t){for(var r=e.split("%s"),n="",o=Array.prototype.slice.call(arguments,1);o.length&&1<r.length;)n+=r.shift()+o.shift();return n+r.join("%s")},goog.string.collapseWhitespace=function(e){return e.replace(/[\s\xa0]+/g," ").replace(/^\s+|\s+$/g,"")},goog.string.isEmptyOrWhitespace=goog.string.internal.isEmptyOrWhitespace,goog.string.isEmptyString=function(e){return 0==e.length},goog.string.isEmpty=goog.string.isEmptyOrWhitespace,goog.string.isEmptyOrWhitespaceSafe=function(e){return goog.string.isEmptyOrWhitespace(goog.string.makeSafe(e))},goog.string.isEmptySafe=goog.string.isEmptyOrWhitespaceSafe,goog.string.isBreakingWhitespace=function(e){return!/[^\t\n\r ]/.test(e)},goog.string.isAlpha=function(e){return!/[^a-zA-Z]/.test(e)},goog.string.isNumeric=function(e){return!/[^0-9]/.test(e)},goog.string.isAlphaNumeric=function(e){return!/[^a-zA-Z0-9]/.test(e)},goog.string.isSpace=function(e){return" "==e},goog.string.isUnicodeChar=function(e){return 1==e.length&&" "<=e&&"~">=e||""<=e&&"�">=e},goog.string.stripNewlines=function(e){return e.replace(/(\r\n|\r|\n)+/g," ")},goog.string.canonicalizeNewlines=function(e){return e.replace(/(\r\n|\r|\n)/g,"\n")},goog.string.normalizeWhitespace=function(e){return e.replace(/\xa0|\s/g," ")},goog.string.normalizeSpaces=function(e){return e.replace(/\xa0|[ \t]+/g," ")},goog.string.collapseBreakingSpaces=function(e){return e.replace(/[\t\r\n ]+/g," ").replace(/^[\t\r\n ]+|[\t\r\n ]+$/g,"")},goog.string.trim=goog.string.internal.trim,goog.string.trimLeft=function(e){return e.replace(/^[\s\xa0]+/,"")},goog.string.trimRight=function(e){return e.replace(/[\s\xa0]+$/,"")},goog.string.caseInsensitiveCompare=goog.string.internal.caseInsensitiveCompare,goog.string.numberAwareCompare_=function(e,t,r){if(e==t)return 0;if(!e)return-1;if(!t)return 1;for(var n=e.toLowerCase().match(r),o=t.toLowerCase().match(r),i=Math.min(n.length,o.length),a=0;a<i;a++){r=n[a];var s=o[a];if(r!=s)return e=parseInt(r,10),!isNaN(e)&&(t=parseInt(s,10),!isNaN(t)&&e-t)?e-t:r<s?-1:1}return n.length!=o.length?n.length-o.length:e<t?-1:1},goog.string.intAwareCompare=function(e,t){return goog.string.numberAwareCompare_(e,t,/\d+|\D+/g)},goog.string.floatAwareCompare=function(e,t){return goog.string.numberAwareCompare_(e,t,/\d+|\.\d+|\D+/g)},goog.string.numerateCompare=goog.string.floatAwareCompare,goog.string.urlEncode=function(e){return encodeURIComponent(String(e))},goog.string.urlDecode=function(e){return decodeURIComponent(e.replace(/\+/g," "))},goog.string.newLineToBr=goog.string.internal.newLineToBr,goog.string.htmlEscape=function(e,t){return e=goog.string.internal.htmlEscape(e,t),goog.string.DETECT_DOUBLE_ESCAPING&&(e=e.replace(goog.string.E_RE_,"&#101;")),e},goog.string.E_RE_=/e/g,goog.string.unescapeEntities=function(e){return goog.string.contains(e,"&")?!goog.string.FORCE_NON_DOM_HTML_UNESCAPING&&"document"in goog.global?goog.string.unescapeEntitiesUsingDom_(e):goog.string.unescapePureXmlEntities_(e):e},goog.string.unescapeEntitiesWithDocument=function(e,t){return goog.string.contains(e,"&")?goog.string.unescapeEntitiesUsingDom_(e,t):e},goog.string.unescapeEntitiesUsingDom_=function(e,t){var r={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"'},n=t?t.createElement("div"):goog.global.document.createElement("div");return e.replace(goog.string.HTML_ENTITY_PATTERN_,(function(e,t){var o=r[e];return o||("#"==t.charAt(0)&&(t=Number("0"+t.substr(1)),isNaN(t)||(o=String.fromCharCode(t))),o||(goog.dom.safe.setInnerHtml(n,goog.html.uncheckedconversions.safeHtmlFromStringKnownToSatisfyTypeContract(goog.string.Const.from("Single HTML entity."),e+" ")),o=n.firstChild.nodeValue.slice(0,-1)),r[e]=o)}))},goog.string.unescapePureXmlEntities_=function(e){return e.replace(/&([^;]+);/g,(function(e,t){switch(t){case"amp":return"&";case"lt":return"<";case"gt":return">";case"quot":return'"';default:return"#"!=t.charAt(0)||(t=Number("0"+t.substr(1)),isNaN(t))?e:String.fromCharCode(t)}}))},goog.string.HTML_ENTITY_PATTERN_=/&([^;\s<&]+);?/g,goog.string.whitespaceEscape=function(e,t){return goog.string.newLineToBr(e.replace(/  /g," &#160;"),t)},goog.string.preserveSpaces=function(e){return e.replace(/(^|[\n ]) /g,"$1"+goog.string.Unicode.NBSP)},goog.string.stripQuotes=function(e,t){for(var r=t.length,n=0;n<r;n++){var o=1==r?t:t.charAt(n);if(e.charAt(0)==o&&e.charAt(e.length-1)==o)return e.substring(1,e.length-1)}return e},goog.string.truncate=function(e,t,r){return r&&(e=goog.string.unescapeEntities(e)),e.length>t&&(e=e.substring(0,t-3)+"..."),r&&(e=goog.string.htmlEscape(e)),e},goog.string.truncateMiddle=function(e,t,r,n){if(r&&(e=goog.string.unescapeEntities(e)),n&&e.length>t){n>t&&(n=t);var o=e.length-n;e=e.substring(0,t-n)+"..."+e.substring(o)}else e.length>t&&(n=Math.floor(t/2),o=e.length-n,e=e.substring(0,n+t%2)+"..."+e.substring(o));return r&&(e=goog.string.htmlEscape(e)),e},goog.string.specialEscapeChars_={"\0":"\\0","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\x0B",'"':'\\"',"\\":"\\\\","<":"\\u003C"},goog.string.jsEscapeCache_={"'":"\\'"},goog.string.quote=function(e){e=String(e);for(var t=['"'],r=0;r<e.length;r++){var n=e.charAt(r),o=n.charCodeAt(0);t[r+1]=goog.string.specialEscapeChars_[n]||(31<o&&127>o?n:goog.string.escapeChar(n))}return t.push('"'),t.join("")},goog.string.escapeString=function(e){for(var t=[],r=0;r<e.length;r++)t[r]=goog.string.escapeChar(e.charAt(r));return t.join("")},goog.string.escapeChar=function(e){if(e in goog.string.jsEscapeCache_)return goog.string.jsEscapeCache_[e];if(e in goog.string.specialEscapeChars_)return goog.string.jsEscapeCache_[e]=goog.string.specialEscapeChars_[e];var t=e.charCodeAt(0);if(31<t&&127>t)var r=e;else 256>t?(r="\\x",(16>t||256<t)&&(r+="0")):(r="\\u",4096>t&&(r+="0")),r+=t.toString(16).toUpperCase();return goog.string.jsEscapeCache_[e]=r},goog.string.contains=goog.string.internal.contains,goog.string.caseInsensitiveContains=goog.string.internal.caseInsensitiveContains,goog.string.countOf=function(e,t){return e&&t?e.split(t).length-1:0},goog.string.removeAt=function(e,t,r){var n=e;return 0<=t&&t<e.length&&0<r&&(n=e.substr(0,t)+e.substr(t+r,e.length-t-r)),n},goog.string.remove=function(e,t){return e.replace(t,"")},goog.string.removeAll=function(e,t){return t=new RegExp(goog.string.regExpEscape(t),"g"),e.replace(t,"")},goog.string.replaceAll=function(e,t,r){return t=new RegExp(goog.string.regExpEscape(t),"g"),e.replace(t,r.replace(/\$/g,"$$$$"))},goog.string.regExpEscape=function(e){return String(e).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")},goog.string.repeat=String.prototype.repeat?function(e,t){return e.repeat(t)}:function(e,t){return Array(t+1).join(e)},goog.string.padNumber=function(e,t,r){return-1==(r=(e=void 0!==r?e.toFixed(r):String(e)).indexOf("."))&&(r=e.length),goog.string.repeat("0",Math.max(0,t-r))+e},goog.string.makeSafe=function(e){return null==e?"":String(e)},goog.string.buildString=function(e){return Array.prototype.join.call(arguments,"")},goog.string.getRandomString=function(){return Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^goog.now()).toString(36)},goog.string.compareVersions=goog.string.internal.compareVersions,goog.string.hashCode=function(e){for(var t=0,r=0;r<e.length;++r)t=31*t+e.charCodeAt(r)>>>0;return t},goog.string.uniqueStringCounter_=2147483648*Math.random()|0,goog.string.createUniqueString=function(){return"goog_"+goog.string.uniqueStringCounter_++},goog.string.toNumber=function(e){var t=Number(e);return 0==t&&goog.string.isEmptyOrWhitespace(e)?NaN:t},goog.string.isLowerCamelCase=function(e){return/^[a-z]+([A-Z][a-z]*)*$/.test(e)},goog.string.isUpperCamelCase=function(e){return/^([A-Z][a-z]*)+$/.test(e)},goog.string.toCamelCase=function(e){return String(e).replace(/\-([a-z])/g,(function(e,t){return t.toUpperCase()}))},goog.string.toSelectorCase=function(e){return String(e).replace(/([A-Z])/g,"-$1").toLowerCase()},goog.string.toTitleCase=function(e,t){return t="string"==typeof t?goog.string.regExpEscape(t):"\\s",e.replace(new RegExp("(^"+(t?"|["+t+"]+":"")+")([a-z])","g"),(function(e,t,r){return t+r.toUpperCase()}))},goog.string.capitalize=function(e){return String(e.charAt(0)).toUpperCase()+String(e.substr(1)).toLowerCase()},goog.string.parseInt=function(e){return isFinite(e)&&(e=String(e)),"string"==typeof e?/^\s*-?0x/i.test(e)?parseInt(e,16):parseInt(e,10):NaN},goog.string.splitLimit=function(e,t,r){e=e.split(t);for(var n=[];0<r&&e.length;)n.push(e.shift()),r--;return e.length&&n.push(e.join(t)),n},goog.string.lastComponent=function(e,t){if(!t)return e;"string"==typeof t&&(t=[t]);for(var r=-1,n=0;n<t.length;n++)if(""!=t[n]){var o=e.lastIndexOf(t[n]);o>r&&(r=o)}return-1==r?e:e.slice(r+1)},goog.string.editDistance=function(e,t){var r=[],n=[];if(e==t)return 0;if(!e.length||!t.length)return Math.max(e.length,t.length);for(var o=0;o<t.length+1;o++)r[o]=o;for(o=0;o<e.length;o++){n[0]=o+1;for(var i=0;i<t.length;i++)n[i+1]=Math.min(n[i]+1,r[i+1]+1,r[i]+Number(e[o]!=t[i]));for(i=0;i<r.length;i++)r[i]=n[i]}return n[t.length]},goog.labs.userAgent.engine={},goog.labs.userAgent.engine.isPresto=function(){return goog.labs.userAgent.util.matchUserAgent("Presto")},goog.labs.userAgent.engine.isTrident=function(){return goog.labs.userAgent.util.matchUserAgent("Trident")||goog.labs.userAgent.util.matchUserAgent("MSIE")},goog.labs.userAgent.engine.isEdge=function(){return goog.labs.userAgent.util.matchUserAgent("Edge")},goog.labs.userAgent.engine.isWebKit=function(){return goog.labs.userAgent.util.matchUserAgentIgnoreCase("WebKit")&&!goog.labs.userAgent.engine.isEdge()},goog.labs.userAgent.engine.isGecko=function(){return goog.labs.userAgent.util.matchUserAgent("Gecko")&&!goog.labs.userAgent.engine.isWebKit()&&!goog.labs.userAgent.engine.isTrident()&&!goog.labs.userAgent.engine.isEdge()},goog.labs.userAgent.engine.getVersion=function(){var e=goog.labs.userAgent.util.getUserAgent();if(e){e=goog.labs.userAgent.util.extractVersionTuples(e);var t,r=goog.labs.userAgent.engine.getEngineTuple_(e);if(r)return"Gecko"==r[0]?goog.labs.userAgent.engine.getVersionForKey_(e,"Firefox"):r[1];if((e=e[0])&&(t=e[2])&&(t=/Trident\/([^\s;]+)/.exec(t)))return t[1]}return""},goog.labs.userAgent.engine.getEngineTuple_=function(e){if(!goog.labs.userAgent.engine.isEdge())return e[1];for(var t=0;t<e.length;t++){var r=e[t];if("Edge"==r[0])return r}},goog.labs.userAgent.engine.isVersionOrHigher=function(e){return 0<=goog.string.compareVersions(goog.labs.userAgent.engine.getVersion(),e)},goog.labs.userAgent.engine.getVersionForKey_=function(e,t){return(e=goog.array.find(e,(function(e){return t==e[0]})))&&e[1]||""},goog.labs.userAgent.platform={},goog.labs.userAgent.platform.isAndroid=function(){return goog.labs.userAgent.util.matchUserAgent("Android")},goog.labs.userAgent.platform.isIpod=function(){return goog.labs.userAgent.util.matchUserAgent("iPod")},goog.labs.userAgent.platform.isIphone=function(){return goog.labs.userAgent.util.matchUserAgent("iPhone")&&!goog.labs.userAgent.util.matchUserAgent("iPod")&&!goog.labs.userAgent.util.matchUserAgent("iPad")},goog.labs.userAgent.platform.isIpad=function(){return goog.labs.userAgent.util.matchUserAgent("iPad")},goog.labs.userAgent.platform.isIos=function(){return goog.labs.userAgent.platform.isIphone()||goog.labs.userAgent.platform.isIpad()||goog.labs.userAgent.platform.isIpod()},goog.labs.userAgent.platform.isMacintosh=function(){return goog.labs.userAgent.util.matchUserAgent("Macintosh")},goog.labs.userAgent.platform.isLinux=function(){return goog.labs.userAgent.util.matchUserAgent("Linux")},goog.labs.userAgent.platform.isWindows=function(){return goog.labs.userAgent.util.matchUserAgent("Windows")},goog.labs.userAgent.platform.isChromeOS=function(){return goog.labs.userAgent.util.matchUserAgent("CrOS")},goog.labs.userAgent.platform.isChromecast=function(){return goog.labs.userAgent.util.matchUserAgent("CrKey")},goog.labs.userAgent.platform.isKaiOS=function(){return goog.labs.userAgent.util.matchUserAgentIgnoreCase("KaiOS")},goog.labs.userAgent.platform.getVersion=function(){var e=goog.labs.userAgent.util.getUserAgent(),t="";return goog.labs.userAgent.platform.isWindows()?t=(e=(t=/Windows (?:NT|Phone) ([0-9.]+)/).exec(e))?e[1]:"0.0":goog.labs.userAgent.platform.isIos()?t=(e=(t=/(?:iPhone|iPod|iPad|CPU)\s+OS\s+(\S+)/).exec(e))&&e[1].replace(/_/g,"."):goog.labs.userAgent.platform.isMacintosh()?t=(e=(t=/Mac OS X ([0-9_.]+)/).exec(e))?e[1].replace(/_/g,"."):"10":goog.labs.userAgent.platform.isKaiOS()?t=(e=(t=/(?:KaiOS)\/(\S+)/i).exec(e))&&e[1]:goog.labs.userAgent.platform.isAndroid()?t=(e=(t=/Android\s+([^\);]+)(\)|;)/).exec(e))&&e[1]:goog.labs.userAgent.platform.isChromeOS()&&(t=(e=(t=/(?:CrOS\s+(?:i686|x86_64)\s+([0-9.]+))/).exec(e))&&e[1]),t||""},goog.labs.userAgent.platform.isVersionOrHigher=function(e){return 0<=goog.string.compareVersions(goog.labs.userAgent.platform.getVersion(),e)},goog.reflect={},goog.reflect.object=function(e,t){return t},goog.reflect.objectProperty=function(e,t){return e},goog.reflect.sinkValue=function(e){return goog.reflect.sinkValue[" "](e),e},goog.reflect.sinkValue[" "]=goog.nullFunction,goog.reflect.canAccessProperty=function(e,t){try{return goog.reflect.sinkValue(e[t]),!0}catch(e){}return!1},goog.reflect.cache=function(e,t,r,n){return n=n?n(t):t,Object.prototype.hasOwnProperty.call(e,n)?e[n]:e[n]=r(t)},goog.userAgent={},goog.userAgent.ASSUME_IE=!1,goog.userAgent.ASSUME_EDGE=!1,goog.userAgent.ASSUME_GECKO=!1,goog.userAgent.ASSUME_WEBKIT=!1,goog.userAgent.ASSUME_MOBILE_WEBKIT=!1,goog.userAgent.ASSUME_OPERA=!1,goog.userAgent.ASSUME_ANY_VERSION=!1,goog.userAgent.BROWSER_KNOWN_=goog.userAgent.ASSUME_IE||goog.userAgent.ASSUME_EDGE||goog.userAgent.ASSUME_GECKO||goog.userAgent.ASSUME_MOBILE_WEBKIT||goog.userAgent.ASSUME_WEBKIT||goog.userAgent.ASSUME_OPERA,goog.userAgent.getUserAgentString=function(){return goog.labs.userAgent.util.getUserAgent()},goog.userAgent.getNavigatorTyped=function(){return goog.global.navigator||null},goog.userAgent.getNavigator=function(){return goog.userAgent.getNavigatorTyped()},goog.userAgent.OPERA=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_OPERA:goog.labs.userAgent.browser.isOpera(),goog.userAgent.IE=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_IE:goog.labs.userAgent.browser.isIE(),goog.userAgent.EDGE=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_EDGE:goog.labs.userAgent.engine.isEdge(),goog.userAgent.EDGE_OR_IE=goog.userAgent.EDGE||goog.userAgent.IE,goog.userAgent.GECKO=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_GECKO:goog.labs.userAgent.engine.isGecko(),goog.userAgent.WEBKIT=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_WEBKIT||goog.userAgent.ASSUME_MOBILE_WEBKIT:goog.labs.userAgent.engine.isWebKit(),goog.userAgent.isMobile_=function(){return goog.userAgent.WEBKIT&&goog.labs.userAgent.util.matchUserAgent("Mobile")},goog.userAgent.MOBILE=goog.userAgent.ASSUME_MOBILE_WEBKIT||goog.userAgent.isMobile_(),goog.userAgent.SAFARI=goog.userAgent.WEBKIT,goog.userAgent.determinePlatform_=function(){var e=goog.userAgent.getNavigatorTyped();return e&&e.platform||""},goog.userAgent.PLATFORM=goog.userAgent.determinePlatform_(),goog.userAgent.ASSUME_MAC=!1,goog.userAgent.ASSUME_WINDOWS=!1,goog.userAgent.ASSUME_LINUX=!1,goog.userAgent.ASSUME_X11=!1,goog.userAgent.ASSUME_ANDROID=!1,goog.userAgent.ASSUME_IPHONE=!1,goog.userAgent.ASSUME_IPAD=!1,goog.userAgent.ASSUME_IPOD=!1,goog.userAgent.ASSUME_KAIOS=!1,goog.userAgent.PLATFORM_KNOWN_=goog.userAgent.ASSUME_MAC||goog.userAgent.ASSUME_WINDOWS||goog.userAgent.ASSUME_LINUX||goog.userAgent.ASSUME_X11||goog.userAgent.ASSUME_ANDROID||goog.userAgent.ASSUME_IPHONE||goog.userAgent.ASSUME_IPAD||goog.userAgent.ASSUME_IPOD,goog.userAgent.MAC=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_MAC:goog.labs.userAgent.platform.isMacintosh();goog.userAgent.WINDOWS=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_WINDOWS:goog.labs.userAgent.platform.isWindows(),goog.userAgent.isLegacyLinux_=function(){return goog.labs.userAgent.platform.isLinux()||goog.labs.userAgent.platform.isChromeOS()},goog.userAgent.LINUX=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_LINUX:goog.userAgent.isLegacyLinux_(),goog.userAgent.isX11_=function(){var e=goog.userAgent.getNavigatorTyped();return!!e&&goog.string.contains(e.appVersion||"","X11")},goog.userAgent.X11=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_X11:goog.userAgent.isX11_(),goog.userAgent.ANDROID=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_ANDROID:goog.labs.userAgent.platform.isAndroid(),goog.userAgent.IPHONE=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPHONE:goog.labs.userAgent.platform.isIphone(),goog.userAgent.IPAD=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPAD:goog.labs.userAgent.platform.isIpad(),goog.userAgent.IPOD=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPOD:goog.labs.userAgent.platform.isIpod(),goog.userAgent.IOS=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPHONE||goog.userAgent.ASSUME_IPAD||goog.userAgent.ASSUME_IPOD:goog.labs.userAgent.platform.isIos(),goog.userAgent.KAIOS=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_KAIOS:goog.labs.userAgent.platform.isKaiOS(),goog.userAgent.determineVersion_=function(){var e="",t=goog.userAgent.getVersionRegexResult_();return t&&(e=t?t[1]:""),goog.userAgent.IE&&(null!=(t=goog.userAgent.getDocumentMode_())&&t>parseFloat(e))?String(t):e},goog.userAgent.getVersionRegexResult_=function(){var e=goog.userAgent.getUserAgentString();return goog.userAgent.GECKO?/rv:([^\);]+)(\)|;)/.exec(e):goog.userAgent.EDGE?/Edge\/([\d\.]+)/.exec(e):goog.userAgent.IE?/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(e):goog.userAgent.WEBKIT?/WebKit\/(\S+)/.exec(e):goog.userAgent.OPERA?/(?:Version)[ \/]?(\S+)/.exec(e):void 0},goog.userAgent.getDocumentMode_=function(){var e=goog.global.document;return e?e.documentMode:void 0},goog.userAgent.VERSION=goog.userAgent.determineVersion_(),goog.userAgent.compare=function(e,t){return goog.string.compareVersions(e,t)},goog.userAgent.isVersionOrHigherCache_={},goog.userAgent.isVersionOrHigher=function(e){return goog.userAgent.ASSUME_ANY_VERSION||goog.reflect.cache(goog.userAgent.isVersionOrHigherCache_,e,(function(){return 0<=goog.string.compareVersions(goog.userAgent.VERSION,e)}))},goog.userAgent.isVersion=goog.userAgent.isVersionOrHigher,goog.userAgent.isDocumentModeOrHigher=function(e){return Number(goog.userAgent.DOCUMENT_MODE)>=e},goog.userAgent.isDocumentMode=goog.userAgent.isDocumentModeOrHigher,goog.userAgent.DOCUMENT_MODE=function(){if(goog.global.document&&goog.userAgent.IE){var e=goog.userAgent.getDocumentMode_();return e||(parseInt(goog.userAgent.VERSION,10)||void 0)}}(),goog.userAgent.product={},goog.userAgent.product.ASSUME_FIREFOX=!1,goog.userAgent.product.ASSUME_IPHONE=!1,goog.userAgent.product.ASSUME_IPAD=!1,goog.userAgent.product.ASSUME_ANDROID=!1,goog.userAgent.product.ASSUME_CHROME=!1,goog.userAgent.product.ASSUME_SAFARI=!1,goog.userAgent.product.PRODUCT_KNOWN_=goog.userAgent.ASSUME_IE||goog.userAgent.ASSUME_EDGE||goog.userAgent.ASSUME_OPERA||goog.userAgent.product.ASSUME_FIREFOX||goog.userAgent.product.ASSUME_IPHONE||goog.userAgent.product.ASSUME_IPAD||goog.userAgent.product.ASSUME_ANDROID||goog.userAgent.product.ASSUME_CHROME||goog.userAgent.product.ASSUME_SAFARI,goog.userAgent.product.OPERA=goog.userAgent.OPERA,goog.userAgent.product.IE=goog.userAgent.IE,goog.userAgent.product.EDGE=goog.userAgent.EDGE,goog.userAgent.product.FIREFOX=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_FIREFOX:goog.labs.userAgent.browser.isFirefox(),goog.userAgent.product.isIphoneOrIpod_=function(){return goog.labs.userAgent.platform.isIphone()||goog.labs.userAgent.platform.isIpod()},goog.userAgent.product.IPHONE=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_IPHONE:goog.userAgent.product.isIphoneOrIpod_(),goog.userAgent.product.IPAD=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_IPAD:goog.labs.userAgent.platform.isIpad(),goog.userAgent.product.ANDROID=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_ANDROID:goog.labs.userAgent.browser.isAndroidBrowser(),goog.userAgent.product.CHROME=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_CHROME:goog.labs.userAgent.browser.isChrome(),goog.userAgent.product.isSafariDesktop_=function(){return goog.labs.userAgent.browser.isSafari()&&!goog.labs.userAgent.platform.isIos()},goog.userAgent.product.SAFARI=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_SAFARI:goog.userAgent.product.isSafariDesktop_(),goog.crypt.base64={},goog.crypt.base64.DEFAULT_ALPHABET_COMMON_="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",goog.crypt.base64.ENCODED_VALS=goog.crypt.base64.DEFAULT_ALPHABET_COMMON_+"+/=",goog.crypt.base64.ENCODED_VALS_WEBSAFE=goog.crypt.base64.DEFAULT_ALPHABET_COMMON_+"-_.",goog.crypt.base64.Alphabet={DEFAULT:0,NO_PADDING:1,WEBSAFE:2,WEBSAFE_DOT_PADDING:3,WEBSAFE_NO_PADDING:4},goog.crypt.base64.paddingChars_="=.",goog.crypt.base64.isPadding_=function(e){return goog.string.contains(goog.crypt.base64.paddingChars_,e)},goog.crypt.base64.byteToCharMaps_={},goog.crypt.base64.charToByteMap_=null,goog.crypt.base64.ASSUME_NATIVE_SUPPORT_=goog.userAgent.GECKO||goog.userAgent.WEBKIT&&!goog.userAgent.product.SAFARI||goog.userAgent.OPERA,goog.crypt.base64.HAS_NATIVE_ENCODE_=goog.crypt.base64.ASSUME_NATIVE_SUPPORT_||"function"==typeof goog.global.btoa,goog.crypt.base64.HAS_NATIVE_DECODE_=goog.crypt.base64.ASSUME_NATIVE_SUPPORT_||!goog.userAgent.product.SAFARI&&!goog.userAgent.IE&&"function"==typeof goog.global.atob,goog.crypt.base64.encodeByteArray=function(e,t){goog.asserts.assert(goog.isArrayLike(e),"encodeByteArray takes an array as a parameter"),void 0===t&&(t=goog.crypt.base64.Alphabet.DEFAULT),goog.crypt.base64.init_(),t=goog.crypt.base64.byteToCharMaps_[t];for(var r=[],n=0;n<e.length;n+=3){var o=e[n],i=n+1<e.length,a=i?e[n+1]:0,s=n+2<e.length,l=s?e[n+2]:0,u=o>>2;o=(3&o)<<4|a>>4,a=(15&a)<<2|l>>6,l&=63,s||(l=64,i||(a=64)),r.push(t[u],t[o],t[a]||"",t[l]||"")}return r.join("")},goog.crypt.base64.encodeString=function(e,t){return goog.crypt.base64.HAS_NATIVE_ENCODE_&&!t?goog.global.btoa(e):goog.crypt.base64.encodeByteArray(goog.crypt.stringToByteArray(e),t)},goog.crypt.base64.decodeString=function(e,t){if(goog.crypt.base64.HAS_NATIVE_DECODE_&&!t)return goog.global.atob(e);var r="";return goog.crypt.base64.decodeStringInternal_(e,(function(e){r+=String.fromCharCode(e)})),r},goog.crypt.base64.decodeStringToByteArray=function(e,t){var r=[];return goog.crypt.base64.decodeStringInternal_(e,(function(e){r.push(e)})),r},goog.crypt.base64.decodeStringToUint8Array=function(e){goog.asserts.assert(!goog.userAgent.IE||goog.userAgent.isVersionOrHigher("10"),"Browser does not support typed arrays");var t=e.length,r=3*t/4;r%3?r=Math.floor(r):goog.crypt.base64.isPadding_(e[t-1])&&(r=goog.crypt.base64.isPadding_(e[t-2])?r-2:r-1);var n=new Uint8Array(r),o=0;return goog.crypt.base64.decodeStringInternal_(e,(function(e){n[o++]=e})),n.subarray(0,o)},goog.crypt.base64.decodeStringInternal_=function(e,t){function r(t){for(;n<e.length;){var r=e.charAt(n++),o=goog.crypt.base64.charToByteMap_[r];if(null!=o)return o;if(!goog.string.isEmptyOrWhitespace(r))throw Error("Unknown base64 encoding at char: "+r)}return t}goog.crypt.base64.init_();for(var n=0;;){var o=r(-1),i=r(0),a=r(64),s=r(64);if(64===s&&-1===o)break;t(o<<2|i>>4),64!=a&&(t(i<<4&240|a>>2),64!=s&&t(a<<6&192|s))}},goog.crypt.base64.init_=function(){if(!goog.crypt.base64.charToByteMap_){goog.crypt.base64.charToByteMap_={};for(var e=goog.crypt.base64.DEFAULT_ALPHABET_COMMON_.split(""),t=["+/=","+/","-_=","-_.","-_"],r=0;5>r;r++){var n=e.concat(t[r].split(""));goog.crypt.base64.byteToCharMaps_[r]=n;for(var o=0;o<n.length;o++){var i=n[o],a=goog.crypt.base64.charToByteMap_[i];void 0===a?goog.crypt.base64.charToByteMap_[i]=o:goog.asserts.assert(a===o)}}}},jspb.utils={},jspb.utils.split64Low=0,jspb.utils.split64High=0,jspb.utils.splitUint64=function(e){var t=e>>>0;e=Math.floor((e-t)/jspb.BinaryConstants.TWO_TO_32)>>>0,jspb.utils.split64Low=t,jspb.utils.split64High=e},jspb.utils.splitInt64=function(e){var t=0>e,r=(e=Math.abs(e))>>>0;e=Math.floor((e-r)/jspb.BinaryConstants.TWO_TO_32),e>>>=0,t&&(e=~e>>>0,4294967295<(r=1+(~r>>>0))&&(r=0,4294967295<++e&&(e=0))),jspb.utils.split64Low=r,jspb.utils.split64High=e},jspb.utils.splitZigzag64=function(e){var t=0>e;e=2*Math.abs(e),jspb.utils.splitUint64(e),e=jspb.utils.split64Low;var r=jspb.utils.split64High;t&&(0==e?0==r?r=e=4294967295:(r--,e=4294967295):e--),jspb.utils.split64Low=e,jspb.utils.split64High=r},jspb.utils.splitFloat32=function(e){var t=0>e?1:0;if(0===(e=t?-e:e))0<1/e?(jspb.utils.split64High=0,jspb.utils.split64Low=0):(jspb.utils.split64High=0,jspb.utils.split64Low=2147483648);else if(isNaN(e))jspb.utils.split64High=0,jspb.utils.split64Low=2147483647;else if(e>jspb.BinaryConstants.FLOAT32_MAX)jspb.utils.split64High=0,jspb.utils.split64Low=(t<<31|2139095040)>>>0;else if(e<jspb.BinaryConstants.FLOAT32_MIN)e=Math.round(e/Math.pow(2,-149)),jspb.utils.split64High=0,jspb.utils.split64Low=(t<<31|e)>>>0;else{var r=Math.floor(Math.log(e)/Math.LN2);e*=Math.pow(2,-r),16777216<=(e=Math.round(e*jspb.BinaryConstants.TWO_TO_23))&&++r,jspb.utils.split64High=0,jspb.utils.split64Low=(t<<31|r+127<<23|8388607&e)>>>0}},jspb.utils.splitFloat64=function(e){var t=0>e?1:0;if(0===(e=t?-e:e))jspb.utils.split64High=0<1/e?0:2147483648,jspb.utils.split64Low=0;else if(isNaN(e))jspb.utils.split64High=2147483647,jspb.utils.split64Low=4294967295;else if(e>jspb.BinaryConstants.FLOAT64_MAX)jspb.utils.split64High=(t<<31|2146435072)>>>0,jspb.utils.split64Low=0;else if(e<jspb.BinaryConstants.FLOAT64_MIN){var r=e/Math.pow(2,-1074);e=r/jspb.BinaryConstants.TWO_TO_32,jspb.utils.split64High=(t<<31|e)>>>0,jspb.utils.split64Low=r>>>0}else{var n=0;if(2<=(r=e))for(;2<=r&&1023>n;)n++,r/=2;else for(;1>r&&-1022<n;)r*=2,n--;e=(r=e*Math.pow(2,-n))*jspb.BinaryConstants.TWO_TO_20&1048575,r=r*jspb.BinaryConstants.TWO_TO_52>>>0,jspb.utils.split64High=(t<<31|n+1023<<20|e)>>>0,jspb.utils.split64Low=r}},jspb.utils.splitHash64=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),o=e.charCodeAt(3),i=e.charCodeAt(4),a=e.charCodeAt(5),s=e.charCodeAt(6);e=e.charCodeAt(7),jspb.utils.split64Low=t+(r<<8)+(n<<16)+(o<<24)>>>0,jspb.utils.split64High=i+(a<<8)+(s<<16)+(e<<24)>>>0},jspb.utils.joinUint64=function(e,t){return t*jspb.BinaryConstants.TWO_TO_32+(e>>>0)},jspb.utils.joinInt64=function(e,t){var r=2147483648&t;return r&&(t=~t>>>0,0==(e=1+~e>>>0)&&(t=t+1>>>0)),e=jspb.utils.joinUint64(e,t),r?-e:e},jspb.utils.toZigzag64=function(e,t,r){var n=t>>31;return r(e<<1^n,(t<<1|e>>>31)^n)},jspb.utils.joinZigzag64=function(e,t){return jspb.utils.fromZigzag64(e,t,jspb.utils.joinInt64)},jspb.utils.fromZigzag64=function(e,t,r){var n=-(1&e);return r((e>>>1|t<<31)^n,t>>>1^n)},jspb.utils.joinFloat32=function(e,t){t=2*(e>>31)+1;var r=e>>>23&255;return e&=8388607,255==r?e?NaN:1/0*t:0==r?t*Math.pow(2,-149)*e:t*Math.pow(2,r-150)*(e+Math.pow(2,23))},jspb.utils.joinFloat64=function(e,t){var r=2*(t>>31)+1,n=t>>>20&2047;return e=jspb.BinaryConstants.TWO_TO_32*(1048575&t)+e,2047==n?e?NaN:1/0*r:0==n?r*Math.pow(2,-1074)*e:r*Math.pow(2,n-1075)*(e+jspb.BinaryConstants.TWO_TO_52)},jspb.utils.joinHash64=function(e,t){return String.fromCharCode(e>>>0&255,e>>>8&255,e>>>16&255,e>>>24&255,t>>>0&255,t>>>8&255,t>>>16&255,t>>>24&255)},jspb.utils.DIGITS="0123456789abcdef".split(""),jspb.utils.ZERO_CHAR_CODE_=48,jspb.utils.A_CHAR_CODE_=97,jspb.utils.joinUnsignedDecimalString=function(e,t){function r(e,t){return e=e?String(e):"",t?"0000000".slice(e.length)+e:e}if(2097151>=t)return""+jspb.utils.joinUint64(e,t);var n=(e>>>24|t<<8)>>>0&16777215;return e=(16777215&e)+6777216*n+6710656*(t=t>>16&65535),n+=8147497*t,t*=2,1e7<=e&&(n+=Math.floor(e/1e7),e%=1e7),1e7<=n&&(t+=Math.floor(n/1e7),n%=1e7),r(t,0)+r(n,t)+r(e,1)},jspb.utils.joinSignedDecimalString=function(e,t){var r=2147483648&t;return r&&(t=~t+(0==(e=1+~e>>>0)?1:0)>>>0),e=jspb.utils.joinUnsignedDecimalString(e,t),r?"-"+e:e},jspb.utils.hash64ToDecimalString=function(e,t){jspb.utils.splitHash64(e),e=jspb.utils.split64Low;var r=jspb.utils.split64High;return t?jspb.utils.joinSignedDecimalString(e,r):jspb.utils.joinUnsignedDecimalString(e,r)},jspb.utils.hash64ArrayToDecimalStrings=function(e,t){for(var r=Array(e.length),n=0;n<e.length;n++)r[n]=jspb.utils.hash64ToDecimalString(e[n],t);return r},jspb.utils.decimalStringToHash64=function(e){function t(e,t){for(var r=0;8>r&&(1!==e||0<t);r++)t=e*n[r]+t,n[r]=255&t,t>>>=8}jspb.asserts.assert(0<e.length);var r=!1;"-"===e[0]&&(r=!0,e=e.slice(1));for(var n=[0,0,0,0,0,0,0,0],o=0;o<e.length;o++)t(10,e.charCodeAt(o)-jspb.utils.ZERO_CHAR_CODE_);return r&&(function(){for(var e=0;8>e;e++)n[e]=255&~n[e]}(),t(1,1)),goog.crypt.byteArrayToString(n)},jspb.utils.splitDecimalString=function(e){jspb.utils.splitHash64(jspb.utils.decimalStringToHash64(e))},jspb.utils.toHexDigit_=function(e){return String.fromCharCode(10>e?jspb.utils.ZERO_CHAR_CODE_+e:jspb.utils.A_CHAR_CODE_-10+e)},jspb.utils.fromHexCharCode_=function(e){return e>=jspb.utils.A_CHAR_CODE_?e-jspb.utils.A_CHAR_CODE_+10:e-jspb.utils.ZERO_CHAR_CODE_},jspb.utils.hash64ToHexString=function(e){var t=Array(18);t[0]="0",t[1]="x";for(var r=0;8>r;r++){var n=e.charCodeAt(7-r);t[2*r+2]=jspb.utils.toHexDigit_(n>>4),t[2*r+3]=jspb.utils.toHexDigit_(15&n)}return t.join("")},jspb.utils.hexStringToHash64=function(e){e=e.toLowerCase(),jspb.asserts.assert(18==e.length),jspb.asserts.assert("0"==e[0]),jspb.asserts.assert("x"==e[1]);for(var t="",r=0;8>r;r++){var n=jspb.utils.fromHexCharCode_(e.charCodeAt(2*r+2)),o=jspb.utils.fromHexCharCode_(e.charCodeAt(2*r+3));t=String.fromCharCode(16*n+o)+t}return t},jspb.utils.hash64ToNumber=function(e,t){jspb.utils.splitHash64(e),e=jspb.utils.split64Low;var r=jspb.utils.split64High;return t?jspb.utils.joinInt64(e,r):jspb.utils.joinUint64(e,r)},jspb.utils.numberToHash64=function(e){return jspb.utils.splitInt64(e),jspb.utils.joinHash64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.utils.countVarints=function(e,t,r){for(var n=0,o=t;o<r;o++)n+=e[o]>>7;return r-t-n},jspb.utils.countVarintFields=function(e,t,r,n){var o=0;if(128>(n=8*n+jspb.BinaryConstants.WireType.VARINT))for(;t<r&&e[t++]==n;)for(o++;;){var i=e[t++];if(0==(128&i))break}else for(;t<r;){for(i=n;128<i;){if(e[t]!=(127&i|128))return o;t++,i>>=7}if(e[t++]!=i)break;for(o++;0!=(128&(i=e[t++])););}return o},jspb.utils.countFixedFields_=function(e,t,r,n,o){var i=0;if(128>n)for(;t<r&&e[t++]==n;)i++,t+=o;else for(;t<r;){for(var a=n;128<a;){if(e[t++]!=(127&a|128))return i;a>>=7}if(e[t++]!=a)break;i++,t+=o}return i},jspb.utils.countFixed32Fields=function(e,t,r,n){return jspb.utils.countFixedFields_(e,t,r,8*n+jspb.BinaryConstants.WireType.FIXED32,4)},jspb.utils.countFixed64Fields=function(e,t,r,n){return jspb.utils.countFixedFields_(e,t,r,8*n+jspb.BinaryConstants.WireType.FIXED64,8)},jspb.utils.countDelimitedFields=function(e,t,r,n){var o=0;for(n=8*n+jspb.BinaryConstants.WireType.DELIMITED;t<r;){for(var i=n;128<i;){if(e[t++]!=(127&i|128))return o;i>>=7}if(e[t++]!=i)break;o++;for(var a=0,s=1;a+=(127&(i=e[t++]))*s,s*=128,0!=(128&i););t+=a}return o},jspb.utils.debugBytesToTextFormat=function(e){var t='"';if(e){e=jspb.utils.byteSourceToUint8Array(e);for(var r=0;r<e.length;r++)t+="\\x",16>e[r]&&(t+="0"),t+=e[r].toString(16)}return t+'"'},jspb.utils.debugScalarToTextFormat=function(e){return"string"==typeof e?goog.string.quote(e):e.toString()},jspb.utils.stringToByteArray=function(e){for(var t=new Uint8Array(e.length),r=0;r<e.length;r++){var n=e.charCodeAt(r);if(255<n)throw Error("Conversion error: string contains codepoint outside of byte range");t[r]=n}return t},jspb.utils.byteSourceToUint8Array=function(e){return e.constructor===Uint8Array?e:e.constructor===ArrayBuffer||e.constructor===Array?new Uint8Array(e):e.constructor===String?goog.crypt.base64.decodeStringToUint8Array(e):e instanceof Uint8Array?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):(jspb.asserts.fail("Type not convertible to Uint8Array."),new Uint8Array(0))},jspb.BinaryDecoder=function(e,t,r){this.bytes_=null,this.cursor_=this.end_=this.start_=0,this.error_=!1,e&&this.setBlock(e,t,r)},jspb.BinaryDecoder.instanceCache_=[],jspb.BinaryDecoder.alloc=function(e,t,r){if(jspb.BinaryDecoder.instanceCache_.length){var n=jspb.BinaryDecoder.instanceCache_.pop();return e&&n.setBlock(e,t,r),n}return new jspb.BinaryDecoder(e,t,r)},jspb.BinaryDecoder.prototype.free=function(){this.clear(),100>jspb.BinaryDecoder.instanceCache_.length&&jspb.BinaryDecoder.instanceCache_.push(this)},jspb.BinaryDecoder.prototype.clone=function(){return jspb.BinaryDecoder.alloc(this.bytes_,this.start_,this.end_-this.start_)},jspb.BinaryDecoder.prototype.clear=function(){this.bytes_=null,this.cursor_=this.end_=this.start_=0,this.error_=!1},jspb.BinaryDecoder.prototype.getBuffer=function(){return this.bytes_},jspb.BinaryDecoder.prototype.setBlock=function(e,t,r){this.bytes_=jspb.utils.byteSourceToUint8Array(e),this.start_=void 0!==t?t:0,this.end_=void 0!==r?this.start_+r:this.bytes_.length,this.cursor_=this.start_},jspb.BinaryDecoder.prototype.getEnd=function(){return this.end_},jspb.BinaryDecoder.prototype.setEnd=function(e){this.end_=e},jspb.BinaryDecoder.prototype.reset=function(){this.cursor_=this.start_},jspb.BinaryDecoder.prototype.getCursor=function(){return this.cursor_},jspb.BinaryDecoder.prototype.setCursor=function(e){this.cursor_=e},jspb.BinaryDecoder.prototype.advance=function(e){this.cursor_+=e,jspb.asserts.assert(this.cursor_<=this.end_)},jspb.BinaryDecoder.prototype.atEnd=function(){return this.cursor_==this.end_},jspb.BinaryDecoder.prototype.pastEnd=function(){return this.cursor_>this.end_},jspb.BinaryDecoder.prototype.getError=function(){return this.error_||0>this.cursor_||this.cursor_>this.end_},jspb.BinaryDecoder.prototype.readSplitVarint64=function(e){for(var t=128,r=0,n=0,o=0;4>o&&128<=t;o++)r|=(127&(t=this.bytes_[this.cursor_++]))<<7*o;if(128<=t&&(r|=(127&(t=this.bytes_[this.cursor_++]))<<28,n|=(127&t)>>4),128<=t)for(o=0;5>o&&128<=t;o++)n|=(127&(t=this.bytes_[this.cursor_++]))<<7*o+3;if(128>t)return e(r>>>0,n>>>0);jspb.asserts.fail("Failed to read varint, encoding is invalid."),this.error_=!0},jspb.BinaryDecoder.prototype.readSplitZigzagVarint64=function(e){return this.readSplitVarint64((function(t,r){return jspb.utils.fromZigzag64(t,r,e)}))},jspb.BinaryDecoder.prototype.readSplitFixed64=function(e){var t=this.bytes_,r=this.cursor_;this.cursor_+=8;for(var n=0,o=0,i=r+7;i>=r;i--)n=n<<8|t[i],o=o<<8|t[i+4];return e(n,o)},jspb.BinaryDecoder.prototype.skipVarint=function(){for(;128&this.bytes_[this.cursor_];)this.cursor_++;this.cursor_++},jspb.BinaryDecoder.prototype.unskipVarint=function(e){for(;128<e;)this.cursor_--,e>>>=7;this.cursor_--},jspb.BinaryDecoder.prototype.readUnsignedVarint32=function(){var e=this.bytes_,t=e[this.cursor_+0],r=127&t;return 128>t?(this.cursor_+=1,jspb.asserts.assert(this.cursor_<=this.end_),r):(r|=(127&(t=e[this.cursor_+1]))<<7,128>t?(this.cursor_+=2,jspb.asserts.assert(this.cursor_<=this.end_),r):(r|=(127&(t=e[this.cursor_+2]))<<14,128>t?(this.cursor_+=3,jspb.asserts.assert(this.cursor_<=this.end_),r):(r|=(127&(t=e[this.cursor_+3]))<<21,128>t?(this.cursor_+=4,jspb.asserts.assert(this.cursor_<=this.end_),r):(r|=(15&(t=e[this.cursor_+4]))<<28,128>t?(this.cursor_+=5,jspb.asserts.assert(this.cursor_<=this.end_),r>>>0):(this.cursor_+=5,128<=e[this.cursor_++]&&128<=e[this.cursor_++]&&128<=e[this.cursor_++]&&128<=e[this.cursor_++]&&128<=e[this.cursor_++]&&jspb.asserts.assert(!1),jspb.asserts.assert(this.cursor_<=this.end_),r)))))},jspb.BinaryDecoder.prototype.readSignedVarint32=function(){return~~this.readUnsignedVarint32()},jspb.BinaryDecoder.prototype.readUnsignedVarint32String=function(){return this.readUnsignedVarint32().toString()},jspb.BinaryDecoder.prototype.readSignedVarint32String=function(){return this.readSignedVarint32().toString()},jspb.BinaryDecoder.prototype.readZigzagVarint32=function(){var e=this.readUnsignedVarint32();return e>>>1^-(1&e)},jspb.BinaryDecoder.prototype.readUnsignedVarint64=function(){return this.readSplitVarint64(jspb.utils.joinUint64)},jspb.BinaryDecoder.prototype.readUnsignedVarint64String=function(){return this.readSplitVarint64(jspb.utils.joinUnsignedDecimalString)},jspb.BinaryDecoder.prototype.readSignedVarint64=function(){return this.readSplitVarint64(jspb.utils.joinInt64)},jspb.BinaryDecoder.prototype.readSignedVarint64String=function(){return this.readSplitVarint64(jspb.utils.joinSignedDecimalString)},jspb.BinaryDecoder.prototype.readZigzagVarint64=function(){return this.readSplitVarint64(jspb.utils.joinZigzag64)},jspb.BinaryDecoder.prototype.readZigzagVarintHash64=function(){return this.readSplitZigzagVarint64(jspb.utils.joinHash64)},jspb.BinaryDecoder.prototype.readZigzagVarint64String=function(){return this.readSplitZigzagVarint64(jspb.utils.joinSignedDecimalString)},jspb.BinaryDecoder.prototype.readUint8=function(){var e=this.bytes_[this.cursor_+0];return this.cursor_+=1,jspb.asserts.assert(this.cursor_<=this.end_),e},jspb.BinaryDecoder.prototype.readUint16=function(){var e=this.bytes_[this.cursor_+0],t=this.bytes_[this.cursor_+1];return this.cursor_+=2,jspb.asserts.assert(this.cursor_<=this.end_),e<<0|t<<8},jspb.BinaryDecoder.prototype.readUint32=function(){var e=this.bytes_[this.cursor_+0],t=this.bytes_[this.cursor_+1],r=this.bytes_[this.cursor_+2],n=this.bytes_[this.cursor_+3];return this.cursor_+=4,jspb.asserts.assert(this.cursor_<=this.end_),(e<<0|t<<8|r<<16|n<<24)>>>0},jspb.BinaryDecoder.prototype.readUint64=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinUint64(e,t)},jspb.BinaryDecoder.prototype.readUint64String=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinUnsignedDecimalString(e,t)},jspb.BinaryDecoder.prototype.readInt8=function(){var e=this.bytes_[this.cursor_+0];return this.cursor_+=1,jspb.asserts.assert(this.cursor_<=this.end_),e<<24>>24},jspb.BinaryDecoder.prototype.readInt16=function(){var e=this.bytes_[this.cursor_+0],t=this.bytes_[this.cursor_+1];return this.cursor_+=2,jspb.asserts.assert(this.cursor_<=this.end_),(e<<0|t<<8)<<16>>16},jspb.BinaryDecoder.prototype.readInt32=function(){var e=this.bytes_[this.cursor_+0],t=this.bytes_[this.cursor_+1],r=this.bytes_[this.cursor_+2],n=this.bytes_[this.cursor_+3];return this.cursor_+=4,jspb.asserts.assert(this.cursor_<=this.end_),e<<0|t<<8|r<<16|n<<24},jspb.BinaryDecoder.prototype.readInt64=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinInt64(e,t)},jspb.BinaryDecoder.prototype.readInt64String=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinSignedDecimalString(e,t)},jspb.BinaryDecoder.prototype.readFloat=function(){var e=this.readUint32();return jspb.utils.joinFloat32(e,0)},jspb.BinaryDecoder.prototype.readDouble=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinFloat64(e,t)},jspb.BinaryDecoder.prototype.readBool=function(){return!!this.bytes_[this.cursor_++]},jspb.BinaryDecoder.prototype.readEnum=function(){return this.readSignedVarint32()},jspb.BinaryDecoder.prototype.readString=function(e){for(var t=this.bytes_,r=this.cursor_,n=r+e,o=[],i="";r<n;){var a=t[r++];if(a<128)o.push(a);else{if(a<192)continue;if(a<224){if(n-r<1)break;var s=t[r++];o.push((31&a)<<6|63&s)}else if(a<240){if(n-r<2)break;s=t[r++];var l=t[r++];o.push((15&a)<<12|(63&s)<<6|63&l)}else if(a<248){if(n-r<3)break;var u=(7&a)<<18|(63&(s=t[r++]))<<12|(63&(l=t[r++]))<<6|63&t[r++],g=56320+(1023&(u-=65536)),c=55296+(u>>10&1023);o.push(c,g)}}o.length>=8192&&(i+=String.fromCharCode.apply(null,o),o.length=0)}return i+=goog.crypt.byteArrayToString(o),this.cursor_=n,i},jspb.BinaryDecoder.prototype.readStringWithLength=function(){var e=this.readUnsignedVarint32();return this.readString(e)},jspb.BinaryDecoder.prototype.readBytes=function(e){if(0>e||this.cursor_+e>this.bytes_.length)return this.error_=!0,jspb.asserts.fail("Invalid byte length!"),new Uint8Array(0);var t=this.bytes_.subarray(this.cursor_,this.cursor_+e);return this.cursor_+=e,jspb.asserts.assert(this.cursor_<=this.end_),t},jspb.BinaryDecoder.prototype.readVarintHash64=function(){return this.readSplitVarint64(jspb.utils.joinHash64)},jspb.BinaryDecoder.prototype.readFixedHash64=function(){var e=this.bytes_,t=this.cursor_,r=e[t+0],n=e[t+1],o=e[t+2],i=e[t+3],a=e[t+4],s=e[t+5],l=e[t+6];return e=e[t+7],this.cursor_+=8,String.fromCharCode(r,n,o,i,a,s,l,e)},jspb.BinaryReader=function(e,t,r){this.decoder_=jspb.BinaryDecoder.alloc(e,t,r),this.fieldCursor_=this.decoder_.getCursor(),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID,this.error_=!1,this.readCallbacks_=null},jspb.BinaryReader.instanceCache_=[],jspb.BinaryReader.alloc=function(e,t,r){if(jspb.BinaryReader.instanceCache_.length){var n=jspb.BinaryReader.instanceCache_.pop();return e&&n.decoder_.setBlock(e,t,r),n}return new jspb.BinaryReader(e,t,r)},jspb.BinaryReader.prototype.alloc=jspb.BinaryReader.alloc,jspb.BinaryReader.prototype.free=function(){this.decoder_.clear(),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID,this.error_=!1,this.readCallbacks_=null,100>jspb.BinaryReader.instanceCache_.length&&jspb.BinaryReader.instanceCache_.push(this)},jspb.BinaryReader.prototype.getFieldCursor=function(){return this.fieldCursor_},jspb.BinaryReader.prototype.getCursor=function(){return this.decoder_.getCursor()},jspb.BinaryReader.prototype.getBuffer=function(){return this.decoder_.getBuffer()},jspb.BinaryReader.prototype.getFieldNumber=function(){return this.nextField_},goog.exportProperty(jspb.BinaryReader.prototype,"getFieldNumber",jspb.BinaryReader.prototype.getFieldNumber),jspb.BinaryReader.prototype.getWireType=function(){return this.nextWireType_},jspb.BinaryReader.prototype.isDelimited=function(){return this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED},goog.exportProperty(jspb.BinaryReader.prototype,"isDelimited",jspb.BinaryReader.prototype.isDelimited),jspb.BinaryReader.prototype.isEndGroup=function(){return this.nextWireType_==jspb.BinaryConstants.WireType.END_GROUP},goog.exportProperty(jspb.BinaryReader.prototype,"isEndGroup",jspb.BinaryReader.prototype.isEndGroup),jspb.BinaryReader.prototype.getError=function(){return this.error_||this.decoder_.getError()},jspb.BinaryReader.prototype.setBlock=function(e,t,r){this.decoder_.setBlock(e,t,r),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID},jspb.BinaryReader.prototype.reset=function(){this.decoder_.reset(),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID},jspb.BinaryReader.prototype.advance=function(e){this.decoder_.advance(e)},jspb.BinaryReader.prototype.nextField=function(){if(this.decoder_.atEnd())return!1;if(this.getError())return jspb.asserts.fail("Decoder hit an error"),!1;this.fieldCursor_=this.decoder_.getCursor();var e=this.decoder_.readUnsignedVarint32(),t=e>>>3;return(e&=7)!=jspb.BinaryConstants.WireType.VARINT&&e!=jspb.BinaryConstants.WireType.FIXED32&&e!=jspb.BinaryConstants.WireType.FIXED64&&e!=jspb.BinaryConstants.WireType.DELIMITED&&e!=jspb.BinaryConstants.WireType.START_GROUP&&e!=jspb.BinaryConstants.WireType.END_GROUP?(jspb.asserts.fail("Invalid wire type: %s (at position %s)",e,this.fieldCursor_),this.error_=!0,!1):(this.nextField_=t,this.nextWireType_=e,!0)},goog.exportProperty(jspb.BinaryReader.prototype,"nextField",jspb.BinaryReader.prototype.nextField),jspb.BinaryReader.prototype.unskipHeader=function(){this.decoder_.unskipVarint(this.nextField_<<3|this.nextWireType_)},jspb.BinaryReader.prototype.skipMatchingFields=function(){var e=this.nextField_;for(this.unskipHeader();this.nextField()&&this.getFieldNumber()==e;)this.skipField();this.decoder_.atEnd()||this.unskipHeader()},jspb.BinaryReader.prototype.skipVarintField=function(){this.nextWireType_!=jspb.BinaryConstants.WireType.VARINT?(jspb.asserts.fail("Invalid wire type for skipVarintField"),this.skipField()):this.decoder_.skipVarint()},jspb.BinaryReader.prototype.skipDelimitedField=function(){if(this.nextWireType_!=jspb.BinaryConstants.WireType.DELIMITED)jspb.asserts.fail("Invalid wire type for skipDelimitedField"),this.skipField();else{var e=this.decoder_.readUnsignedVarint32();this.decoder_.advance(e)}},jspb.BinaryReader.prototype.skipFixed32Field=function(){this.nextWireType_!=jspb.BinaryConstants.WireType.FIXED32?(jspb.asserts.fail("Invalid wire type for skipFixed32Field"),this.skipField()):this.decoder_.advance(4)},jspb.BinaryReader.prototype.skipFixed64Field=function(){this.nextWireType_!=jspb.BinaryConstants.WireType.FIXED64?(jspb.asserts.fail("Invalid wire type for skipFixed64Field"),this.skipField()):this.decoder_.advance(8)},jspb.BinaryReader.prototype.skipGroup=function(){for(var e=this.nextField_;;){if(!this.nextField()){jspb.asserts.fail("Unmatched start-group tag: stream EOF"),this.error_=!0;break}if(this.nextWireType_==jspb.BinaryConstants.WireType.END_GROUP){this.nextField_!=e&&(jspb.asserts.fail("Unmatched end-group tag"),this.error_=!0);break}this.skipField()}},jspb.BinaryReader.prototype.skipField=function(){switch(this.nextWireType_){case jspb.BinaryConstants.WireType.VARINT:this.skipVarintField();break;case jspb.BinaryConstants.WireType.FIXED64:this.skipFixed64Field();break;case jspb.BinaryConstants.WireType.DELIMITED:this.skipDelimitedField();break;case jspb.BinaryConstants.WireType.FIXED32:this.skipFixed32Field();break;case jspb.BinaryConstants.WireType.START_GROUP:this.skipGroup();break;default:jspb.asserts.fail("Invalid wire encoding for field.")}},jspb.BinaryReader.prototype.registerReadCallback=function(e,t){null===this.readCallbacks_&&(this.readCallbacks_={}),jspb.asserts.assert(!this.readCallbacks_[e]),this.readCallbacks_[e]=t},jspb.BinaryReader.prototype.runReadCallback=function(e){return jspb.asserts.assert(null!==this.readCallbacks_),e=this.readCallbacks_[e],jspb.asserts.assert(e),e(this)},jspb.BinaryReader.prototype.readAny=function(e){this.nextWireType_=jspb.BinaryConstants.FieldTypeToWireType(e);var t=jspb.BinaryConstants.FieldType;switch(e){case t.DOUBLE:return this.readDouble();case t.FLOAT:return this.readFloat();case t.INT64:return this.readInt64();case t.UINT64:return this.readUint64();case t.INT32:return this.readInt32();case t.FIXED64:return this.readFixed64();case t.FIXED32:return this.readFixed32();case t.BOOL:return this.readBool();case t.STRING:return this.readString();case t.GROUP:jspb.asserts.fail("Group field type not supported in readAny()");case t.MESSAGE:jspb.asserts.fail("Message field type not supported in readAny()");case t.BYTES:return this.readBytes();case t.UINT32:return this.readUint32();case t.ENUM:return this.readEnum();case t.SFIXED32:return this.readSfixed32();case t.SFIXED64:return this.readSfixed64();case t.SINT32:return this.readSint32();case t.SINT64:return this.readSint64();case t.FHASH64:return this.readFixedHash64();case t.VHASH64:return this.readVarintHash64();default:jspb.asserts.fail("Invalid field type in readAny()")}return 0},jspb.BinaryReader.prototype.readMessage=function(e,t){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var r=this.decoder_.getEnd(),n=this.decoder_.readUnsignedVarint32();n=this.decoder_.getCursor()+n,this.decoder_.setEnd(n),t(e,this),this.decoder_.setCursor(n),this.decoder_.setEnd(r)},goog.exportProperty(jspb.BinaryReader.prototype,"readMessage",jspb.BinaryReader.prototype.readMessage),jspb.BinaryReader.prototype.readGroup=function(e,t,r){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.START_GROUP),jspb.asserts.assert(this.nextField_==e),r(t,this),this.error_||this.nextWireType_==jspb.BinaryConstants.WireType.END_GROUP||(jspb.asserts.fail("Group submessage did not end with an END_GROUP tag"),this.error_=!0)},goog.exportProperty(jspb.BinaryReader.prototype,"readGroup",jspb.BinaryReader.prototype.readGroup),jspb.BinaryReader.prototype.getFieldDecoder=function(){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var e=this.decoder_.readUnsignedVarint32(),t=this.decoder_.getCursor(),r=t+e;return e=jspb.BinaryDecoder.alloc(this.decoder_.getBuffer(),t,e),this.decoder_.setCursor(r),e},jspb.BinaryReader.prototype.readInt32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readInt32",jspb.BinaryReader.prototype.readInt32),jspb.BinaryReader.prototype.readInt32String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint32String()},jspb.BinaryReader.prototype.readInt64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readInt64",jspb.BinaryReader.prototype.readInt64),jspb.BinaryReader.prototype.readInt64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint64String()},jspb.BinaryReader.prototype.readUint32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readUint32",jspb.BinaryReader.prototype.readUint32),jspb.BinaryReader.prototype.readUint32String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint32String()},jspb.BinaryReader.prototype.readUint64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readUint64",jspb.BinaryReader.prototype.readUint64),jspb.BinaryReader.prototype.readUint64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint64String()},jspb.BinaryReader.prototype.readSint32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readSint32",jspb.BinaryReader.prototype.readSint32),jspb.BinaryReader.prototype.readSint64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readSint64",jspb.BinaryReader.prototype.readSint64),jspb.BinaryReader.prototype.readSint64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarint64String()},jspb.BinaryReader.prototype.readFixed32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readUint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readFixed32",jspb.BinaryReader.prototype.readFixed32),jspb.BinaryReader.prototype.readFixed64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readUint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readFixed64",jspb.BinaryReader.prototype.readFixed64),jspb.BinaryReader.prototype.readFixed64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readUint64String()},jspb.BinaryReader.prototype.readSfixed32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readInt32()},goog.exportProperty(jspb.BinaryReader.prototype,"readSfixed32",jspb.BinaryReader.prototype.readSfixed32),jspb.BinaryReader.prototype.readSfixed32String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readInt32().toString()},jspb.BinaryReader.prototype.readSfixed64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readInt64()},goog.exportProperty(jspb.BinaryReader.prototype,"readSfixed64",jspb.BinaryReader.prototype.readSfixed64),jspb.BinaryReader.prototype.readSfixed64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readInt64String()},jspb.BinaryReader.prototype.readFloat=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readFloat()},goog.exportProperty(jspb.BinaryReader.prototype,"readFloat",jspb.BinaryReader.prototype.readFloat),jspb.BinaryReader.prototype.readDouble=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readDouble()},goog.exportProperty(jspb.BinaryReader.prototype,"readDouble",jspb.BinaryReader.prototype.readDouble),jspb.BinaryReader.prototype.readBool=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),!!this.decoder_.readUnsignedVarint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readBool",jspb.BinaryReader.prototype.readBool),jspb.BinaryReader.prototype.readEnum=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readEnum",jspb.BinaryReader.prototype.readEnum),jspb.BinaryReader.prototype.readString=function(){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var e=this.decoder_.readUnsignedVarint32();return this.decoder_.readString(e)},goog.exportProperty(jspb.BinaryReader.prototype,"readString",jspb.BinaryReader.prototype.readString),jspb.BinaryReader.prototype.readBytes=function(){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var e=this.decoder_.readUnsignedVarint32();return this.decoder_.readBytes(e)},goog.exportProperty(jspb.BinaryReader.prototype,"readBytes",jspb.BinaryReader.prototype.readBytes),jspb.BinaryReader.prototype.readVarintHash64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readVarintHash64()},jspb.BinaryReader.prototype.readSintHash64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarintHash64()},jspb.BinaryReader.prototype.readSplitVarint64=function(e){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSplitVarint64(e)},jspb.BinaryReader.prototype.readSplitZigzagVarint64=function(e){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSplitVarint64((function(t,r){return jspb.utils.fromZigzag64(t,r,e)}))},jspb.BinaryReader.prototype.readFixedHash64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readFixedHash64()},jspb.BinaryReader.prototype.readSplitFixed64=function(e){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readSplitFixed64(e)},jspb.BinaryReader.prototype.readPackedField_=function(e){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var t=this.decoder_.readUnsignedVarint32();t=this.decoder_.getCursor()+t;for(var r=[];this.decoder_.getCursor()<t;)r.push(e.call(this.decoder_));return r},jspb.BinaryReader.prototype.readPackedInt32=function(){return this.readPackedField_(this.decoder_.readSignedVarint32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedInt32",jspb.BinaryReader.prototype.readPackedInt32),jspb.BinaryReader.prototype.readPackedInt32String=function(){return this.readPackedField_(this.decoder_.readSignedVarint32String)},jspb.BinaryReader.prototype.readPackedInt64=function(){return this.readPackedField_(this.decoder_.readSignedVarint64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedInt64",jspb.BinaryReader.prototype.readPackedInt64),jspb.BinaryReader.prototype.readPackedInt64String=function(){return this.readPackedField_(this.decoder_.readSignedVarint64String)},jspb.BinaryReader.prototype.readPackedUint32=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedUint32",jspb.BinaryReader.prototype.readPackedUint32),jspb.BinaryReader.prototype.readPackedUint32String=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint32String)},jspb.BinaryReader.prototype.readPackedUint64=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedUint64",jspb.BinaryReader.prototype.readPackedUint64),jspb.BinaryReader.prototype.readPackedUint64String=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint64String)},jspb.BinaryReader.prototype.readPackedSint32=function(){return this.readPackedField_(this.decoder_.readZigzagVarint32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedSint32",jspb.BinaryReader.prototype.readPackedSint32),jspb.BinaryReader.prototype.readPackedSint64=function(){return this.readPackedField_(this.decoder_.readZigzagVarint64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedSint64",jspb.BinaryReader.prototype.readPackedSint64),jspb.BinaryReader.prototype.readPackedSint64String=function(){return this.readPackedField_(this.decoder_.readZigzagVarint64String)},jspb.BinaryReader.prototype.readPackedFixed32=function(){return this.readPackedField_(this.decoder_.readUint32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedFixed32",jspb.BinaryReader.prototype.readPackedFixed32),jspb.BinaryReader.prototype.readPackedFixed64=function(){return this.readPackedField_(this.decoder_.readUint64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedFixed64",jspb.BinaryReader.prototype.readPackedFixed64),jspb.BinaryReader.prototype.readPackedFixed64String=function(){return this.readPackedField_(this.decoder_.readUint64String)},jspb.BinaryReader.prototype.readPackedSfixed32=function(){return this.readPackedField_(this.decoder_.readInt32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedSfixed32",jspb.BinaryReader.prototype.readPackedSfixed32),jspb.BinaryReader.prototype.readPackedSfixed64=function(){return this.readPackedField_(this.decoder_.readInt64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedSfixed64",jspb.BinaryReader.prototype.readPackedSfixed64),jspb.BinaryReader.prototype.readPackedSfixed64String=function(){return this.readPackedField_(this.decoder_.readInt64String)},jspb.BinaryReader.prototype.readPackedFloat=function(){return this.readPackedField_(this.decoder_.readFloat)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedFloat",jspb.BinaryReader.prototype.readPackedFloat),jspb.BinaryReader.prototype.readPackedDouble=function(){return this.readPackedField_(this.decoder_.readDouble)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedDouble",jspb.BinaryReader.prototype.readPackedDouble),jspb.BinaryReader.prototype.readPackedBool=function(){return this.readPackedField_(this.decoder_.readBool)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedBool",jspb.BinaryReader.prototype.readPackedBool),jspb.BinaryReader.prototype.readPackedEnum=function(){return this.readPackedField_(this.decoder_.readEnum)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedEnum",jspb.BinaryReader.prototype.readPackedEnum),jspb.BinaryReader.prototype.readPackedVarintHash64=function(){return this.readPackedField_(this.decoder_.readVarintHash64)},jspb.BinaryReader.prototype.readPackedFixedHash64=function(){return this.readPackedField_(this.decoder_.readFixedHash64)},jspb.BinaryEncoder=function(){this.buffer_=[]},jspb.BinaryEncoder.prototype.length=function(){return this.buffer_.length},jspb.BinaryEncoder.prototype.end=function(){var e=this.buffer_;return this.buffer_=[],e},jspb.BinaryEncoder.prototype.writeSplitVarint64=function(e,t){for(jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(t==Math.floor(t)),jspb.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_32),jspb.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_32);0<t||127<e;)this.buffer_.push(127&e|128),e=(e>>>7|t<<25)>>>0,t>>>=7;this.buffer_.push(e)},jspb.BinaryEncoder.prototype.writeSplitFixed64=function(e,t){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(t==Math.floor(t)),jspb.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_32),jspb.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_32),this.writeUint32(e),this.writeUint32(t)},jspb.BinaryEncoder.prototype.writeUnsignedVarint32=function(e){for(jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_32);127<e;)this.buffer_.push(127&e|128),e>>>=7;this.buffer_.push(e)},jspb.BinaryEncoder.prototype.writeSignedVarint32=function(e){if(jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_31&&e<jspb.BinaryConstants.TWO_TO_31),0<=e)this.writeUnsignedVarint32(e);else{for(var t=0;9>t;t++)this.buffer_.push(127&e|128),e>>=7;this.buffer_.push(1)}},jspb.BinaryEncoder.prototype.writeUnsignedVarint64=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_64),jspb.utils.splitInt64(e),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeSignedVarint64=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_63&&e<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitInt64(e),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeZigzagVarint32=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_31&&e<jspb.BinaryConstants.TWO_TO_31),this.writeUnsignedVarint32((e<<1^e>>31)>>>0)},jspb.BinaryEncoder.prototype.writeZigzagVarint64=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_63&&e<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitZigzag64(e),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeZigzagVarint64String=function(e){this.writeZigzagVarintHash64(jspb.utils.decimalStringToHash64(e))},jspb.BinaryEncoder.prototype.writeZigzagVarintHash64=function(e){var t=this;jspb.utils.splitHash64(e),jspb.utils.toZigzag64(jspb.utils.split64Low,jspb.utils.split64High,(function(e,r){t.writeSplitVarint64(e>>>0,r>>>0)}))},jspb.BinaryEncoder.prototype.writeUint8=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(0<=e&&256>e),this.buffer_.push(e>>>0&255)},jspb.BinaryEncoder.prototype.writeUint16=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(0<=e&&65536>e),this.buffer_.push(e>>>0&255),this.buffer_.push(e>>>8&255)},jspb.BinaryEncoder.prototype.writeUint32=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_32),this.buffer_.push(e>>>0&255),this.buffer_.push(e>>>8&255),this.buffer_.push(e>>>16&255),this.buffer_.push(e>>>24&255)},jspb.BinaryEncoder.prototype.writeUint64=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_64),jspb.utils.splitUint64(e),this.writeUint32(jspb.utils.split64Low),this.writeUint32(jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeInt8=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(-128<=e&&128>e),this.buffer_.push(e>>>0&255)},jspb.BinaryEncoder.prototype.writeInt16=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(-32768<=e&&32768>e),this.buffer_.push(e>>>0&255),this.buffer_.push(e>>>8&255)},jspb.BinaryEncoder.prototype.writeInt32=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_31&&e<jspb.BinaryConstants.TWO_TO_31),this.buffer_.push(e>>>0&255),this.buffer_.push(e>>>8&255),this.buffer_.push(e>>>16&255),this.buffer_.push(e>>>24&255)},jspb.BinaryEncoder.prototype.writeInt64=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_63&&e<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitInt64(e),this.writeSplitFixed64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeInt64String=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(+e>=-jspb.BinaryConstants.TWO_TO_63&&+e<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitHash64(jspb.utils.decimalStringToHash64(e)),this.writeSplitFixed64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeFloat=function(e){jspb.asserts.assert(1/0===e||-1/0===e||isNaN(e)||e>=-jspb.BinaryConstants.FLOAT32_MAX&&e<=jspb.BinaryConstants.FLOAT32_MAX),jspb.utils.splitFloat32(e),this.writeUint32(jspb.utils.split64Low)},jspb.BinaryEncoder.prototype.writeDouble=function(e){jspb.asserts.assert(1/0===e||-1/0===e||isNaN(e)||e>=-jspb.BinaryConstants.FLOAT64_MAX&&e<=jspb.BinaryConstants.FLOAT64_MAX),jspb.utils.splitFloat64(e),this.writeUint32(jspb.utils.split64Low),this.writeUint32(jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeBool=function(e){jspb.asserts.assert("boolean"==typeof e||"number"==typeof e),this.buffer_.push(e?1:0)},jspb.BinaryEncoder.prototype.writeEnum=function(e){jspb.asserts.assert(e==Math.floor(e)),jspb.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_31&&e<jspb.BinaryConstants.TWO_TO_31),this.writeSignedVarint32(e)},jspb.BinaryEncoder.prototype.writeBytes=function(e){this.buffer_.push.apply(this.buffer_,e)},jspb.BinaryEncoder.prototype.writeVarintHash64=function(e){jspb.utils.splitHash64(e),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeFixedHash64=function(e){jspb.utils.splitHash64(e),this.writeUint32(jspb.utils.split64Low),this.writeUint32(jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeString=function(e){var t=this.buffer_.length;jspb.asserts.assertString(e);for(var r=0;r<e.length;r++){var n=e.charCodeAt(r);if(128>n)this.buffer_.push(n);else if(2048>n)this.buffer_.push(n>>6|192),this.buffer_.push(63&n|128);else if(65536>n)if(55296<=n&&56319>=n&&r+1<e.length){var o=e.charCodeAt(r+1);56320<=o&&57343>=o&&(n=1024*(n-55296)+o-56320+65536,this.buffer_.push(n>>18|240),this.buffer_.push(n>>12&63|128),this.buffer_.push(n>>6&63|128),this.buffer_.push(63&n|128),r++)}else this.buffer_.push(n>>12|224),this.buffer_.push(n>>6&63|128),this.buffer_.push(63&n|128)}return this.buffer_.length-t},jspb.arith={},jspb.arith.UInt64=function(e,t){this.lo=e,this.hi=t},jspb.arith.UInt64.prototype.cmp=function(e){return this.hi<e.hi||this.hi==e.hi&&this.lo<e.lo?-1:this.hi==e.hi&&this.lo==e.lo?0:1},jspb.arith.UInt64.prototype.rightShift=function(){return new jspb.arith.UInt64((this.lo>>>1|(1&this.hi)<<31)>>>0,this.hi>>>1>>>0)},jspb.arith.UInt64.prototype.leftShift=function(){return new jspb.arith.UInt64(this.lo<<1>>>0,(this.hi<<1|this.lo>>>31)>>>0)},jspb.arith.UInt64.prototype.msb=function(){return!!(2147483648&this.hi)},jspb.arith.UInt64.prototype.lsb=function(){return!!(1&this.lo)},jspb.arith.UInt64.prototype.zero=function(){return 0==this.lo&&0==this.hi},jspb.arith.UInt64.prototype.add=function(e){return new jspb.arith.UInt64((this.lo+e.lo&4294967295)>>>0>>>0,((this.hi+e.hi&4294967295)>>>0)+(4294967296<=this.lo+e.lo?1:0)>>>0)},jspb.arith.UInt64.prototype.sub=function(e){return new jspb.arith.UInt64((this.lo-e.lo&4294967295)>>>0>>>0,((this.hi-e.hi&4294967295)>>>0)-(0>this.lo-e.lo?1:0)>>>0)},jspb.arith.UInt64.mul32x32=function(e,t){var r=65535&e,n=65535&t,o=t>>>16;for(t=r*n+65536*(r*o&65535)+65536*((e>>>=16)*n&65535),r=e*o+(r*o>>>16)+(e*n>>>16);4294967296<=t;)t-=4294967296,r+=1;return new jspb.arith.UInt64(t>>>0,r>>>0)},jspb.arith.UInt64.prototype.mul=function(e){var t=jspb.arith.UInt64.mul32x32(this.lo,e);return(e=jspb.arith.UInt64.mul32x32(this.hi,e)).hi=e.lo,e.lo=0,t.add(e)},jspb.arith.UInt64.prototype.div=function(e){if(0==e)return[];var t=new jspb.arith.UInt64(0,0),r=new jspb.arith.UInt64(this.lo,this.hi);e=new jspb.arith.UInt64(e,0);for(var n=new jspb.arith.UInt64(1,0);!e.msb();)e=e.leftShift(),n=n.leftShift();for(;!n.zero();)0>=e.cmp(r)&&(t=t.add(n),r=r.sub(e)),e=e.rightShift(),n=n.rightShift();return[t,r]},jspb.arith.UInt64.prototype.toString=function(){for(var e="",t=this;!t.zero();){var r=(t=t.div(10))[0];e=t[1].lo+e,t=r}return""==e&&(e="0"),e},jspb.arith.UInt64.fromString=function(e){for(var t=new jspb.arith.UInt64(0,0),r=new jspb.arith.UInt64(0,0),n=0;n<e.length;n++){if("0">e[n]||"9"<e[n])return null;var o=parseInt(e[n],10);r.lo=o,t=t.mul(10).add(r)}return t},jspb.arith.UInt64.prototype.clone=function(){return new jspb.arith.UInt64(this.lo,this.hi)},jspb.arith.Int64=function(e,t){this.lo=e,this.hi=t},jspb.arith.Int64.prototype.add=function(e){return new jspb.arith.Int64((this.lo+e.lo&4294967295)>>>0>>>0,((this.hi+e.hi&4294967295)>>>0)+(4294967296<=this.lo+e.lo?1:0)>>>0)},jspb.arith.Int64.prototype.sub=function(e){return new jspb.arith.Int64((this.lo-e.lo&4294967295)>>>0>>>0,((this.hi-e.hi&4294967295)>>>0)-(0>this.lo-e.lo?1:0)>>>0)},jspb.arith.Int64.prototype.clone=function(){return new jspb.arith.Int64(this.lo,this.hi)},jspb.arith.Int64.prototype.toString=function(){var e=0!=(2147483648&this.hi),t=new jspb.arith.UInt64(this.lo,this.hi);return e&&(t=new jspb.arith.UInt64(0,0).sub(t)),(e?"-":"")+t.toString()},jspb.arith.Int64.fromString=function(e){var t=0<e.length&&"-"==e[0];return t&&(e=e.substring(1)),null===(e=jspb.arith.UInt64.fromString(e))?null:(t&&(e=new jspb.arith.UInt64(0,0).sub(e)),new jspb.arith.Int64(e.lo,e.hi))},jspb.BinaryWriter=function(){this.blocks_=[],this.totalLength_=0,this.encoder_=new jspb.BinaryEncoder,this.bookmarks_=[]},jspb.BinaryWriter.prototype.appendUint8Array_=function(e){var t=this.encoder_.end();this.blocks_.push(t),this.blocks_.push(e),this.totalLength_+=t.length+e.length},jspb.BinaryWriter.prototype.beginDelimited_=function(e){return this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),e=this.encoder_.end(),this.blocks_.push(e),this.totalLength_+=e.length,e.push(this.totalLength_),e},jspb.BinaryWriter.prototype.endDelimited_=function(e){var t=e.pop();for(t=this.totalLength_+this.encoder_.length()-t,jspb.asserts.assert(0<=t);127<t;)e.push(127&t|128),t>>>=7,this.totalLength_++;e.push(t),this.totalLength_++},jspb.BinaryWriter.prototype.writeSerializedMessage=function(e,t,r){this.appendUint8Array_(e.subarray(t,r))},jspb.BinaryWriter.prototype.maybeWriteSerializedMessage=function(e,t,r){null!=e&&null!=t&&null!=r&&this.writeSerializedMessage(e,t,r)},jspb.BinaryWriter.prototype.reset=function(){this.blocks_=[],this.encoder_.end(),this.totalLength_=0,this.bookmarks_=[]},jspb.BinaryWriter.prototype.getResultBuffer=function(){jspb.asserts.assert(0==this.bookmarks_.length);for(var e=new Uint8Array(this.totalLength_+this.encoder_.length()),t=this.blocks_,r=t.length,n=0,o=0;o<r;o++){var i=t[o];e.set(i,n),n+=i.length}return t=this.encoder_.end(),e.set(t,n),n+=t.length,jspb.asserts.assert(n==e.length),this.blocks_=[e],e},goog.exportProperty(jspb.BinaryWriter.prototype,"getResultBuffer",jspb.BinaryWriter.prototype.getResultBuffer),jspb.BinaryWriter.prototype.getResultBase64String=function(e){return goog.crypt.base64.encodeByteArray(this.getResultBuffer(),e)},jspb.BinaryWriter.prototype.beginSubMessage=function(e){this.bookmarks_.push(this.beginDelimited_(e))},jspb.BinaryWriter.prototype.endSubMessage=function(){jspb.asserts.assert(0<=this.bookmarks_.length),this.endDelimited_(this.bookmarks_.pop())},jspb.BinaryWriter.prototype.writeFieldHeader_=function(e,t){jspb.asserts.assert(1<=e&&e==Math.floor(e)),this.encoder_.writeUnsignedVarint32(8*e+t)},jspb.BinaryWriter.prototype.writeAny=function(e,t,r){var n=jspb.BinaryConstants.FieldType;switch(e){case n.DOUBLE:this.writeDouble(t,r);break;case n.FLOAT:this.writeFloat(t,r);break;case n.INT64:this.writeInt64(t,r);break;case n.UINT64:this.writeUint64(t,r);break;case n.INT32:this.writeInt32(t,r);break;case n.FIXED64:this.writeFixed64(t,r);break;case n.FIXED32:this.writeFixed32(t,r);break;case n.BOOL:this.writeBool(t,r);break;case n.STRING:this.writeString(t,r);break;case n.GROUP:jspb.asserts.fail("Group field type not supported in writeAny()");break;case n.MESSAGE:jspb.asserts.fail("Message field type not supported in writeAny()");break;case n.BYTES:this.writeBytes(t,r);break;case n.UINT32:this.writeUint32(t,r);break;case n.ENUM:this.writeEnum(t,r);break;case n.SFIXED32:this.writeSfixed32(t,r);break;case n.SFIXED64:this.writeSfixed64(t,r);break;case n.SINT32:this.writeSint32(t,r);break;case n.SINT64:this.writeSint64(t,r);break;case n.FHASH64:this.writeFixedHash64(t,r);break;case n.VHASH64:this.writeVarintHash64(t,r);break;default:jspb.asserts.fail("Invalid field type in writeAny()")}},jspb.BinaryWriter.prototype.writeUnsignedVarint32_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeUnsignedVarint32(t))},jspb.BinaryWriter.prototype.writeSignedVarint32_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint32(t))},jspb.BinaryWriter.prototype.writeUnsignedVarint64_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeUnsignedVarint64(t))},jspb.BinaryWriter.prototype.writeSignedVarint64_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint64(t))},jspb.BinaryWriter.prototype.writeZigzagVarint32_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarint32(t))},jspb.BinaryWriter.prototype.writeZigzagVarint64_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarint64(t))},jspb.BinaryWriter.prototype.writeZigzagVarint64String_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarint64String(t))},jspb.BinaryWriter.prototype.writeZigzagVarintHash64_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarintHash64(t))},jspb.BinaryWriter.prototype.writeInt32=function(e,t){null!=t&&(jspb.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_31&&t<jspb.BinaryConstants.TWO_TO_31),this.writeSignedVarint32_(e,t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeInt32",jspb.BinaryWriter.prototype.writeInt32),jspb.BinaryWriter.prototype.writeInt32String=function(e,t){null!=t&&(t=parseInt(t,10),jspb.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_31&&t<jspb.BinaryConstants.TWO_TO_31),this.writeSignedVarint32_(e,t))},jspb.BinaryWriter.prototype.writeInt64=function(e,t){null!=t&&(jspb.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_63&&t<jspb.BinaryConstants.TWO_TO_63),this.writeSignedVarint64_(e,t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeInt64",jspb.BinaryWriter.prototype.writeInt64),jspb.BinaryWriter.prototype.writeInt64String=function(e,t){null!=t&&(t=jspb.arith.Int64.fromString(t),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSplitVarint64(t.lo,t.hi))},jspb.BinaryWriter.prototype.writeUint32=function(e,t){null!=t&&(jspb.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_32),this.writeUnsignedVarint32_(e,t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeUint32",jspb.BinaryWriter.prototype.writeUint32),jspb.BinaryWriter.prototype.writeUint32String=function(e,t){null!=t&&(t=parseInt(t,10),jspb.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_32),this.writeUnsignedVarint32_(e,t))},jspb.BinaryWriter.prototype.writeUint64=function(e,t){null!=t&&(jspb.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_64),this.writeUnsignedVarint64_(e,t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeUint64",jspb.BinaryWriter.prototype.writeUint64),jspb.BinaryWriter.prototype.writeUint64String=function(e,t){null!=t&&(t=jspb.arith.UInt64.fromString(t),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSplitVarint64(t.lo,t.hi))},jspb.BinaryWriter.prototype.writeSint32=function(e,t){null!=t&&(jspb.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_31&&t<jspb.BinaryConstants.TWO_TO_31),this.writeZigzagVarint32_(e,t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeSint32",jspb.BinaryWriter.prototype.writeSint32),jspb.BinaryWriter.prototype.writeSint64=function(e,t){null!=t&&(jspb.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_63&&t<jspb.BinaryConstants.TWO_TO_63),this.writeZigzagVarint64_(e,t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeSint64",jspb.BinaryWriter.prototype.writeSint64),jspb.BinaryWriter.prototype.writeSintHash64=function(e,t){null!=t&&this.writeZigzagVarintHash64_(e,t)},jspb.BinaryWriter.prototype.writeSint64String=function(e,t){null!=t&&this.writeZigzagVarint64String_(e,t)},jspb.BinaryWriter.prototype.writeFixed32=function(e,t){null!=t&&(jspb.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_32),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED32),this.encoder_.writeUint32(t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeFixed32",jspb.BinaryWriter.prototype.writeFixed32),jspb.BinaryWriter.prototype.writeFixed64=function(e,t){null!=t&&(jspb.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_64),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeUint64(t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeFixed64",jspb.BinaryWriter.prototype.writeFixed64),jspb.BinaryWriter.prototype.writeFixed64String=function(e,t){null!=t&&(t=jspb.arith.UInt64.fromString(t),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeSplitFixed64(t.lo,t.hi))},jspb.BinaryWriter.prototype.writeSfixed32=function(e,t){null!=t&&(jspb.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_31&&t<jspb.BinaryConstants.TWO_TO_31),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED32),this.encoder_.writeInt32(t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeSfixed32",jspb.BinaryWriter.prototype.writeSfixed32),jspb.BinaryWriter.prototype.writeSfixed64=function(e,t){null!=t&&(jspb.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_63&&t<jspb.BinaryConstants.TWO_TO_63),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeInt64(t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeSfixed64",jspb.BinaryWriter.prototype.writeSfixed64),jspb.BinaryWriter.prototype.writeSfixed64String=function(e,t){null!=t&&(t=jspb.arith.Int64.fromString(t),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeSplitFixed64(t.lo,t.hi))},jspb.BinaryWriter.prototype.writeFloat=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED32),this.encoder_.writeFloat(t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeFloat",jspb.BinaryWriter.prototype.writeFloat),jspb.BinaryWriter.prototype.writeDouble=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeDouble(t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeDouble",jspb.BinaryWriter.prototype.writeDouble),jspb.BinaryWriter.prototype.writeBool=function(e,t){null!=t&&(jspb.asserts.assert("boolean"==typeof t||"number"==typeof t),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeBool(t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeBool",jspb.BinaryWriter.prototype.writeBool),jspb.BinaryWriter.prototype.writeEnum=function(e,t){null!=t&&(jspb.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_31&&t<jspb.BinaryConstants.TWO_TO_31),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint32(t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeEnum",jspb.BinaryWriter.prototype.writeEnum),jspb.BinaryWriter.prototype.writeString=function(e,t){null!=t&&(e=this.beginDelimited_(e),this.encoder_.writeString(t),this.endDelimited_(e))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeString",jspb.BinaryWriter.prototype.writeString),jspb.BinaryWriter.prototype.writeBytes=function(e,t){null!=t&&(t=jspb.utils.byteSourceToUint8Array(t),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(t.length),this.appendUint8Array_(t))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeBytes",jspb.BinaryWriter.prototype.writeBytes),jspb.BinaryWriter.prototype.writeMessage=function(e,t,r){null!=t&&(e=this.beginDelimited_(e),r(t,this),this.endDelimited_(e))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeMessage",jspb.BinaryWriter.prototype.writeMessage),jspb.BinaryWriter.prototype.writeMessageSet=function(e,t,r){null!=t&&(this.writeFieldHeader_(1,jspb.BinaryConstants.WireType.START_GROUP),this.writeFieldHeader_(2,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint32(e),e=this.beginDelimited_(3),r(t,this),this.endDelimited_(e),this.writeFieldHeader_(1,jspb.BinaryConstants.WireType.END_GROUP))},jspb.BinaryWriter.prototype.writeGroup=function(e,t,r){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.START_GROUP),r(t,this),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.END_GROUP))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeGroup",jspb.BinaryWriter.prototype.writeGroup),jspb.BinaryWriter.prototype.writeFixedHash64=function(e,t){null!=t&&(jspb.asserts.assert(8==t.length),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeFixedHash64(t))},jspb.BinaryWriter.prototype.writeVarintHash64=function(e,t){null!=t&&(jspb.asserts.assert(8==t.length),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeVarintHash64(t))},jspb.BinaryWriter.prototype.writeSplitFixed64=function(e,t,r){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeSplitFixed64(t,r)},jspb.BinaryWriter.prototype.writeSplitVarint64=function(e,t,r){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSplitVarint64(t,r)},jspb.BinaryWriter.prototype.writeSplitZigzagVarint64=function(e,t,r){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT);var n=this.encoder_;jspb.utils.toZigzag64(t,r,(function(e,t){n.writeSplitVarint64(e>>>0,t>>>0)}))},jspb.BinaryWriter.prototype.writeRepeatedInt32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSignedVarint32_(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedInt32",jspb.BinaryWriter.prototype.writeRepeatedInt32),jspb.BinaryWriter.prototype.writeRepeatedInt32String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeInt32String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedInt64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSignedVarint64_(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedInt64",jspb.BinaryWriter.prototype.writeRepeatedInt64),jspb.BinaryWriter.prototype.writeRepeatedSplitFixed64=function(e,t,r,n){if(null!=t)for(var o=0;o<t.length;o++)this.writeSplitFixed64(e,r(t[o]),n(t[o]))},jspb.BinaryWriter.prototype.writeRepeatedSplitVarint64=function(e,t,r,n){if(null!=t)for(var o=0;o<t.length;o++)this.writeSplitVarint64(e,r(t[o]),n(t[o]))},jspb.BinaryWriter.prototype.writeRepeatedSplitZigzagVarint64=function(e,t,r,n){if(null!=t)for(var o=0;o<t.length;o++)this.writeSplitZigzagVarint64(e,r(t[o]),n(t[o]))},jspb.BinaryWriter.prototype.writeRepeatedInt64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeInt64String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedUint32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeUnsignedVarint32_(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedUint32",jspb.BinaryWriter.prototype.writeRepeatedUint32),jspb.BinaryWriter.prototype.writeRepeatedUint32String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeUint32String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedUint64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeUnsignedVarint64_(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedUint64",jspb.BinaryWriter.prototype.writeRepeatedUint64),jspb.BinaryWriter.prototype.writeRepeatedUint64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeUint64String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedSint32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeZigzagVarint32_(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedSint32",jspb.BinaryWriter.prototype.writeRepeatedSint32),jspb.BinaryWriter.prototype.writeRepeatedSint64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeZigzagVarint64_(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedSint64",jspb.BinaryWriter.prototype.writeRepeatedSint64),jspb.BinaryWriter.prototype.writeRepeatedSint64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeZigzagVarint64String_(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedSintHash64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeZigzagVarintHash64_(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedFixed32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFixed32(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedFixed32",jspb.BinaryWriter.prototype.writeRepeatedFixed32),jspb.BinaryWriter.prototype.writeRepeatedFixed64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFixed64(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedFixed64",jspb.BinaryWriter.prototype.writeRepeatedFixed64),jspb.BinaryWriter.prototype.writeRepeatedFixed64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFixed64String(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedFixed64String",jspb.BinaryWriter.prototype.writeRepeatedFixed64String),jspb.BinaryWriter.prototype.writeRepeatedSfixed32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSfixed32(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedSfixed32",jspb.BinaryWriter.prototype.writeRepeatedSfixed32),jspb.BinaryWriter.prototype.writeRepeatedSfixed64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSfixed64(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedSfixed64",jspb.BinaryWriter.prototype.writeRepeatedSfixed64),jspb.BinaryWriter.prototype.writeRepeatedSfixed64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSfixed64String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedFloat=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFloat(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedFloat",jspb.BinaryWriter.prototype.writeRepeatedFloat),jspb.BinaryWriter.prototype.writeRepeatedDouble=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeDouble(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedDouble",jspb.BinaryWriter.prototype.writeRepeatedDouble),jspb.BinaryWriter.prototype.writeRepeatedBool=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeBool(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedBool",jspb.BinaryWriter.prototype.writeRepeatedBool),jspb.BinaryWriter.prototype.writeRepeatedEnum=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeEnum(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedEnum",jspb.BinaryWriter.prototype.writeRepeatedEnum),jspb.BinaryWriter.prototype.writeRepeatedString=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeString(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedString",jspb.BinaryWriter.prototype.writeRepeatedString),jspb.BinaryWriter.prototype.writeRepeatedBytes=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeBytes(e,t[r])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedBytes",jspb.BinaryWriter.prototype.writeRepeatedBytes),jspb.BinaryWriter.prototype.writeRepeatedMessage=function(e,t,r){if(null!=t)for(var n=0;n<t.length;n++){var o=this.beginDelimited_(e);r(t[n],this),this.endDelimited_(o)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedMessage",jspb.BinaryWriter.prototype.writeRepeatedMessage),jspb.BinaryWriter.prototype.writeRepeatedGroup=function(e,t,r){if(null!=t)for(var n=0;n<t.length;n++)this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.START_GROUP),r(t[n],this),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.END_GROUP)},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedGroup",jspb.BinaryWriter.prototype.writeRepeatedGroup),jspb.BinaryWriter.prototype.writeRepeatedFixedHash64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFixedHash64(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedVarintHash64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeVarintHash64(e,t[r])},jspb.BinaryWriter.prototype.writePackedInt32=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeSignedVarint32(t[r]);this.endDelimited_(e)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedInt32",jspb.BinaryWriter.prototype.writePackedInt32),jspb.BinaryWriter.prototype.writePackedInt32String=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeSignedVarint32(parseInt(t[r],10));this.endDelimited_(e)}},jspb.BinaryWriter.prototype.writePackedInt64=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeSignedVarint64(t[r]);this.endDelimited_(e)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedInt64",jspb.BinaryWriter.prototype.writePackedInt64),jspb.BinaryWriter.prototype.writePackedSplitFixed64=function(e,t,r,n){if(null!=t){e=this.beginDelimited_(e);for(var o=0;o<t.length;o++)this.encoder_.writeSplitFixed64(r(t[o]),n(t[o]));this.endDelimited_(e)}},jspb.BinaryWriter.prototype.writePackedSplitVarint64=function(e,t,r,n){if(null!=t){e=this.beginDelimited_(e);for(var o=0;o<t.length;o++)this.encoder_.writeSplitVarint64(r(t[o]),n(t[o]));this.endDelimited_(e)}},jspb.BinaryWriter.prototype.writePackedSplitZigzagVarint64=function(e,t,r,n){if(null!=t){e=this.beginDelimited_(e);for(var o=this.encoder_,i=0;i<t.length;i++)jspb.utils.toZigzag64(r(t[i]),n(t[i]),(function(e,t){o.writeSplitVarint64(e>>>0,t>>>0)}));this.endDelimited_(e)}},jspb.BinaryWriter.prototype.writePackedInt64String=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++){var n=jspb.arith.Int64.fromString(t[r]);this.encoder_.writeSplitVarint64(n.lo,n.hi)}this.endDelimited_(e)}},jspb.BinaryWriter.prototype.writePackedUint32=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeUnsignedVarint32(t[r]);this.endDelimited_(e)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedUint32",jspb.BinaryWriter.prototype.writePackedUint32),jspb.BinaryWriter.prototype.writePackedUint32String=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeUnsignedVarint32(parseInt(t[r],10));this.endDelimited_(e)}},jspb.BinaryWriter.prototype.writePackedUint64=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeUnsignedVarint64(t[r]);this.endDelimited_(e)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedUint64",jspb.BinaryWriter.prototype.writePackedUint64),jspb.BinaryWriter.prototype.writePackedUint64String=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++){var n=jspb.arith.UInt64.fromString(t[r]);this.encoder_.writeSplitVarint64(n.lo,n.hi)}this.endDelimited_(e)}},jspb.BinaryWriter.prototype.writePackedSint32=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeZigzagVarint32(t[r]);this.endDelimited_(e)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedSint32",jspb.BinaryWriter.prototype.writePackedSint32),jspb.BinaryWriter.prototype.writePackedSint64=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeZigzagVarint64(t[r]);this.endDelimited_(e)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedSint64",jspb.BinaryWriter.prototype.writePackedSint64),jspb.BinaryWriter.prototype.writePackedSint64String=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeZigzagVarintHash64(jspb.utils.decimalStringToHash64(t[r]));this.endDelimited_(e)}},jspb.BinaryWriter.prototype.writePackedSintHash64=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeZigzagVarintHash64(t[r]);this.endDelimited_(e)}},jspb.BinaryWriter.prototype.writePackedFixed32=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(4*t.length),e=0;e<t.length;e++)this.encoder_.writeUint32(t[e])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedFixed32",jspb.BinaryWriter.prototype.writePackedFixed32),jspb.BinaryWriter.prototype.writePackedFixed64=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length),e=0;e<t.length;e++)this.encoder_.writeUint64(t[e])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedFixed64",jspb.BinaryWriter.prototype.writePackedFixed64),jspb.BinaryWriter.prototype.writePackedFixed64String=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length),e=0;e<t.length;e++){var r=jspb.arith.UInt64.fromString(t[e]);this.encoder_.writeSplitFixed64(r.lo,r.hi)}},jspb.BinaryWriter.prototype.writePackedSfixed32=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(4*t.length),e=0;e<t.length;e++)this.encoder_.writeInt32(t[e])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedSfixed32",jspb.BinaryWriter.prototype.writePackedSfixed32),jspb.BinaryWriter.prototype.writePackedSfixed64=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length),e=0;e<t.length;e++)this.encoder_.writeInt64(t[e])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedSfixed64",jspb.BinaryWriter.prototype.writePackedSfixed64),jspb.BinaryWriter.prototype.writePackedSfixed64String=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length),e=0;e<t.length;e++)this.encoder_.writeInt64String(t[e])},jspb.BinaryWriter.prototype.writePackedFloat=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(4*t.length),e=0;e<t.length;e++)this.encoder_.writeFloat(t[e])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedFloat",jspb.BinaryWriter.prototype.writePackedFloat),jspb.BinaryWriter.prototype.writePackedDouble=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length),e=0;e<t.length;e++)this.encoder_.writeDouble(t[e])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedDouble",jspb.BinaryWriter.prototype.writePackedDouble),jspb.BinaryWriter.prototype.writePackedBool=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(t.length),e=0;e<t.length;e++)this.encoder_.writeBool(t[e])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedBool",jspb.BinaryWriter.prototype.writePackedBool),jspb.BinaryWriter.prototype.writePackedEnum=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeEnum(t[r]);this.endDelimited_(e)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedEnum",jspb.BinaryWriter.prototype.writePackedEnum),jspb.BinaryWriter.prototype.writePackedFixedHash64=function(e,t){if(null!=t&&t.length)for(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length),e=0;e<t.length;e++)this.encoder_.writeFixedHash64(t[e])},jspb.BinaryWriter.prototype.writePackedVarintHash64=function(e,t){if(null!=t&&t.length){e=this.beginDelimited_(e);for(var r=0;r<t.length;r++)this.encoder_.writeVarintHash64(t[r]);this.endDelimited_(e)}},jspb.Map=function(e,t){this.arr_=e,this.valueCtor_=t,this.map_={},this.arrClean=!0,0<this.arr_.length&&this.loadFromArray_()},goog.exportSymbol("jspb.Map",jspb.Map),jspb.Map.prototype.loadFromArray_=function(){for(var e=0;e<this.arr_.length;e++){var t=this.arr_[e],r=t[0];this.map_[r.toString()]=new jspb.Map.Entry_(r,t[1])}this.arrClean=!0},jspb.Map.prototype.toArray=function(){if(this.arrClean){if(this.valueCtor_){var e,t=this.map_;for(e in t)if(Object.prototype.hasOwnProperty.call(t,e)){var r=t[e].valueWrapper;r&&r.toArray()}}}else{for(this.arr_.length=0,(t=this.stringKeys_()).sort(),e=0;e<t.length;e++){var n=this.map_[t[e]];(r=n.valueWrapper)&&r.toArray(),this.arr_.push([n.key,n.value])}this.arrClean=!0}return this.arr_},goog.exportProperty(jspb.Map.prototype,"toArray",jspb.Map.prototype.toArray),jspb.Map.prototype.toObject=function(e,t){for(var r=this.toArray(),n=[],o=0;o<r.length;o++){var i=this.map_[r[o][0].toString()];this.wrapEntry_(i);var a=i.valueWrapper;a?(jspb.asserts.assert(t),n.push([i.key,t(e,a)])):n.push([i.key,i.value])}return n},goog.exportProperty(jspb.Map.prototype,"toObject",jspb.Map.prototype.toObject),jspb.Map.fromObject=function(e,t,r){t=new jspb.Map([],t);for(var n=0;n<e.length;n++){var o=e[n][0],i=r(e[n][1]);t.set(o,i)}return t},goog.exportProperty(jspb.Map,"fromObject",jspb.Map.fromObject),jspb.Map.ArrayIteratorIterable_=function(e){this.idx_=0,this.arr_=e},jspb.Map.ArrayIteratorIterable_.prototype.next=function(){return this.idx_<this.arr_.length?{done:!1,value:this.arr_[this.idx_++]}:{done:!0,value:void 0}},"undefined"!=typeof Symbol&&(jspb.Map.ArrayIteratorIterable_.prototype[Symbol.iterator]=function(){return this}),jspb.Map.prototype.getLength=function(){return this.stringKeys_().length},goog.exportProperty(jspb.Map.prototype,"getLength",jspb.Map.prototype.getLength),jspb.Map.prototype.clear=function(){this.map_={},this.arrClean=!1},goog.exportProperty(jspb.Map.prototype,"clear",jspb.Map.prototype.clear),jspb.Map.prototype.del=function(e){e=e.toString();var t=this.map_.hasOwnProperty(e);return delete this.map_[e],this.arrClean=!1,t},goog.exportProperty(jspb.Map.prototype,"del",jspb.Map.prototype.del),jspb.Map.prototype.getEntryList=function(){var e=[],t=this.stringKeys_();t.sort();for(var r=0;r<t.length;r++){var n=this.map_[t[r]];e.push([n.key,n.value])}return e},goog.exportProperty(jspb.Map.prototype,"getEntryList",jspb.Map.prototype.getEntryList),jspb.Map.prototype.entries=function(){var e=[],t=this.stringKeys_();t.sort();for(var r=0;r<t.length;r++){var n=this.map_[t[r]];e.push([n.key,this.wrapEntry_(n)])}return new jspb.Map.ArrayIteratorIterable_(e)},goog.exportProperty(jspb.Map.prototype,"entries",jspb.Map.prototype.entries),jspb.Map.prototype.keys=function(){var e=[],t=this.stringKeys_();t.sort();for(var r=0;r<t.length;r++)e.push(this.map_[t[r]].key);return new jspb.Map.ArrayIteratorIterable_(e)},goog.exportProperty(jspb.Map.prototype,"keys",jspb.Map.prototype.keys),jspb.Map.prototype.values=function(){var e=[],t=this.stringKeys_();t.sort();for(var r=0;r<t.length;r++)e.push(this.wrapEntry_(this.map_[t[r]]));return new jspb.Map.ArrayIteratorIterable_(e)},goog.exportProperty(jspb.Map.prototype,"values",jspb.Map.prototype.values),jspb.Map.prototype.forEach=function(e,t){var r=this.stringKeys_();r.sort();for(var n=0;n<r.length;n++){var o=this.map_[r[n]];e.call(t,this.wrapEntry_(o),o.key,this)}},goog.exportProperty(jspb.Map.prototype,"forEach",jspb.Map.prototype.forEach),jspb.Map.prototype.set=function(e,t){var r=new jspb.Map.Entry_(e);return this.valueCtor_?(r.valueWrapper=t,r.value=t.toArray()):r.value=t,this.map_[e.toString()]=r,this.arrClean=!1,this},goog.exportProperty(jspb.Map.prototype,"set",jspb.Map.prototype.set),jspb.Map.prototype.wrapEntry_=function(e){return this.valueCtor_?(e.valueWrapper||(e.valueWrapper=new this.valueCtor_(e.value)),e.valueWrapper):e.value},jspb.Map.prototype.get=function(e){if(e=this.map_[e.toString()])return this.wrapEntry_(e)},goog.exportProperty(jspb.Map.prototype,"get",jspb.Map.prototype.get),jspb.Map.prototype.has=function(e){return e.toString()in this.map_},goog.exportProperty(jspb.Map.prototype,"has",jspb.Map.prototype.has),jspb.Map.prototype.serializeBinary=function(e,t,r,n,o){var i=this.stringKeys_();i.sort();for(var a=0;a<i.length;a++){var s=this.map_[i[a]];t.beginSubMessage(e),r.call(t,1,s.key),this.valueCtor_?n.call(t,2,this.wrapEntry_(s),o):n.call(t,2,s.value),t.endSubMessage()}},goog.exportProperty(jspb.Map.prototype,"serializeBinary",jspb.Map.prototype.serializeBinary),jspb.Map.deserializeBinary=function(e,t,r,n,o,i,a){for(;t.nextField()&&!t.isEndGroup();){var s=t.getFieldNumber();1==s?i=r.call(t):2==s&&(e.valueCtor_?(jspb.asserts.assert(o),a||(a=new e.valueCtor_),n.call(t,a,o)):a=n.call(t))}jspb.asserts.assert(null!=i),jspb.asserts.assert(null!=a),e.set(i,a)},goog.exportProperty(jspb.Map,"deserializeBinary",jspb.Map.deserializeBinary),jspb.Map.prototype.stringKeys_=function(){var e,t=this.map_,r=[];for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&r.push(e);return r},jspb.Map.Entry_=function(e,t){this.key=e,this.value=t,this.valueWrapper=void 0},jspb.ExtensionFieldInfo=function(e,t,r,n,o){this.fieldIndex=e,this.fieldName=t,this.ctor=r,this.toObjectFn=n,this.isRepeated=o},goog.exportSymbol("jspb.ExtensionFieldInfo",jspb.ExtensionFieldInfo),jspb.ExtensionFieldBinaryInfo=function(e,t,r,n,o,i){this.fieldInfo=e,this.binaryReaderFn=t,this.binaryWriterFn=r,this.binaryMessageSerializeFn=n,this.binaryMessageDeserializeFn=o,this.isPacked=i},goog.exportSymbol("jspb.ExtensionFieldBinaryInfo",jspb.ExtensionFieldBinaryInfo),jspb.ExtensionFieldInfo.prototype.isMessageType=function(){return!!this.ctor},goog.exportProperty(jspb.ExtensionFieldInfo.prototype,"isMessageType",jspb.ExtensionFieldInfo.prototype.isMessageType),jspb.Message=function(){},goog.exportSymbol("jspb.Message",jspb.Message),jspb.Message.GENERATE_TO_OBJECT=!0,goog.exportProperty(jspb.Message,"GENERATE_TO_OBJECT",jspb.Message.GENERATE_TO_OBJECT),jspb.Message.GENERATE_FROM_OBJECT=!goog.DISALLOW_TEST_ONLY_CODE,goog.exportProperty(jspb.Message,"GENERATE_FROM_OBJECT",jspb.Message.GENERATE_FROM_OBJECT),jspb.Message.GENERATE_TO_STRING=!0,jspb.Message.ASSUME_LOCAL_ARRAYS=!1,jspb.Message.SERIALIZE_EMPTY_TRAILING_FIELDS=!0,jspb.Message.SUPPORTS_UINT8ARRAY_="function"==typeof Uint8Array,jspb.Message.prototype.getJsPbMessageId=function(){return this.messageId_},goog.exportProperty(jspb.Message.prototype,"getJsPbMessageId",jspb.Message.prototype.getJsPbMessageId),jspb.Message.getIndex_=function(e,t){return t+e.arrayIndexOffset_},jspb.Message.hiddenES6Property_=function(){},jspb.Message.getFieldNumber_=function(e,t){return t-e.arrayIndexOffset_},jspb.Message.initialize=function(e,t,r,n,o,i){if(e.wrappers_=null,t||(t=r?[r]:[]),e.messageId_=r?String(r):void 0,e.arrayIndexOffset_=0===r?-1:0,e.array=t,jspb.Message.initPivotAndExtensionObject_(e,n),e.convertedPrimitiveFields_={},jspb.Message.SERIALIZE_EMPTY_TRAILING_FIELDS||(e.repeatedFields=o),o)for(t=0;t<o.length;t++)(r=o[t])<e.pivot_?(r=jspb.Message.getIndex_(e,r),e.array[r]=e.array[r]||jspb.Message.EMPTY_LIST_SENTINEL_):(jspb.Message.maybeInitEmptyExtensionObject_(e),e.extensionObject_[r]=e.extensionObject_[r]||jspb.Message.EMPTY_LIST_SENTINEL_);if(i&&i.length)for(t=0;t<i.length;t++)jspb.Message.computeOneofCase(e,i[t])},goog.exportProperty(jspb.Message,"initialize",jspb.Message.initialize),jspb.Message.EMPTY_LIST_SENTINEL_=goog.DEBUG&&Object.freeze?Object.freeze([]):[],jspb.Message.isArray_=function(e){return jspb.Message.ASSUME_LOCAL_ARRAYS?e instanceof Array:Array.isArray(e)},jspb.Message.isExtensionObject_=function(e){return!(null===e||"object"!=typeof e||jspb.Message.isArray_(e)||jspb.Message.SUPPORTS_UINT8ARRAY_&&e instanceof Uint8Array)},jspb.Message.initPivotAndExtensionObject_=function(e,t){var r=e.array.length,n=-1;if(r&&(n=r-1,r=e.array[n],jspb.Message.isExtensionObject_(r)))return e.pivot_=jspb.Message.getFieldNumber_(e,n),void(e.extensionObject_=r);-1<t?(e.pivot_=Math.max(t,jspb.Message.getFieldNumber_(e,n+1)),e.extensionObject_=null):e.pivot_=Number.MAX_VALUE},jspb.Message.maybeInitEmptyExtensionObject_=function(e){var t=jspb.Message.getIndex_(e,e.pivot_);e.array[t]||(e.extensionObject_=e.array[t]={})},jspb.Message.toObjectList=function(e,t,r){for(var n=[],o=0;o<e.length;o++)n[o]=t.call(e[o],r,e[o]);return n},goog.exportProperty(jspb.Message,"toObjectList",jspb.Message.toObjectList),jspb.Message.toObjectExtension=function(e,t,r,n,o){for(var i in r){var a=r[i],s=n.call(e,a);if(null!=s){for(var l in a.fieldName)if(a.fieldName.hasOwnProperty(l))break;t[l]=a.toObjectFn?a.isRepeated?jspb.Message.toObjectList(s,a.toObjectFn,o):a.toObjectFn(o,s):s}}},goog.exportProperty(jspb.Message,"toObjectExtension",jspb.Message.toObjectExtension),jspb.Message.serializeBinaryExtensions=function(e,t,r,n){for(var o in r){var i=r[o],a=i.fieldInfo;if(!i.binaryWriterFn)throw Error("Message extension present that was generated without binary serialization support");var s=n.call(e,a);if(null!=s)if(a.isMessageType()){if(!i.binaryMessageSerializeFn)throw Error("Message extension present holding submessage without binary support enabled, and message is being serialized to binary format");i.binaryWriterFn.call(t,a.fieldIndex,s,i.binaryMessageSerializeFn)}else i.binaryWriterFn.call(t,a.fieldIndex,s)}},goog.exportProperty(jspb.Message,"serializeBinaryExtensions",jspb.Message.serializeBinaryExtensions),jspb.Message.readBinaryExtension=function(e,t,r,n,o){var i=r[t.getFieldNumber()];if(i){if(r=i.fieldInfo,!i.binaryReaderFn)throw Error("Deserializing extension whose generated code does not support binary format");if(r.isMessageType()){var a=new r.ctor;i.binaryReaderFn.call(t,a,i.binaryMessageDeserializeFn)}else a=i.binaryReaderFn.call(t);r.isRepeated&&!i.isPacked?(t=n.call(e,r))?t.push(a):o.call(e,r,[a]):o.call(e,r,a)}else t.skipField()},goog.exportProperty(jspb.Message,"readBinaryExtension",jspb.Message.readBinaryExtension),jspb.Message.getField=function(e,t){if(t<e.pivot_){t=jspb.Message.getIndex_(e,t);var r=e.array[t];return r===jspb.Message.EMPTY_LIST_SENTINEL_?e.array[t]=[]:r}if(e.extensionObject_)return(r=e.extensionObject_[t])===jspb.Message.EMPTY_LIST_SENTINEL_?e.extensionObject_[t]=[]:r},goog.exportProperty(jspb.Message,"getField",jspb.Message.getField),jspb.Message.getRepeatedField=function(e,t){return jspb.Message.getField(e,t)},goog.exportProperty(jspb.Message,"getRepeatedField",jspb.Message.getRepeatedField),jspb.Message.getOptionalFloatingPointField=function(e,t){return null==(e=jspb.Message.getField(e,t))?e:+e},goog.exportProperty(jspb.Message,"getOptionalFloatingPointField",jspb.Message.getOptionalFloatingPointField),jspb.Message.getBooleanField=function(e,t){return null==(e=jspb.Message.getField(e,t))?e:!!e},goog.exportProperty(jspb.Message,"getBooleanField",jspb.Message.getBooleanField),jspb.Message.getRepeatedFloatingPointField=function(e,t){var r=jspb.Message.getRepeatedField(e,t);if(e.convertedPrimitiveFields_||(e.convertedPrimitiveFields_={}),!e.convertedPrimitiveFields_[t]){for(var n=0;n<r.length;n++)r[n]=+r[n];e.convertedPrimitiveFields_[t]=!0}return r},goog.exportProperty(jspb.Message,"getRepeatedFloatingPointField",jspb.Message.getRepeatedFloatingPointField),jspb.Message.getRepeatedBooleanField=function(e,t){var r=jspb.Message.getRepeatedField(e,t);if(e.convertedPrimitiveFields_||(e.convertedPrimitiveFields_={}),!e.convertedPrimitiveFields_[t]){for(var n=0;n<r.length;n++)r[n]=!!r[n];e.convertedPrimitiveFields_[t]=!0}return r},goog.exportProperty(jspb.Message,"getRepeatedBooleanField",jspb.Message.getRepeatedBooleanField),jspb.Message.bytesAsB64=function(e){return null==e||"string"==typeof e?e:jspb.Message.SUPPORTS_UINT8ARRAY_&&e instanceof Uint8Array?goog.crypt.base64.encodeByteArray(e):(jspb.asserts.fail("Cannot coerce to b64 string: "+goog.typeOf(e)),null)},goog.exportProperty(jspb.Message,"bytesAsB64",jspb.Message.bytesAsB64),jspb.Message.bytesAsU8=function(e){return null==e||e instanceof Uint8Array?e:"string"==typeof e?goog.crypt.base64.decodeStringToUint8Array(e):(jspb.asserts.fail("Cannot coerce to Uint8Array: "+goog.typeOf(e)),null)},goog.exportProperty(jspb.Message,"bytesAsU8",jspb.Message.bytesAsU8),jspb.Message.bytesListAsB64=function(e){return jspb.Message.assertConsistentTypes_(e),e.length&&"string"!=typeof e[0]?goog.array.map(e,jspb.Message.bytesAsB64):e},goog.exportProperty(jspb.Message,"bytesListAsB64",jspb.Message.bytesListAsB64),jspb.Message.bytesListAsU8=function(e){return jspb.Message.assertConsistentTypes_(e),!e.length||e[0]instanceof Uint8Array?e:goog.array.map(e,jspb.Message.bytesAsU8)},goog.exportProperty(jspb.Message,"bytesListAsU8",jspb.Message.bytesListAsU8),jspb.Message.assertConsistentTypes_=function(e){if(goog.DEBUG&&e&&1<e.length){var t=goog.typeOf(e[0]);goog.array.forEach(e,(function(e){goog.typeOf(e)!=t&&jspb.asserts.fail("Inconsistent type in JSPB repeated field array. Got "+goog.typeOf(e)+" expected "+t)}))}},jspb.Message.getFieldWithDefault=function(e,t,r){return null==(e=jspb.Message.getField(e,t))?r:e},goog.exportProperty(jspb.Message,"getFieldWithDefault",jspb.Message.getFieldWithDefault),jspb.Message.getBooleanFieldWithDefault=function(e,t,r){return null==(e=jspb.Message.getBooleanField(e,t))?r:e},goog.exportProperty(jspb.Message,"getBooleanFieldWithDefault",jspb.Message.getBooleanFieldWithDefault),jspb.Message.getFloatingPointFieldWithDefault=function(e,t,r){return null==(e=jspb.Message.getOptionalFloatingPointField(e,t))?r:e},goog.exportProperty(jspb.Message,"getFloatingPointFieldWithDefault",jspb.Message.getFloatingPointFieldWithDefault),jspb.Message.getFieldProto3=jspb.Message.getFieldWithDefault,goog.exportProperty(jspb.Message,"getFieldProto3",jspb.Message.getFieldProto3),jspb.Message.getMapField=function(e,t,r,n){if(e.wrappers_||(e.wrappers_={}),t in e.wrappers_)return e.wrappers_[t];var o=jspb.Message.getField(e,t);if(!o){if(r)return;o=[],jspb.Message.setField(e,t,o)}return e.wrappers_[t]=new jspb.Map(o,n)},goog.exportProperty(jspb.Message,"getMapField",jspb.Message.getMapField),jspb.Message.setField=function(e,t,r){return jspb.asserts.assertInstanceof(e,jspb.Message),t<e.pivot_?e.array[jspb.Message.getIndex_(e,t)]=r:(jspb.Message.maybeInitEmptyExtensionObject_(e),e.extensionObject_[t]=r),e},goog.exportProperty(jspb.Message,"setField",jspb.Message.setField),jspb.Message.setProto3IntField=function(e,t,r){return jspb.Message.setFieldIgnoringDefault_(e,t,r,0)},goog.exportProperty(jspb.Message,"setProto3IntField",jspb.Message.setProto3IntField),jspb.Message.setProto3FloatField=function(e,t,r){return jspb.Message.setFieldIgnoringDefault_(e,t,r,0)},goog.exportProperty(jspb.Message,"setProto3FloatField",jspb.Message.setProto3FloatField),jspb.Message.setProto3BooleanField=function(e,t,r){return jspb.Message.setFieldIgnoringDefault_(e,t,r,!1)},goog.exportProperty(jspb.Message,"setProto3BooleanField",jspb.Message.setProto3BooleanField),jspb.Message.setProto3StringField=function(e,t,r){return jspb.Message.setFieldIgnoringDefault_(e,t,r,"")},goog.exportProperty(jspb.Message,"setProto3StringField",jspb.Message.setProto3StringField),jspb.Message.setProto3BytesField=function(e,t,r){return jspb.Message.setFieldIgnoringDefault_(e,t,r,"")},goog.exportProperty(jspb.Message,"setProto3BytesField",jspb.Message.setProto3BytesField),jspb.Message.setProto3EnumField=function(e,t,r){return jspb.Message.setFieldIgnoringDefault_(e,t,r,0)},goog.exportProperty(jspb.Message,"setProto3EnumField",jspb.Message.setProto3EnumField),jspb.Message.setProto3StringIntField=function(e,t,r){return jspb.Message.setFieldIgnoringDefault_(e,t,r,"0")},goog.exportProperty(jspb.Message,"setProto3StringIntField",jspb.Message.setProto3StringIntField),jspb.Message.setFieldIgnoringDefault_=function(e,t,r,n){return jspb.asserts.assertInstanceof(e,jspb.Message),r!==n?jspb.Message.setField(e,t,r):t<e.pivot_?e.array[jspb.Message.getIndex_(e,t)]=null:(jspb.Message.maybeInitEmptyExtensionObject_(e),delete e.extensionObject_[t]),e},jspb.Message.addToRepeatedField=function(e,t,r,n){return jspb.asserts.assertInstanceof(e,jspb.Message),t=jspb.Message.getRepeatedField(e,t),null!=n?t.splice(n,0,r):t.push(r),e},goog.exportProperty(jspb.Message,"addToRepeatedField",jspb.Message.addToRepeatedField),jspb.Message.setOneofField=function(e,t,r,n){return jspb.asserts.assertInstanceof(e,jspb.Message),(r=jspb.Message.computeOneofCase(e,r))&&r!==t&&void 0!==n&&(e.wrappers_&&r in e.wrappers_&&(e.wrappers_[r]=void 0),jspb.Message.setField(e,r,void 0)),jspb.Message.setField(e,t,n)},goog.exportProperty(jspb.Message,"setOneofField",jspb.Message.setOneofField),jspb.Message.computeOneofCase=function(e,t){for(var r,n,o=0;o<t.length;o++){var i=t[o],a=jspb.Message.getField(e,i);null!=a&&(r=i,n=a,jspb.Message.setField(e,i,void 0))}return r?(jspb.Message.setField(e,r,n),r):0},goog.exportProperty(jspb.Message,"computeOneofCase",jspb.Message.computeOneofCase),jspb.Message.getWrapperField=function(e,t,r,n){if(e.wrappers_||(e.wrappers_={}),!e.wrappers_[r]){var o=jspb.Message.getField(e,r);(n||o)&&(e.wrappers_[r]=new t(o))}return e.wrappers_[r]},goog.exportProperty(jspb.Message,"getWrapperField",jspb.Message.getWrapperField),jspb.Message.getRepeatedWrapperField=function(e,t,r){return jspb.Message.wrapRepeatedField_(e,t,r),(t=e.wrappers_[r])==jspb.Message.EMPTY_LIST_SENTINEL_&&(t=e.wrappers_[r]=[]),t},goog.exportProperty(jspb.Message,"getRepeatedWrapperField",jspb.Message.getRepeatedWrapperField),jspb.Message.wrapRepeatedField_=function(e,t,r){if(e.wrappers_||(e.wrappers_={}),!e.wrappers_[r]){for(var n=jspb.Message.getRepeatedField(e,r),o=[],i=0;i<n.length;i++)o[i]=new t(n[i]);e.wrappers_[r]=o}},jspb.Message.setWrapperField=function(e,t,r){jspb.asserts.assertInstanceof(e,jspb.Message),e.wrappers_||(e.wrappers_={});var n=r?r.toArray():r;return e.wrappers_[t]=r,jspb.Message.setField(e,t,n)},goog.exportProperty(jspb.Message,"setWrapperField",jspb.Message.setWrapperField),jspb.Message.setOneofWrapperField=function(e,t,r,n){jspb.asserts.assertInstanceof(e,jspb.Message),e.wrappers_||(e.wrappers_={});var o=n?n.toArray():n;return e.wrappers_[t]=n,jspb.Message.setOneofField(e,t,r,o)},goog.exportProperty(jspb.Message,"setOneofWrapperField",jspb.Message.setOneofWrapperField),jspb.Message.setRepeatedWrapperField=function(e,t,r){jspb.asserts.assertInstanceof(e,jspb.Message),e.wrappers_||(e.wrappers_={}),r=r||[];for(var n=[],o=0;o<r.length;o++)n[o]=r[o].toArray();return e.wrappers_[t]=r,jspb.Message.setField(e,t,n)},goog.exportProperty(jspb.Message,"setRepeatedWrapperField",jspb.Message.setRepeatedWrapperField),jspb.Message.addToRepeatedWrapperField=function(e,t,r,n,o){jspb.Message.wrapRepeatedField_(e,n,t);var i=e.wrappers_[t];return i||(i=e.wrappers_[t]=[]),r=r||new n,e=jspb.Message.getRepeatedField(e,t),null!=o?(i.splice(o,0,r),e.splice(o,0,r.toArray())):(i.push(r),e.push(r.toArray())),r},goog.exportProperty(jspb.Message,"addToRepeatedWrapperField",jspb.Message.addToRepeatedWrapperField),jspb.Message.toMap=function(e,t,r,n){for(var o={},i=0;i<e.length;i++)o[t.call(e[i])]=r?r.call(e[i],n,e[i]):e[i];return o},goog.exportProperty(jspb.Message,"toMap",jspb.Message.toMap),jspb.Message.prototype.syncMapFields_=function(){if(this.wrappers_)for(var e in this.wrappers_){var t=this.wrappers_[e];if(Array.isArray(t))for(var r=0;r<t.length;r++)t[r]&&t[r].toArray();else t&&t.toArray()}},jspb.Message.prototype.toArray=function(){return this.syncMapFields_(),this.array},goog.exportProperty(jspb.Message.prototype,"toArray",jspb.Message.prototype.toArray),jspb.Message.GENERATE_TO_STRING&&(jspb.Message.prototype.toString=function(){return this.syncMapFields_(),this.array.toString()}),jspb.Message.prototype.getExtension=function(e){if(this.extensionObject_){this.wrappers_||(this.wrappers_={});var t=e.fieldIndex;if(e.isRepeated){if(e.isMessageType())return this.wrappers_[t]||(this.wrappers_[t]=goog.array.map(this.extensionObject_[t]||[],(function(t){return new e.ctor(t)}))),this.wrappers_[t]}else if(e.isMessageType())return!this.wrappers_[t]&&this.extensionObject_[t]&&(this.wrappers_[t]=new e.ctor(this.extensionObject_[t])),this.wrappers_[t];return this.extensionObject_[t]}},goog.exportProperty(jspb.Message.prototype,"getExtension",jspb.Message.prototype.getExtension),jspb.Message.prototype.setExtension=function(e,t){this.wrappers_||(this.wrappers_={}),jspb.Message.maybeInitEmptyExtensionObject_(this);var r=e.fieldIndex;return e.isRepeated?(t=t||[],e.isMessageType()?(this.wrappers_[r]=t,this.extensionObject_[r]=goog.array.map(t,(function(e){return e.toArray()}))):this.extensionObject_[r]=t):e.isMessageType()?(this.wrappers_[r]=t,this.extensionObject_[r]=t?t.toArray():t):this.extensionObject_[r]=t,this},goog.exportProperty(jspb.Message.prototype,"setExtension",jspb.Message.prototype.setExtension),jspb.Message.difference=function(e,t){if(!(e instanceof t.constructor))throw Error("Messages have different types.");var r=e.toArray();t=t.toArray();var n=[],o=0,i=r.length>t.length?r.length:t.length;for(e.getJsPbMessageId()&&(n[0]=e.getJsPbMessageId(),o=1);o<i;o++)jspb.Message.compareFields(r[o],t[o])||(n[o]=t[o]);return new e.constructor(n)},goog.exportProperty(jspb.Message,"difference",jspb.Message.difference),jspb.Message.equals=function(e,t){return e==t||!(!e||!t)&&e instanceof t.constructor&&jspb.Message.compareFields(e.toArray(),t.toArray())},goog.exportProperty(jspb.Message,"equals",jspb.Message.equals),jspb.Message.compareExtensions=function(e,t){e=e||{},t=t||{};var r,n={};for(r in e)n[r]=0;for(r in t)n[r]=0;for(r in n)if(!jspb.Message.compareFields(e[r],t[r]))return!1;return!0},goog.exportProperty(jspb.Message,"compareExtensions",jspb.Message.compareExtensions),jspb.Message.compareFields=function(e,t){if(e==t)return!0;if(!goog.isObject(e)||!goog.isObject(t))return!!("number"==typeof e&&isNaN(e)||"number"==typeof t&&isNaN(t))&&String(e)==String(t);if(e.constructor!=t.constructor)return!1;if(jspb.Message.SUPPORTS_UINT8ARRAY_&&e.constructor===Uint8Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!=t[r])return!1;return!0}if(e.constructor===Array){var n=void 0,o=void 0,i=Math.max(e.length,t.length);for(r=0;r<i;r++){var a=e[r],s=t[r];if(a&&a.constructor==Object&&(jspb.asserts.assert(void 0===n),jspb.asserts.assert(r===e.length-1),n=a,a=void 0),s&&s.constructor==Object&&(jspb.asserts.assert(void 0===o),jspb.asserts.assert(r===t.length-1),o=s,s=void 0),!jspb.Message.compareFields(a,s))return!1}return!n&&!o||(n=n||{},o=o||{},jspb.Message.compareExtensions(n,o))}if(e.constructor===Object)return jspb.Message.compareExtensions(e,t);throw Error("Invalid type in JSPB array")},goog.exportProperty(jspb.Message,"compareFields",jspb.Message.compareFields),jspb.Message.prototype.cloneMessage=function(){return jspb.Message.cloneMessage(this)},goog.exportProperty(jspb.Message.prototype,"cloneMessage",jspb.Message.prototype.cloneMessage),jspb.Message.prototype.clone=function(){return jspb.Message.cloneMessage(this)},goog.exportProperty(jspb.Message.prototype,"clone",jspb.Message.prototype.clone),jspb.Message.clone=function(e){return jspb.Message.cloneMessage(e)},goog.exportProperty(jspb.Message,"clone",jspb.Message.clone),jspb.Message.cloneMessage=function(e){return new e.constructor(jspb.Message.clone_(e.toArray()))},jspb.Message.copyInto=function(e,t){jspb.asserts.assertInstanceof(e,jspb.Message),jspb.asserts.assertInstanceof(t,jspb.Message),jspb.asserts.assert(e.constructor==t.constructor,"Copy source and target message should have the same type."),e=jspb.Message.clone(e);for(var r=t.toArray(),n=e.toArray(),o=r.length=0;o<n.length;o++)r[o]=n[o];t.wrappers_=e.wrappers_,t.extensionObject_=e.extensionObject_},goog.exportProperty(jspb.Message,"copyInto",jspb.Message.copyInto),jspb.Message.clone_=function(e){if(Array.isArray(e)){for(var t=Array(e.length),r=0;r<e.length;r++){var n=e[r];null!=n&&(t[r]="object"==typeof n?jspb.Message.clone_(jspb.asserts.assert(n)):n)}return t}if(jspb.Message.SUPPORTS_UINT8ARRAY_&&e instanceof Uint8Array)return new Uint8Array(e);for(r in t={},e)null!=(n=e[r])&&(t[r]="object"==typeof n?jspb.Message.clone_(jspb.asserts.assert(n)):n);return t},jspb.Message.registerMessageType=function(e,t){t.messageId=e},goog.exportProperty(jspb.Message,"registerMessageType",jspb.Message.registerMessageType),jspb.Message.messageSetExtensions={},jspb.Message.messageSetExtensionsBinary={},jspb.Export={},"object"==typeof exports&&(exports.Map=jspb.Map,exports.Message=jspb.Message,exports.BinaryReader=jspb.BinaryReader,exports.BinaryWriter=jspb.BinaryWriter,exports.ExtensionFieldInfo=jspb.ExtensionFieldInfo,exports.ExtensionFieldBinaryInfo=jspb.ExtensionFieldBinaryInfo,exports.exportSymbol=goog.exportSymbol,exports.inherits=goog.inherits,exports.object={extend:goog.object.extend},exports.typeOf=goog.typeOf)},3288:(e,t,r)=>{"use strict";r.d(t,{PA:()=>L});var n=r(3175),o=r(626);if(!o.useState)throw new Error("mobx-react-lite requires React with Hooks support");if(!n.Gn)throw new Error("mobx-react-lite@3 requires mobx at least version 6 to be available");var i=r(2739);function a(e){e()}function s(e){return(0,n.yl)(e)}var l=!1;function u(){return l}var g=function(){function e(e){var t=this;Object.defineProperty(this,"finalize",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"registrations",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"sweepTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"sweep",{enumerable:!0,configurable:!0,writable:!0,value:function(e){void 0===e&&(e=1e4),clearTimeout(t.sweepTimeout),t.sweepTimeout=void 0;var r=Date.now();t.registrations.forEach((function(n,o){r-n.registeredAt>=e&&(t.finalize(n.value),t.registrations.delete(o))})),t.registrations.size>0&&t.scheduleSweep()}}),Object.defineProperty(this,"finalizeAllImmediately",{enumerable:!0,configurable:!0,writable:!0,value:function(){t.sweep(0)}})}return Object.defineProperty(e.prototype,"register",{enumerable:!1,configurable:!0,writable:!0,value:function(e,t,r){this.registrations.set(r,{value:t,registeredAt:Date.now()}),this.scheduleSweep()}}),Object.defineProperty(e.prototype,"unregister",{enumerable:!1,configurable:!0,writable:!0,value:function(e){this.registrations.delete(e)}}),Object.defineProperty(e.prototype,"scheduleSweep",{enumerable:!1,configurable:!0,writable:!0,value:function(){void 0===this.sweepTimeout&&(this.sweepTimeout=setTimeout(this.sweep,1e4))}}),e}(),c=new("undefined"!=typeof FinalizationRegistry?FinalizationRegistry:g)((function(e){var t;null===(t=e.reaction)||void 0===t||t.dispose(),e.reaction=null})),p=r(1702),d=function(){};function f(e){e.reaction=new n.qT("observer".concat(e.name),(function(){var t;e.stateVersion=Symbol(),null===(t=e.onStoreChange)||void 0===t||t.call(e)}))}function h(e,t){if(void 0===t&&(t="observed"),u())return e();var r=o.useRef(null);if(!r.current){var n={reaction:null,onStoreChange:null,stateVersion:Symbol(),name:t,subscribe:function(e){return c.unregister(n),n.onStoreChange=e,n.reaction||(f(n),n.stateVersion=Symbol()),function(){var e;n.onStoreChange=null,null===(e=n.reaction)||void 0===e||e.dispose(),n.reaction=null}},getSnapshot:function(){return n.stateVersion}};r.current=n}var i,a,l=r.current;if(l.reaction||(f(l),c.register(r,l,l)),o.useDebugValue(l.reaction,s),(0,p.useSyncExternalStore)(l.subscribe,l.getSnapshot,d),l.reaction.track((function(){try{i=e()}catch(e){a=e}})),a)throw a;return i}var y="function"==typeof Symbol&&Symbol.for,b=y?Symbol.for("react.forward_ref"):"function"==typeof o.forwardRef&&(0,o.forwardRef)((function(e){return null})).$$typeof,m=y?Symbol.for("react.memo"):"function"==typeof o.memo&&(0,o.memo)((function(e){return null})).$$typeof;function _(e,t){var r;if(m&&e.$$typeof===m)throw new Error("[mobx-react-lite] You are trying to use `observer` on a function component wrapped in either another `observer` or `React.memo`. The observer already applies 'React.memo' for you.");if(u())return e;var n=null!==(r=null==t?void 0:t.forwardRef)&&void 0!==r&&r,i=e,a=e.displayName||e.name;if(b&&e.$$typeof===b&&(n=!0,"function"!=typeof(i=e.render)))throw new Error("[mobx-react-lite] `render` property of ForwardRef was not a function");var s,l,g=function(e,t){return h((function(){return i(e,t)}),a)};return g.displayName=e.displayName,Object.defineProperty(g,"name",{value:e.name,writable:!0,configurable:!0}),e.contextTypes&&(g.contextTypes=e.contextTypes),n&&(g=(0,o.forwardRef)(g)),g=(0,o.memo)(g),s=e,l=g,Object.keys(s).forEach((function(e){v[e]||Object.defineProperty(l,e,Object.getOwnPropertyDescriptor(s,e))})),g}var v={$$typeof:!0,render:!0,compare:!0,type:!0,displayName:!0};var S,E;(E=i.unstable_batchedUpdates)||(E=a),(0,n.jK)({reactionScheduler:E});S=c.finalizeAllImmediately;function T(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}var A=Symbol("patchMixins"),w=Symbol("patchedDefinition");function j(e,t){for(var r=this,n=arguments.length,o=new Array(n>2?n-2:0),i=2;i<n;i++)o[i-2]=arguments[i];t.locks++;try{var a;return null!=e&&(a=e.apply(this,o)),a}finally{t.locks--,0===t.locks&&t.methods.forEach((function(e){e.apply(r,o)}))}}function O(e,t){return function(){for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];j.call.apply(j,[this,e,t].concat(n))}}function R(e,t,r){var n=function(e,t){var r=e[A]=e[A]||{},n=r[t]=r[t]||{};return n.locks=n.locks||0,n.methods=n.methods||[],n}(e,t);n.methods.indexOf(r)<0&&n.methods.push(r);var o=Object.getOwnPropertyDescriptor(e,t);if(!o||!o[w]){var i=e[t],a=N(e,t,o?o.enumerable:void 0,n,i);Object.defineProperty(e,t,a)}}function N(e,t,r,n,o){var i,a=O(o,n);return(i={})[w]=!0,i.get=function(){return a},i.set=function(o){if(this===e)a=O(o,n);else{var i=N(this,t,r,n,o);Object.defineProperty(this,t,i)}},i.configurable=!0,i.enumerable=r,i}var C=Symbol("ObserverAdministration"),I=Symbol("isMobXReactObserver");function P(e){var t;return null!=(t=e[C])?t:e[C]={reaction:null,mounted:!1,reactionInvalidatedBeforeMount:!1,forceUpdate:null,name:x(e.constructor),state:void 0,props:void 0,context:void 0}}function x(e){return e.displayName||e.name||"<component>"}function M(e){var t=e.bind(this),r=P(this);return function(){r.reaction||(r.reaction=function(e){return new n.qT(e.name+".render()",(function(){if(e.mounted)try{null==e.forceUpdate||e.forceUpdate()}catch(r){var t;null==(t=e.reaction)||t.dispose(),e.reaction=null}else e.reactionInvalidatedBeforeMount=!0}))}(r),r.mounted||c.register(this,r,this));var e=void 0,o=void 0;if(r.reaction.track((function(){try{o=(0,n.vx)(!1,t)}catch(t){e=t}})),e)throw e;return o}}function U(e,t){return u()&&console.warn("[mobx-react] It seems that a re-rendering of a React component is triggered while in static (server-side) mode. Please make sure components are rendered only once server-side."),this.state!==t||!function(e,t){if(T(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(var o=0;o<r.length;o++)if(!Object.hasOwnProperty.call(t,r[o])||!T(e[r[o]],t[r[o]]))return!1;return!0}(this.props,e)}function L(e,t){if(t&&"class"!==t.kind)throw new Error("The @observer decorator can be used on classes only");return!0===e.isMobxInjector&&console.warn("Mobx observer: You are trying to use `observer` on a component that already has `inject`. Please apply `observer` before applying `inject`"),Object.prototype.isPrototypeOf.call(o.Component,e)||Object.prototype.isPrototypeOf.call(o.PureComponent,e)?function(e){var t=e.prototype;if(e[I]){var r=x(e);throw new Error("The provided component class ("+r+") has already been declared as an observer component.")}if(e[I]=!0,t.componentWillReact)throw new Error("The componentWillReact life-cycle event is no longer supported");if(e.__proto__!==o.PureComponent)if(t.shouldComponentUpdate){if(t.shouldComponentUpdate!==U)throw new Error("It is not allowed to use shouldComponentUpdate in observer based components.")}else t.shouldComponentUpdate=U;var n=t.render;if("function"!=typeof n){var i=x(e);throw new Error("[mobx-react] class component ("+i+") is missing `render` method.\n`observer` requires `render` being a function defined on prototype.\n`render = () => {}` or `render = function() {}` is not supported.")}t.render=function(){return Object.defineProperty(this,"render",{configurable:!1,writable:!1,value:u()?n:M.call(this,n)}),this.render()};var a=t.componentDidMount;return t.componentDidMount=function(){var e=this,t=P(this);return t.mounted=!0,c.unregister(this),t.forceUpdate=function(){return e.forceUpdate()},t.reaction&&!t.reactionInvalidatedBeforeMount||t.forceUpdate(),null==a?void 0:a.apply(this,arguments)},R(t,"componentWillUnmount",(function(){var e;if(!u()){var t=P(this);null==(e=t.reaction)||e.dispose(),t.reaction=null,t.forceUpdate=null,t.mounted=!1,t.reactionInvalidatedBeforeMount=!1}})),e}(e):_(e)}o.version.split(".")[0];if(!o.Component)throw new Error("mobx-react requires React to be available");if(!n.sH)throw new Error("mobx-react requires mobx to be available")},5589:(e,t,r)=>{"use strict";var n=r(626),o=r(1016);function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)a.add(t[e])}var g=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),c=Object.prototype.hasOwnProperty,p=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,d={},f={};function h(e,t,r,n,o,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=n,this.attributeNamespace=o,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var y={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){y[e]=new h(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];y[t]=new h(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){y[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){y[e]=new h(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){y[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){y[e]=new h(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){y[e]=new h(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){y[e]=new h(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){y[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)}));var b=/[\-:]([a-z])/g;function m(e){return e[1].toUpperCase()}function _(e,t,r,n){var o=y.hasOwnProperty(t)?y[t]:null;(null!==o?0!==o.type:n||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,r,n){if(null==t||function(e,t,r,n){if(null!==r&&0===r.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!n&&(null!==r?!r.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,r,n))return!0;if(n)return!1;if(null!==r)switch(r.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,r,o,n)&&(r=null),n||null===o?function(e){return!!c.call(f,e)||!c.call(d,e)&&(p.test(e)?f[e]=!0:(d[e]=!0,!1))}(t)&&(null===r?e.removeAttribute(t):e.setAttribute(t,""+r)):o.mustUseProperty?e[o.propertyName]=null===r?3!==o.type&&"":r:(t=o.attributeName,n=o.attributeNamespace,null===r?e.removeAttribute(t):(r=3===(o=o.type)||4===o&&!0===r?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(b,m);y[t]=new h(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(b,m);y[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(b,m);y[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){y[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)})),y.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){y[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)}));var v=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,S=Symbol.for("react.element"),E=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),A=Symbol.for("react.strict_mode"),w=Symbol.for("react.profiler"),j=Symbol.for("react.provider"),O=Symbol.for("react.context"),R=Symbol.for("react.forward_ref"),N=Symbol.for("react.suspense"),C=Symbol.for("react.suspense_list"),I=Symbol.for("react.memo"),P=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var x=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var M=Symbol.iterator;function U(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=M&&e[M]||e["@@iterator"])?e:null}var L,B=Object.assign;function D(e){if(void 0===L)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);L=t&&t[1]||""}return"\n"+L+e}var k=!1;function F(e,t){if(!e||k)return"";k=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var n=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){n=e}e.call(t.prototype)}else{try{throw Error()}catch(e){n=e}e()}}catch(t){if(t&&n&&"string"==typeof t.stack){for(var o=t.stack.split("\n"),i=n.stack.split("\n"),a=o.length-1,s=i.length-1;1<=a&&0<=s&&o[a]!==i[s];)s--;for(;1<=a&&0<=s;a--,s--)if(o[a]!==i[s]){if(1!==a||1!==s)do{if(a--,0>--s||o[a]!==i[s]){var l="\n"+o[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=a&&0<=s);break}}}finally{k=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?D(e):""}function W(e){switch(e.tag){case 5:return D(e.type);case 16:return D("Lazy");case 13:return D("Suspense");case 19:return D("SuspenseList");case 0:case 2:case 15:return e=F(e.type,!1);case 11:return e=F(e.type.render,!1);case 1:return e=F(e.type,!0);default:return""}}function V(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case T:return"Fragment";case E:return"Portal";case w:return"Profiler";case A:return"StrictMode";case N:return"Suspense";case C:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case O:return(e.displayName||"Context")+".Consumer";case j:return(e._context.displayName||"Context")+".Provider";case R:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case I:return null!==(t=e.displayName||null)?t:V(e.type)||"Memo";case P:t=e._payload,e=e._init;try{return V(e(t))}catch(e){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return V(t);case 8:return t===A?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function z(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function G(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Y(e){e._valueTracker||(e._valueTracker=function(e){var t=G(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==r&&"function"==typeof r.get&&"function"==typeof r.set){var o=r.get,i=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){n=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(e){n=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function K(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=G(e)?e.checked?"true":"false":e.value),(e=n)!==r&&(t.setValue(e),!0)}function $(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function q(e,t){var r=t.checked;return B({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=r?r:e._wrapperState.initialChecked})}function X(e,t){var r=null==t.defaultValue?"":t.defaultValue,n=null!=t.checked?t.checked:t.defaultChecked;r=z(null!=t.value?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Z(e,t){null!=(t=t.checked)&&_(e,"checked",t,!1)}function Q(e,t){Z(e,t);var r=z(t.value),n=t.type;if(null!=r)"number"===n?(0===r&&""===e.value||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if("submit"===n||"reset"===n)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,r):t.hasOwnProperty("defaultValue")&&ee(e,t.type,z(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!("submit"!==n&&"reset"!==n||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}""!==(r=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==r&&(e.name=r)}function ee(e,t,r){"number"===t&&$(e.ownerDocument)===e||(null==r?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var te=Array.isArray;function re(e,t,r,n){if(e=e.options,t){t={};for(var o=0;o<r.length;o++)t["$"+r[o]]=!0;for(r=0;r<e.length;r++)o=t.hasOwnProperty("$"+e[r].value),e[r].selected!==o&&(e[r].selected=o),o&&n&&(e[r].defaultSelected=!0)}else{for(r=""+z(r),t=null,o=0;o<e.length;o++){if(e[o].value===r)return e[o].selected=!0,void(n&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function ne(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(i(91));return B({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var r=t.value;if(null==r){if(r=t.children,t=t.defaultValue,null!=r){if(null!=t)throw Error(i(92));if(te(r)){if(1<r.length)throw Error(i(93));r=r[0]}t=r}null==t&&(t=""),r=t}e._wrapperState={initialValue:z(r)}}function ie(e,t){var r=z(t.value),n=z(t.defaultValue);null!=r&&((r=""+r)!==e.value&&(e.value=r),null==t.defaultValue&&e.defaultValue!==r&&(e.defaultValue=r)),null!=n&&(e.defaultValue=""+n)}function ae(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ge,ce=(ge=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,r,n){MSApp.execUnsafeLocalFunction((function(){return ge(e,t)}))}:ge);function pe(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&3===r.nodeType)return void(r.nodeValue=t)}e.textContent=t}var de={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},fe=["Webkit","ms","Moz","O"];function he(e,t,r){return null==t||"boolean"==typeof t||""===t?"":r||"number"!=typeof t||0===t||de.hasOwnProperty(e)&&de[e]?(""+t).trim():t+"px"}function ye(e,t){for(var r in e=e.style,t)if(t.hasOwnProperty(r)){var n=0===r.indexOf("--"),o=he(r,t[r],n);"float"===r&&(r="cssFloat"),n?e.setProperty(r,o):e[r]=o}}Object.keys(de).forEach((function(e){fe.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),de[t]=de[e]}))}));var be=B({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function me(e,t){if(t){if(be[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(i(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(i(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(i(62))}}function _e(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ve=null;function Se(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Ee=null,Te=null,Ae=null;function we(e){if(e=vo(e)){if("function"!=typeof Ee)throw Error(i(280));var t=e.stateNode;t&&(t=Eo(t),Ee(e.stateNode,e.type,t))}}function je(e){Te?Ae?Ae.push(e):Ae=[e]:Te=e}function Oe(){if(Te){var e=Te,t=Ae;if(Ae=Te=null,we(e),t)for(e=0;e<t.length;e++)we(t[e])}}function Re(e,t){return e(t)}function Ne(){}var Ce=!1;function Ie(e,t,r){if(Ce)return e(t,r);Ce=!0;try{return Re(e,t,r)}finally{Ce=!1,(null!==Te||null!==Ae)&&(Ne(),Oe())}}function Pe(e,t){var r=e.stateNode;if(null===r)return null;var n=Eo(r);if(null===n)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(n=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!n;break e;default:e=!1}if(e)return null;if(r&&"function"!=typeof r)throw Error(i(231,t,typeof r));return r}var xe=!1;if(g)try{var Me={};Object.defineProperty(Me,"passive",{get:function(){xe=!0}}),window.addEventListener("test",Me,Me),window.removeEventListener("test",Me,Me)}catch(ge){xe=!1}function Ue(e,t,r,n,o,i,a,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(r,u)}catch(e){this.onError(e)}}var Le=!1,Be=null,De=!1,ke=null,Fe={onError:function(e){Le=!0,Be=e}};function We(e,t,r,n,o,i,a,s,l){Le=!1,Be=null,Ue.apply(Fe,arguments)}function Ve(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!=(4098&(t=e).flags)&&(r=t.return),e=t.return}while(e)}return 3===t.tag?r:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function ze(e){if(Ve(e)!==e)throw Error(i(188))}function Ge(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ve(e)))throw Error(i(188));return t!==e?null:e}for(var r=e,n=t;;){var o=r.return;if(null===o)break;var a=o.alternate;if(null===a){if(null!==(n=o.return)){r=n;continue}break}if(o.child===a.child){for(a=o.child;a;){if(a===r)return ze(o),e;if(a===n)return ze(o),t;a=a.sibling}throw Error(i(188))}if(r.return!==n.return)r=o,n=a;else{for(var s=!1,l=o.child;l;){if(l===r){s=!0,r=o,n=a;break}if(l===n){s=!0,n=o,r=a;break}l=l.sibling}if(!s){for(l=a.child;l;){if(l===r){s=!0,r=a,n=o;break}if(l===n){s=!0,n=a,r=o;break}l=l.sibling}if(!s)throw Error(i(189))}}if(r.alternate!==n)throw Error(i(190))}if(3!==r.tag)throw Error(i(188));return r.stateNode.current===r?e:t}(e))?Ye(e):null}function Ye(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ye(e);if(null!==t)return t;e=e.sibling}return null}var Ke=o.unstable_scheduleCallback,$e=o.unstable_cancelCallback,qe=o.unstable_shouldYield,Xe=o.unstable_requestPaint,Ze=o.unstable_now,Qe=o.unstable_getCurrentPriorityLevel,Je=o.unstable_ImmediatePriority,et=o.unstable_UserBlockingPriority,tt=o.unstable_NormalPriority,rt=o.unstable_LowPriority,nt=o.unstable_IdlePriority,ot=null,it=null;var at=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,gt=4194304;function ct(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pt(e,t){var r=e.pendingLanes;if(0===r)return 0;var n=0,o=e.suspendedLanes,i=e.pingedLanes,a=268435455&r;if(0!==a){var s=a&~o;0!==s?n=ct(s):0!==(i&=a)&&(n=ct(i))}else 0!==(a=r&~o)?n=ct(a):0!==i&&(n=ct(i));if(0===n)return 0;if(0!==t&&t!==n&&0==(t&o)&&((o=n&-n)>=(i=t&-t)||16===o&&0!=(4194240&i)))return t;if(0!=(4&n)&&(n|=16&r),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=n;0<t;)o=1<<(r=31-at(t)),n|=e[r],t&=~o;return n}function dt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ft(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ut;return 0==(4194240&(ut<<=1))&&(ut=64),e}function yt(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function bt(e,t,r){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-at(t)]=r}function mt(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-at(r),o=1<<n;o&t|e[n]&t&&(e[n]|=t),r&=~o}}var _t=0;function vt(e){return 1<(e&=-e)?4<e?0!=(268435455&e)?16:536870912:4:1}var St,Et,Tt,At,wt,jt=!1,Ot=[],Rt=null,Nt=null,Ct=null,It=new Map,Pt=new Map,xt=[],Mt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ut(e,t){switch(e){case"focusin":case"focusout":Rt=null;break;case"dragenter":case"dragleave":Nt=null;break;case"mouseover":case"mouseout":Ct=null;break;case"pointerover":case"pointerout":It.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pt.delete(t.pointerId)}}function Lt(e,t,r,n,o,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:i,targetContainers:[o]},null!==t&&(null!==(t=vo(t))&&Et(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function Bt(e){var t=_o(e.target);if(null!==t){var r=Ve(t);if(null!==r)if(13===(t=r.tag)){if(null!==(t=He(r)))return e.blockedOn=t,void wt(e.priority,(function(){Tt(r)}))}else if(3===t&&r.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===r.tag?r.stateNode.containerInfo:null)}e.blockedOn=null}function Dt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var r=qt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==r)return null!==(t=vo(r))&&Et(t),e.blockedOn=r,!1;var n=new(r=e.nativeEvent).constructor(r.type,r);ve=n,r.target.dispatchEvent(n),ve=null,t.shift()}return!0}function kt(e,t,r){Dt(e)&&r.delete(t)}function Ft(){jt=!1,null!==Rt&&Dt(Rt)&&(Rt=null),null!==Nt&&Dt(Nt)&&(Nt=null),null!==Ct&&Dt(Ct)&&(Ct=null),It.forEach(kt),Pt.forEach(kt)}function Wt(e,t){e.blockedOn===t&&(e.blockedOn=null,jt||(jt=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,Ft)))}function Vt(e){function t(t){return Wt(t,e)}if(0<Ot.length){Wt(Ot[0],e);for(var r=1;r<Ot.length;r++){var n=Ot[r];n.blockedOn===e&&(n.blockedOn=null)}}for(null!==Rt&&Wt(Rt,e),null!==Nt&&Wt(Nt,e),null!==Ct&&Wt(Ct,e),It.forEach(t),Pt.forEach(t),r=0;r<xt.length;r++)(n=xt[r]).blockedOn===e&&(n.blockedOn=null);for(;0<xt.length&&null===(r=xt[0]).blockedOn;)Bt(r),null===r.blockedOn&&xt.shift()}var Ht=v.ReactCurrentBatchConfig,zt=!0;function Gt(e,t,r,n){var o=_t,i=Ht.transition;Ht.transition=null;try{_t=1,Kt(e,t,r,n)}finally{_t=o,Ht.transition=i}}function Yt(e,t,r,n){var o=_t,i=Ht.transition;Ht.transition=null;try{_t=4,Kt(e,t,r,n)}finally{_t=o,Ht.transition=i}}function Kt(e,t,r,n){if(zt){var o=qt(e,t,r,n);if(null===o)zn(e,t,n,$t,r),Ut(e,n);else if(function(e,t,r,n,o){switch(t){case"focusin":return Rt=Lt(Rt,e,t,r,n,o),!0;case"dragenter":return Nt=Lt(Nt,e,t,r,n,o),!0;case"mouseover":return Ct=Lt(Ct,e,t,r,n,o),!0;case"pointerover":var i=o.pointerId;return It.set(i,Lt(It.get(i)||null,e,t,r,n,o)),!0;case"gotpointercapture":return i=o.pointerId,Pt.set(i,Lt(Pt.get(i)||null,e,t,r,n,o)),!0}return!1}(o,e,t,r,n))n.stopPropagation();else if(Ut(e,n),4&t&&-1<Mt.indexOf(e)){for(;null!==o;){var i=vo(o);if(null!==i&&St(i),null===(i=qt(e,t,r,n))&&zn(e,t,n,$t,r),i===o)break;o=i}null!==o&&n.stopPropagation()}else zn(e,t,n,null,r)}}var $t=null;function qt(e,t,r,n){if($t=null,null!==(e=_o(e=Se(n))))if(null===(t=Ve(e)))e=null;else if(13===(r=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===r){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return $t=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Qe()){case Je:return 1;case et:return 4;case tt:case rt:return 16;case nt:return 536870912;default:return 16}default:return 16}}var Zt=null,Qt=null,Jt=null;function er(){if(Jt)return Jt;var e,t,r=Qt,n=r.length,o="value"in Zt?Zt.value:Zt.textContent,i=o.length;for(e=0;e<n&&r[e]===o[e];e++);var a=n-e;for(t=1;t<=a&&r[n-t]===o[i-t];t++);return Jt=o.slice(e,1<t?1-t:void 0)}function tr(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function rr(){return!0}function nr(){return!1}function or(e){function t(t,r,n,o,i){for(var a in this._reactName=t,this._targetInst=n,this.type=r,this.nativeEvent=o,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(o):o[a]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?rr:nr,this.isPropagationStopped=nr,this}return B(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=rr)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=rr)},persist:function(){},isPersistent:rr}),t}var ir,ar,sr,lr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ur=or(lr),gr=B({},lr,{view:0,detail:0}),cr=or(gr),pr=B({},gr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ar,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sr&&(sr&&"mousemove"===e.type?(ir=e.screenX-sr.screenX,ar=e.screenY-sr.screenY):ar=ir=0,sr=e),ir)},movementY:function(e){return"movementY"in e?e.movementY:ar}}),dr=or(pr),fr=or(B({},pr,{dataTransfer:0})),hr=or(B({},gr,{relatedTarget:0})),yr=or(B({},lr,{animationName:0,elapsedTime:0,pseudoElement:0})),br=B({},lr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),mr=or(br),_r=or(B({},lr,{data:0})),vr={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sr={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Er={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Tr(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Er[e])&&!!t[e]}function Ar(){return Tr}var wr=B({},gr,{key:function(e){if(e.key){var t=vr[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tr(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sr[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ar,charCode:function(e){return"keypress"===e.type?tr(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tr(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),jr=or(wr),Or=or(B({},pr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Rr=or(B({},gr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ar})),Nr=or(B({},lr,{propertyName:0,elapsedTime:0,pseudoElement:0})),Cr=B({},pr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ir=or(Cr),Pr=[9,13,27,32],xr=g&&"CompositionEvent"in window,Mr=null;g&&"documentMode"in document&&(Mr=document.documentMode);var Ur=g&&"TextEvent"in window&&!Mr,Lr=g&&(!xr||Mr&&8<Mr&&11>=Mr),Br=String.fromCharCode(32),Dr=!1;function kr(e,t){switch(e){case"keyup":return-1!==Pr.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Fr(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Wr=!1;var Vr={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vr[e.type]:"textarea"===t}function zr(e,t,r,n){je(n),0<(t=Yn(t,"onChange")).length&&(r=new ur("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var Gr=null,Yr=null;function Kr(e){Dn(e,0)}function $r(e){if(K(So(e)))return e}function qr(e,t){if("change"===e)return t}var Xr=!1;if(g){var Zr;if(g){var Qr="oninput"in document;if(!Qr){var Jr=document.createElement("div");Jr.setAttribute("oninput","return;"),Qr="function"==typeof Jr.oninput}Zr=Qr}else Zr=!1;Xr=Zr&&(!document.documentMode||9<document.documentMode)}function en(){Gr&&(Gr.detachEvent("onpropertychange",tn),Yr=Gr=null)}function tn(e){if("value"===e.propertyName&&$r(Yr)){var t=[];zr(t,Yr,e,Se(e)),Ie(Kr,t)}}function rn(e,t,r){"focusin"===e?(en(),Yr=r,(Gr=t).attachEvent("onpropertychange",tn)):"focusout"===e&&en()}function nn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return $r(Yr)}function on(e,t){if("click"===e)return $r(t)}function an(e,t){if("input"===e||"change"===e)return $r(t)}var sn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function ln(e,t){if(sn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var o=r[n];if(!c.call(t,o)||!sn(e[o],t[o]))return!1}return!0}function un(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function gn(e,t){var r,n=un(e);for(e=0;n;){if(3===n.nodeType){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=un(n)}}function cn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?cn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function pn(){for(var e=window,t=$();t instanceof e.HTMLIFrameElement;){try{var r="string"==typeof t.contentWindow.location.href}catch(e){r=!1}if(!r)break;t=$((e=t.contentWindow).document)}return t}function dn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function fn(e){var t=pn(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&cn(r.ownerDocument.documentElement,r)){if(null!==n&&dn(r))if(t=n.start,void 0===(e=n.end)&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if((e=(t=r.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=r.textContent.length,i=Math.min(n.start,o);n=void 0===n.end?i:Math.min(n.end,o),!e.extend&&i>n&&(o=n,n=i,i=o),o=gn(r,i);var a=gn(r,n);o&&a&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),i>n?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}for(t=[],e=r;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof r.focus&&r.focus(),r=0;r<t.length;r++)(e=t[r]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hn=g&&"documentMode"in document&&11>=document.documentMode,yn=null,bn=null,mn=null,_n=!1;function vn(e,t,r){var n=r.window===r?r.document:9===r.nodeType?r:r.ownerDocument;_n||null==yn||yn!==$(n)||("selectionStart"in(n=yn)&&dn(n)?n={start:n.selectionStart,end:n.selectionEnd}:n={anchorNode:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset},mn&&ln(mn,n)||(mn=n,0<(n=Yn(bn,"onSelect")).length&&(t=new ur("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=yn)))}function Sn(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var En={animationend:Sn("Animation","AnimationEnd"),animationiteration:Sn("Animation","AnimationIteration"),animationstart:Sn("Animation","AnimationStart"),transitionend:Sn("Transition","TransitionEnd")},Tn={},An={};function wn(e){if(Tn[e])return Tn[e];if(!En[e])return e;var t,r=En[e];for(t in r)if(r.hasOwnProperty(t)&&t in An)return Tn[e]=r[t];return e}g&&(An=document.createElement("div").style,"AnimationEvent"in window||(delete En.animationend.animation,delete En.animationiteration.animation,delete En.animationstart.animation),"TransitionEvent"in window||delete En.transitionend.transition);var jn=wn("animationend"),On=wn("animationiteration"),Rn=wn("animationstart"),Nn=wn("transitionend"),Cn=new Map,In="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Pn(e,t){Cn.set(e,t),l(t,[e])}for(var xn=0;xn<In.length;xn++){var Mn=In[xn];Pn(Mn.toLowerCase(),"on"+(Mn[0].toUpperCase()+Mn.slice(1)))}Pn(jn,"onAnimationEnd"),Pn(On,"onAnimationIteration"),Pn(Rn,"onAnimationStart"),Pn("dblclick","onDoubleClick"),Pn("focusin","onFocus"),Pn("focusout","onBlur"),Pn(Nn,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Un="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ln=new Set("cancel close invalid load scroll toggle".split(" ").concat(Un));function Bn(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,function(e,t,r,n,o,a,s,l,u){if(We.apply(this,arguments),Le){if(!Le)throw Error(i(198));var g=Be;Le=!1,Be=null,De||(De=!0,ke=g)}}(n,t,void 0,e),e.currentTarget=null}function Dn(e,t){t=0!=(4&t);for(var r=0;r<e.length;r++){var n=e[r],o=n.event;n=n.listeners;e:{var i=void 0;if(t)for(var a=n.length-1;0<=a;a--){var s=n[a],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==i&&o.isPropagationStopped())break e;Bn(o,s,u),i=l}else for(a=0;a<n.length;a++){if(l=(s=n[a]).instance,u=s.currentTarget,s=s.listener,l!==i&&o.isPropagationStopped())break e;Bn(o,s,u),i=l}}}if(De)throw e=ke,De=!1,ke=null,e}function kn(e,t){var r=t[yo];void 0===r&&(r=t[yo]=new Set);var n=e+"__bubble";r.has(n)||(Hn(t,e,2,!1),r.add(n))}function Fn(e,t,r){var n=0;t&&(n|=4),Hn(r,e,n,t)}var Wn="_reactListening"+Math.random().toString(36).slice(2);function Vn(e){if(!e[Wn]){e[Wn]=!0,a.forEach((function(t){"selectionchange"!==t&&(Ln.has(t)||Fn(t,!1,e),Fn(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Wn]||(t[Wn]=!0,Fn("selectionchange",!1,t))}}function Hn(e,t,r,n){switch(Xt(t)){case 1:var o=Gt;break;case 4:o=Yt;break;default:o=Kt}r=o.bind(null,t,r,e),o=void 0,!xe||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),n?void 0!==o?e.addEventListener(t,r,{capture:!0,passive:o}):e.addEventListener(t,r,!0):void 0!==o?e.addEventListener(t,r,{passive:o}):e.addEventListener(t,r,!1)}function zn(e,t,r,n,o){var i=n;if(0==(1&t)&&0==(2&t)&&null!==n)e:for(;;){if(null===n)return;var a=n.tag;if(3===a||4===a){var s=n.stateNode.containerInfo;if(s===o||8===s.nodeType&&s.parentNode===o)break;if(4===a)for(a=n.return;null!==a;){var l=a.tag;if((3===l||4===l)&&((l=a.stateNode.containerInfo)===o||8===l.nodeType&&l.parentNode===o))return;a=a.return}for(;null!==s;){if(null===(a=_o(s)))return;if(5===(l=a.tag)||6===l){n=i=a;continue e}s=s.parentNode}}n=n.return}Ie((function(){var n=i,o=Se(r),a=[];e:{var s=Cn.get(e);if(void 0!==s){var l=ur,u=e;switch(e){case"keypress":if(0===tr(r))break e;case"keydown":case"keyup":l=jr;break;case"focusin":u="focus",l=hr;break;case"focusout":u="blur",l=hr;break;case"beforeblur":case"afterblur":l=hr;break;case"click":if(2===r.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=dr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=fr;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Rr;break;case jn:case On:case Rn:l=yr;break;case Nn:l=Nr;break;case"scroll":l=cr;break;case"wheel":l=Ir;break;case"copy":case"cut":case"paste":l=mr;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Or}var g=0!=(4&t),c=!g&&"scroll"===e,p=g?null!==s?s+"Capture":null:s;g=[];for(var d,f=n;null!==f;){var h=(d=f).stateNode;if(5===d.tag&&null!==h&&(d=h,null!==p&&(null!=(h=Pe(f,p))&&g.push(Gn(f,h,d)))),c)break;f=f.return}0<g.length&&(s=new l(s,u,null,r,o),a.push({event:s,listeners:g}))}}if(0==(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||r===ve||!(u=r.relatedTarget||r.fromElement)||!_o(u)&&!u[ho])&&(l||s)&&(s=o.window===o?o:(s=o.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=n,null!==(u=(u=r.relatedTarget||r.toElement)?_o(u):null)&&(u!==(c=Ve(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=n),l!==u)){if(g=dr,h="onMouseLeave",p="onMouseEnter",f="mouse","pointerout"!==e&&"pointerover"!==e||(g=Or,h="onPointerLeave",p="onPointerEnter",f="pointer"),c=null==l?s:So(l),d=null==u?s:So(u),(s=new g(h,f+"leave",l,r,o)).target=c,s.relatedTarget=d,h=null,_o(o)===n&&((g=new g(p,f+"enter",u,r,o)).target=d,g.relatedTarget=c,h=g),c=h,l&&u)e:{for(p=u,f=0,d=g=l;d;d=Kn(d))f++;for(d=0,h=p;h;h=Kn(h))d++;for(;0<f-d;)g=Kn(g),f--;for(;0<d-f;)p=Kn(p),d--;for(;f--;){if(g===p||null!==p&&g===p.alternate)break e;g=Kn(g),p=Kn(p)}g=null}else g=null;null!==l&&$n(a,s,l,g,!1),null!==u&&null!==c&&$n(a,c,u,g,!0)}if("select"===(l=(s=n?So(n):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var y=qr;else if(Hr(s))if(Xr)y=an;else{y=nn;var b=rn}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(y=on);switch(y&&(y=y(e,n))?zr(a,y,r,o):(b&&b(e,s,n),"focusout"===e&&(b=s._wrapperState)&&b.controlled&&"number"===s.type&&ee(s,"number",s.value)),b=n?So(n):window,e){case"focusin":(Hr(b)||"true"===b.contentEditable)&&(yn=b,bn=n,mn=null);break;case"focusout":mn=bn=yn=null;break;case"mousedown":_n=!0;break;case"contextmenu":case"mouseup":case"dragend":_n=!1,vn(a,r,o);break;case"selectionchange":if(hn)break;case"keydown":case"keyup":vn(a,r,o)}var m;if(xr)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else Wr?kr(e,r)&&(_="onCompositionEnd"):"keydown"===e&&229===r.keyCode&&(_="onCompositionStart");_&&(Lr&&"ko"!==r.locale&&(Wr||"onCompositionStart"!==_?"onCompositionEnd"===_&&Wr&&(m=er()):(Qt="value"in(Zt=o)?Zt.value:Zt.textContent,Wr=!0)),0<(b=Yn(n,_)).length&&(_=new _r(_,e,null,r,o),a.push({event:_,listeners:b}),m?_.data=m:null!==(m=Fr(r))&&(_.data=m))),(m=Ur?function(e,t){switch(e){case"compositionend":return Fr(t);case"keypress":return 32!==t.which?null:(Dr=!0,Br);case"textInput":return(e=t.data)===Br&&Dr?null:e;default:return null}}(e,r):function(e,t){if(Wr)return"compositionend"===e||!xr&&kr(e,t)?(e=er(),Jt=Qt=Zt=null,Wr=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Lr&&"ko"!==t.locale?null:t.data}}(e,r))&&(0<(n=Yn(n,"onBeforeInput")).length&&(o=new _r("onBeforeInput","beforeinput",null,r,o),a.push({event:o,listeners:n}),o.data=m))}Dn(a,t)}))}function Gn(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Yn(e,t){for(var r=t+"Capture",n=[];null!==e;){var o=e,i=o.stateNode;5===o.tag&&null!==i&&(o=i,null!=(i=Pe(e,r))&&n.unshift(Gn(e,i,o)),null!=(i=Pe(e,t))&&n.push(Gn(e,i,o))),e=e.return}return n}function Kn(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function $n(e,t,r,n,o){for(var i=t._reactName,a=[];null!==r&&r!==n;){var s=r,l=s.alternate,u=s.stateNode;if(null!==l&&l===n)break;5===s.tag&&null!==u&&(s=u,o?null!=(l=Pe(r,i))&&a.unshift(Gn(r,l,s)):o||null!=(l=Pe(r,i))&&a.push(Gn(r,l,s))),r=r.return}0!==a.length&&e.push({event:t,listeners:a})}var qn=/\r\n?/g,Xn=/\u0000|\uFFFD/g;function Zn(e){return("string"==typeof e?e:""+e).replace(qn,"\n").replace(Xn,"")}function Qn(e,t,r){if(t=Zn(t),Zn(e)!==t&&r)throw Error(i(425))}function Jn(){}var eo=null,to=null;function ro(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var no="function"==typeof setTimeout?setTimeout:void 0,oo="function"==typeof clearTimeout?clearTimeout:void 0,io="function"==typeof Promise?Promise:void 0,ao="function"==typeof queueMicrotask?queueMicrotask:void 0!==io?function(e){return io.resolve(null).then(e).catch(so)}:no;function so(e){setTimeout((function(){throw e}))}function lo(e,t){var r=t,n=0;do{var o=r.nextSibling;if(e.removeChild(r),o&&8===o.nodeType)if("/$"===(r=o.data)){if(0===n)return e.removeChild(o),void Vt(t);n--}else"$"!==r&&"$?"!==r&&"$!"!==r||n++;r=o}while(r);Vt(t)}function uo(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function go(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var r=e.data;if("$"===r||"$!"===r||"$?"===r){if(0===t)return e;t--}else"/$"===r&&t++}e=e.previousSibling}return null}var co=Math.random().toString(36).slice(2),po="__reactFiber$"+co,fo="__reactProps$"+co,ho="__reactContainer$"+co,yo="__reactEvents$"+co,bo="__reactListeners$"+co,mo="__reactHandles$"+co;function _o(e){var t=e[po];if(t)return t;for(var r=e.parentNode;r;){if(t=r[ho]||r[po]){if(r=t.alternate,null!==t.child||null!==r&&null!==r.child)for(e=go(e);null!==e;){if(r=e[po])return r;e=go(e)}return t}r=(e=r).parentNode}return null}function vo(e){return!(e=e[po]||e[ho])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function So(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(i(33))}function Eo(e){return e[fo]||null}var To=[],Ao=-1;function wo(e){return{current:e}}function jo(e){0>Ao||(e.current=To[Ao],To[Ao]=null,Ao--)}function Oo(e,t){Ao++,To[Ao]=e.current,e.current=t}var Ro={},No=wo(Ro),Co=wo(!1),Io=Ro;function Po(e,t){var r=e.type.contextTypes;if(!r)return Ro;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var o,i={};for(o in r)i[o]=t[o];return n&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function xo(e){return null!=(e=e.childContextTypes)}function Mo(){jo(Co),jo(No)}function Uo(e,t,r){if(No.current!==Ro)throw Error(i(168));Oo(No,t),Oo(Co,r)}function Lo(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,"function"!=typeof n.getChildContext)return r;for(var o in n=n.getChildContext())if(!(o in t))throw Error(i(108,H(e)||"Unknown",o));return B({},r,n)}function Bo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ro,Io=No.current,Oo(No,e),Oo(Co,Co.current),!0}function Do(e,t,r){var n=e.stateNode;if(!n)throw Error(i(169));r?(e=Lo(e,t,Io),n.__reactInternalMemoizedMergedChildContext=e,jo(Co),jo(No),Oo(No,e)):jo(Co),Oo(Co,r)}var ko=null,Fo=!1,Wo=!1;function Vo(e){null===ko?ko=[e]:ko.push(e)}function Ho(){if(!Wo&&null!==ko){Wo=!0;var e=0,t=_t;try{var r=ko;for(_t=1;e<r.length;e++){var n=r[e];do{n=n(!0)}while(null!==n)}ko=null,Fo=!1}catch(t){throw null!==ko&&(ko=ko.slice(e+1)),Ke(Je,Ho),t}finally{_t=t,Wo=!1}}return null}var zo=[],Go=0,Yo=null,Ko=0,$o=[],qo=0,Xo=null,Zo=1,Qo="";function Jo(e,t){zo[Go++]=Ko,zo[Go++]=Yo,Yo=e,Ko=t}function ei(e,t,r){$o[qo++]=Zo,$o[qo++]=Qo,$o[qo++]=Xo,Xo=e;var n=Zo;e=Qo;var o=32-at(n)-1;n&=~(1<<o),r+=1;var i=32-at(t)+o;if(30<i){var a=o-o%5;i=(n&(1<<a)-1).toString(32),n>>=a,o-=a,Zo=1<<32-at(t)+o|r<<o|n,Qo=i+e}else Zo=1<<i|r<<o|n,Qo=e}function ti(e){null!==e.return&&(Jo(e,1),ei(e,1,0))}function ri(e){for(;e===Yo;)Yo=zo[--Go],zo[Go]=null,Ko=zo[--Go],zo[Go]=null;for(;e===Xo;)Xo=$o[--qo],$o[qo]=null,Qo=$o[--qo],$o[qo]=null,Zo=$o[--qo],$o[qo]=null}var ni=null,oi=null,ii=!1,ai=null;function si(e,t){var r=Iu(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,null===(t=e.deletions)?(e.deletions=[r],e.flags|=16):t.push(r)}function li(e,t){switch(e.tag){case 5:var r=e.type;return null!==(t=1!==t.nodeType||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ni=e,oi=uo(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ni=e,oi=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(r=null!==Xo?{id:Zo,overflow:Qo}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},(r=Iu(18,null,null,0)).stateNode=t,r.return=e,e.child=r,ni=e,oi=null,!0);default:return!1}}function ui(e){return 0!=(1&e.mode)&&0==(128&e.flags)}function gi(e){if(ii){var t=oi;if(t){var r=t;if(!li(e,t)){if(ui(e))throw Error(i(418));t=uo(r.nextSibling);var n=ni;t&&li(e,t)?si(n,r):(e.flags=-4097&e.flags|2,ii=!1,ni=e)}}else{if(ui(e))throw Error(i(418));e.flags=-4097&e.flags|2,ii=!1,ni=e}}}function ci(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ni=e}function pi(e){if(e!==ni)return!1;if(!ii)return ci(e),ii=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ro(e.type,e.memoizedProps)),t&&(t=oi)){if(ui(e))throw di(),Error(i(418));for(;t;)si(e,t),t=uo(t.nextSibling)}if(ci(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var r=e.data;if("/$"===r){if(0===t){oi=uo(e.nextSibling);break e}t--}else"$"!==r&&"$!"!==r&&"$?"!==r||t++}e=e.nextSibling}oi=null}}else oi=ni?uo(e.stateNode.nextSibling):null;return!0}function di(){for(var e=oi;e;)e=uo(e.nextSibling)}function fi(){oi=ni=null,ii=!1}function hi(e){null===ai?ai=[e]:ai.push(e)}var yi=v.ReactCurrentBatchConfig;function bi(e,t,r){if(null!==(e=r.ref)&&"function"!=typeof e&&"object"!=typeof e){if(r._owner){if(r=r._owner){if(1!==r.tag)throw Error(i(309));var n=r.stateNode}if(!n)throw Error(i(147,e));var o=n,a=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===a?t.ref:(t=function(e){var t=o.refs;null===e?delete t[a]:t[a]=e},t._stringRef=a,t)}if("string"!=typeof e)throw Error(i(284));if(!r._owner)throw Error(i(290,e))}return e}function mi(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function _i(e){return(0,e._init)(e._payload)}function vi(e){function t(t,r){if(e){var n=t.deletions;null===n?(t.deletions=[r],t.flags|=16):n.push(r)}}function r(r,n){if(!e)return null;for(;null!==n;)t(r,n),n=n.sibling;return null}function n(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=xu(e,t)).index=0,e.sibling=null,e}function a(t,r,n){return t.index=n,e?null!==(n=t.alternate)?(n=n.index)<r?(t.flags|=2,r):n:(t.flags|=2,r):(t.flags|=1048576,r)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,r,n){return null===t||6!==t.tag?((t=Bu(r,e.mode,n)).return=e,t):((t=o(t,r)).return=e,t)}function u(e,t,r,n){var i=r.type;return i===T?c(e,t,r.props.children,n,r.key):null!==t&&(t.elementType===i||"object"==typeof i&&null!==i&&i.$$typeof===P&&_i(i)===t.type)?((n=o(t,r.props)).ref=bi(e,t,r),n.return=e,n):((n=Mu(r.type,r.key,r.props,null,e.mode,n)).ref=bi(e,t,r),n.return=e,n)}function g(e,t,r,n){return null===t||4!==t.tag||t.stateNode.containerInfo!==r.containerInfo||t.stateNode.implementation!==r.implementation?((t=Du(r,e.mode,n)).return=e,t):((t=o(t,r.children||[])).return=e,t)}function c(e,t,r,n,i){return null===t||7!==t.tag?((t=Uu(r,e.mode,n,i)).return=e,t):((t=o(t,r)).return=e,t)}function p(e,t,r){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Bu(""+t,e.mode,r)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case S:return(r=Mu(t.type,t.key,t.props,null,e.mode,r)).ref=bi(e,null,t),r.return=e,r;case E:return(t=Du(t,e.mode,r)).return=e,t;case P:return p(e,(0,t._init)(t._payload),r)}if(te(t)||U(t))return(t=Uu(t,e.mode,r,null)).return=e,t;mi(e,t)}return null}function d(e,t,r,n){var o=null!==t?t.key:null;if("string"==typeof r&&""!==r||"number"==typeof r)return null!==o?null:l(e,t,""+r,n);if("object"==typeof r&&null!==r){switch(r.$$typeof){case S:return r.key===o?u(e,t,r,n):null;case E:return r.key===o?g(e,t,r,n):null;case P:return d(e,t,(o=r._init)(r._payload),n)}if(te(r)||U(r))return null!==o?null:c(e,t,r,n,null);mi(e,r)}return null}function f(e,t,r,n,o){if("string"==typeof n&&""!==n||"number"==typeof n)return l(t,e=e.get(r)||null,""+n,o);if("object"==typeof n&&null!==n){switch(n.$$typeof){case S:return u(t,e=e.get(null===n.key?r:n.key)||null,n,o);case E:return g(t,e=e.get(null===n.key?r:n.key)||null,n,o);case P:return f(e,t,r,(0,n._init)(n._payload),o)}if(te(n)||U(n))return c(t,e=e.get(r)||null,n,o,null);mi(t,n)}return null}function h(o,i,s,l){for(var u=null,g=null,c=i,h=i=0,y=null;null!==c&&h<s.length;h++){c.index>h?(y=c,c=null):y=c.sibling;var b=d(o,c,s[h],l);if(null===b){null===c&&(c=y);break}e&&c&&null===b.alternate&&t(o,c),i=a(b,i,h),null===g?u=b:g.sibling=b,g=b,c=y}if(h===s.length)return r(o,c),ii&&Jo(o,h),u;if(null===c){for(;h<s.length;h++)null!==(c=p(o,s[h],l))&&(i=a(c,i,h),null===g?u=c:g.sibling=c,g=c);return ii&&Jo(o,h),u}for(c=n(o,c);h<s.length;h++)null!==(y=f(c,o,h,s[h],l))&&(e&&null!==y.alternate&&c.delete(null===y.key?h:y.key),i=a(y,i,h),null===g?u=y:g.sibling=y,g=y);return e&&c.forEach((function(e){return t(o,e)})),ii&&Jo(o,h),u}function y(o,s,l,u){var g=U(l);if("function"!=typeof g)throw Error(i(150));if(null==(l=g.call(l)))throw Error(i(151));for(var c=g=null,h=s,y=s=0,b=null,m=l.next();null!==h&&!m.done;y++,m=l.next()){h.index>y?(b=h,h=null):b=h.sibling;var _=d(o,h,m.value,u);if(null===_){null===h&&(h=b);break}e&&h&&null===_.alternate&&t(o,h),s=a(_,s,y),null===c?g=_:c.sibling=_,c=_,h=b}if(m.done)return r(o,h),ii&&Jo(o,y),g;if(null===h){for(;!m.done;y++,m=l.next())null!==(m=p(o,m.value,u))&&(s=a(m,s,y),null===c?g=m:c.sibling=m,c=m);return ii&&Jo(o,y),g}for(h=n(o,h);!m.done;y++,m=l.next())null!==(m=f(h,o,y,m.value,u))&&(e&&null!==m.alternate&&h.delete(null===m.key?y:m.key),s=a(m,s,y),null===c?g=m:c.sibling=m,c=m);return e&&h.forEach((function(e){return t(o,e)})),ii&&Jo(o,y),g}return function e(n,i,a,l){if("object"==typeof a&&null!==a&&a.type===T&&null===a.key&&(a=a.props.children),"object"==typeof a&&null!==a){switch(a.$$typeof){case S:e:{for(var u=a.key,g=i;null!==g;){if(g.key===u){if((u=a.type)===T){if(7===g.tag){r(n,g.sibling),(i=o(g,a.props.children)).return=n,n=i;break e}}else if(g.elementType===u||"object"==typeof u&&null!==u&&u.$$typeof===P&&_i(u)===g.type){r(n,g.sibling),(i=o(g,a.props)).ref=bi(n,g,a),i.return=n,n=i;break e}r(n,g);break}t(n,g),g=g.sibling}a.type===T?((i=Uu(a.props.children,n.mode,l,a.key)).return=n,n=i):((l=Mu(a.type,a.key,a.props,null,n.mode,l)).ref=bi(n,i,a),l.return=n,n=l)}return s(n);case E:e:{for(g=a.key;null!==i;){if(i.key===g){if(4===i.tag&&i.stateNode.containerInfo===a.containerInfo&&i.stateNode.implementation===a.implementation){r(n,i.sibling),(i=o(i,a.children||[])).return=n,n=i;break e}r(n,i);break}t(n,i),i=i.sibling}(i=Du(a,n.mode,l)).return=n,n=i}return s(n);case P:return e(n,i,(g=a._init)(a._payload),l)}if(te(a))return h(n,i,a,l);if(U(a))return y(n,i,a,l);mi(n,a)}return"string"==typeof a&&""!==a||"number"==typeof a?(a=""+a,null!==i&&6===i.tag?(r(n,i.sibling),(i=o(i,a)).return=n,n=i):(r(n,i),(i=Bu(a,n.mode,l)).return=n,n=i),s(n)):r(n,i)}}var Si=vi(!0),Ei=vi(!1),Ti=wo(null),Ai=null,wi=null,ji=null;function Oi(){ji=wi=Ai=null}function Ri(e){var t=Ti.current;jo(Ti),e._currentValue=t}function Ni(e,t,r){for(;null!==e;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==n&&(n.childLanes|=t)):null!==n&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function Ci(e,t){Ai=e,ji=wi=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!=(e.lanes&t)&&(_s=!0),e.firstContext=null)}function Ii(e){var t=e._currentValue;if(ji!==e)if(e={context:e,memoizedValue:t,next:null},null===wi){if(null===Ai)throw Error(i(308));wi=e,Ai.dependencies={lanes:0,firstContext:e}}else wi=wi.next=e;return t}var Pi=null;function xi(e){null===Pi?Pi=[e]:Pi.push(e)}function Mi(e,t,r,n){var o=t.interleaved;return null===o?(r.next=r,xi(t)):(r.next=o.next,o.next=r),t.interleaved=r,Ui(e,n)}function Ui(e,t){e.lanes|=t;var r=e.alternate;for(null!==r&&(r.lanes|=t),r=e,e=e.return;null!==e;)e.childLanes|=t,null!==(r=e.alternate)&&(r.childLanes|=t),r=e,e=e.return;return 3===r.tag?r.stateNode:null}var Li=!1;function Bi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Di(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ki(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Fi(e,t,r){var n=e.updateQueue;if(null===n)return null;if(n=n.shared,0!=(2&Rl)){var o=n.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),n.pending=t,Ui(e,r)}return null===(o=n.interleaved)?(t.next=t,xi(n)):(t.next=o.next,o.next=t),n.interleaved=t,Ui(e,r)}function Wi(e,t,r){if(null!==(t=t.updateQueue)&&(t=t.shared,0!=(4194240&r))){var n=t.lanes;r|=n&=e.pendingLanes,t.lanes=r,mt(e,r)}}function Vi(e,t){var r=e.updateQueue,n=e.alternate;if(null!==n&&r===(n=n.updateQueue)){var o=null,i=null;if(null!==(r=r.firstBaseUpdate)){do{var a={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};null===i?o=i=a:i=i.next=a,r=r.next}while(null!==r);null===i?o=i=t:i=i.next=t}else o=i=t;return r={baseState:n.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:n.shared,effects:n.effects},void(e.updateQueue=r)}null===(e=r.lastBaseUpdate)?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Hi(e,t,r,n){var o=e.updateQueue;Li=!1;var i=o.firstBaseUpdate,a=o.lastBaseUpdate,s=o.shared.pending;if(null!==s){o.shared.pending=null;var l=s,u=l.next;l.next=null,null===a?i=u:a.next=u,a=l;var g=e.alternate;null!==g&&((s=(g=g.updateQueue).lastBaseUpdate)!==a&&(null===s?g.firstBaseUpdate=u:s.next=u,g.lastBaseUpdate=l))}if(null!==i){var c=o.baseState;for(a=0,g=u=l=null,s=i;;){var p=s.lane,d=s.eventTime;if((n&p)===p){null!==g&&(g=g.next={eventTime:d,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var f=e,h=s;switch(p=t,d=r,h.tag){case 1:if("function"==typeof(f=h.payload)){c=f.call(d,c,p);break e}c=f;break e;case 3:f.flags=-65537&f.flags|128;case 0:if(null==(p="function"==typeof(f=h.payload)?f.call(d,c,p):f))break e;c=B({},c,p);break e;case 2:Li=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(p=o.effects)?o.effects=[s]:p.push(s))}else d={eventTime:d,lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===g?(u=g=d,l=c):g=g.next=d,a|=p;if(null===(s=s.next)){if(null===(s=o.shared.pending))break;s=(p=s).next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}if(null===g&&(l=c),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=g,null!==(t=o.shared.interleaved)){o=t;do{a|=o.lane,o=o.next}while(o!==t)}else null===i&&(o.shared.lanes=0);Ll|=a,e.lanes=a,e.memoizedState=c}}function zi(e,t,r){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var n=e[t],o=n.callback;if(null!==o){if(n.callback=null,n=r,"function"!=typeof o)throw Error(i(191,o));o.call(n)}}}var Gi={},Yi=wo(Gi),Ki=wo(Gi),$i=wo(Gi);function qi(e){if(e===Gi)throw Error(i(174));return e}function Xi(e,t){switch(Oo($i,t),Oo(Ki,e),Oo(Yi,Gi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}jo(Yi),Oo(Yi,t)}function Zi(){jo(Yi),jo(Ki),jo($i)}function Qi(e){qi($i.current);var t=qi(Yi.current),r=le(t,e.type);t!==r&&(Oo(Ki,e),Oo(Yi,r))}function Ji(e){Ki.current===e&&(jo(Yi),jo(Ki))}var ea=wo(0);function ta(e){for(var t=e;null!==t;){if(13===t.tag){var r=t.memoizedState;if(null!==r&&(null===(r=r.dehydrated)||"$?"===r.data||"$!"===r.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ra=[];function na(){for(var e=0;e<ra.length;e++)ra[e]._workInProgressVersionPrimary=null;ra.length=0}var oa=v.ReactCurrentDispatcher,ia=v.ReactCurrentBatchConfig,aa=0,sa=null,la=null,ua=null,ga=!1,ca=!1,pa=0,da=0;function fa(){throw Error(i(321))}function ha(e,t){if(null===t)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!sn(e[r],t[r]))return!1;return!0}function ya(e,t,r,n,o,a){if(aa=a,sa=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,oa.current=null===e||null===e.memoizedState?Ja:es,e=r(n,o),ca){a=0;do{if(ca=!1,pa=0,25<=a)throw Error(i(301));a+=1,ua=la=null,t.updateQueue=null,oa.current=ts,e=r(n,o)}while(ca)}if(oa.current=Qa,t=null!==la&&null!==la.next,aa=0,ua=la=sa=null,ga=!1,t)throw Error(i(300));return e}function ba(){var e=0!==pa;return pa=0,e}function ma(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ua?sa.memoizedState=ua=e:ua=ua.next=e,ua}function _a(){if(null===la){var e=sa.alternate;e=null!==e?e.memoizedState:null}else e=la.next;var t=null===ua?sa.memoizedState:ua.next;if(null!==t)ua=t,la=e;else{if(null===e)throw Error(i(310));e={memoizedState:(la=e).memoizedState,baseState:la.baseState,baseQueue:la.baseQueue,queue:la.queue,next:null},null===ua?sa.memoizedState=ua=e:ua=ua.next=e}return ua}function va(e,t){return"function"==typeof t?t(e):t}function Sa(e){var t=_a(),r=t.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=e;var n=la,o=n.baseQueue,a=r.pending;if(null!==a){if(null!==o){var s=o.next;o.next=a.next,a.next=s}n.baseQueue=o=a,r.pending=null}if(null!==o){a=o.next,n=n.baseState;var l=s=null,u=null,g=a;do{var c=g.lane;if((aa&c)===c)null!==u&&(u=u.next={lane:0,action:g.action,hasEagerState:g.hasEagerState,eagerState:g.eagerState,next:null}),n=g.hasEagerState?g.eagerState:e(n,g.action);else{var p={lane:c,action:g.action,hasEagerState:g.hasEagerState,eagerState:g.eagerState,next:null};null===u?(l=u=p,s=n):u=u.next=p,sa.lanes|=c,Ll|=c}g=g.next}while(null!==g&&g!==a);null===u?s=n:u.next=l,sn(n,t.memoizedState)||(_s=!0),t.memoizedState=n,t.baseState=s,t.baseQueue=u,r.lastRenderedState=n}if(null!==(e=r.interleaved)){o=e;do{a=o.lane,sa.lanes|=a,Ll|=a,o=o.next}while(o!==e)}else null===o&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Ea(e){var t=_a(),r=t.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=e;var n=r.dispatch,o=r.pending,a=t.memoizedState;if(null!==o){r.pending=null;var s=o=o.next;do{a=e(a,s.action),s=s.next}while(s!==o);sn(a,t.memoizedState)||(_s=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),r.lastRenderedState=a}return[a,n]}function Ta(){}function Aa(e,t){var r=sa,n=_a(),o=t(),a=!sn(n.memoizedState,o);if(a&&(n.memoizedState=o,_s=!0),n=n.queue,La(Oa.bind(null,r,n,e),[e]),n.getSnapshot!==t||a||null!==ua&&1&ua.memoizedState.tag){if(r.flags|=2048,Ia(9,ja.bind(null,r,n,o,t),void 0,null),null===Nl)throw Error(i(349));0!=(30&aa)||wa(r,t,o)}return o}function wa(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},null===(t=sa.updateQueue)?(t={lastEffect:null,stores:null},sa.updateQueue=t,t.stores=[e]):null===(r=t.stores)?t.stores=[e]:r.push(e)}function ja(e,t,r,n){t.value=r,t.getSnapshot=n,Ra(t)&&Na(e)}function Oa(e,t,r){return r((function(){Ra(t)&&Na(e)}))}function Ra(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!sn(e,r)}catch(e){return!0}}function Na(e){var t=Ui(e,1);null!==t&&ru(t,e,1,-1)}function Ca(e){var t=ma();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:va,lastRenderedState:e},t.queue=e,e=e.dispatch=$a.bind(null,sa,e),[t.memoizedState,e]}function Ia(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},null===(t=sa.updateQueue)?(t={lastEffect:null,stores:null},sa.updateQueue=t,t.lastEffect=e.next=e):null===(r=t.lastEffect)?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e),e}function Pa(){return _a().memoizedState}function xa(e,t,r,n){var o=ma();sa.flags|=e,o.memoizedState=Ia(1|t,r,void 0,void 0===n?null:n)}function Ma(e,t,r,n){var o=_a();n=void 0===n?null:n;var i=void 0;if(null!==la){var a=la.memoizedState;if(i=a.destroy,null!==n&&ha(n,a.deps))return void(o.memoizedState=Ia(t,r,i,n))}sa.flags|=e,o.memoizedState=Ia(1|t,r,i,n)}function Ua(e,t){return xa(8390656,8,e,t)}function La(e,t){return Ma(2048,8,e,t)}function Ba(e,t){return Ma(4,2,e,t)}function Da(e,t){return Ma(4,4,e,t)}function ka(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Fa(e,t,r){return r=null!=r?r.concat([e]):null,Ma(4,4,ka.bind(null,t,e),r)}function Wa(){}function Va(e,t){var r=_a();t=void 0===t?null:t;var n=r.memoizedState;return null!==n&&null!==t&&ha(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Ha(e,t){var r=_a();t=void 0===t?null:t;var n=r.memoizedState;return null!==n&&null!==t&&ha(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function za(e,t,r){return 0==(21&aa)?(e.baseState&&(e.baseState=!1,_s=!0),e.memoizedState=r):(sn(r,t)||(r=ht(),sa.lanes|=r,Ll|=r,e.baseState=!0),t)}function Ga(e,t){var r=_t;_t=0!==r&&4>r?r:4,e(!0);var n=ia.transition;ia.transition={};try{e(!1),t()}finally{_t=r,ia.transition=n}}function Ya(){return _a().memoizedState}function Ka(e,t,r){var n=tu(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},qa(e))Xa(t,r);else if(null!==(r=Mi(e,t,r,n))){ru(r,e,n,eu()),Za(r,t,n)}}function $a(e,t,r){var n=tu(e),o={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(qa(e))Xa(t,o);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var a=t.lastRenderedState,s=i(a,r);if(o.hasEagerState=!0,o.eagerState=s,sn(s,a)){var l=t.interleaved;return null===l?(o.next=o,xi(t)):(o.next=l.next,l.next=o),void(t.interleaved=o)}}catch(e){}null!==(r=Mi(e,t,o,n))&&(ru(r,e,n,o=eu()),Za(r,t,n))}}function qa(e){var t=e.alternate;return e===sa||null!==t&&t===sa}function Xa(e,t){ca=ga=!0;var r=e.pending;null===r?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Za(e,t,r){if(0!=(4194240&r)){var n=t.lanes;r|=n&=e.pendingLanes,t.lanes=r,mt(e,r)}}var Qa={readContext:Ii,useCallback:fa,useContext:fa,useEffect:fa,useImperativeHandle:fa,useInsertionEffect:fa,useLayoutEffect:fa,useMemo:fa,useReducer:fa,useRef:fa,useState:fa,useDebugValue:fa,useDeferredValue:fa,useTransition:fa,useMutableSource:fa,useSyncExternalStore:fa,useId:fa,unstable_isNewReconciler:!1},Ja={readContext:Ii,useCallback:function(e,t){return ma().memoizedState=[e,void 0===t?null:t],e},useContext:Ii,useEffect:Ua,useImperativeHandle:function(e,t,r){return r=null!=r?r.concat([e]):null,xa(4194308,4,ka.bind(null,t,e),r)},useLayoutEffect:function(e,t){return xa(4194308,4,e,t)},useInsertionEffect:function(e,t){return xa(4,2,e,t)},useMemo:function(e,t){var r=ma();return t=void 0===t?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=ma();return t=void 0!==r?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=Ka.bind(null,sa,e),[n.memoizedState,e]},useRef:function(e){return e={current:e},ma().memoizedState=e},useState:Ca,useDebugValue:Wa,useDeferredValue:function(e){return ma().memoizedState=e},useTransition:function(){var e=Ca(!1),t=e[0];return e=Ga.bind(null,e[1]),ma().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=sa,o=ma();if(ii){if(void 0===r)throw Error(i(407));r=r()}else{if(r=t(),null===Nl)throw Error(i(349));0!=(30&aa)||wa(n,t,r)}o.memoizedState=r;var a={value:r,getSnapshot:t};return o.queue=a,Ua(Oa.bind(null,n,a,e),[e]),n.flags|=2048,Ia(9,ja.bind(null,n,a,r,t),void 0,null),r},useId:function(){var e=ma(),t=Nl.identifierPrefix;if(ii){var r=Qo;t=":"+t+"R"+(r=(Zo&~(1<<32-at(Zo)-1)).toString(32)+r),0<(r=pa++)&&(t+="H"+r.toString(32)),t+=":"}else t=":"+t+"r"+(r=da++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Ii,useCallback:Va,useContext:Ii,useEffect:La,useImperativeHandle:Fa,useInsertionEffect:Ba,useLayoutEffect:Da,useMemo:Ha,useReducer:Sa,useRef:Pa,useState:function(){return Sa(va)},useDebugValue:Wa,useDeferredValue:function(e){return za(_a(),la.memoizedState,e)},useTransition:function(){return[Sa(va)[0],_a().memoizedState]},useMutableSource:Ta,useSyncExternalStore:Aa,useId:Ya,unstable_isNewReconciler:!1},ts={readContext:Ii,useCallback:Va,useContext:Ii,useEffect:La,useImperativeHandle:Fa,useInsertionEffect:Ba,useLayoutEffect:Da,useMemo:Ha,useReducer:Ea,useRef:Pa,useState:function(){return Ea(va)},useDebugValue:Wa,useDeferredValue:function(e){var t=_a();return null===la?t.memoizedState=e:za(t,la.memoizedState,e)},useTransition:function(){return[Ea(va)[0],_a().memoizedState]},useMutableSource:Ta,useSyncExternalStore:Aa,useId:Ya,unstable_isNewReconciler:!1};function rs(e,t){if(e&&e.defaultProps){for(var r in t=B({},t),e=e.defaultProps)void 0===t[r]&&(t[r]=e[r]);return t}return t}function ns(e,t,r,n){r=null==(r=r(n,t=e.memoizedState))?t:B({},t,r),e.memoizedState=r,0===e.lanes&&(e.updateQueue.baseState=r)}var os={isMounted:function(e){return!!(e=e._reactInternals)&&Ve(e)===e},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=eu(),o=tu(e),i=ki(n,o);i.payload=t,null!=r&&(i.callback=r),null!==(t=Fi(e,i,o))&&(ru(t,e,o,n),Wi(t,e,o))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=eu(),o=tu(e),i=ki(n,o);i.tag=1,i.payload=t,null!=r&&(i.callback=r),null!==(t=Fi(e,i,o))&&(ru(t,e,o,n),Wi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=eu(),n=tu(e),o=ki(r,n);o.tag=2,null!=t&&(o.callback=t),null!==(t=Fi(e,o,n))&&(ru(t,e,n,r),Wi(t,e,n))}};function is(e,t,r,n,o,i,a){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(n,i,a):!t.prototype||!t.prototype.isPureReactComponent||(!ln(r,n)||!ln(o,i))}function as(e,t,r){var n=!1,o=Ro,i=t.contextType;return"object"==typeof i&&null!==i?i=Ii(i):(o=xo(t)?Io:No.current,i=(n=null!=(n=t.contextTypes))?Po(e,o):Ro),t=new t(r,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=os,e.stateNode=t,t._reactInternals=e,n&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function ss(e,t,r,n){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(r,n),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&os.enqueueReplaceState(t,t.state,null)}function ls(e,t,r,n){var o=e.stateNode;o.props=r,o.state=e.memoizedState,o.refs={},Bi(e);var i=t.contextType;"object"==typeof i&&null!==i?o.context=Ii(i):(i=xo(t)?Io:No.current,o.context=Po(e,i)),o.state=e.memoizedState,"function"==typeof(i=t.getDerivedStateFromProps)&&(ns(e,t,i,r),o.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof o.getSnapshotBeforeUpdate||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||(t=o.state,"function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&os.enqueueReplaceState(o,o.state,null),Hi(e,r,o,n),o.state=e.memoizedState),"function"==typeof o.componentDidMount&&(e.flags|=4194308)}function us(e,t){try{var r="",n=t;do{r+=W(n),n=n.return}while(n);var o=r}catch(e){o="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:o,digest:null}}function gs(e,t,r){return{value:e,source:null,stack:null!=r?r:null,digest:null!=t?t:null}}function cs(e,t){try{console.error(t.value)}catch(e){setTimeout((function(){throw e}))}}var ps="function"==typeof WeakMap?WeakMap:Map;function ds(e,t,r){(r=ki(-1,r)).tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){zl||(zl=!0,Gl=n),cs(0,t)},r}function fs(e,t,r){(r=ki(-1,r)).tag=3;var n=e.type.getDerivedStateFromError;if("function"==typeof n){var o=t.value;r.payload=function(){return n(o)},r.callback=function(){cs(0,t)}}var i=e.stateNode;return null!==i&&"function"==typeof i.componentDidCatch&&(r.callback=function(){cs(0,t),"function"!=typeof n&&(null===Yl?Yl=new Set([this]):Yl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),r}function hs(e,t,r){var n=e.pingCache;if(null===n){n=e.pingCache=new ps;var o=new Set;n.set(t,o)}else void 0===(o=n.get(t))&&(o=new Set,n.set(t,o));o.has(r)||(o.add(r),e=wu.bind(null,e,t,r),t.then(e,e))}function ys(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function bs(e,t,r,n,o){return 0==(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,1===r.tag&&(null===r.alternate?r.tag=17:((t=ki(-1,1)).tag=2,Fi(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var ms=v.ReactCurrentOwner,_s=!1;function vs(e,t,r,n){t.child=null===e?Ei(t,null,r,n):Si(t,e.child,r,n)}function Ss(e,t,r,n,o){r=r.render;var i=t.ref;return Ci(t,o),n=ya(e,t,r,n,i,o),r=ba(),null===e||_s?(ii&&r&&ti(t),t.flags|=1,vs(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,zs(e,t,o))}function Es(e,t,r,n,o){if(null===e){var i=r.type;return"function"!=typeof i||Pu(i)||void 0!==i.defaultProps||null!==r.compare||void 0!==r.defaultProps?((e=Mu(r.type,null,n,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Ts(e,t,i,n,o))}if(i=e.child,0==(e.lanes&o)){var a=i.memoizedProps;if((r=null!==(r=r.compare)?r:ln)(a,n)&&e.ref===t.ref)return zs(e,t,o)}return t.flags|=1,(e=xu(i,n)).ref=t.ref,e.return=t,t.child=e}function Ts(e,t,r,n,o){if(null!==e){var i=e.memoizedProps;if(ln(i,n)&&e.ref===t.ref){if(_s=!1,t.pendingProps=n=i,0==(e.lanes&o))return t.lanes=e.lanes,zs(e,t,o);0!=(131072&e.flags)&&(_s=!0)}}return js(e,t,r,n,o)}function As(e,t,r){var n=t.pendingProps,o=n.children,i=null!==e?e.memoizedState:null;if("hidden"===n.mode)if(0==(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Oo(xl,Pl),Pl|=r;else{if(0==(1073741824&r))return e=null!==i?i.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Oo(xl,Pl),Pl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=null!==i?i.baseLanes:r,Oo(xl,Pl),Pl|=n}else null!==i?(n=i.baseLanes|r,t.memoizedState=null):n=r,Oo(xl,Pl),Pl|=n;return vs(e,t,o,r),t.child}function ws(e,t){var r=t.ref;(null===e&&null!==r||null!==e&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function js(e,t,r,n,o){var i=xo(r)?Io:No.current;return i=Po(t,i),Ci(t,o),r=ya(e,t,r,n,i,o),n=ba(),null===e||_s?(ii&&n&&ti(t),t.flags|=1,vs(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,zs(e,t,o))}function Os(e,t,r,n,o){if(xo(r)){var i=!0;Bo(t)}else i=!1;if(Ci(t,o),null===t.stateNode)Hs(e,t),as(t,r,n),ls(t,r,n,o),n=!0;else if(null===e){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,u=r.contextType;"object"==typeof u&&null!==u?u=Ii(u):u=Po(t,u=xo(r)?Io:No.current);var g=r.getDerivedStateFromProps,c="function"==typeof g||"function"==typeof a.getSnapshotBeforeUpdate;c||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(s!==n||l!==u)&&ss(t,a,n,u),Li=!1;var p=t.memoizedState;a.state=p,Hi(t,n,a,o),l=t.memoizedState,s!==n||p!==l||Co.current||Li?("function"==typeof g&&(ns(t,r,g,n),l=t.memoizedState),(s=Li||is(t,r,s,n,p,l,u))?(c||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(t.flags|=4194308)):("function"==typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=l),a.props=n,a.state=l,a.context=u,n=s):("function"==typeof a.componentDidMount&&(t.flags|=4194308),n=!1)}else{a=t.stateNode,Di(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:rs(t.type,s),a.props=u,c=t.pendingProps,p=a.context,"object"==typeof(l=r.contextType)&&null!==l?l=Ii(l):l=Po(t,l=xo(r)?Io:No.current);var d=r.getDerivedStateFromProps;(g="function"==typeof d||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(s!==c||p!==l)&&ss(t,a,n,l),Li=!1,p=t.memoizedState,a.state=p,Hi(t,n,a,o);var f=t.memoizedState;s!==c||p!==f||Co.current||Li?("function"==typeof d&&(ns(t,r,d,n),f=t.memoizedState),(u=Li||is(t,r,u,n,p,f,l)||!1)?(g||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(n,f,l),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(n,f,l)),"function"==typeof a.componentDidUpdate&&(t.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof a.componentDidUpdate||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=f),a.props=n,a.state=f,a.context=l,n=u):("function"!=typeof a.componentDidUpdate||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),n=!1)}return Rs(e,t,r,n,i,o)}function Rs(e,t,r,n,o,i){ws(e,t);var a=0!=(128&t.flags);if(!n&&!a)return o&&Do(t,r,!1),zs(e,t,i);n=t.stateNode,ms.current=t;var s=a&&"function"!=typeof r.getDerivedStateFromError?null:n.render();return t.flags|=1,null!==e&&a?(t.child=Si(t,e.child,null,i),t.child=Si(t,null,s,i)):vs(e,t,s,i),t.memoizedState=n.state,o&&Do(t,r,!0),t.child}function Ns(e){var t=e.stateNode;t.pendingContext?Uo(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Uo(0,t.context,!1),Xi(e,t.containerInfo)}function Cs(e,t,r,n,o){return fi(),hi(o),t.flags|=256,vs(e,t,r,n),t.child}var Is,Ps,xs,Ms,Us={dehydrated:null,treeContext:null,retryLane:0};function Ls(e){return{baseLanes:e,cachePool:null,transitions:null}}function Bs(e,t,r){var n,o=t.pendingProps,a=ea.current,s=!1,l=0!=(128&t.flags);if((n=l)||(n=(null===e||null!==e.memoizedState)&&0!=(2&a)),n?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(a|=1),Oo(ea,1&a),null===e)return gi(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0==(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=o.children,e=o.fallback,s?(o=t.mode,s=t.child,l={mode:"hidden",children:l},0==(1&o)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Lu(l,o,0,null),e=Uu(e,o,r,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Ls(r),t.memoizedState=Us,e):Ds(t,l));if(null!==(a=e.memoizedState)&&null!==(n=a.dehydrated))return function(e,t,r,n,o,a,s){if(r)return 256&t.flags?(t.flags&=-257,ks(e,t,s,n=gs(Error(i(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(a=n.fallback,o=t.mode,n=Lu({mode:"visible",children:n.children},o,0,null),(a=Uu(a,o,s,null)).flags|=2,n.return=t,a.return=t,n.sibling=a,t.child=n,0!=(1&t.mode)&&Si(t,e.child,null,s),t.child.memoizedState=Ls(s),t.memoizedState=Us,a);if(0==(1&t.mode))return ks(e,t,s,null);if("$!"===o.data){if(n=o.nextSibling&&o.nextSibling.dataset)var l=n.dgst;return n=l,ks(e,t,s,n=gs(a=Error(i(419)),n,void 0))}if(l=0!=(s&e.childLanes),_s||l){if(null!==(n=Nl)){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!=(o&(n.suspendedLanes|s))?0:o)&&o!==a.retryLane&&(a.retryLane=o,Ui(e,o),ru(n,e,o,-1))}return hu(),ks(e,t,s,n=gs(Error(i(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=Ou.bind(null,e),o._reactRetry=t,null):(e=a.treeContext,oi=uo(o.nextSibling),ni=t,ii=!0,ai=null,null!==e&&($o[qo++]=Zo,$o[qo++]=Qo,$o[qo++]=Xo,Zo=e.id,Qo=e.overflow,Xo=t),t=Ds(t,n.children),t.flags|=4096,t)}(e,t,l,o,n,a,r);if(s){s=o.fallback,l=t.mode,n=(a=e.child).sibling;var u={mode:"hidden",children:o.children};return 0==(1&l)&&t.child!==a?((o=t.child).childLanes=0,o.pendingProps=u,t.deletions=null):(o=xu(a,u)).subtreeFlags=14680064&a.subtreeFlags,null!==n?s=xu(n,s):(s=Uu(s,l,r,null)).flags|=2,s.return=t,o.return=t,o.sibling=s,t.child=o,o=s,s=t.child,l=null===(l=e.child.memoizedState)?Ls(r):{baseLanes:l.baseLanes|r,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~r,t.memoizedState=Us,o}return e=(s=e.child).sibling,o=xu(s,{mode:"visible",children:o.children}),0==(1&t.mode)&&(o.lanes=r),o.return=t,o.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=o,t.memoizedState=null,o}function Ds(e,t){return(t=Lu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function ks(e,t,r,n){return null!==n&&hi(n),Si(t,e.child,null,r),(e=Ds(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Fs(e,t,r){e.lanes|=t;var n=e.alternate;null!==n&&(n.lanes|=t),Ni(e.return,t,r)}function Ws(e,t,r,n,o){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=n,i.tail=r,i.tailMode=o)}function Vs(e,t,r){var n=t.pendingProps,o=n.revealOrder,i=n.tail;if(vs(e,t,n.children,r),0!=(2&(n=ea.current)))n=1&n|2,t.flags|=128;else{if(null!==e&&0!=(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Fs(e,r,t);else if(19===e.tag)Fs(e,r,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(Oo(ea,n),0==(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(r=t.child,o=null;null!==r;)null!==(e=r.alternate)&&null===ta(e)&&(o=r),r=r.sibling;null===(r=o)?(o=t.child,t.child=null):(o=r.sibling,r.sibling=null),Ws(t,!1,o,r,i);break;case"backwards":for(r=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ta(e)){t.child=o;break}e=o.sibling,o.sibling=r,r=o,o=e}Ws(t,!0,r,null,i);break;case"together":Ws(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Hs(e,t){0==(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function zs(e,t,r){if(null!==e&&(t.dependencies=e.dependencies),Ll|=t.lanes,0==(r&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(r=xu(e=t.child,e.pendingProps),t.child=r,r.return=t;null!==e.sibling;)e=e.sibling,(r=r.sibling=xu(e,e.pendingProps)).return=t;r.sibling=null}return t.child}function Gs(e,t){if(!ii)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;null!==t;)null!==t.alternate&&(r=t),t=t.sibling;null===r?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;null!==r;)null!==r.alternate&&(n=r),r=r.sibling;null===n?t||null===e.tail?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Ys(e){var t=null!==e.alternate&&e.alternate.child===e.child,r=0,n=0;if(t)for(var o=e.child;null!==o;)r|=o.lanes|o.childLanes,n|=14680064&o.subtreeFlags,n|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)r|=o.lanes|o.childLanes,n|=o.subtreeFlags,n|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function Ks(e,t,r){var n=t.pendingProps;switch(ri(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ys(t),null;case 1:case 17:return xo(t.type)&&Mo(),Ys(t),null;case 3:return n=t.stateNode,Zi(),jo(Co),jo(No),na(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(pi(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0==(256&t.flags)||(t.flags|=1024,null!==ai&&(au(ai),ai=null))),Ps(e,t),Ys(t),null;case 5:Ji(t);var o=qi($i.current);if(r=t.type,null!==e&&null!=t.stateNode)xs(e,t,r,n,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(null===t.stateNode)throw Error(i(166));return Ys(t),null}if(e=qi(Yi.current),pi(t)){n=t.stateNode,r=t.type;var a=t.memoizedProps;switch(n[po]=t,n[fo]=a,e=0!=(1&t.mode),r){case"dialog":kn("cancel",n),kn("close",n);break;case"iframe":case"object":case"embed":kn("load",n);break;case"video":case"audio":for(o=0;o<Un.length;o++)kn(Un[o],n);break;case"source":kn("error",n);break;case"img":case"image":case"link":kn("error",n),kn("load",n);break;case"details":kn("toggle",n);break;case"input":X(n,a),kn("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!a.multiple},kn("invalid",n);break;case"textarea":oe(n,a),kn("invalid",n)}for(var l in me(r,a),o=null,a)if(a.hasOwnProperty(l)){var u=a[l];"children"===l?"string"==typeof u?n.textContent!==u&&(!0!==a.suppressHydrationWarning&&Qn(n.textContent,u,e),o=["children",u]):"number"==typeof u&&n.textContent!==""+u&&(!0!==a.suppressHydrationWarning&&Qn(n.textContent,u,e),o=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&kn("scroll",n)}switch(r){case"input":Y(n),J(n,a,!0);break;case"textarea":Y(n),ae(n);break;case"select":case"option":break;default:"function"==typeof a.onClick&&(n.onclick=Jn)}n=o,t.updateQueue=n,null!==n&&(t.flags|=4)}else{l=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(r)),"http://www.w3.org/1999/xhtml"===e?"script"===r?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof n.is?e=l.createElement(r,{is:n.is}):(e=l.createElement(r),"select"===r&&(l=e,n.multiple?l.multiple=!0:n.size&&(l.size=n.size))):e=l.createElementNS(e,r),e[po]=t,e[fo]=n,Is(e,t,!1,!1),t.stateNode=e;e:{switch(l=_e(r,n),r){case"dialog":kn("cancel",e),kn("close",e),o=n;break;case"iframe":case"object":case"embed":kn("load",e),o=n;break;case"video":case"audio":for(o=0;o<Un.length;o++)kn(Un[o],e);o=n;break;case"source":kn("error",e),o=n;break;case"img":case"image":case"link":kn("error",e),kn("load",e),o=n;break;case"details":kn("toggle",e),o=n;break;case"input":X(e,n),o=q(e,n),kn("invalid",e);break;case"option":default:o=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},o=B({},n,{value:void 0}),kn("invalid",e);break;case"textarea":oe(e,n),o=ne(e,n),kn("invalid",e)}for(a in me(r,o),u=o)if(u.hasOwnProperty(a)){var g=u[a];"style"===a?ye(e,g):"dangerouslySetInnerHTML"===a?null!=(g=g?g.__html:void 0)&&ce(e,g):"children"===a?"string"==typeof g?("textarea"!==r||""!==g)&&pe(e,g):"number"==typeof g&&pe(e,""+g):"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&"autoFocus"!==a&&(s.hasOwnProperty(a)?null!=g&&"onScroll"===a&&kn("scroll",e):null!=g&&_(e,a,g,l))}switch(r){case"input":Y(e),J(e,n,!1);break;case"textarea":Y(e),ae(e);break;case"option":null!=n.value&&e.setAttribute("value",""+z(n.value));break;case"select":e.multiple=!!n.multiple,null!=(a=n.value)?re(e,!!n.multiple,a,!1):null!=n.defaultValue&&re(e,!!n.multiple,n.defaultValue,!0);break;default:"function"==typeof o.onClick&&(e.onclick=Jn)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ys(t),null;case 6:if(e&&null!=t.stateNode)Ms(e,t,e.memoizedProps,n);else{if("string"!=typeof n&&null===t.stateNode)throw Error(i(166));if(r=qi($i.current),qi(Yi.current),pi(t)){if(n=t.stateNode,r=t.memoizedProps,n[po]=t,(a=n.nodeValue!==r)&&null!==(e=ni))switch(e.tag){case 3:Qn(n.nodeValue,r,0!=(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Qn(n.nodeValue,r,0!=(1&e.mode))}a&&(t.flags|=4)}else(n=(9===r.nodeType?r:r.ownerDocument).createTextNode(n))[po]=t,t.stateNode=n}return Ys(t),null;case 13:if(jo(ea),n=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ii&&null!==oi&&0!=(1&t.mode)&&0==(128&t.flags))di(),fi(),t.flags|=98560,a=!1;else if(a=pi(t),null!==n&&null!==n.dehydrated){if(null===e){if(!a)throw Error(i(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(i(317));a[po]=t}else fi(),0==(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ys(t),a=!1}else null!==ai&&(au(ai),ai=null),a=!0;if(!a)return 65536&t.flags?t:null}return 0!=(128&t.flags)?(t.lanes=r,t):((n=null!==n)!==(null!==e&&null!==e.memoizedState)&&n&&(t.child.flags|=8192,0!=(1&t.mode)&&(null===e||0!=(1&ea.current)?0===Ml&&(Ml=3):hu())),null!==t.updateQueue&&(t.flags|=4),Ys(t),null);case 4:return Zi(),Ps(e,t),null===e&&Vn(t.stateNode.containerInfo),Ys(t),null;case 10:return Ri(t.type._context),Ys(t),null;case 19:if(jo(ea),null===(a=t.memoizedState))return Ys(t),null;if(n=0!=(128&t.flags),null===(l=a.rendering))if(n)Gs(a,!1);else{if(0!==Ml||null!==e&&0!=(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ta(e))){for(t.flags|=128,Gs(a,!1),null!==(n=l.updateQueue)&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;null!==r;)e=n,(a=r).flags&=14680066,null===(l=a.alternate)?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=l.childLanes,a.lanes=l.lanes,a.child=l.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=l.memoizedProps,a.memoizedState=l.memoizedState,a.updateQueue=l.updateQueue,a.type=l.type,e=l.dependencies,a.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return Oo(ea,1&ea.current|2),t.child}e=e.sibling}null!==a.tail&&Ze()>Vl&&(t.flags|=128,n=!0,Gs(a,!1),t.lanes=4194304)}else{if(!n)if(null!==(e=ta(l))){if(t.flags|=128,n=!0,null!==(r=e.updateQueue)&&(t.updateQueue=r,t.flags|=4),Gs(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!ii)return Ys(t),null}else 2*Ze()-a.renderingStartTime>Vl&&1073741824!==r&&(t.flags|=128,n=!0,Gs(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(r=a.last)?r.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=Ze(),t.sibling=null,r=ea.current,Oo(ea,n?1&r|2:1&r),t):(Ys(t),null);case 22:case 23:return cu(),n=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==n&&(t.flags|=8192),n&&0!=(1&t.mode)?0!=(1073741824&Pl)&&(Ys(t),6&t.subtreeFlags&&(t.flags|=8192)):Ys(t),null;case 24:case 25:return null}throw Error(i(156,t.tag))}function $s(e,t){switch(ri(t),t.tag){case 1:return xo(t.type)&&Mo(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Zi(),jo(Co),jo(No),na(),0!=(65536&(e=t.flags))&&0==(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Ji(t),null;case 13:if(jo(ea),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));fi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return jo(ea),null;case 4:return Zi(),null;case 10:return Ri(t.type._context),null;case 22:case 23:return cu(),null;default:return null}}Is=function(e,t){for(var r=t.child;null!==r;){if(5===r.tag||6===r.tag)e.appendChild(r.stateNode);else if(4!==r.tag&&null!==r.child){r.child.return=r,r=r.child;continue}if(r===t)break;for(;null===r.sibling;){if(null===r.return||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Ps=function(){},xs=function(e,t,r,n){var o=e.memoizedProps;if(o!==n){e=t.stateNode,qi(Yi.current);var i,a=null;switch(r){case"input":o=q(e,o),n=q(e,n),a=[];break;case"select":o=B({},o,{value:void 0}),n=B({},n,{value:void 0}),a=[];break;case"textarea":o=ne(e,o),n=ne(e,n),a=[];break;default:"function"!=typeof o.onClick&&"function"==typeof n.onClick&&(e.onclick=Jn)}for(g in me(r,n),r=null,o)if(!n.hasOwnProperty(g)&&o.hasOwnProperty(g)&&null!=o[g])if("style"===g){var l=o[g];for(i in l)l.hasOwnProperty(i)&&(r||(r={}),r[i]="")}else"dangerouslySetInnerHTML"!==g&&"children"!==g&&"suppressContentEditableWarning"!==g&&"suppressHydrationWarning"!==g&&"autoFocus"!==g&&(s.hasOwnProperty(g)?a||(a=[]):(a=a||[]).push(g,null));for(g in n){var u=n[g];if(l=null!=o?o[g]:void 0,n.hasOwnProperty(g)&&u!==l&&(null!=u||null!=l))if("style"===g)if(l){for(i in l)!l.hasOwnProperty(i)||u&&u.hasOwnProperty(i)||(r||(r={}),r[i]="");for(i in u)u.hasOwnProperty(i)&&l[i]!==u[i]&&(r||(r={}),r[i]=u[i])}else r||(a||(a=[]),a.push(g,r)),r=u;else"dangerouslySetInnerHTML"===g?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(a=a||[]).push(g,u)):"children"===g?"string"!=typeof u&&"number"!=typeof u||(a=a||[]).push(g,""+u):"suppressContentEditableWarning"!==g&&"suppressHydrationWarning"!==g&&(s.hasOwnProperty(g)?(null!=u&&"onScroll"===g&&kn("scroll",e),a||l===u||(a=[])):(a=a||[]).push(g,u))}r&&(a=a||[]).push("style",r);var g=a;(t.updateQueue=g)&&(t.flags|=4)}},Ms=function(e,t,r,n){r!==n&&(t.flags|=4)};var qs=!1,Xs=!1,Zs="function"==typeof WeakSet?WeakSet:Set,Qs=null;function Js(e,t){var r=e.ref;if(null!==r)if("function"==typeof r)try{r(null)}catch(r){Au(e,t,r)}else r.current=null}function el(e,t,r){try{r()}catch(r){Au(e,t,r)}}var tl=!1;function rl(e,t,r){var n=t.updateQueue;if(null!==(n=null!==n?n.lastEffect:null)){var o=n=n.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,void 0!==i&&el(t,r,i)}o=o.next}while(o!==n)}}function nl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function ol(e){var t=e.ref;if(null!==t){var r=e.stateNode;e.tag,e=r,"function"==typeof t?t(e):t.current=e}}function il(e){var t=e.alternate;null!==t&&(e.alternate=null,il(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[po],delete t[fo],delete t[yo],delete t[bo],delete t[mo])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function al(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||al(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,r){var n=e.tag;if(5===n||6===n)e=e.stateNode,t?8===r.nodeType?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(8===r.nodeType?(t=r.parentNode).insertBefore(e,r):(t=r).appendChild(e),null!=(r=r._reactRootContainer)||null!==t.onclick||(t.onclick=Jn));else if(4!==n&&null!==(e=e.child))for(ll(e,t,r),e=e.sibling;null!==e;)ll(e,t,r),e=e.sibling}function ul(e,t,r){var n=e.tag;if(5===n||6===n)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(4!==n&&null!==(e=e.child))for(ul(e,t,r),e=e.sibling;null!==e;)ul(e,t,r),e=e.sibling}var gl=null,cl=!1;function pl(e,t,r){for(r=r.child;null!==r;)dl(e,t,r),r=r.sibling}function dl(e,t,r){if(it&&"function"==typeof it.onCommitFiberUnmount)try{it.onCommitFiberUnmount(ot,r)}catch(e){}switch(r.tag){case 5:Xs||Js(r,t);case 6:var n=gl,o=cl;gl=null,pl(e,t,r),cl=o,null!==(gl=n)&&(cl?(e=gl,r=r.stateNode,8===e.nodeType?e.parentNode.removeChild(r):e.removeChild(r)):gl.removeChild(r.stateNode));break;case 18:null!==gl&&(cl?(e=gl,r=r.stateNode,8===e.nodeType?lo(e.parentNode,r):1===e.nodeType&&lo(e,r),Vt(e)):lo(gl,r.stateNode));break;case 4:n=gl,o=cl,gl=r.stateNode.containerInfo,cl=!0,pl(e,t,r),gl=n,cl=o;break;case 0:case 11:case 14:case 15:if(!Xs&&(null!==(n=r.updateQueue)&&null!==(n=n.lastEffect))){o=n=n.next;do{var i=o,a=i.destroy;i=i.tag,void 0!==a&&(0!=(2&i)||0!=(4&i))&&el(r,t,a),o=o.next}while(o!==n)}pl(e,t,r);break;case 1:if(!Xs&&(Js(r,t),"function"==typeof(n=r.stateNode).componentWillUnmount))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(e){Au(r,t,e)}pl(e,t,r);break;case 21:pl(e,t,r);break;case 22:1&r.mode?(Xs=(n=Xs)||null!==r.memoizedState,pl(e,t,r),Xs=n):pl(e,t,r);break;default:pl(e,t,r)}}function fl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var r=e.stateNode;null===r&&(r=e.stateNode=new Zs),t.forEach((function(t){var n=Ru.bind(null,e,t);r.has(t)||(r.add(t),t.then(n,n))}))}}function hl(e,t){var r=t.deletions;if(null!==r)for(var n=0;n<r.length;n++){var o=r[n];try{var a=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:gl=l.stateNode,cl=!1;break e;case 3:case 4:gl=l.stateNode.containerInfo,cl=!0;break e}l=l.return}if(null===gl)throw Error(i(160));dl(a,s,o),gl=null,cl=!1;var u=o.alternate;null!==u&&(u.return=null),o.return=null}catch(e){Au(o,t,e)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)yl(t,e),t=t.sibling}function yl(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(hl(t,e),bl(e),4&n){try{rl(3,e,e.return),nl(3,e)}catch(t){Au(e,e.return,t)}try{rl(5,e,e.return)}catch(t){Au(e,e.return,t)}}break;case 1:hl(t,e),bl(e),512&n&&null!==r&&Js(r,r.return);break;case 5:if(hl(t,e),bl(e),512&n&&null!==r&&Js(r,r.return),32&e.flags){var o=e.stateNode;try{pe(o,"")}catch(t){Au(e,e.return,t)}}if(4&n&&null!=(o=e.stateNode)){var a=e.memoizedProps,s=null!==r?r.memoizedProps:a,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===a.type&&null!=a.name&&Z(o,a),_e(l,s);var g=_e(l,a);for(s=0;s<u.length;s+=2){var c=u[s],p=u[s+1];"style"===c?ye(o,p):"dangerouslySetInnerHTML"===c?ce(o,p):"children"===c?pe(o,p):_(o,c,p,g)}switch(l){case"input":Q(o,a);break;case"textarea":ie(o,a);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!a.multiple;var f=a.value;null!=f?re(o,!!a.multiple,f,!1):d!==!!a.multiple&&(null!=a.defaultValue?re(o,!!a.multiple,a.defaultValue,!0):re(o,!!a.multiple,a.multiple?[]:"",!1))}o[fo]=a}catch(t){Au(e,e.return,t)}}break;case 6:if(hl(t,e),bl(e),4&n){if(null===e.stateNode)throw Error(i(162));o=e.stateNode,a=e.memoizedProps;try{o.nodeValue=a}catch(t){Au(e,e.return,t)}}break;case 3:if(hl(t,e),bl(e),4&n&&null!==r&&r.memoizedState.isDehydrated)try{Vt(t.containerInfo)}catch(t){Au(e,e.return,t)}break;case 4:default:hl(t,e),bl(e);break;case 13:hl(t,e),bl(e),8192&(o=e.child).flags&&(a=null!==o.memoizedState,o.stateNode.isHidden=a,!a||null!==o.alternate&&null!==o.alternate.memoizedState||(Wl=Ze())),4&n&&fl(e);break;case 22:if(c=null!==r&&null!==r.memoizedState,1&e.mode?(Xs=(g=Xs)||c,hl(t,e),Xs=g):hl(t,e),bl(e),8192&n){if(g=null!==e.memoizedState,(e.stateNode.isHidden=g)&&!c&&0!=(1&e.mode))for(Qs=e,c=e.child;null!==c;){for(p=Qs=c;null!==Qs;){switch(f=(d=Qs).child,d.tag){case 0:case 11:case 14:case 15:rl(4,d,d.return);break;case 1:Js(d,d.return);var h=d.stateNode;if("function"==typeof h.componentWillUnmount){n=d,r=d.return;try{t=n,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(e){Au(n,r,e)}}break;case 5:Js(d,d.return);break;case 22:if(null!==d.memoizedState){Sl(p);continue}}null!==f?(f.return=d,Qs=f):Sl(p)}c=c.sibling}e:for(c=null,p=e;;){if(5===p.tag){if(null===c){c=p;try{o=p.stateNode,g?"function"==typeof(a=o.style).setProperty?a.setProperty("display","none","important"):a.display="none":(l=p.stateNode,s=null!=(u=p.memoizedProps.style)&&u.hasOwnProperty("display")?u.display:null,l.style.display=he("display",s))}catch(t){Au(e,e.return,t)}}}else if(6===p.tag){if(null===c)try{p.stateNode.nodeValue=g?"":p.memoizedProps}catch(t){Au(e,e.return,t)}}else if((22!==p.tag&&23!==p.tag||null===p.memoizedState||p===e)&&null!==p.child){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;null===p.sibling;){if(null===p.return||p.return===e)break e;c===p&&(c=null),p=p.return}c===p&&(c=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:hl(t,e),bl(e),4&n&&fl(e);case 21:}}function bl(e){var t=e.flags;if(2&t){try{e:{for(var r=e.return;null!==r;){if(al(r)){var n=r;break e}r=r.return}throw Error(i(160))}switch(n.tag){case 5:var o=n.stateNode;32&n.flags&&(pe(o,""),n.flags&=-33),ul(e,sl(e),o);break;case 3:case 4:var a=n.stateNode.containerInfo;ll(e,sl(e),a);break;default:throw Error(i(161))}}catch(t){Au(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function ml(e,t,r){Qs=e,_l(e,t,r)}function _l(e,t,r){for(var n=0!=(1&e.mode);null!==Qs;){var o=Qs,i=o.child;if(22===o.tag&&n){var a=null!==o.memoizedState||qs;if(!a){var s=o.alternate,l=null!==s&&null!==s.memoizedState||Xs;s=qs;var u=Xs;if(qs=a,(Xs=l)&&!u)for(Qs=o;null!==Qs;)l=(a=Qs).child,22===a.tag&&null!==a.memoizedState?El(o):null!==l?(l.return=a,Qs=l):El(o);for(;null!==i;)Qs=i,_l(i,t,r),i=i.sibling;Qs=o,qs=s,Xs=u}vl(e)}else 0!=(8772&o.subtreeFlags)&&null!==i?(i.return=o,Qs=i):vl(e)}}function vl(e){for(;null!==Qs;){var t=Qs;if(0!=(8772&t.flags)){var r=t.alternate;try{if(0!=(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xs||nl(5,t);break;case 1:var n=t.stateNode;if(4&t.flags&&!Xs)if(null===r)n.componentDidMount();else{var o=t.elementType===t.type?r.memoizedProps:rs(t.type,r.memoizedProps);n.componentDidUpdate(o,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;null!==a&&zi(t,a,n);break;case 3:var s=t.updateQueue;if(null!==s){if(r=null,null!==t.child)switch(t.child.tag){case 5:case 1:r=t.child.stateNode}zi(t,s,r)}break;case 5:var l=t.stateNode;if(null===r&&4&t.flags){r=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&r.focus();break;case"img":u.src&&(r.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var g=t.alternate;if(null!==g){var c=g.memoizedState;if(null!==c){var p=c.dehydrated;null!==p&&Vt(p)}}}break;default:throw Error(i(163))}Xs||512&t.flags&&ol(t)}catch(e){Au(t,t.return,e)}}if(t===e){Qs=null;break}if(null!==(r=t.sibling)){r.return=t.return,Qs=r;break}Qs=t.return}}function Sl(e){for(;null!==Qs;){var t=Qs;if(t===e){Qs=null;break}var r=t.sibling;if(null!==r){r.return=t.return,Qs=r;break}Qs=t.return}}function El(e){for(;null!==Qs;){var t=Qs;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{nl(4,t)}catch(e){Au(t,r,e)}break;case 1:var n=t.stateNode;if("function"==typeof n.componentDidMount){var o=t.return;try{n.componentDidMount()}catch(e){Au(t,o,e)}}var i=t.return;try{ol(t)}catch(e){Au(t,i,e)}break;case 5:var a=t.return;try{ol(t)}catch(e){Au(t,a,e)}}}catch(e){Au(t,t.return,e)}if(t===e){Qs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Qs=s;break}Qs=t.return}}var Tl,Al=Math.ceil,wl=v.ReactCurrentDispatcher,jl=v.ReactCurrentOwner,Ol=v.ReactCurrentBatchConfig,Rl=0,Nl=null,Cl=null,Il=0,Pl=0,xl=wo(0),Ml=0,Ul=null,Ll=0,Bl=0,Dl=0,kl=null,Fl=null,Wl=0,Vl=1/0,Hl=null,zl=!1,Gl=null,Yl=null,Kl=!1,$l=null,ql=0,Xl=0,Zl=null,Ql=-1,Jl=0;function eu(){return 0!=(6&Rl)?Ze():-1!==Ql?Ql:Ql=Ze()}function tu(e){return 0==(1&e.mode)?1:0!=(2&Rl)&&0!==Il?Il&-Il:null!==yi.transition?(0===Jl&&(Jl=ht()),Jl):0!==(e=_t)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function ru(e,t,r,n){if(50<Xl)throw Xl=0,Zl=null,Error(i(185));bt(e,r,n),0!=(2&Rl)&&e===Nl||(e===Nl&&(0==(2&Rl)&&(Bl|=r),4===Ml&&su(e,Il)),nu(e,n),1===r&&0===Rl&&0==(1&t.mode)&&(Vl=Ze()+500,Fo&&Ho()))}function nu(e,t){var r=e.callbackNode;!function(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-at(i),s=1<<a,l=o[a];-1===l?0!=(s&r)&&0==(s&n)||(o[a]=dt(s,t)):l<=t&&(e.expiredLanes|=s),i&=~s}}(e,t);var n=pt(e,e===Nl?Il:0);if(0===n)null!==r&&$e(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(null!=r&&$e(r),1===t)0===e.tag?function(e){Fo=!0,Vo(e)}(lu.bind(null,e)):Vo(lu.bind(null,e)),ao((function(){0==(6&Rl)&&Ho()})),r=null;else{switch(vt(n)){case 1:r=Je;break;case 4:r=et;break;case 16:default:r=tt;break;case 536870912:r=nt}r=Nu(r,ou.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function ou(e,t){if(Ql=-1,Jl=0,0!=(6&Rl))throw Error(i(327));var r=e.callbackNode;if(Eu()&&e.callbackNode!==r)return null;var n=pt(e,e===Nl?Il:0);if(0===n)return null;if(0!=(30&n)||0!=(n&e.expiredLanes)||t)t=yu(e,n);else{t=n;var o=Rl;Rl|=2;var a=fu();for(Nl===e&&Il===t||(Hl=null,Vl=Ze()+500,pu(e,t));;)try{mu();break}catch(t){du(e,t)}Oi(),wl.current=a,Rl=o,null!==Cl?t=0:(Nl=null,Il=0,t=Ml)}if(0!==t){if(2===t&&(0!==(o=ft(e))&&(n=o,t=iu(e,o))),1===t)throw r=Ul,pu(e,0),su(e,n),nu(e,Ze()),r;if(6===t)su(e,n);else{if(o=e.current.alternate,0==(30&n)&&!function(e){for(var t=e;;){if(16384&t.flags){var r=t.updateQueue;if(null!==r&&null!==(r=r.stores))for(var n=0;n<r.length;n++){var o=r[n],i=o.getSnapshot;o=o.value;try{if(!sn(i(),o))return!1}catch(e){return!1}}}if(r=t.child,16384&t.subtreeFlags&&null!==r)r.return=t,t=r;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=yu(e,n))&&(0!==(a=ft(e))&&(n=a,t=iu(e,a))),1===t))throw r=Ul,pu(e,0),su(e,n),nu(e,Ze()),r;switch(e.finishedWork=o,e.finishedLanes=n,t){case 0:case 1:throw Error(i(345));case 2:case 5:Su(e,Fl,Hl);break;case 3:if(su(e,n),(130023424&n)===n&&10<(t=Wl+500-Ze())){if(0!==pt(e,0))break;if(((o=e.suspendedLanes)&n)!==n){eu(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=no(Su.bind(null,e,Fl,Hl),t);break}Su(e,Fl,Hl);break;case 4:if(su(e,n),(4194240&n)===n)break;for(t=e.eventTimes,o=-1;0<n;){var s=31-at(n);a=1<<s,(s=t[s])>o&&(o=s),n&=~a}if(n=o,10<(n=(120>(n=Ze()-n)?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*Al(n/1960))-n)){e.timeoutHandle=no(Su.bind(null,e,Fl,Hl),n);break}Su(e,Fl,Hl);break;default:throw Error(i(329))}}}return nu(e,Ze()),e.callbackNode===r?ou.bind(null,e):null}function iu(e,t){var r=kl;return e.current.memoizedState.isDehydrated&&(pu(e,t).flags|=256),2!==(e=yu(e,t))&&(t=Fl,Fl=r,null!==t&&au(t)),e}function au(e){null===Fl?Fl=e:Fl.push.apply(Fl,e)}function su(e,t){for(t&=~Dl,t&=~Bl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-at(t),n=1<<r;e[r]=-1,t&=~n}}function lu(e){if(0!=(6&Rl))throw Error(i(327));Eu();var t=pt(e,0);if(0==(1&t))return nu(e,Ze()),null;var r=yu(e,t);if(0!==e.tag&&2===r){var n=ft(e);0!==n&&(t=n,r=iu(e,n))}if(1===r)throw r=Ul,pu(e,0),su(e,t),nu(e,Ze()),r;if(6===r)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Su(e,Fl,Hl),nu(e,Ze()),null}function uu(e,t){var r=Rl;Rl|=1;try{return e(t)}finally{0===(Rl=r)&&(Vl=Ze()+500,Fo&&Ho())}}function gu(e){null!==$l&&0===$l.tag&&0==(6&Rl)&&Eu();var t=Rl;Rl|=1;var r=Ol.transition,n=_t;try{if(Ol.transition=null,_t=1,e)return e()}finally{_t=n,Ol.transition=r,0==(6&(Rl=t))&&Ho()}}function cu(){Pl=xl.current,jo(xl)}function pu(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(-1!==r&&(e.timeoutHandle=-1,oo(r)),null!==Cl)for(r=Cl.return;null!==r;){var n=r;switch(ri(n),n.tag){case 1:null!=(n=n.type.childContextTypes)&&Mo();break;case 3:Zi(),jo(Co),jo(No),na();break;case 5:Ji(n);break;case 4:Zi();break;case 13:case 19:jo(ea);break;case 10:Ri(n.type._context);break;case 22:case 23:cu()}r=r.return}if(Nl=e,Cl=e=xu(e.current,null),Il=Pl=t,Ml=0,Ul=null,Dl=Bl=Ll=0,Fl=kl=null,null!==Pi){for(t=0;t<Pi.length;t++)if(null!==(n=(r=Pi[t]).interleaved)){r.interleaved=null;var o=n.next,i=r.pending;if(null!==i){var a=i.next;i.next=o,n.next=a}r.pending=n}Pi=null}return e}function du(e,t){for(;;){var r=Cl;try{if(Oi(),oa.current=Qa,ga){for(var n=sa.memoizedState;null!==n;){var o=n.queue;null!==o&&(o.pending=null),n=n.next}ga=!1}if(aa=0,ua=la=sa=null,ca=!1,pa=0,jl.current=null,null===r||null===r.return){Ml=1,Ul=t,Cl=null;break}e:{var a=e,s=r.return,l=r,u=t;if(t=Il,l.flags|=32768,null!==u&&"object"==typeof u&&"function"==typeof u.then){var g=u,c=l,p=c.tag;if(0==(1&c.mode)&&(0===p||11===p||15===p)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var f=ys(s);if(null!==f){f.flags&=-257,bs(f,s,l,0,t),1&f.mode&&hs(a,g,t),u=g;var h=(t=f).updateQueue;if(null===h){var y=new Set;y.add(u),t.updateQueue=y}else h.add(u);break e}if(0==(1&t)){hs(a,g,t),hu();break e}u=Error(i(426))}else if(ii&&1&l.mode){var b=ys(s);if(null!==b){0==(65536&b.flags)&&(b.flags|=256),bs(b,s,l,0,t),hi(us(u,l));break e}}a=u=us(u,l),4!==Ml&&(Ml=2),null===kl?kl=[a]:kl.push(a),a=s;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t,Vi(a,ds(0,u,t));break e;case 1:l=u;var m=a.type,_=a.stateNode;if(0==(128&a.flags)&&("function"==typeof m.getDerivedStateFromError||null!==_&&"function"==typeof _.componentDidCatch&&(null===Yl||!Yl.has(_)))){a.flags|=65536,t&=-t,a.lanes|=t,Vi(a,fs(a,l,t));break e}}a=a.return}while(null!==a)}vu(r)}catch(e){t=e,Cl===r&&null!==r&&(Cl=r=r.return);continue}break}}function fu(){var e=wl.current;return wl.current=Qa,null===e?Qa:e}function hu(){0!==Ml&&3!==Ml&&2!==Ml||(Ml=4),null===Nl||0==(268435455&Ll)&&0==(268435455&Bl)||su(Nl,Il)}function yu(e,t){var r=Rl;Rl|=2;var n=fu();for(Nl===e&&Il===t||(Hl=null,pu(e,t));;)try{bu();break}catch(t){du(e,t)}if(Oi(),Rl=r,wl.current=n,null!==Cl)throw Error(i(261));return Nl=null,Il=0,Ml}function bu(){for(;null!==Cl;)_u(Cl)}function mu(){for(;null!==Cl&&!qe();)_u(Cl)}function _u(e){var t=Tl(e.alternate,e,Pl);e.memoizedProps=e.pendingProps,null===t?vu(e):Cl=t,jl.current=null}function vu(e){var t=e;do{var r=t.alternate;if(e=t.return,0==(32768&t.flags)){if(null!==(r=Ks(r,t,Pl)))return void(Cl=r)}else{if(null!==(r=$s(r,t)))return r.flags&=32767,void(Cl=r);if(null===e)return Ml=6,void(Cl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Cl=t);Cl=t=e}while(null!==t);0===Ml&&(Ml=5)}function Su(e,t,r){var n=_t,o=Ol.transition;try{Ol.transition=null,_t=1,function(e,t,r,n){do{Eu()}while(null!==$l);if(0!=(6&Rl))throw Error(i(327));r=e.finishedWork;var o=e.finishedLanes;if(null===r)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var a=r.lanes|r.childLanes;if(function(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var o=31-at(r),i=1<<o;t[o]=0,n[o]=-1,e[o]=-1,r&=~i}}(e,a),e===Nl&&(Cl=Nl=null,Il=0),0==(2064&r.subtreeFlags)&&0==(2064&r.flags)||Kl||(Kl=!0,Nu(tt,(function(){return Eu(),null}))),a=0!=(15990&r.flags),0!=(15990&r.subtreeFlags)||a){a=Ol.transition,Ol.transition=null;var s=_t;_t=1;var l=Rl;Rl|=4,jl.current=null,function(e,t){if(eo=zt,dn(e=pn())){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{var n=(r=(r=e.ownerDocument)&&r.defaultView||window).getSelection&&r.getSelection();if(n&&0!==n.rangeCount){r=n.anchorNode;var o=n.anchorOffset,a=n.focusNode;n=n.focusOffset;try{r.nodeType,a.nodeType}catch(e){r=null;break e}var s=0,l=-1,u=-1,g=0,c=0,p=e,d=null;t:for(;;){for(var f;p!==r||0!==o&&3!==p.nodeType||(l=s+o),p!==a||0!==n&&3!==p.nodeType||(u=s+n),3===p.nodeType&&(s+=p.nodeValue.length),null!==(f=p.firstChild);)d=p,p=f;for(;;){if(p===e)break t;if(d===r&&++g===o&&(l=s),d===a&&++c===n&&(u=s),null!==(f=p.nextSibling))break;d=(p=d).parentNode}p=f}r=-1===l||-1===u?null:{start:l,end:u}}else r=null}r=r||{start:0,end:0}}else r=null;for(to={focusedElem:e,selectionRange:r},zt=!1,Qs=t;null!==Qs;)if(e=(t=Qs).child,0!=(1028&t.subtreeFlags)&&null!==e)e.return=t,Qs=e;else for(;null!==Qs;){t=Qs;try{var h=t.alternate;if(0!=(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var y=h.memoizedProps,b=h.memoizedState,m=t.stateNode,_=m.getSnapshotBeforeUpdate(t.elementType===t.type?y:rs(t.type,y),b);m.__reactInternalSnapshotBeforeUpdate=_}break;case 3:var v=t.stateNode.containerInfo;1===v.nodeType?v.textContent="":9===v.nodeType&&v.documentElement&&v.removeChild(v.documentElement);break;default:throw Error(i(163))}}catch(e){Au(t,t.return,e)}if(null!==(e=t.sibling)){e.return=t.return,Qs=e;break}Qs=t.return}h=tl,tl=!1}(e,r),yl(r,e),fn(to),zt=!!eo,to=eo=null,e.current=r,ml(r,e,o),Xe(),Rl=l,_t=s,Ol.transition=a}else e.current=r;if(Kl&&(Kl=!1,$l=e,ql=o),a=e.pendingLanes,0===a&&(Yl=null),function(e){if(it&&"function"==typeof it.onCommitFiberRoot)try{it.onCommitFiberRoot(ot,e,void 0,128==(128&e.current.flags))}catch(e){}}(r.stateNode),nu(e,Ze()),null!==t)for(n=e.onRecoverableError,r=0;r<t.length;r++)o=t[r],n(o.value,{componentStack:o.stack,digest:o.digest});if(zl)throw zl=!1,e=Gl,Gl=null,e;0!=(1&ql)&&0!==e.tag&&Eu(),a=e.pendingLanes,0!=(1&a)?e===Zl?Xl++:(Xl=0,Zl=e):Xl=0,Ho()}(e,t,r,n)}finally{Ol.transition=o,_t=n}return null}function Eu(){if(null!==$l){var e=vt(ql),t=Ol.transition,r=_t;try{if(Ol.transition=null,_t=16>e?16:e,null===$l)var n=!1;else{if(e=$l,$l=null,ql=0,0!=(6&Rl))throw Error(i(331));var o=Rl;for(Rl|=4,Qs=e.current;null!==Qs;){var a=Qs,s=a.child;if(0!=(16&Qs.flags)){var l=a.deletions;if(null!==l){for(var u=0;u<l.length;u++){var g=l[u];for(Qs=g;null!==Qs;){var c=Qs;switch(c.tag){case 0:case 11:case 15:rl(8,c,a)}var p=c.child;if(null!==p)p.return=c,Qs=p;else for(;null!==Qs;){var d=(c=Qs).sibling,f=c.return;if(il(c),c===g){Qs=null;break}if(null!==d){d.return=f,Qs=d;break}Qs=f}}}var h=a.alternate;if(null!==h){var y=h.child;if(null!==y){h.child=null;do{var b=y.sibling;y.sibling=null,y=b}while(null!==y)}}Qs=a}}if(0!=(2064&a.subtreeFlags)&&null!==s)s.return=a,Qs=s;else e:for(;null!==Qs;){if(0!=(2048&(a=Qs).flags))switch(a.tag){case 0:case 11:case 15:rl(9,a,a.return)}var m=a.sibling;if(null!==m){m.return=a.return,Qs=m;break e}Qs=a.return}}var _=e.current;for(Qs=_;null!==Qs;){var v=(s=Qs).child;if(0!=(2064&s.subtreeFlags)&&null!==v)v.return=s,Qs=v;else e:for(s=_;null!==Qs;){if(0!=(2048&(l=Qs).flags))try{switch(l.tag){case 0:case 11:case 15:nl(9,l)}}catch(e){Au(l,l.return,e)}if(l===s){Qs=null;break e}var S=l.sibling;if(null!==S){S.return=l.return,Qs=S;break e}Qs=l.return}}if(Rl=o,Ho(),it&&"function"==typeof it.onPostCommitFiberRoot)try{it.onPostCommitFiberRoot(ot,e)}catch(e){}n=!0}return n}finally{_t=r,Ol.transition=t}}return!1}function Tu(e,t,r){e=Fi(e,t=ds(0,t=us(r,t),1),1),t=eu(),null!==e&&(bt(e,1,t),nu(e,t))}function Au(e,t,r){if(3===e.tag)Tu(e,e,r);else for(;null!==t;){if(3===t.tag){Tu(t,e,r);break}if(1===t.tag){var n=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof n.componentDidCatch&&(null===Yl||!Yl.has(n))){t=Fi(t,e=fs(t,e=us(r,e),1),1),e=eu(),null!==t&&(bt(t,1,e),nu(t,e));break}}t=t.return}}function wu(e,t,r){var n=e.pingCache;null!==n&&n.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&r,Nl===e&&(Il&r)===r&&(4===Ml||3===Ml&&(130023424&Il)===Il&&500>Ze()-Wl?pu(e,0):Dl|=r),nu(e,t)}function ju(e,t){0===t&&(0==(1&e.mode)?t=1:(t=gt,0==(130023424&(gt<<=1))&&(gt=4194304)));var r=eu();null!==(e=Ui(e,t))&&(bt(e,t,r),nu(e,r))}function Ou(e){var t=e.memoizedState,r=0;null!==t&&(r=t.retryLane),ju(e,r)}function Ru(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,o=e.memoizedState;null!==o&&(r=o.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(i(314))}null!==n&&n.delete(t),ju(e,r)}function Nu(e,t){return Ke(e,t)}function Cu(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Iu(e,t,r,n){return new Cu(e,t,r,n)}function Pu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function xu(e,t){var r=e.alternate;return null===r?((r=Iu(e.tag,t,e.key,e.mode)).elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=14680064&e.flags,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function Mu(e,t,r,n,o,a){var s=2;if(n=e,"function"==typeof e)Pu(e)&&(s=1);else if("string"==typeof e)s=5;else e:switch(e){case T:return Uu(r.children,o,a,t);case A:s=8,o|=8;break;case w:return(e=Iu(12,r,t,2|o)).elementType=w,e.lanes=a,e;case N:return(e=Iu(13,r,t,o)).elementType=N,e.lanes=a,e;case C:return(e=Iu(19,r,t,o)).elementType=C,e.lanes=a,e;case x:return Lu(r,o,a,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case j:s=10;break e;case O:s=9;break e;case R:s=11;break e;case I:s=14;break e;case P:s=16,n=null;break e}throw Error(i(130,null==e?e:typeof e,""))}return(t=Iu(s,r,t,o)).elementType=e,t.type=n,t.lanes=a,t}function Uu(e,t,r,n){return(e=Iu(7,e,n,t)).lanes=r,e}function Lu(e,t,r,n){return(e=Iu(22,e,n,t)).elementType=x,e.lanes=r,e.stateNode={isHidden:!1},e}function Bu(e,t,r){return(e=Iu(6,e,null,t)).lanes=r,e}function Du(e,t,r){return(t=Iu(4,null!==e.children?e.children:[],e.key,t)).lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ku(e,t,r,n,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=yt(0),this.expirationTimes=yt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=yt(0),this.identifierPrefix=n,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Fu(e,t,r,n,o,i,a,s,l){return e=new ku(e,t,r,s,l),1===t?(t=1,!0===i&&(t|=8)):t=0,i=Iu(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},Bi(i),e}function Wu(e){if(!e)return Ro;e:{if(Ve(e=e._reactInternals)!==e||1!==e.tag)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(xo(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(i(171))}if(1===e.tag){var r=e.type;if(xo(r))return Lo(e,r,t)}return t}function Vu(e,t,r,n,o,i,a,s,l){return(e=Fu(r,n,!0,e,0,i,0,s,l)).context=Wu(null),r=e.current,(i=ki(n=eu(),o=tu(r))).callback=null!=t?t:null,Fi(r,i,o),e.current.lanes=o,bt(e,o,n),nu(e,n),e}function Hu(e,t,r,n){var o=t.current,i=eu(),a=tu(o);return r=Wu(r),null===t.context?t.context=r:t.pendingContext=r,(t=ki(i,a)).payload={element:e},null!==(n=void 0===n?null:n)&&(t.callback=n),null!==(e=Fi(o,t,a))&&(ru(e,o,a,i),Wi(e,o,a)),a}function zu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Gu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var r=e.retryLane;e.retryLane=0!==r&&r<t?r:t}}function Yu(e,t){Gu(e,t),(e=e.alternate)&&Gu(e,t)}Tl=function(e,t,r){if(null!==e)if(e.memoizedProps!==t.pendingProps||Co.current)_s=!0;else{if(0==(e.lanes&r)&&0==(128&t.flags))return _s=!1,function(e,t,r){switch(t.tag){case 3:Ns(t),fi();break;case 5:Qi(t);break;case 1:xo(t.type)&&Bo(t);break;case 4:Xi(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,o=t.memoizedProps.value;Oo(Ti,n._currentValue),n._currentValue=o;break;case 13:if(null!==(n=t.memoizedState))return null!==n.dehydrated?(Oo(ea,1&ea.current),t.flags|=128,null):0!=(r&t.child.childLanes)?Bs(e,t,r):(Oo(ea,1&ea.current),null!==(e=zs(e,t,r))?e.sibling:null);Oo(ea,1&ea.current);break;case 19:if(n=0!=(r&t.childLanes),0!=(128&e.flags)){if(n)return Vs(e,t,r);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),Oo(ea,ea.current),n)break;return null;case 22:case 23:return t.lanes=0,As(e,t,r)}return zs(e,t,r)}(e,t,r);_s=0!=(131072&e.flags)}else _s=!1,ii&&0!=(1048576&t.flags)&&ei(t,Ko,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;Hs(e,t),e=t.pendingProps;var o=Po(t,No.current);Ci(t,r),o=ya(null,t,n,e,o,r);var a=ba();return t.flags|=1,"object"==typeof o&&null!==o&&"function"==typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,xo(n)?(a=!0,Bo(t)):a=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,Bi(t),o.updater=os,t.stateNode=o,o._reactInternals=t,ls(t,n,e,r),t=Rs(null,t,n,!0,a,r)):(t.tag=0,ii&&a&&ti(t),vs(null,t,o,r),t=t.child),t;case 16:n=t.elementType;e:{switch(Hs(e,t),e=t.pendingProps,n=(o=n._init)(n._payload),t.type=n,o=t.tag=function(e){if("function"==typeof e)return Pu(e)?1:0;if(null!=e){if((e=e.$$typeof)===R)return 11;if(e===I)return 14}return 2}(n),e=rs(n,e),o){case 0:t=js(null,t,n,e,r);break e;case 1:t=Os(null,t,n,e,r);break e;case 11:t=Ss(null,t,n,e,r);break e;case 14:t=Es(null,t,n,rs(n.type,e),r);break e}throw Error(i(306,n,""))}return t;case 0:return n=t.type,o=t.pendingProps,js(e,t,n,o=t.elementType===n?o:rs(n,o),r);case 1:return n=t.type,o=t.pendingProps,Os(e,t,n,o=t.elementType===n?o:rs(n,o),r);case 3:e:{if(Ns(t),null===e)throw Error(i(387));n=t.pendingProps,o=(a=t.memoizedState).element,Di(e,t),Hi(t,n,null,r);var s=t.memoizedState;if(n=s.element,a.isDehydrated){if(a={element:n,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Cs(e,t,n,r,o=us(Error(i(423)),t));break e}if(n!==o){t=Cs(e,t,n,r,o=us(Error(i(424)),t));break e}for(oi=uo(t.stateNode.containerInfo.firstChild),ni=t,ii=!0,ai=null,r=Ei(t,null,n,r),t.child=r;r;)r.flags=-3&r.flags|4096,r=r.sibling}else{if(fi(),n===o){t=zs(e,t,r);break e}vs(e,t,n,r)}t=t.child}return t;case 5:return Qi(t),null===e&&gi(t),n=t.type,o=t.pendingProps,a=null!==e?e.memoizedProps:null,s=o.children,ro(n,o)?s=null:null!==a&&ro(n,a)&&(t.flags|=32),ws(e,t),vs(e,t,s,r),t.child;case 6:return null===e&&gi(t),null;case 13:return Bs(e,t,r);case 4:return Xi(t,t.stateNode.containerInfo),n=t.pendingProps,null===e?t.child=Si(t,null,n,r):vs(e,t,n,r),t.child;case 11:return n=t.type,o=t.pendingProps,Ss(e,t,n,o=t.elementType===n?o:rs(n,o),r);case 7:return vs(e,t,t.pendingProps,r),t.child;case 8:case 12:return vs(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,o=t.pendingProps,a=t.memoizedProps,s=o.value,Oo(Ti,n._currentValue),n._currentValue=s,null!==a)if(sn(a.value,s)){if(a.children===o.children&&!Co.current){t=zs(e,t,r);break e}}else for(null!==(a=t.child)&&(a.return=t);null!==a;){var l=a.dependencies;if(null!==l){s=a.child;for(var u=l.firstContext;null!==u;){if(u.context===n){if(1===a.tag){(u=ki(-1,r&-r)).tag=2;var g=a.updateQueue;if(null!==g){var c=(g=g.shared).pending;null===c?u.next=u:(u.next=c.next,c.next=u),g.pending=u}}a.lanes|=r,null!==(u=a.alternate)&&(u.lanes|=r),Ni(a.return,r,t),l.lanes|=r;break}u=u.next}}else if(10===a.tag)s=a.type===t.type?null:a.child;else if(18===a.tag){if(null===(s=a.return))throw Error(i(341));s.lanes|=r,null!==(l=s.alternate)&&(l.lanes|=r),Ni(s,r,t),s=a.sibling}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===t){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}vs(e,t,o.children,r),t=t.child}return t;case 9:return o=t.type,n=t.pendingProps.children,Ci(t,r),n=n(o=Ii(o)),t.flags|=1,vs(e,t,n,r),t.child;case 14:return o=rs(n=t.type,t.pendingProps),Es(e,t,n,o=rs(n.type,o),r);case 15:return Ts(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,o=t.pendingProps,o=t.elementType===n?o:rs(n,o),Hs(e,t),t.tag=1,xo(n)?(e=!0,Bo(t)):e=!1,Ci(t,r),as(t,n,o),ls(t,n,o,r),Rs(null,t,n,!0,e,r);case 19:return Vs(e,t,r);case 22:return As(e,t,r)}throw Error(i(156,t.tag))};var Ku="function"==typeof reportError?reportError:function(e){console.error(e)};function $u(e){this._internalRoot=e}function qu(e){this._internalRoot=e}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Zu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Qu(){}function Ju(e,t,r,n,o){var i=r._reactRootContainer;if(i){var a=i;if("function"==typeof o){var s=o;o=function(){var e=zu(a);s.call(e)}}Hu(t,a,e,o)}else a=function(e,t,r,n,o){if(o){if("function"==typeof n){var i=n;n=function(){var e=zu(a);i.call(e)}}var a=Vu(t,n,e,0,null,!1,0,"",Qu);return e._reactRootContainer=a,e[ho]=a.current,Vn(8===e.nodeType?e.parentNode:e),gu(),a}for(;o=e.lastChild;)e.removeChild(o);if("function"==typeof n){var s=n;n=function(){var e=zu(l);s.call(e)}}var l=Fu(e,0,!1,null,0,!1,0,"",Qu);return e._reactRootContainer=l,e[ho]=l.current,Vn(8===e.nodeType?e.parentNode:e),gu((function(){Hu(t,l,r,n)})),l}(r,t,e,o,n);return zu(a)}qu.prototype.render=$u.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Hu(e,t,null,null)},qu.prototype.unmount=$u.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;gu((function(){Hu(null,e,null,null)})),t[ho]=null}},qu.prototype.unstable_scheduleHydration=function(e){if(e){var t=At();e={blockedOn:null,target:e,priority:t};for(var r=0;r<xt.length&&0!==t&&t<xt[r].priority;r++);xt.splice(r,0,e),0===r&&Bt(e)}},St=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=ct(t.pendingLanes);0!==r&&(mt(t,1|r),nu(t,Ze()),0==(6&Rl)&&(Vl=Ze()+500,Ho()))}break;case 13:gu((function(){var t=Ui(e,1);if(null!==t){var r=eu();ru(t,e,1,r)}})),Yu(e,1)}},Et=function(e){if(13===e.tag){var t=Ui(e,134217728);if(null!==t)ru(t,e,134217728,eu());Yu(e,134217728)}},Tt=function(e){if(13===e.tag){var t=tu(e),r=Ui(e,t);if(null!==r)ru(r,e,t,eu());Yu(e,t)}},At=function(){return _t},wt=function(e,t){var r=_t;try{return _t=e,t()}finally{_t=r}},Ee=function(e,t,r){switch(t){case"input":if(Q(e,r),t=r.name,"radio"===r.type&&null!=t){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var o=Eo(n);if(!o)throw Error(i(90));K(n),Q(n,o)}}}break;case"textarea":ie(e,r);break;case"select":null!=(t=r.value)&&re(e,!!r.multiple,t,!1)}},Re=uu,Ne=gu;var eg={usingClientEntryPoint:!1,Events:[vo,So,Eo,je,Oe,uu]},tg={findFiberByHostInstance:_o,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},rg={bundleType:tg.bundleType,version:tg.version,rendererPackageName:tg.rendererPackageName,rendererConfig:tg.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:v.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ge(e))?null:e.stateNode},findFiberByHostInstance:tg.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ng=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ng.isDisabled&&ng.supportsFiber)try{ot=ng.inject(rg),it=ng}catch(ge){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eg,t.createPortal=function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xu(t))throw Error(i(200));return function(e,t,r){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:E,key:null==n?null:""+n,children:e,containerInfo:t,implementation:r}}(e,t,null,r)},t.createRoot=function(e,t){if(!Xu(e))throw Error(i(299));var r=!1,n="",o=Ku;return null!=t&&(!0===t.unstable_strictMode&&(r=!0),void 0!==t.identifierPrefix&&(n=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=Fu(e,1,!1,null,0,r,0,n,o),e[ho]=t.current,Vn(8===e.nodeType?e.parentNode:e),new $u(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=null===(e=Ge(t))?null:e.stateNode},t.flushSync=function(e){return gu(e)},t.hydrate=function(e,t,r){if(!Zu(t))throw Error(i(200));return Ju(null,e,t,!0,r)},t.hydrateRoot=function(e,t,r){if(!Xu(e))throw Error(i(405));var n=null!=r&&r.hydratedSources||null,o=!1,a="",s=Ku;if(null!=r&&(!0===r.unstable_strictMode&&(o=!0),void 0!==r.identifierPrefix&&(a=r.identifierPrefix),void 0!==r.onRecoverableError&&(s=r.onRecoverableError)),t=Vu(t,null,e,1,null!=r?r:null,o,0,a,s),e[ho]=t.current,Vn(e),n)for(e=0;e<n.length;e++)o=(o=(r=n[e])._getVersion)(r._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[r,o]:t.mutableSourceEagerHydrationData.push(r,o);return new qu(t)},t.render=function(e,t,r){if(!Zu(t))throw Error(i(200));return Ju(null,e,t,!1,r)},t.unmountComponentAtNode=function(e){if(!Zu(e))throw Error(i(40));return!!e._reactRootContainer&&(gu((function(){Ju(null,null,e,!1,(function(){e._reactRootContainer=null,e[ho]=null}))})),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!Zu(r))throw Error(i(200));if(null==e||void 0===e._reactInternals)throw Error(i(38));return Ju(e,t,r,!1,n)},t.version="18.3.1-next-f1338f8080-20240426"},4844:(e,t,r)=>{"use strict";var n=r(2739);t.H=n.createRoot,n.hydrateRoot},2739:(e,t,r)=>{"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=r(5589)},3741:(e,t,r)=>{"use strict";r.d(t,{A:()=>E});var n=r(8584),o=r(1115),i=r(5033);function a(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}var s=r(626),l=r(2739);const u=!1;var g=r(8679),c=function(e){return e.scrollTop},p="unmounted",d="exited",f="entering",h="entered",y="exiting",b=function(e){function t(t,r){var n;n=e.call(this,t,r)||this;var o,i=r&&!r.isMounting?t.enter:t.appear;return n.appearStatus=null,t.in?i?(o=d,n.appearStatus=f):o=h:o=t.unmountOnExit||t.mountOnEnter?p:d,n.state={status:o},n.nextCallback=null,n}(0,i.A)(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===p?{status:d}:null};var r=t.prototype;return r.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},r.componentDidUpdate=function(e){var t=null;if(e!==this.props){var r=this.state.status;this.props.in?r!==f&&r!==h&&(t=f):r!==f&&r!==h||(t=y)}this.updateStatus(!1,t)},r.componentWillUnmount=function(){this.cancelNextCallback()},r.getTimeouts=function(){var e,t,r,n=this.props.timeout;return e=t=r=n,null!=n&&"number"!=typeof n&&(e=n.exit,t=n.enter,r=void 0!==n.appear?n.appear:t),{exit:e,enter:t,appear:r}},r.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===f){if(this.props.unmountOnExit||this.props.mountOnEnter){var r=this.props.nodeRef?this.props.nodeRef.current:l.findDOMNode(this);r&&c(r)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===d&&this.setState({status:p})},r.performEnter=function(e){var t=this,r=this.props.enter,n=this.context?this.context.isMounting:e,o=this.props.nodeRef?[n]:[l.findDOMNode(this),n],i=o[0],a=o[1],s=this.getTimeouts(),g=n?s.appear:s.enter;!e&&!r||u?this.safeSetState({status:h},(function(){t.props.onEntered(i)})):(this.props.onEnter(i,a),this.safeSetState({status:f},(function(){t.props.onEntering(i,a),t.onTransitionEnd(g,(function(){t.safeSetState({status:h},(function(){t.props.onEntered(i,a)}))}))})))},r.performExit=function(){var e=this,t=this.props.exit,r=this.getTimeouts(),n=this.props.nodeRef?void 0:l.findDOMNode(this);t&&!u?(this.props.onExit(n),this.safeSetState({status:y},(function(){e.props.onExiting(n),e.onTransitionEnd(r.exit,(function(){e.safeSetState({status:d},(function(){e.props.onExited(n)}))}))}))):this.safeSetState({status:d},(function(){e.props.onExited(n)}))},r.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},r.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},r.setNextCallback=function(e){var t=this,r=!0;return this.nextCallback=function(n){r&&(r=!1,t.nextCallback=null,e(n))},this.nextCallback.cancel=function(){r=!1},this.nextCallback},r.onTransitionEnd=function(e,t){this.setNextCallback(t);var r=this.props.nodeRef?this.props.nodeRef.current:l.findDOMNode(this),n=null==e&&!this.props.addEndListener;if(r&&!n){if(this.props.addEndListener){var o=this.props.nodeRef?[this.nextCallback]:[r,this.nextCallback],i=o[0],a=o[1];this.props.addEndListener(i,a)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},r.render=function(){var e=this.state.status;if(e===p)return null;var t=this.props,r=t.children,n=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,(0,o.A)(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return s.createElement(g.A.Provider,{value:null},"function"==typeof r?r(e,n):s.cloneElement(s.Children.only(r),n))},t}(s.Component);function m(){}b.contextType=g.A,b.propTypes={},b.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:m,onEntering:m,onEntered:m,onExit:m,onExiting:m,onExited:m},b.UNMOUNTED=p,b.EXITED=d,b.ENTERING=f,b.ENTERED=h,b.EXITING=y;const _=b;var v=function(e,t){return e&&t&&t.split(" ").forEach((function(t){return n=t,void((r=e).classList?r.classList.remove(n):"string"==typeof r.className?r.className=a(r.className,n):r.setAttribute("class",a(r.className&&r.className.baseVal||"",n)));var r,n}))},S=function(e){function t(){for(var t,r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return(t=e.call.apply(e,[this].concat(n))||this).appliedClasses={appear:{},enter:{},exit:{}},t.onEnter=function(e,r){var n=t.resolveArguments(e,r),o=n[0],i=n[1];t.removeClasses(o,"exit"),t.addClass(o,i?"appear":"enter","base"),t.props.onEnter&&t.props.onEnter(e,r)},t.onEntering=function(e,r){var n=t.resolveArguments(e,r),o=n[0],i=n[1]?"appear":"enter";t.addClass(o,i,"active"),t.props.onEntering&&t.props.onEntering(e,r)},t.onEntered=function(e,r){var n=t.resolveArguments(e,r),o=n[0],i=n[1]?"appear":"enter";t.removeClasses(o,i),t.addClass(o,i,"done"),t.props.onEntered&&t.props.onEntered(e,r)},t.onExit=function(e){var r=t.resolveArguments(e)[0];t.removeClasses(r,"appear"),t.removeClasses(r,"enter"),t.addClass(r,"exit","base"),t.props.onExit&&t.props.onExit(e)},t.onExiting=function(e){var r=t.resolveArguments(e)[0];t.addClass(r,"exit","active"),t.props.onExiting&&t.props.onExiting(e)},t.onExited=function(e){var r=t.resolveArguments(e)[0];t.removeClasses(r,"exit"),t.addClass(r,"exit","done"),t.props.onExited&&t.props.onExited(e)},t.resolveArguments=function(e,r){return t.props.nodeRef?[t.props.nodeRef.current,e]:[e,r]},t.getClassNames=function(e){var r=t.props.classNames,n="string"==typeof r,o=n?""+(n&&r?r+"-":"")+e:r[e];return{baseClassName:o,activeClassName:n?o+"-active":r[e+"Active"],doneClassName:n?o+"-done":r[e+"Done"]}},t}(0,i.A)(t,e);var r=t.prototype;return r.addClass=function(e,t,r){var n=this.getClassNames(t)[r+"ClassName"],o=this.getClassNames("enter").doneClassName;"appear"===t&&"done"===r&&o&&(n+=" "+o),"active"===r&&e&&c(e),n&&(this.appliedClasses[t][r]=n,function(e,t){e&&t&&t.split(" ").forEach((function(t){return n=t,void((r=e).classList?r.classList.add(n):function(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}(r,n)||("string"==typeof r.className?r.className=r.className+" "+n:r.setAttribute("class",(r.className&&r.className.baseVal||"")+" "+n)));var r,n}))}(e,n))},r.removeClasses=function(e,t){var r=this.appliedClasses[t],n=r.base,o=r.active,i=r.done;this.appliedClasses[t]={},n&&v(e,n),o&&v(e,o),i&&v(e,i)},r.render=function(){var e=this.props,t=(e.classNames,(0,o.A)(e,["classNames"]));return s.createElement(_,(0,n.A)({},t,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},t}(s.Component);S.defaultProps={classNames:""},S.propTypes={};const E=S},2630:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(1115),o=r(8584);var i=r(5033),a=r(626),s=r(8679);function l(e,t){var r=Object.create(null);return e&&a.Children.map(e,(function(e){return e})).forEach((function(e){r[e.key]=function(e){return t&&(0,a.isValidElement)(e)?t(e):e}(e)})),r}function u(e,t,r){return null!=r[t]?r[t]:e.props[t]}function g(e,t,r){var n=l(e.children),o=function(e,t){function r(r){return r in t?t[r]:e[r]}e=e||{},t=t||{};var n,o=Object.create(null),i=[];for(var a in e)a in t?i.length&&(o[a]=i,i=[]):i.push(a);var s={};for(var l in t){if(o[l])for(n=0;n<o[l].length;n++){var u=o[l][n];s[o[l][n]]=r(u)}s[l]=r(l)}for(n=0;n<i.length;n++)s[i[n]]=r(i[n]);return s}(t,n);return Object.keys(o).forEach((function(i){var s=o[i];if((0,a.isValidElement)(s)){var l=i in t,g=i in n,c=t[i],p=(0,a.isValidElement)(c)&&!c.props.in;!g||l&&!p?g||!l||p?g&&l&&(0,a.isValidElement)(c)&&(o[i]=(0,a.cloneElement)(s,{onExited:r.bind(null,s),in:c.props.in,exit:u(s,"exit",e),enter:u(s,"enter",e)})):o[i]=(0,a.cloneElement)(s,{in:!1}):o[i]=(0,a.cloneElement)(s,{onExited:r.bind(null,s),in:!0,exit:u(s,"exit",e),enter:u(s,"enter",e)})}})),o}var c=Object.values||function(e){return Object.keys(e).map((function(t){return e[t]}))},p=function(e){function t(t,r){var n,o=(n=e.call(this,t,r)||this).handleExited.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(n));return n.state={contextValue:{isMounting:!0},handleExited:o,firstRender:!0},n}(0,i.A)(t,e);var r=t.prototype;return r.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},r.componentWillUnmount=function(){this.mounted=!1},t.getDerivedStateFromProps=function(e,t){var r,n,o=t.children,i=t.handleExited;return{children:t.firstRender?(r=e,n=i,l(r.children,(function(e){return(0,a.cloneElement)(e,{onExited:n.bind(null,e),in:!0,appear:u(e,"appear",r),enter:u(e,"enter",r),exit:u(e,"exit",r)})}))):g(e,o,i),firstRender:!1}},r.handleExited=function(e,t){var r=l(this.props.children);e.key in r||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState((function(t){var r=(0,o.A)({},t.children);return delete r[e.key],{children:r}})))},r.render=function(){var e=this.props,t=e.component,r=e.childFactory,o=(0,n.A)(e,["component","childFactory"]),i=this.state.contextValue,l=c(this.state.children).map(r);return delete o.appear,delete o.enter,delete o.exit,null===t?a.createElement(s.A.Provider,{value:i},l):a.createElement(s.A.Provider,{value:i},a.createElement(t,o,l))},t}(a.Component);p.propTypes={},p.defaultProps={component:"div",childFactory:function(e){return e}};const d=p},8679:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=r(626).createContext(null)},9069:(e,t)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),c=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),d=Symbol.iterator;var f={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,y={};function b(e,t,r){this.props=e,this.context=t,this.refs=y,this.updater=r||f}function m(){}function _(e,t,r){this.props=e,this.context=t,this.refs=y,this.updater=r||f}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},m.prototype=b.prototype;var v=_.prototype=new m;v.constructor=_,h(v,b.prototype),v.isPureReactComponent=!0;var S=Array.isArray,E=Object.prototype.hasOwnProperty,T={current:null},A={key:!0,ref:!0,__self:!0,__source:!0};function w(e,t,n){var o,i={},a=null,s=null;if(null!=t)for(o in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)E.call(t,o)&&!A.hasOwnProperty(o)&&(i[o]=t[o]);var l=arguments.length-2;if(1===l)i.children=n;else if(1<l){for(var u=Array(l),g=0;g<l;g++)u[g]=arguments[g+2];i.children=u}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===i[o]&&(i[o]=l[o]);return{$$typeof:r,type:e,key:a,ref:s,props:i,_owner:T.current}}function j(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}var O=/\/+/g;function R(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function N(e,t,o,i,a){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case r:case n:l=!0}}if(l)return a=a(l=e),e=""===i?"."+R(l,0):i,S(a)?(o="",null!=e&&(o=e.replace(O,"$&/")+"/"),N(a,t,o,"",(function(e){return e}))):null!=a&&(j(a)&&(a=function(e,t){return{$$typeof:r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,o+(!a.key||l&&l.key===a.key?"":(""+a.key).replace(O,"$&/")+"/")+e)),t.push(a)),1;if(l=0,i=""===i?".":i+":",S(e))for(var u=0;u<e.length;u++){var g=i+R(s=e[u],u);l+=N(s,t,o,g,a)}else if(g=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=d&&e[d]||e["@@iterator"])?e:null}(e),"function"==typeof g)for(e=g.call(e),u=0;!(s=e.next()).done;)l+=N(s=s.value,t,o,g=i+R(s,u++),a);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function C(e,t,r){if(null==e)return e;var n=[],o=0;return N(e,n,"","",(function(e){return t.call(r,e,o++)})),n}function I(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var P={current:null},x={transition:null},M={ReactCurrentDispatcher:P,ReactCurrentBatchConfig:x,ReactCurrentOwner:T};function U(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:C,forEach:function(e,t,r){C(e,(function(){t.apply(this,arguments)}),r)},count:function(e){var t=0;return C(e,(function(){t++})),t},toArray:function(e){return C(e,(function(e){return e}))||[]},only:function(e){if(!j(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=o,t.Profiler=a,t.PureComponent=_,t.StrictMode=i,t.Suspense=g,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M,t.act=U,t.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=h({},e.props),i=e.key,a=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,s=T.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)E.call(t,u)&&!A.hasOwnProperty(u)&&(o[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)o.children=n;else if(1<u){l=Array(u);for(var g=0;g<u;g++)l[g]=arguments[g+2];o.children=l}return{$$typeof:r,type:e.type,key:i,ref:a,props:o,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=w,t.createFactory=function(e){var t=w.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=j,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:I}},t.memo=function(e,t){return{$$typeof:c,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=x.transition;x.transition={};try{e()}finally{x.transition=t}},t.unstable_act=U,t.useCallback=function(e,t){return P.current.useCallback(e,t)},t.useContext=function(e){return P.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return P.current.useDeferredValue(e)},t.useEffect=function(e,t){return P.current.useEffect(e,t)},t.useId=function(){return P.current.useId()},t.useImperativeHandle=function(e,t,r){return P.current.useImperativeHandle(e,t,r)},t.useInsertionEffect=function(e,t){return P.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return P.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return P.current.useMemo(e,t)},t.useReducer=function(e,t,r){return P.current.useReducer(e,t,r)},t.useRef=function(e){return P.current.useRef(e)},t.useState=function(e){return P.current.useState(e)},t.useSyncExternalStore=function(e,t,r){return P.current.useSyncExternalStore(e,t,r)},t.useTransition=function(){return P.current.useTransition()},t.version="18.3.1"},626:(e,t,r)=>{"use strict";e.exports=r(9069)},2621:(e,t)=>{"use strict";function r(e,t){var r=e.length;e.push(t);e:for(;0<r;){var n=r-1>>>1,o=e[n];if(!(0<i(o,t)))break e;e[n]=t,e[r]=o,r=n}}function n(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],r=e.pop();if(r!==t){e[0]=r;e:for(var n=0,o=e.length,a=o>>>1;n<a;){var s=2*(n+1)-1,l=e[s],u=s+1,g=e[u];if(0>i(l,r))u<o&&0>i(g,l)?(e[n]=g,e[u]=r,n=u):(e[n]=l,e[s]=r,n=s);else{if(!(u<o&&0>i(g,r)))break e;e[n]=g,e[u]=r,n=u}}}return t}function i(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var a=performance;t.unstable_now=function(){return a.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],g=[],c=1,p=null,d=3,f=!1,h=!1,y=!1,b="function"==typeof setTimeout?setTimeout:null,m="function"==typeof clearTimeout?clearTimeout:null,_="undefined"!=typeof setImmediate?setImmediate:null;function v(e){for(var t=n(g);null!==t;){if(null===t.callback)o(g);else{if(!(t.startTime<=e))break;o(g),t.sortIndex=t.expirationTime,r(u,t)}t=n(g)}}function S(e){if(y=!1,v(e),!h)if(null!==n(u))h=!0,x(E);else{var t=n(g);null!==t&&M(S,t.startTime-e)}}function E(e,r){h=!1,y&&(y=!1,m(j),j=-1),f=!0;var i=d;try{for(v(r),p=n(u);null!==p&&(!(p.expirationTime>r)||e&&!N());){var a=p.callback;if("function"==typeof a){p.callback=null,d=p.priorityLevel;var s=a(p.expirationTime<=r);r=t.unstable_now(),"function"==typeof s?p.callback=s:p===n(u)&&o(u),v(r)}else o(u);p=n(u)}if(null!==p)var l=!0;else{var c=n(g);null!==c&&M(S,c.startTime-r),l=!1}return l}finally{p=null,d=i,f=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var T,A=!1,w=null,j=-1,O=5,R=-1;function N(){return!(t.unstable_now()-R<O)}function C(){if(null!==w){var e=t.unstable_now();R=e;var r=!0;try{r=w(!0,e)}finally{r?T():(A=!1,w=null)}}else A=!1}if("function"==typeof _)T=function(){_(C)};else if("undefined"!=typeof MessageChannel){var I=new MessageChannel,P=I.port2;I.port1.onmessage=C,T=function(){P.postMessage(null)}}else T=function(){b(C,0)};function x(e){w=e,A||(A=!0,T())}function M(e,r){j=b((function(){e(t.unstable_now())}),r)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||f||(h=!0,x(E))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return d},t.unstable_getFirstCallbackNode=function(){return n(u)},t.unstable_next=function(e){switch(d){case 1:case 2:case 3:var t=3;break;default:t=d}var r=d;d=t;try{return e()}finally{d=r}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=d;d=e;try{return t()}finally{d=r}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch("object"==typeof i&&null!==i?i="number"==typeof(i=i.delay)&&0<i?a+i:a:i=a,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:c++,callback:o,priorityLevel:e,startTime:i,expirationTime:s=i+s,sortIndex:-1},i>a?(e.sortIndex=i,r(g,e),null===n(u)&&e===n(g)&&(y?(m(j),j=-1):y=!0,M(S,i-a))):(e.sortIndex=s,r(u,e),h||f||(h=!0,x(E))),e},t.unstable_shouldYield=N,t.unstable_wrapCallback=function(e){var t=d;return function(){var r=d;d=t;try{return e.apply(this,arguments)}finally{d=r}}}},1016:(e,t,r)=>{"use strict";e.exports=r(2621)},2109:(e,t,r)=>{"use strict";var n=r(626);var o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,a=n.useEffect,s=n.useLayoutEffect,l=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var g="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),o=n[0].inst,g=n[1];return s((function(){o.value=r,o.getSnapshot=t,u(o)&&g({inst:o})}),[e,r,t]),a((function(){return u(o)&&g({inst:o}),e((function(){u(o)&&g({inst:o})}))}),[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:g},1702:(e,t,r)=>{"use strict";e.exports=r(2109)},3175:(e,t,r)=>{"use strict";r.d(t,{EW:()=>Ie,Fq:()=>jr,Gn:()=>pr,O8:()=>et,OB:()=>Hr,XI:()=>It,jK:()=>zt,qT:()=>bt,sH:()=>Oe,uz:()=>xr,vx:()=>ke,yl:()=>Yt});function n(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];throw new Error("number"==typeof e?"[MobX] minified error nr: "+e+(r.length?" "+r.map(String).join(","):"")+". Find the full error at: https://github.com/mobxjs/mobx/blob/main/packages/mobx/src/errors.ts":"[MobX] "+e)}var o={};function i(){return"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self?self:o}var a=Object.assign,s=Object.getOwnPropertyDescriptor,l=Object.defineProperty,u=Object.prototype,g=[];Object.freeze(g);var c={};Object.freeze(c);var p="undefined"!=typeof Proxy,d=Object.toString();function f(){p||n("Proxy not available")}function h(e){var t=!1;return function(){if(!t)return t=!0,e.apply(this,arguments)}}var y=function(){};function b(e){return"function"==typeof e}function m(e){switch(typeof e){case"string":case"symbol":case"number":return!0}return!1}function _(e){return null!==e&&"object"==typeof e}function v(e){if(!_(e))return!1;var t=Object.getPrototypeOf(e);if(null==t)return!0;var r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r.toString()===d}function S(e){var t=null==e?void 0:e.constructor;return!!t&&("GeneratorFunction"===t.name||"GeneratorFunction"===t.displayName)}function E(e,t,r){l(e,t,{enumerable:!1,writable:!0,configurable:!0,value:r})}function T(e,t,r){l(e,t,{enumerable:!1,writable:!1,configurable:!0,value:r})}function A(e,t){var r="isMobX"+e;return t.prototype[r]=!0,function(e){return _(e)&&!0===e[r]}}function w(e){return e instanceof Map}function j(e){return e instanceof Set}var O=void 0!==Object.getOwnPropertySymbols;var R="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:O?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames;function N(e){return null===e?null:"object"==typeof e?""+e:e}function C(e,t){return u.hasOwnProperty.call(e,t)}var I=Object.getOwnPropertyDescriptors||function(e){var t={};return R(e).forEach((function(r){t[r]=s(e,r)})),t};function P(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(o=n.key,i=void 0,"symbol"==typeof(i=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"))?i:String(i)),n)}var o,i}function x(e,t,r){return t&&P(e.prototype,t),r&&P(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},M.apply(this,arguments)}function U(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,L(e,t)}function L(e,t){return L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},L(e,t)}function B(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function k(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(r)return(r=r.call(e)).next.bind(r);if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return D(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?D(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var F=Symbol("mobx-stored-annotations");function W(e){return Object.assign((function(t,r){V(t,r,e)}),e)}function V(e,t,r){C(e,F)||E(e,F,M({},e[F])),function(e){return e.annotationType_===X}(r)||(e[F][t]=r)}var H=Symbol("mobx administration"),z=function(){function e(e){void 0===e&&(e="Atom"),this.name_=void 0,this.isPendingUnobservation_=!1,this.isBeingObserved_=!1,this.observers_=new Set,this.batchId_=void 0,this.diffValue_=0,this.lastAccessedBy_=0,this.lowestObserverState_=ze.NOT_TRACKING_,this.onBOL=void 0,this.onBUOL=void 0,this.name_=e,this.batchId_=ut.inBatch?ut.batchId:NaN}var t=e.prototype;return t.onBO=function(){this.onBOL&&this.onBOL.forEach((function(e){return e()}))},t.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(e){return e()}))},t.reportObserved=function(){return ht(this)},t.reportChanged=function(){ut.inBatch&&this.batchId_===ut.batchId||(ut.stateVersion=ut.stateVersion<Number.MAX_SAFE_INTEGER?ut.stateVersion+1:Number.MIN_SAFE_INTEGER,this.batchId_=NaN),dt(),yt(this),ft()},t.toString=function(){return this.name_},e}(),G=A("Atom",z);function Y(e,t,r){void 0===t&&(t=y),void 0===r&&(r=y);var n,o=new z(e);return t!==y&&Ft(Bt,o,t,n),r!==y&&kt(o,r),o}var K={identity:function(e,t){return e===t},structural:function(e,t){return un(e,t)},default:function(e,t){return Object.is?Object.is(e,t):e===t?0!==e||1/e==1/t:e!=e&&t!=t},shallow:function(e,t){return un(e,t,1)}};function $(e,t,r){return rr(e)?e:Array.isArray(e)?Oe.array(e,{name:r}):v(e)?Oe.object(e,void 0,{name:r}):w(e)?Oe.map(e,{name:r}):j(e)?Oe.set(e,{name:r}):"function"!=typeof e||xt(e)||er(e)?e:S(e)?Qt(e):Pt(r,e)}function q(e){return e}var X="override";function Z(e,t){return{annotationType_:e,options_:t,make_:Q,extend_:J}}function Q(e,t,r,n){var o;if(null!=(o=this.options_)&&o.bound)return null===this.extend_(e,t,r,!1)?0:1;if(n===e.target_)return null===this.extend_(e,t,r,!1)?0:2;if(xt(r.value))return 1;var i=ee(e,this,t,r,!1);return l(n,t,i),2}function J(e,t,r,n){var o=ee(e,this,t,r);return e.defineProperty_(t,o,n)}function ee(e,t,r,n,o){var i,a,s,l,u,g,c,p;void 0===o&&(o=ut.safeDescriptors),p=n,t.annotationType_,p.value;var d,f=n.value;null!=(i=t.options_)&&i.bound&&(f=f.bind(null!=(d=e.proxy_)?d:e.target_));return{value:Be(null!=(a=null==(s=t.options_)?void 0:s.name)?a:r.toString(),f,null!=(l=null==(u=t.options_)?void 0:u.autoAction)&&l,null!=(g=t.options_)&&g.bound?null!=(c=e.proxy_)?c:e.target_:void 0),configurable:!o||e.isPlainObject_,enumerable:!1,writable:!o}}function te(e,t){return{annotationType_:e,options_:t,make_:re,extend_:ne}}function re(e,t,r,n){var o;if(n===e.target_)return null===this.extend_(e,t,r,!1)?0:2;if(null!=(o=this.options_)&&o.bound&&(!C(e.target_,t)||!er(e.target_[t]))&&null===this.extend_(e,t,r,!1))return 0;if(er(r.value))return 1;var i=oe(e,this,t,r,!1,!1);return l(n,t,i),2}function ne(e,t,r,n){var o,i=oe(e,this,t,r,null==(o=this.options_)?void 0:o.bound);return e.defineProperty_(t,i,n)}function oe(e,t,r,n,o,i){var a;void 0===i&&(i=ut.safeDescriptors),a=n,t.annotationType_,a.value;var s,l=n.value;(er(l)||(l=Qt(l)),o)&&((l=l.bind(null!=(s=e.proxy_)?s:e.target_)).isMobXFlow=!0);return{value:l,configurable:!i||e.isPlainObject_,enumerable:!1,writable:!i}}function ie(e,t){return{annotationType_:e,options_:t,make_:ae,extend_:se}}function ae(e,t,r){return null===this.extend_(e,t,r,!1)?0:1}function se(e,t,r,n){return function(e,t,r,n){t.annotationType_,n.get;0}(0,this,0,r),e.defineComputedProperty_(t,M({},this.options_,{get:r.get,set:r.set}),n)}function le(e,t){return{annotationType_:e,options_:t,make_:ue,extend_:ge}}function ue(e,t,r){return null===this.extend_(e,t,r,!1)?0:1}function ge(e,t,r,n){var o,i;return function(e,t,r,n){t.annotationType_;0}(0,this),e.defineObservableProperty_(t,r.value,null!=(o=null==(i=this.options_)?void 0:i.enhancer)?o:$,n)}var ce="true",pe=de();function de(e){return{annotationType_:ce,options_:e,make_:fe,extend_:he}}function fe(e,t,r,n){var o,i,a,s;if(r.get)return Ie.make_(e,t,r,n);if(r.set){var u=Be(t.toString(),r.set);return n===e.target_?null===e.defineProperty_(t,{configurable:!ut.safeDescriptors||e.isPlainObject_,set:u})?0:2:(l(n,t,{configurable:!0,set:u}),2)}if(n!==e.target_&&"function"==typeof r.value)return S(r.value)?(null!=(s=this.options_)&&s.autoBind?Qt.bound:Qt).make_(e,t,r,n):(null!=(a=this.options_)&&a.autoBind?Pt.bound:Pt).make_(e,t,r,n);var g,c=!1===(null==(o=this.options_)?void 0:o.deep)?Oe.ref:Oe;"function"==typeof r.value&&null!=(i=this.options_)&&i.autoBind&&(r.value=r.value.bind(null!=(g=e.proxy_)?g:e.target_));return c.make_(e,t,r,n)}function he(e,t,r,n){var o,i,a;if(r.get)return Ie.extend_(e,t,r,n);if(r.set)return e.defineProperty_(t,{configurable:!ut.safeDescriptors||e.isPlainObject_,set:Be(t.toString(),r.set)},n);"function"==typeof r.value&&null!=(o=this.options_)&&o.autoBind&&(r.value=r.value.bind(null!=(a=e.proxy_)?a:e.target_));return(!1===(null==(i=this.options_)?void 0:i.deep)?Oe.ref:Oe).extend_(e,t,r,n)}var ye={deep:!0,name:void 0,defaultDecorator:void 0,proxy:!0};function be(e){return e||ye}Object.freeze(ye);var me=le("observable"),_e=le("observable.ref",{enhancer:q}),ve=le("observable.shallow",{enhancer:function(e,t,r){return null==e||Hr(e)||jr(e)||xr(e)||Lr(e)?e:Array.isArray(e)?Oe.array(e,{name:r,deep:!1}):v(e)?Oe.object(e,void 0,{name:r,deep:!1}):w(e)?Oe.map(e,{name:r,deep:!1}):j(e)?Oe.set(e,{name:r,deep:!1}):void 0}}),Se=le("observable.struct",{enhancer:function(e,t){return un(e,t)?t:e}}),Ee=W(me);function Te(e){return!0===e.deep?$:!1===e.deep?q:(t=e.defaultDecorator)&&null!=(r=null==(n=t.options_)?void 0:n.enhancer)?r:$;var t,r,n}function Ae(e,t,r){if(!m(t))return rr(e)?e:v(e)?Oe.object(e,t,r):Array.isArray(e)?Oe.array(e,t):w(e)?Oe.map(e,t):j(e)?Oe.set(e,t):"object"==typeof e&&null!==e?e:Oe.box(e,t);V(e,t,me)}a(Ae,Ee);var we,je,Oe=a(Ae,{box:function(e,t){var r=be(t);return new He(e,Te(r),r.name,!0,r.equals)},array:function(e,t){var r=be(t);return(!1===ut.useProxies||!1===r.proxy?rn:br)(e,Te(r),r.name)},map:function(e,t){var r=be(t);return new Pr(e,Te(r),r.name)},set:function(e,t){var r=be(t);return new Ur(e,Te(r),r.name)},object:function(e,t,r){return sn((function(){return Gt(!1===ut.useProxies||!1===(null==r?void 0:r.proxy)?Fr({},r):function(e,t){var r,n;return f(),e=Fr(e,t),null!=(n=(r=e[H]).proxy_)?n:r.proxy_=new Proxy(e,ir)}({},r),e,t)}))},ref:W(_e),shallow:W(ve),deep:Ee,struct:W(Se)}),Re="computed",Ne=ie(Re),Ce=ie("computed.struct",{equals:K.structural}),Ie=function(e,t){if(m(t))return V(e,t,Ne);if(v(e))return W(ie(Re,e));var r=v(t)?t:{};return r.get=e,r.name||(r.name=e.name||""),new Ye(r)};Object.assign(Ie,Ne),Ie.struct=W(Ce);var Pe,xe=0,Me=1,Ue=null!=(we=null==(je=s((function(){}),"name"))?void 0:je.configurable)&&we,Le={value:"action",configurable:!0,writable:!1,enumerable:!1};function Be(e,t,r,n){function o(){return De(e,r,t,n||this,arguments)}return void 0===r&&(r=!1),o.isMobxAction=!0,Ue&&(Le.value=e,l(o,"name",Le)),o}function De(e,t,r,o,i){var a=function(e,t,r,n){var o=!1,i=0;0;var a=ut.trackingDerivation,s=!t||!a;dt();var l=ut.allowStateChanges;s&&(tt(),l=Fe(!0));var u=nt(!0),g={runAsAction_:s,prevDerivation_:a,prevAllowStateChanges_:l,prevAllowStateReads_:u,notifySpy_:o,startTime_:i,actionId_:Me++,parentActionId_:xe};return xe=g.actionId_,g}(0,t);try{return r.apply(o,i)}catch(e){throw a.error_=e,e}finally{!function(e){xe!==e.actionId_&&n(30);xe=e.parentActionId_,void 0!==e.error_&&(ut.suppressReactionErrors=!0);We(e.prevAllowStateChanges_),ot(e.prevAllowStateReads_),ft(),e.runAsAction_&&rt(e.prevDerivation_);0;ut.suppressReactionErrors=!1}(a)}}function ke(e,t){var r=Fe(e);try{return t()}finally{We(r)}}function Fe(e){var t=ut.allowStateChanges;return ut.allowStateChanges=e,t}function We(e){ut.allowStateChanges=e}Pe=Symbol.toPrimitive;var Ve,He=function(e){function t(t,r,n,o,i){var a;return void 0===n&&(n="ObservableValue"),void 0===o&&(o=!0),void 0===i&&(i=K.default),(a=e.call(this,n)||this).enhancer=void 0,a.name_=void 0,a.equals=void 0,a.hasUnreportedChange_=!1,a.interceptors_=void 0,a.changeListeners_=void 0,a.value_=void 0,a.dehancer=void 0,a.enhancer=r,a.name_=n,a.equals=i,a.value_=r(t,void 0,n),a}U(t,e);var r=t.prototype;return r.dehanceValue=function(e){return void 0!==this.dehancer?this.dehancer(e):e},r.set=function(e){this.value_;if((e=this.prepareNewValue_(e))!==ut.UNCHANGED){0,this.setNewValue_(e)}},r.prepareNewValue_=function(e){if(Ze(this),ar(this)){var t=lr(this,{object:this,type:fr,newValue:e});if(!t)return ut.UNCHANGED;e=t.newValue}return e=this.enhancer(e,this.value_,this.name_),this.equals(this.value_,e)?ut.UNCHANGED:e},r.setNewValue_=function(e){var t=this.value_;this.value_=e,this.reportChanged(),ur(this)&&cr(this,{type:fr,object:this,newValue:e,oldValue:t})},r.get=function(){return this.reportObserved(),this.dehanceValue(this.value_)},r.intercept_=function(e){return sr(this,e)},r.observe_=function(e,t){return t&&e({observableKind:"value",debugObjectName:this.name_,object:this,type:fr,newValue:this.value_,oldValue:void 0}),gr(this,e)},r.raw=function(){return this.value_},r.toJSON=function(){return this.get()},r.toString=function(){return this.name_+"["+this.value_+"]"},r.valueOf=function(){return N(this.get())},r[Pe]=function(){return this.valueOf()},t}(z);Ve=Symbol.toPrimitive;var ze,Ge,Ye=function(){function e(e){this.dependenciesState_=ze.NOT_TRACKING_,this.observing_=[],this.newObserving_=null,this.isBeingObserved_=!1,this.isPendingUnobservation_=!1,this.observers_=new Set,this.diffValue_=0,this.runId_=0,this.lastAccessedBy_=0,this.lowestObserverState_=ze.UP_TO_DATE_,this.unboundDepsCount_=0,this.value_=new $e(null),this.name_=void 0,this.triggeredBy_=void 0,this.isComputing_=!1,this.isRunningSetter_=!1,this.derivation=void 0,this.setter_=void 0,this.isTracing_=Ge.NONE,this.scope_=void 0,this.equals_=void 0,this.requiresReaction_=void 0,this.keepAlive_=void 0,this.onBOL=void 0,this.onBUOL=void 0,e.get||n(31),this.derivation=e.get,this.name_=e.name||"ComputedValue",e.set&&(this.setter_=Be("ComputedValue-setter",e.set)),this.equals_=e.equals||(e.compareStructural||e.struct?K.structural:K.default),this.scope_=e.context,this.requiresReaction_=e.requiresReaction,this.keepAlive_=!!e.keepAlive}var t=e.prototype;return t.onBecomeStale_=function(){!function(e){if(e.lowestObserverState_!==ze.UP_TO_DATE_)return;e.lowestObserverState_=ze.POSSIBLY_STALE_,e.observers_.forEach((function(e){e.dependenciesState_===ze.UP_TO_DATE_&&(e.dependenciesState_=ze.POSSIBLY_STALE_,e.onBecomeStale_())}))}(this)},t.onBO=function(){this.onBOL&&this.onBOL.forEach((function(e){return e()}))},t.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(e){return e()}))},t.get=function(){if(this.isComputing_&&n(32,this.name_,this.derivation),0!==ut.inBatch||0!==this.observers_.size||this.keepAlive_){if(ht(this),Xe(this)){var e=ut.trackingContext;this.keepAlive_&&!e&&(ut.trackingContext=this),this.trackAndCompute()&&function(e){if(e.lowestObserverState_===ze.STALE_)return;e.lowestObserverState_=ze.STALE_,e.observers_.forEach((function(t){t.dependenciesState_===ze.POSSIBLY_STALE_?t.dependenciesState_=ze.STALE_:t.dependenciesState_===ze.UP_TO_DATE_&&(e.lowestObserverState_=ze.UP_TO_DATE_)}))}(this),ut.trackingContext=e}}else Xe(this)&&(this.warnAboutUntrackedRead_(),dt(),this.value_=this.computeValue_(!1),ft());var t=this.value_;if(qe(t))throw t.cause;return t},t.set=function(e){if(this.setter_){this.isRunningSetter_&&n(33,this.name_),this.isRunningSetter_=!0;try{this.setter_.call(this.scope_,e)}finally{this.isRunningSetter_=!1}}else n(34,this.name_)},t.trackAndCompute=function(){var e=this.value_,t=this.dependenciesState_===ze.NOT_TRACKING_,r=this.computeValue_(!0),n=t||qe(e)||qe(r)||!this.equals_(e,r);return n&&(this.value_=r),n},t.computeValue_=function(e){this.isComputing_=!0;var t,r=Fe(!1);if(e)t=Qe(this,this.derivation,this.scope_);else if(!0===ut.disableErrorBoundaries)t=this.derivation.call(this.scope_);else try{t=this.derivation.call(this.scope_)}catch(e){t=new $e(e)}return We(r),this.isComputing_=!1,t},t.suspend_=function(){this.keepAlive_||(Je(this),this.value_=void 0)},t.observe_=function(e,t){var r=this,n=!0,o=void 0;return Mt((function(){var i=r.get();if(!n||t){var a=tt();e({observableKind:"computed",debugObjectName:r.name_,type:fr,object:r,newValue:i,oldValue:o}),rt(a)}n=!1,o=i}))},t.warnAboutUntrackedRead_=function(){},t.toString=function(){return this.name_+"["+this.derivation.toString()+"]"},t.valueOf=function(){return N(this.get())},t[Ve]=function(){return this.valueOf()},e}(),Ke=A("ComputedValue",Ye);!function(e){e[e.NOT_TRACKING_=-1]="NOT_TRACKING_",e[e.UP_TO_DATE_=0]="UP_TO_DATE_",e[e.POSSIBLY_STALE_=1]="POSSIBLY_STALE_",e[e.STALE_=2]="STALE_"}(ze||(ze={})),function(e){e[e.NONE=0]="NONE",e[e.LOG=1]="LOG",e[e.BREAK=2]="BREAK"}(Ge||(Ge={}));var $e=function(e){this.cause=void 0,this.cause=e};function qe(e){return e instanceof $e}function Xe(e){switch(e.dependenciesState_){case ze.UP_TO_DATE_:return!1;case ze.NOT_TRACKING_:case ze.STALE_:return!0;case ze.POSSIBLY_STALE_:for(var t=nt(!0),r=tt(),n=e.observing_,o=n.length,i=0;i<o;i++){var a=n[i];if(Ke(a)){if(ut.disableErrorBoundaries)a.get();else try{a.get()}catch(e){return rt(r),ot(t),!0}if(e.dependenciesState_===ze.STALE_)return rt(r),ot(t),!0}}return it(e),rt(r),ot(t),!1}}function Ze(e){}function Qe(e,t,r){var n=nt(!0);it(e),e.newObserving_=new Array(e.observing_.length+100),e.unboundDepsCount_=0,e.runId_=++ut.runId;var o,i=ut.trackingDerivation;if(ut.trackingDerivation=e,ut.inBatch++,!0===ut.disableErrorBoundaries)o=t.call(r);else try{o=t.call(r)}catch(e){o=new $e(e)}return ut.inBatch--,ut.trackingDerivation=i,function(e){for(var t=e.observing_,r=e.observing_=e.newObserving_,n=ze.UP_TO_DATE_,o=0,i=e.unboundDepsCount_,a=0;a<i;a++){var s=r[a];0===s.diffValue_&&(s.diffValue_=1,o!==a&&(r[o]=s),o++),s.dependenciesState_>n&&(n=s.dependenciesState_)}r.length=o,e.newObserving_=null,i=t.length;for(;i--;){var l=t[i];0===l.diffValue_&&ct(l,e),l.diffValue_=0}for(;o--;){var u=r[o];1===u.diffValue_&&(u.diffValue_=0,gt(u,e))}n!==ze.UP_TO_DATE_&&(e.dependenciesState_=n,e.onBecomeStale_())}(e),ot(n),o}function Je(e){var t=e.observing_;e.observing_=[];for(var r=t.length;r--;)ct(t[r],e);e.dependenciesState_=ze.NOT_TRACKING_}function et(e){var t=tt();try{return e()}finally{rt(t)}}function tt(){var e=ut.trackingDerivation;return ut.trackingDerivation=null,e}function rt(e){ut.trackingDerivation=e}function nt(e){var t=ut.allowStateReads;return ut.allowStateReads=e,t}function ot(e){ut.allowStateReads=e}function it(e){if(e.dependenciesState_!==ze.UP_TO_DATE_){e.dependenciesState_=ze.UP_TO_DATE_;for(var t=e.observing_,r=t.length;r--;)t[r].lowestObserverState_=ze.UP_TO_DATE_}}var at=function(){this.version=6,this.UNCHANGED={},this.trackingDerivation=null,this.trackingContext=null,this.runId=0,this.mobxGuid=0,this.inBatch=0,this.batchId=Number.MIN_SAFE_INTEGER,this.pendingUnobservations=[],this.pendingReactions=[],this.isRunningReactions=!1,this.allowStateChanges=!1,this.allowStateReads=!0,this.enforceActions=!0,this.spyListeners=[],this.globalReactionErrorHandlers=[],this.computedRequiresReaction=!1,this.reactionRequiresObservable=!1,this.observableRequiresReaction=!1,this.disableErrorBoundaries=!1,this.suppressReactionErrors=!1,this.useProxies=!0,this.verifyProxies=!1,this.safeDescriptors=!0,this.stateVersion=Number.MIN_SAFE_INTEGER},st=!0,lt=!1,ut=function(){var e=i();return e.__mobxInstanceCount>0&&!e.__mobxGlobals&&(st=!1),e.__mobxGlobals&&e.__mobxGlobals.version!==(new at).version&&(st=!1),st?e.__mobxGlobals?(e.__mobxInstanceCount+=1,e.__mobxGlobals.UNCHANGED||(e.__mobxGlobals.UNCHANGED={}),e.__mobxGlobals):(e.__mobxInstanceCount=1,e.__mobxGlobals=new at):(setTimeout((function(){lt||n(35)}),1),new at)}();function gt(e,t){e.observers_.add(t),e.lowestObserverState_>t.dependenciesState_&&(e.lowestObserverState_=t.dependenciesState_)}function ct(e,t){e.observers_.delete(t),0===e.observers_.size&&pt(e)}function pt(e){!1===e.isPendingUnobservation_&&(e.isPendingUnobservation_=!0,ut.pendingUnobservations.push(e))}function dt(){0===ut.inBatch&&(ut.batchId=ut.batchId<Number.MAX_SAFE_INTEGER?ut.batchId+1:Number.MIN_SAFE_INTEGER),ut.inBatch++}function ft(){if(0==--ut.inBatch){vt();for(var e=ut.pendingUnobservations,t=0;t<e.length;t++){var r=e[t];r.isPendingUnobservation_=!1,0===r.observers_.size&&(r.isBeingObserved_&&(r.isBeingObserved_=!1,r.onBUO()),r instanceof Ye&&r.suspend_())}ut.pendingUnobservations=[]}}function ht(e){var t=ut.trackingDerivation;return null!==t?(t.runId_!==e.lastAccessedBy_&&(e.lastAccessedBy_=t.runId_,t.newObserving_[t.unboundDepsCount_++]=e,!e.isBeingObserved_&&ut.trackingContext&&(e.isBeingObserved_=!0,e.onBO())),e.isBeingObserved_):(0===e.observers_.size&&ut.inBatch>0&&pt(e),!1)}function yt(e){e.lowestObserverState_!==ze.STALE_&&(e.lowestObserverState_=ze.STALE_,e.observers_.forEach((function(e){e.dependenciesState_===ze.UP_TO_DATE_&&e.onBecomeStale_(),e.dependenciesState_=ze.STALE_})))}var bt=function(){function e(e,t,r,n){void 0===e&&(e="Reaction"),this.name_=void 0,this.onInvalidate_=void 0,this.errorHandler_=void 0,this.requiresObservable_=void 0,this.observing_=[],this.newObserving_=[],this.dependenciesState_=ze.NOT_TRACKING_,this.diffValue_=0,this.runId_=0,this.unboundDepsCount_=0,this.isDisposed_=!1,this.isScheduled_=!1,this.isTrackPending_=!1,this.isRunning_=!1,this.isTracing_=Ge.NONE,this.name_=e,this.onInvalidate_=t,this.errorHandler_=r,this.requiresObservable_=n}var t=e.prototype;return t.onBecomeStale_=function(){this.schedule_()},t.schedule_=function(){this.isScheduled_||(this.isScheduled_=!0,ut.pendingReactions.push(this),vt())},t.isScheduled=function(){return this.isScheduled_},t.runReaction_=function(){if(!this.isDisposed_){dt(),this.isScheduled_=!1;var e=ut.trackingContext;if(ut.trackingContext=this,Xe(this)){this.isTrackPending_=!0;try{this.onInvalidate_()}catch(e){this.reportExceptionInDerivation_(e)}}ut.trackingContext=e,ft()}},t.track=function(e){if(!this.isDisposed_){dt();0,this.isRunning_=!0;var t=ut.trackingContext;ut.trackingContext=this;var r=Qe(this,e,void 0);ut.trackingContext=t,this.isRunning_=!1,this.isTrackPending_=!1,this.isDisposed_&&Je(this),qe(r)&&this.reportExceptionInDerivation_(r.cause),ft()}},t.reportExceptionInDerivation_=function(e){var t=this;if(this.errorHandler_)this.errorHandler_(e,this);else{if(ut.disableErrorBoundaries)throw e;var r="[mobx] uncaught error in '"+this+"'";ut.suppressReactionErrors||console.error(r,e),ut.globalReactionErrorHandlers.forEach((function(r){return r(e,t)}))}},t.dispose=function(){this.isDisposed_||(this.isDisposed_=!0,this.isRunning_||(dt(),Je(this),ft()))},t.getDisposer_=function(e){var t=this,r=function r(){t.dispose(),null==e||null==e.removeEventListener||e.removeEventListener("abort",r)};return null==e||null==e.addEventListener||e.addEventListener("abort",r),r[H]=this,r},t.toString=function(){return"Reaction["+this.name_+"]"},t.trace=function(e){void 0===e&&(e=!1)},e}();var mt=100,_t=function(e){return e()};function vt(){ut.inBatch>0||ut.isRunningReactions||_t(St)}function St(){ut.isRunningReactions=!0;for(var e=ut.pendingReactions,t=0;e.length>0;){++t===mt&&(console.error("[mobx] cycle in reaction: "+e[0]),e.splice(0));for(var r=e.splice(0),n=0,o=r.length;n<o;n++)r[n].runReaction_()}ut.isRunningReactions=!1}var Et=A("Reaction",bt);var Tt="action",At="autoAction",wt="<unnamed action>",jt=Z(Tt),Ot=Z("action.bound",{bound:!0}),Rt=Z(At,{autoAction:!0}),Nt=Z("autoAction.bound",{autoAction:!0,bound:!0});function Ct(e){return function(t,r){return b(t)?Be(t.name||wt,t,e):b(r)?Be(t,r,e):m(r)?V(t,r,e?Rt:jt):m(t)?W(Z(e?At:Tt,{name:t,autoAction:e})):void 0}}var It=Ct(!1);Object.assign(It,jt);var Pt=Ct(!0);function xt(e){return b(e)&&!0===e.isMobxAction}function Mt(e,t){var r,n,o,i,a;void 0===t&&(t=c);var s,l=null!=(r=null==(n=t)?void 0:n.name)?r:"Autorun";if(!t.scheduler&&!t.delay)s=new bt(l,(function(){this.track(p)}),t.onError,t.requiresObservable);else{var u=Lt(t),g=!1;s=new bt(l,(function(){g||(g=!0,u((function(){g=!1,s.isDisposed_||s.track(p)})))}),t.onError,t.requiresObservable)}function p(){e(s)}return null!=(o=t)&&null!=(i=o.signal)&&i.aborted||s.schedule_(),s.getDisposer_(null==(a=t)?void 0:a.signal)}Object.assign(Pt,Rt),It.bound=W(Ot),Pt.bound=W(Nt);var Ut=function(e){return e()};function Lt(e){return e.scheduler?e.scheduler:e.delay?function(t){return setTimeout(t,e.delay)}:Ut}var Bt="onBO",Dt="onBUO";function kt(e,t,r){return Ft(Dt,e,t,r)}function Ft(e,t,r,n){var o="function"==typeof n?nn(t,r):nn(t),i=b(n)?n:r,a=e+"L";return o[a]?o[a].add(i):o[a]=new Set([i]),function(){var e=o[a];e&&(e.delete(i),0===e.size&&delete o[a])}}var Wt="never",Vt="always",Ht="observed";function zt(e){!0===e.isolateGlobalState&&function(){if((ut.pendingReactions.length||ut.inBatch||ut.isRunningReactions)&&n(36),lt=!0,st){var e=i();0==--e.__mobxInstanceCount&&(e.__mobxGlobals=void 0),ut=new at}}();var t,r,o=e.useProxies,a=e.enforceActions;if(void 0!==o&&(ut.useProxies=o===Vt||o!==Wt&&"undefined"!=typeof Proxy),"ifavailable"===o&&(ut.verifyProxies=!0),void 0!==a){var s=a===Vt?Vt:a===Ht;ut.enforceActions=s,ut.allowStateChanges=!0!==s&&s!==Vt}["computedRequiresReaction","reactionRequiresObservable","observableRequiresReaction","disableErrorBoundaries","safeDescriptors"].forEach((function(t){t in e&&(ut[t]=!!e[t])})),ut.allowStateReads=!ut.observableRequiresReaction,e.reactionScheduler&&(t=e.reactionScheduler,r=_t,_t=function(e){return t((function(){return r(e)}))})}function Gt(e,t,r,n){var o=I(t);return sn((function(){var t=Fr(e,n)[H];R(o).forEach((function(e){t.extend_(e,o[e],!r||(!(e in r)||r[e]))}))})),e}function Yt(e,t){return Kt(nn(e,t))}function Kt(e){var t,r={name:e.name_};return e.observing_&&e.observing_.length>0&&(r.dependencies=(t=e.observing_,Array.from(new Set(t))).map(Kt)),r}var $t=0;function qt(){this.message="FLOW_CANCELLED"}qt.prototype=Object.create(Error.prototype);var Xt=te("flow"),Zt=te("flow.bound",{bound:!0}),Qt=Object.assign((function(e,t){if(m(t))return V(e,t,Xt);var r=e,n=r.name||"<unnamed flow>",o=function(){var e,t=arguments,o=++$t,i=It(n+" - runid: "+o+" - init",r).apply(this,t),a=void 0,s=new Promise((function(t,r){var s=0;function l(e){var t;a=void 0;try{t=It(n+" - runid: "+o+" - yield "+s++,i.next).call(i,e)}catch(e){return r(e)}g(t)}function u(e){var t;a=void 0;try{t=It(n+" - runid: "+o+" - yield "+s++,i.throw).call(i,e)}catch(e){return r(e)}g(t)}function g(e){if(!b(null==e?void 0:e.then))return e.done?t(e.value):(a=Promise.resolve(e.value)).then(l,u);e.then(g,r)}e=r,l(void 0)}));return s.cancel=It(n+" - runid: "+o+" - cancel",(function(){try{a&&Jt(a);var t=i.return(void 0),r=Promise.resolve(t.value);r.then(y,y),Jt(r),e(new qt)}catch(t){e(t)}})),s};return o.isMobXFlow=!0,o}),Xt);function Jt(e){b(e.cancel)&&e.cancel()}function er(e){return!0===(null==e?void 0:e.isMobXFlow)}function tr(e,t){return!!e&&(void 0!==t?!!Hr(e)&&e[H].values_.has(t):Hr(e)||!!e[H]||G(e)||Et(e)||Ke(e))}function rr(e){return tr(e)}function nr(e,t){void 0===t&&(t=void 0),dt();try{return e.apply(t)}finally{ft()}}function or(e){return e[H]}Qt.bound=W(Zt);var ir={has:function(e,t){return or(e).has_(t)},get:function(e,t){return or(e).get_(t)},set:function(e,t,r){var n;return!!m(t)&&(null==(n=or(e).set_(t,r,!0))||n)},deleteProperty:function(e,t){var r;return!!m(t)&&(null==(r=or(e).delete_(t,!0))||r)},defineProperty:function(e,t,r){var n;return null==(n=or(e).defineProperty_(t,r))||n},ownKeys:function(e){return or(e).ownKeys_()},preventExtensions:function(e){n(13)}};function ar(e){return void 0!==e.interceptors_&&e.interceptors_.length>0}function sr(e,t){var r=e.interceptors_||(e.interceptors_=[]);return r.push(t),h((function(){var e=r.indexOf(t);-1!==e&&r.splice(e,1)}))}function lr(e,t){var r=tt();try{for(var o=[].concat(e.interceptors_||[]),i=0,a=o.length;i<a&&((t=o[i](t))&&!t.type&&n(14),t);i++);return t}finally{rt(r)}}function ur(e){return void 0!==e.changeListeners_&&e.changeListeners_.length>0}function gr(e,t){var r=e.changeListeners_||(e.changeListeners_=[]);return r.push(t),h((function(){var e=r.indexOf(t);-1!==e&&r.splice(e,1)}))}function cr(e,t){var r=tt(),n=e.changeListeners_;if(n){for(var o=0,i=(n=n.slice()).length;o<i;o++)n[o](t);rt(r)}}function pr(e,t,r){return sn((function(){var n=Fr(e,r)[H];null!=t||(t=function(e){return C(e,F)||E(e,F,M({},e[F])),e[F]}(e)),R(t).forEach((function(e){return n.make_(e,t[e])}))})),e}var dr="splice",fr="update",hr={get:function(e,t){var r=e[H];return t===H?r:"length"===t?r.getArrayLength_():"string"!=typeof t||isNaN(t)?C(mr,t)?mr[t]:e[t]:r.get_(parseInt(t))},set:function(e,t,r){var n=e[H];return"length"===t&&n.setArrayLength_(r),"symbol"==typeof t||isNaN(t)?e[t]=r:n.set_(parseInt(t),r),!0},preventExtensions:function(){n(15)}},yr=function(){function e(e,t,r,n){void 0===e&&(e="ObservableArray"),this.owned_=void 0,this.legacyMode_=void 0,this.atom_=void 0,this.values_=[],this.interceptors_=void 0,this.changeListeners_=void 0,this.enhancer_=void 0,this.dehancer=void 0,this.proxy_=void 0,this.lastKnownLength_=0,this.owned_=r,this.legacyMode_=n,this.atom_=new z(e),this.enhancer_=function(e,r){return t(e,r,"ObservableArray[..]")}}var t=e.prototype;return t.dehanceValue_=function(e){return void 0!==this.dehancer?this.dehancer(e):e},t.dehanceValues_=function(e){return void 0!==this.dehancer&&e.length>0?e.map(this.dehancer):e},t.intercept_=function(e){return sr(this,e)},t.observe_=function(e,t){return void 0===t&&(t=!1),t&&e({observableKind:"array",object:this.proxy_,debugObjectName:this.atom_.name_,type:"splice",index:0,added:this.values_.slice(),addedCount:this.values_.length,removed:[],removedCount:0}),gr(this,e)},t.getArrayLength_=function(){return this.atom_.reportObserved(),this.values_.length},t.setArrayLength_=function(e){("number"!=typeof e||isNaN(e)||e<0)&&n("Out of range: "+e);var t=this.values_.length;if(e!==t)if(e>t){for(var r=new Array(e-t),o=0;o<e-t;o++)r[o]=void 0;this.spliceWithArray_(t,0,r)}else this.spliceWithArray_(e,t-e)},t.updateArrayLength_=function(e,t){e!==this.lastKnownLength_&&n(16),this.lastKnownLength_+=t,this.legacyMode_&&t>0&&tn(e+t+1)},t.spliceWithArray_=function(e,t,r){var n=this;this.atom_;var o=this.values_.length;if(void 0===e?e=0:e>o?e=o:e<0&&(e=Math.max(0,o+e)),t=1===arguments.length?o-e:null==t?0:Math.max(0,Math.min(t,o-e)),void 0===r&&(r=g),ar(this)){var i=lr(this,{object:this.proxy_,type:dr,index:e,removedCount:t,added:r});if(!i)return g;t=i.removedCount,r=i.added}if(r=0===r.length?r:r.map((function(e){return n.enhancer_(e,void 0)})),this.legacyMode_){var a=r.length-t;this.updateArrayLength_(o,a)}var s=this.spliceItemsIntoValues_(e,t,r);return 0===t&&0===r.length||this.notifyArraySplice_(e,r,s),this.dehanceValues_(s)},t.spliceItemsIntoValues_=function(e,t,r){var n;if(r.length<1e4)return(n=this.values_).splice.apply(n,[e,t].concat(r));var o=this.values_.slice(e,e+t),i=this.values_.slice(e+t);this.values_.length+=r.length-t;for(var a=0;a<r.length;a++)this.values_[e+a]=r[a];for(var s=0;s<i.length;s++)this.values_[e+r.length+s]=i[s];return o},t.notifyArrayChildUpdate_=function(e,t,r){var n=!this.owned_&&!1,o=ur(this),i=o||n?{observableKind:"array",object:this.proxy_,type:fr,debugObjectName:this.atom_.name_,index:e,newValue:t,oldValue:r}:null;this.atom_.reportChanged(),o&&cr(this,i)},t.notifyArraySplice_=function(e,t,r){var n=!this.owned_&&!1,o=ur(this),i=o||n?{observableKind:"array",object:this.proxy_,debugObjectName:this.atom_.name_,type:dr,index:e,removed:r,added:t,removedCount:r.length,addedCount:t.length}:null;this.atom_.reportChanged(),o&&cr(this,i)},t.get_=function(e){if(!(this.legacyMode_&&e>=this.values_.length))return this.atom_.reportObserved(),this.dehanceValue_(this.values_[e]);console.warn("[mobx] Out of bounds read: "+e)},t.set_=function(e,t){var r=this.values_;if(this.legacyMode_&&e>r.length&&n(17,e,r.length),e<r.length){this.atom_;var o=r[e];if(ar(this)){var i=lr(this,{type:fr,object:this.proxy_,index:e,newValue:t});if(!i)return;t=i.newValue}(t=this.enhancer_(t,o))!==o&&(r[e]=t,this.notifyArrayChildUpdate_(e,t,o))}else{for(var a=new Array(e+1-r.length),s=0;s<a.length-1;s++)a[s]=void 0;a[a.length-1]=t,this.spliceWithArray_(r.length,0,a)}},e}();function br(e,t,r,n){return void 0===r&&(r="ObservableArray"),void 0===n&&(n=!1),f(),sn((function(){var o=new yr(r,t,n,!1);T(o.values_,H,o);var i=new Proxy(o.values_,hr);return o.proxy_=i,e&&e.length&&o.spliceWithArray_(0,0,e),i}))}var mr={clear:function(){return this.splice(0)},replace:function(e){var t=this[H];return t.spliceWithArray_(0,t.values_.length,e)},toJSON:function(){return this.slice()},splice:function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];var i=this[H];switch(arguments.length){case 0:return[];case 1:return i.spliceWithArray_(e);case 2:return i.spliceWithArray_(e,t)}return i.spliceWithArray_(e,t,n)},spliceWithArray:function(e,t,r){return this[H].spliceWithArray_(e,t,r)},push:function(){for(var e=this[H],t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e.spliceWithArray_(e.values_.length,0,r),e.values_.length},pop:function(){return this.splice(Math.max(this[H].values_.length-1,0),1)[0]},shift:function(){return this.splice(0,1)[0]},unshift:function(){for(var e=this[H],t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e.spliceWithArray_(0,0,r),e.values_.length},reverse:function(){return ut.trackingDerivation&&n(37,"reverse"),this.replace(this.slice().reverse()),this},sort:function(){ut.trackingDerivation&&n(37,"sort");var e=this.slice();return e.sort.apply(e,arguments),this.replace(e),this},remove:function(e){var t=this[H],r=t.dehanceValues_(t.values_).indexOf(e);return r>-1&&(this.splice(r,1),!0)}};function _r(e,t){"function"==typeof Array.prototype[e]&&(mr[e]=t(e))}function vr(e){return function(){var t=this[H];t.atom_.reportObserved();var r=t.dehanceValues_(t.values_);return r[e].apply(r,arguments)}}function Sr(e){return function(t,r){var n=this,o=this[H];return o.atom_.reportObserved(),o.dehanceValues_(o.values_)[e]((function(e,o){return t.call(r,e,o,n)}))}}function Er(e){return function(){var t=this,r=this[H];r.atom_.reportObserved();var n=r.dehanceValues_(r.values_),o=arguments[0];return arguments[0]=function(e,r,n){return o(e,r,n,t)},n[e].apply(n,arguments)}}_r("concat",vr),_r("flat",vr),_r("includes",vr),_r("indexOf",vr),_r("join",vr),_r("lastIndexOf",vr),_r("slice",vr),_r("toString",vr),_r("toLocaleString",vr),_r("every",Sr),_r("filter",Sr),_r("find",Sr),_r("findIndex",Sr),_r("flatMap",Sr),_r("forEach",Sr),_r("map",Sr),_r("some",Sr),_r("reduce",Er),_r("reduceRight",Er);var Tr,Ar,wr=A("ObservableArrayAdministration",yr);function jr(e){return _(e)&&wr(e[H])}var Or={},Rr="add",Nr="delete";Tr=Symbol.iterator,Ar=Symbol.toStringTag;var Cr,Ir,Pr=function(){function e(e,t,r){var o=this;void 0===t&&(t=$),void 0===r&&(r="ObservableMap"),this.enhancer_=void 0,this.name_=void 0,this[H]=Or,this.data_=void 0,this.hasMap_=void 0,this.keysAtom_=void 0,this.interceptors_=void 0,this.changeListeners_=void 0,this.dehancer=void 0,this.enhancer_=t,this.name_=r,b(Map)||n(18),sn((function(){o.keysAtom_=Y("ObservableMap.keys()"),o.data_=new Map,o.hasMap_=new Map,e&&o.merge(e)}))}var t=e.prototype;return t.has_=function(e){return this.data_.has(e)},t.has=function(e){var t=this;if(!ut.trackingDerivation)return this.has_(e);var r=this.hasMap_.get(e);if(!r){var n=r=new He(this.has_(e),q,"ObservableMap.key?",!1);this.hasMap_.set(e,n),kt(n,(function(){return t.hasMap_.delete(e)}))}return r.get()},t.set=function(e,t){var r=this.has_(e);if(ar(this)){var n=lr(this,{type:r?fr:Rr,object:this,newValue:t,name:e});if(!n)return this;t=n.newValue}return r?this.updateValue_(e,t):this.addValue_(e,t),this},t.delete=function(e){var t=this;if((this.keysAtom_,ar(this))&&!lr(this,{type:Nr,object:this,name:e}))return!1;if(this.has_(e)){var r=ur(this),n=r?{observableKind:"map",debugObjectName:this.name_,type:Nr,object:this,oldValue:this.data_.get(e).value_,name:e}:null;return nr((function(){var r;t.keysAtom_.reportChanged(),null==(r=t.hasMap_.get(e))||r.setNewValue_(!1),t.data_.get(e).setNewValue_(void 0),t.data_.delete(e)})),r&&cr(this,n),!0}return!1},t.updateValue_=function(e,t){var r=this.data_.get(e);if((t=r.prepareNewValue_(t))!==ut.UNCHANGED){var n=ur(this),o=n?{observableKind:"map",debugObjectName:this.name_,type:fr,object:this,oldValue:r.value_,name:e,newValue:t}:null;0,r.setNewValue_(t),n&&cr(this,o)}},t.addValue_=function(e,t){var r=this;this.keysAtom_,nr((function(){var n,o=new He(t,r.enhancer_,"ObservableMap.key",!1);r.data_.set(e,o),t=o.value_,null==(n=r.hasMap_.get(e))||n.setNewValue_(!0),r.keysAtom_.reportChanged()}));var n=ur(this),o=n?{observableKind:"map",debugObjectName:this.name_,type:Rr,object:this,name:e,newValue:t}:null;n&&cr(this,o)},t.get=function(e){return this.has(e)?this.dehanceValue_(this.data_.get(e).get()):this.dehanceValue_(void 0)},t.dehanceValue_=function(e){return void 0!==this.dehancer?this.dehancer(e):e},t.keys=function(){return this.keysAtom_.reportObserved(),this.data_.keys()},t.values=function(){var e=this,t=this.keys();return pn({next:function(){var r=t.next(),n=r.done,o=r.value;return{done:n,value:n?void 0:e.get(o)}}})},t.entries=function(){var e=this,t=this.keys();return pn({next:function(){var r=t.next(),n=r.done,o=r.value;return{done:n,value:n?void 0:[o,e.get(o)]}}})},t[Tr]=function(){return this.entries()},t.forEach=function(e,t){for(var r,n=k(this);!(r=n()).done;){var o=r.value,i=o[0],a=o[1];e.call(t,a,i,this)}},t.merge=function(e){var t=this;return xr(e)&&(e=new Map(e)),nr((function(){v(e)?function(e){var t=Object.keys(e);if(!O)return t;var r=Object.getOwnPropertySymbols(e);return r.length?[].concat(t,r.filter((function(t){return u.propertyIsEnumerable.call(e,t)}))):t}(e).forEach((function(r){return t.set(r,e[r])})):Array.isArray(e)?e.forEach((function(e){var r=e[0],n=e[1];return t.set(r,n)})):w(e)?(e.constructor!==Map&&n(19,e),e.forEach((function(e,r){return t.set(r,e)}))):null!=e&&n(20,e)})),this},t.clear=function(){var e=this;nr((function(){et((function(){for(var t,r=k(e.keys());!(t=r()).done;){var n=t.value;e.delete(n)}}))}))},t.replace=function(e){var t=this;return nr((function(){for(var r,o=function(e){if(w(e)||xr(e))return e;if(Array.isArray(e))return new Map(e);if(v(e)){var t=new Map;for(var r in e)t.set(r,e[r]);return t}return n(21,e)}(e),i=new Map,a=!1,s=k(t.data_.keys());!(r=s()).done;){var l=r.value;if(!o.has(l))if(t.delete(l))a=!0;else{var u=t.data_.get(l);i.set(l,u)}}for(var g,c=k(o.entries());!(g=c()).done;){var p=g.value,d=p[0],f=p[1],h=t.data_.has(d);if(t.set(d,f),t.data_.has(d)){var y=t.data_.get(d);i.set(d,y),h||(a=!0)}}if(!a)if(t.data_.size!==i.size)t.keysAtom_.reportChanged();else for(var b=t.data_.keys(),m=i.keys(),_=b.next(),S=m.next();!_.done;){if(_.value!==S.value){t.keysAtom_.reportChanged();break}_=b.next(),S=m.next()}t.data_=i})),this},t.toString=function(){return"[object ObservableMap]"},t.toJSON=function(){return Array.from(this)},t.observe_=function(e,t){return gr(this,e)},t.intercept_=function(e){return sr(this,e)},x(e,[{key:"size",get:function(){return this.keysAtom_.reportObserved(),this.data_.size}},{key:Ar,get:function(){return"Map"}}]),e}(),xr=A("ObservableMap",Pr);var Mr={};Cr=Symbol.iterator,Ir=Symbol.toStringTag;var Ur=function(){function e(e,t,r){var o=this;void 0===t&&(t=$),void 0===r&&(r="ObservableSet"),this.name_=void 0,this[H]=Mr,this.data_=new Set,this.atom_=void 0,this.changeListeners_=void 0,this.interceptors_=void 0,this.dehancer=void 0,this.enhancer_=void 0,this.name_=r,b(Set)||n(22),this.enhancer_=function(e,n){return t(e,n,r)},sn((function(){o.atom_=Y(o.name_),e&&o.replace(e)}))}var t=e.prototype;return t.dehanceValue_=function(e){return void 0!==this.dehancer?this.dehancer(e):e},t.clear=function(){var e=this;nr((function(){et((function(){for(var t,r=k(e.data_.values());!(t=r()).done;){var n=t.value;e.delete(n)}}))}))},t.forEach=function(e,t){for(var r,n=k(this);!(r=n()).done;){var o=r.value;e.call(t,o,o,this)}},t.add=function(e){var t=this;if((this.atom_,ar(this))&&!lr(this,{type:Rr,object:this,newValue:e}))return this;if(!this.has(e)){nr((function(){t.data_.add(t.enhancer_(e,void 0)),t.atom_.reportChanged()}));var r=!1,n=ur(this),o=n?{observableKind:"set",debugObjectName:this.name_,type:Rr,object:this,newValue:e}:null;r,n&&cr(this,o)}return this},t.delete=function(e){var t=this;if(ar(this)&&!lr(this,{type:Nr,object:this,oldValue:e}))return!1;if(this.has(e)){var r=ur(this),n=r?{observableKind:"set",debugObjectName:this.name_,type:Nr,object:this,oldValue:e}:null;return nr((function(){t.atom_.reportChanged(),t.data_.delete(e)})),r&&cr(this,n),!0}return!1},t.has=function(e){return this.atom_.reportObserved(),this.data_.has(this.dehanceValue_(e))},t.entries=function(){var e=0,t=Array.from(this.keys()),r=Array.from(this.values());return pn({next:function(){var n=e;return e+=1,n<r.length?{value:[t[n],r[n]],done:!1}:{done:!0}}})},t.keys=function(){return this.values()},t.values=function(){this.atom_.reportObserved();var e=this,t=0,r=Array.from(this.data_.values());return pn({next:function(){return t<r.length?{value:e.dehanceValue_(r[t++]),done:!1}:{done:!0}}})},t.replace=function(e){var t=this;return Lr(e)&&(e=new Set(e)),nr((function(){Array.isArray(e)||j(e)?(t.clear(),e.forEach((function(e){return t.add(e)}))):null!=e&&n("Cannot initialize set from "+e)})),this},t.observe_=function(e,t){return gr(this,e)},t.intercept_=function(e){return sr(this,e)},t.toJSON=function(){return Array.from(this)},t.toString=function(){return"[object ObservableSet]"},t[Cr]=function(){return this.values()},x(e,[{key:"size",get:function(){return this.atom_.reportObserved(),this.data_.size}},{key:Ir,get:function(){return"Set"}}]),e}(),Lr=A("ObservableSet",Ur),Br=Object.create(null),Dr="remove",kr=function(){function e(e,t,r,n){void 0===t&&(t=new Map),void 0===n&&(n=pe),this.target_=void 0,this.values_=void 0,this.name_=void 0,this.defaultAnnotation_=void 0,this.keysAtom_=void 0,this.changeListeners_=void 0,this.interceptors_=void 0,this.proxy_=void 0,this.isPlainObject_=void 0,this.appliedAnnotations_=void 0,this.pendingKeys_=void 0,this.target_=e,this.values_=t,this.name_=r,this.defaultAnnotation_=n,this.keysAtom_=new z("ObservableObject.keys"),this.isPlainObject_=v(this.target_)}var t=e.prototype;return t.getObservablePropValue_=function(e){return this.values_.get(e).get()},t.setObservablePropValue_=function(e,t){var r=this.values_.get(e);if(r instanceof Ye)return r.set(t),!0;if(ar(this)){var n=lr(this,{type:fr,object:this.proxy_||this.target_,name:e,newValue:t});if(!n)return null;t=n.newValue}if((t=r.prepareNewValue_(t))!==ut.UNCHANGED){var o=ur(this),i=o?{type:fr,observableKind:"object",debugObjectName:this.name_,object:this.proxy_||this.target_,oldValue:r.value_,name:e,newValue:t}:null;0,r.setNewValue_(t),o&&cr(this,i)}return!0},t.get_=function(e){return ut.trackingDerivation&&!C(this.target_,e)&&this.has_(e),this.target_[e]},t.set_=function(e,t,r){return void 0===r&&(r=!1),C(this.target_,e)?this.values_.has(e)?this.setObservablePropValue_(e,t):r?Reflect.set(this.target_,e,t):(this.target_[e]=t,!0):this.extend_(e,{value:t,enumerable:!0,writable:!0,configurable:!0},this.defaultAnnotation_,r)},t.has_=function(e){if(!ut.trackingDerivation)return e in this.target_;this.pendingKeys_||(this.pendingKeys_=new Map);var t=this.pendingKeys_.get(e);return t||(t=new He(e in this.target_,q,"ObservableObject.key?",!1),this.pendingKeys_.set(e,t)),t.get()},t.make_=function(e,t){if(!0===t&&(t=this.defaultAnnotation_),!1!==t){if(Gr(this,t,e),!(e in this.target_)){var r;if(null!=(r=this.target_[F])&&r[e])return;n(1,t.annotationType_,this.name_+"."+e.toString())}for(var o=this.target_;o&&o!==u;){var i=s(o,e);if(i){var a=t.make_(this,e,i,o);if(0===a)return;if(1===a)break}o=Object.getPrototypeOf(o)}zr(this,t,e)}},t.extend_=function(e,t,r,n){if(void 0===n&&(n=!1),!0===r&&(r=this.defaultAnnotation_),!1===r)return this.defineProperty_(e,t,n);Gr(this,r,e);var o=r.extend_(this,e,t,n);return o&&zr(this,r,e),o},t.defineProperty_=function(e,t,r){void 0===r&&(r=!1),this.keysAtom_;try{dt();var n=this.delete_(e);if(!n)return n;if(ar(this)){var o=lr(this,{object:this.proxy_||this.target_,name:e,type:Rr,newValue:t.value});if(!o)return null;var i=o.newValue;t.value!==i&&(t=M({},t,{value:i}))}if(r){if(!Reflect.defineProperty(this.target_,e,t))return!1}else l(this.target_,e,t);this.notifyPropertyAddition_(e,t.value)}finally{ft()}return!0},t.defineObservableProperty_=function(e,t,r,n){void 0===n&&(n=!1),this.keysAtom_;try{dt();var o=this.delete_(e);if(!o)return o;if(ar(this)){var i=lr(this,{object:this.proxy_||this.target_,name:e,type:Rr,newValue:t});if(!i)return null;t=i.newValue}var a=Vr(e),s={configurable:!ut.safeDescriptors||this.isPlainObject_,enumerable:!0,get:a.get,set:a.set};if(n){if(!Reflect.defineProperty(this.target_,e,s))return!1}else l(this.target_,e,s);var u=new He(t,r,"ObservableObject.key",!1);this.values_.set(e,u),this.notifyPropertyAddition_(e,u.value_)}finally{ft()}return!0},t.defineComputedProperty_=function(e,t,r){void 0===r&&(r=!1),this.keysAtom_;try{dt();var n=this.delete_(e);if(!n)return n;if(ar(this))if(!lr(this,{object:this.proxy_||this.target_,name:e,type:Rr,newValue:void 0}))return null;t.name||(t.name="ObservableObject.key"),t.context=this.proxy_||this.target_;var o=Vr(e),i={configurable:!ut.safeDescriptors||this.isPlainObject_,enumerable:!1,get:o.get,set:o.set};if(r){if(!Reflect.defineProperty(this.target_,e,i))return!1}else l(this.target_,e,i);this.values_.set(e,new Ye(t)),this.notifyPropertyAddition_(e,void 0)}finally{ft()}return!0},t.delete_=function(e,t){if(void 0===t&&(t=!1),this.keysAtom_,!C(this.target_,e))return!0;if(ar(this)&&!lr(this,{object:this.proxy_||this.target_,name:e,type:Dr}))return null;try{var r,n;dt();var o,i=ur(this),a=this.values_.get(e),l=void 0;if(!a&&i)l=null==(o=s(this.target_,e))?void 0:o.value;if(t){if(!Reflect.deleteProperty(this.target_,e))return!1}else delete this.target_[e];if(a&&(this.values_.delete(e),a instanceof He&&(l=a.value_),yt(a)),this.keysAtom_.reportChanged(),null==(r=this.pendingKeys_)||null==(n=r.get(e))||n.set(e in this.target_),i){var u={type:Dr,observableKind:"object",object:this.proxy_||this.target_,debugObjectName:this.name_,oldValue:l,name:e};0,i&&cr(this,u)}}finally{ft()}return!0},t.observe_=function(e,t){return gr(this,e)},t.intercept_=function(e){return sr(this,e)},t.notifyPropertyAddition_=function(e,t){var r,n,o=ur(this);if(o){var i=o?{type:Rr,observableKind:"object",debugObjectName:this.name_,object:this.proxy_||this.target_,name:e,newValue:t}:null;0,o&&cr(this,i)}null==(r=this.pendingKeys_)||null==(n=r.get(e))||n.set(!0),this.keysAtom_.reportChanged()},t.ownKeys_=function(){return this.keysAtom_.reportObserved(),R(this.target_)},t.keys_=function(){return this.keysAtom_.reportObserved(),Object.keys(this.target_)},e}();function Fr(e,t){var r;if(C(e,H))return e;var n=null!=(r=null==t?void 0:t.name)?r:"ObservableObject",o=new kr(e,new Map,String(n),function(e){var t;return e?null!=(t=e.defaultDecorator)?t:de(e):void 0}(t));return E(e,H,o),e}var Wr=A("ObservableObjectAdministration",kr);function Vr(e){return Br[e]||(Br[e]={get:function(){return this[H].getObservablePropValue_(e)},set:function(t){return this[H].setObservablePropValue_(e,t)}})}function Hr(e){return!!_(e)&&Wr(e[H])}function zr(e,t,r){var n;null==(n=e.target_[F])||delete n[r]}function Gr(e,t,r){}var Yr,Kr,$r=Jr(0),qr=function(){var e=!1,t={};return Object.defineProperty(t,"0",{set:function(){e=!0}}),Object.create(t)[0]=1,!1===e}(),Xr=0,Zr=function(){};Yr=Zr,Kr=Array.prototype,Object.setPrototypeOf?Object.setPrototypeOf(Yr.prototype,Kr):void 0!==Yr.prototype.__proto__?Yr.prototype.__proto__=Kr:Yr.prototype=Kr;var Qr=function(e,t,r){function n(t,r,n,o){var i;return void 0===n&&(n="ObservableArray"),void 0===o&&(o=!1),i=e.call(this)||this,sn((function(){var e=new yr(n,r,o,!0);e.proxy_=B(i),T(B(i),H,e),t&&t.length&&i.spliceWithArray(0,0,t),qr&&Object.defineProperty(B(i),"0",$r)})),i}U(n,e);var o=n.prototype;return o.concat=function(){this[H].atom_.reportObserved();for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Array.prototype.concat.apply(this.slice(),t.map((function(e){return jr(e)?e.slice():e})))},o[r]=function(){var e=this,t=0;return pn({next:function(){return t<e.length?{value:e[t++],done:!1}:{done:!0,value:void 0}}})},x(n,[{key:"length",get:function(){return this[H].getArrayLength_()},set:function(e){this[H].setArrayLength_(e)}},{key:t,get:function(){return"Array"}}]),n}(Zr,Symbol.toStringTag,Symbol.iterator);function Jr(e){return{enumerable:!1,configurable:!0,get:function(){return this[H].get_(e)},set:function(t){this[H].set_(e,t)}}}function en(e){l(Qr.prototype,""+e,Jr(e))}function tn(e){if(e>Xr){for(var t=Xr;t<e+100;t++)en(t);Xr=e}}function rn(e,t,r){return new Qr(e,t,r)}function nn(e,t){if("object"==typeof e&&null!==e){if(jr(e))return void 0!==t&&n(23),e[H].atom_;if(Lr(e))return e.atom_;if(xr(e)){if(void 0===t)return e.keysAtom_;var r=e.data_.get(t)||e.hasMap_.get(t);return r||n(25,t,an(e)),r}if(Hr(e)){if(!t)return n(26);var o=e[H].values_.get(t);return o||n(27,t,an(e)),o}if(G(e)||Ke(e)||Et(e))return e}else if(b(e)&&Et(e[H]))return e[H];n(28)}function on(e,t){return e||n(29),void 0!==t?on(nn(e,t)):G(e)||Ke(e)||Et(e)||xr(e)||Lr(e)?e:e[H]?e[H]:void n(24,e)}function an(e,t){var r;if(void 0!==t)r=nn(e,t);else{if(xt(e))return e.name;r=Hr(e)||xr(e)||Lr(e)?on(e):nn(e)}return r.name_}function sn(e){var t=tt(),r=Fe(!0);dt();try{return e()}finally{ft(),We(r),rt(t)}}Object.entries(mr).forEach((function(e){var t=e[0],r=e[1];"concat"!==t&&E(Qr.prototype,t,r)})),tn(1e3);var ln=u.toString;function un(e,t,r){return void 0===r&&(r=-1),gn(e,t,r)}function gn(e,t,r,n,o){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return!1;if(e!=e)return t!=t;var i=typeof e;if("function"!==i&&"object"!==i&&"object"!=typeof t)return!1;var a=ln.call(e);if(a!==ln.call(t))return!1;switch(a){case"[object RegExp]":case"[object String]":return""+e==""+t;case"[object Number]":return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object Symbol]":return"undefined"!=typeof Symbol&&Symbol.valueOf.call(e)===Symbol.valueOf.call(t);case"[object Map]":case"[object Set]":r>=0&&r++}e=cn(e),t=cn(t);var s="[object Array]"===a;if(!s){if("object"!=typeof e||"object"!=typeof t)return!1;var l=e.constructor,u=t.constructor;if(l!==u&&!(b(l)&&l instanceof l&&b(u)&&u instanceof u)&&"constructor"in e&&"constructor"in t)return!1}if(0===r)return!1;r<0&&(r=-1),o=o||[];for(var g=(n=n||[]).length;g--;)if(n[g]===e)return o[g]===t;if(n.push(e),o.push(t),s){if((g=e.length)!==t.length)return!1;for(;g--;)if(!gn(e[g],t[g],r-1,n,o))return!1}else{var c,p=Object.keys(e);if(g=p.length,Object.keys(t).length!==g)return!1;for(;g--;)if(!C(t,c=p[g])||!gn(e[c],t[c],r-1,n,o))return!1}return n.pop(),o.pop(),!0}function cn(e){return jr(e)?e.slice():w(e)||xr(e)||j(e)||Lr(e)?Array.from(e.entries()):e}function pn(e){return e[Symbol.iterator]=dn,e}function dn(){return this}["Symbol","Map","Set"].forEach((function(e){void 0===i()[e]&&n("MobX requires global '"+e+"' to be available or polyfilled")})),"object"==typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__&&__MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({spy:function(e){return console.warn("[mobx.spy] Is a no-op in production builds"),function(){}},extras:{getDebugName:an},$mobx:H})},8584:(e,t,r)=>{"use strict";function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},n.apply(null,arguments)}r.d(t,{A:()=>n})},5033:(e,t,r)=>{"use strict";function n(e,t){return n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},n(e,t)}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,n(e,t)}r.d(t,{A:()=>o})},1115:(e,t,r)=>{"use strict";function n(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}r.d(t,{A:()=>n})},8506:()=>{"use strict";var e=null;try{e=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}function t(e,t,r){this.low=0|e,this.high=0|t,this.unsigned=!!r}function r(e){return!0===(e&&e.__isLong__)}function n(e){var t=Math.clz32(e&-e);return e?31-t:t}t.prototype.__isLong__,Object.defineProperty(t.prototype,"__isLong__",{value:!0}),t.isLong=r;var o={},i={};function a(e,t){var r,n,a;return t?(a=0<=(e>>>=0)&&e<256)&&(n=i[e])?n:(r=l(e,0,!0),a&&(i[e]=r),r):(a=-128<=(e|=0)&&e<128)&&(n=o[e])?n:(r=l(e,e<0?-1:0,!1),a&&(o[e]=r),r)}function s(e,t){if(isNaN(e))return t?b:y;if(t){if(e<0)return b;if(e>=d)return E}else{if(e<=-f)return T;if(e+1>=f)return S}return e<0?s(-e,t).neg():l(e%p|0,e/p|0,t)}function l(e,r,n){return new t(e,r,n)}t.fromInt=a,t.fromNumber=s,t.fromBits=l;var u=Math.pow;function g(e,t,r){if(0===e.length)throw Error("empty string");if("number"==typeof t?(r=t,t=!1):t=!!t,"NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return t?b:y;if((r=r||10)<2||36<r)throw RangeError("radix");var n;if((n=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===n)return g(e.substring(1),t,r).neg();for(var o=s(u(r,8)),i=y,a=0;a<e.length;a+=8){var l=Math.min(8,e.length-a),c=parseInt(e.substring(a,a+l),r);if(l<8){var p=s(u(r,l));i=i.mul(p).add(s(c))}else i=(i=i.mul(o)).add(s(c))}return i.unsigned=t,i}function c(e,t){return"number"==typeof e?s(e,t):"string"==typeof e?g(e,t):l(e.low,e.high,"boolean"==typeof t?t:e.unsigned)}t.fromString=g,t.fromValue=c;var p=4294967296,d=p*p,f=d/2,h=a(1<<24),y=a(0);t.ZERO=y;var b=a(0,!0);t.UZERO=b;var m=a(1);t.ONE=m;var _=a(1,!0);t.UONE=_;var v=a(-1);t.NEG_ONE=v;var S=l(-1,2147483647,!1);t.MAX_VALUE=S;var E=l(-1,-1,!0);t.MAX_UNSIGNED_VALUE=E;var T=l(0,-2147483648,!1);t.MIN_VALUE=T;var A=t.prototype;A.toInt=function(){return this.unsigned?this.low>>>0:this.low},A.toNumber=function(){return this.unsigned?(this.high>>>0)*p+(this.low>>>0):this.high*p+(this.low>>>0)},A.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(T)){var t=s(e),r=this.div(t),n=r.mul(t).sub(this);return r.toString(e)+n.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var o=s(u(e,6),this.unsigned),i=this,a="";;){var l=i.div(o),g=(i.sub(l.mul(o)).toInt()>>>0).toString(e);if((i=l).isZero())return g+a;for(;g.length<6;)g="0"+g;a=""+g+a}},A.getHighBits=function(){return this.high},A.getHighBitsUnsigned=function(){return this.high>>>0},A.getLowBits=function(){return this.low},A.getLowBitsUnsigned=function(){return this.low>>>0},A.getNumBitsAbs=function(){if(this.isNegative())return this.eq(T)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&0==(e&1<<t);t--);return 0!=this.high?t+33:t+1},A.isZero=function(){return 0===this.high&&0===this.low},A.eqz=A.isZero,A.isNegative=function(){return!this.unsigned&&this.high<0},A.isPositive=function(){return this.unsigned||this.high>=0},A.isOdd=function(){return 1==(1&this.low)},A.isEven=function(){return 0==(1&this.low)},A.equals=function(e){return r(e)||(e=c(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&(this.high===e.high&&this.low===e.low)},A.eq=A.equals,A.notEquals=function(e){return!this.eq(e)},A.neq=A.notEquals,A.ne=A.notEquals,A.lessThan=function(e){return this.comp(e)<0},A.lt=A.lessThan,A.lessThanOrEqual=function(e){return this.comp(e)<=0},A.lte=A.lessThanOrEqual,A.le=A.lessThanOrEqual,A.greaterThan=function(e){return this.comp(e)>0},A.gt=A.greaterThan,A.greaterThanOrEqual=function(e){return this.comp(e)>=0},A.gte=A.greaterThanOrEqual,A.ge=A.greaterThanOrEqual,A.compare=function(e){if(r(e)||(e=c(e)),this.eq(e))return 0;var t=this.isNegative(),n=e.isNegative();return t&&!n?-1:!t&&n?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},A.comp=A.compare,A.negate=function(){return!this.unsigned&&this.eq(T)?T:this.not().add(m)},A.neg=A.negate,A.add=function(e){r(e)||(e=c(e));var t=this.high>>>16,n=65535&this.high,o=this.low>>>16,i=65535&this.low,a=e.high>>>16,s=65535&e.high,u=e.low>>>16,g=0,p=0,d=0,f=0;return d+=(f+=i+(65535&e.low))>>>16,p+=(d+=o+u)>>>16,g+=(p+=n+s)>>>16,g+=t+a,l((d&=65535)<<16|(f&=65535),(g&=65535)<<16|(p&=65535),this.unsigned)},A.subtract=function(e){return r(e)||(e=c(e)),this.add(e.neg())},A.sub=A.subtract,A.multiply=function(t){if(this.isZero())return this;if(r(t)||(t=c(t)),e)return l(e.mul(this.low,this.high,t.low,t.high),e.get_high(),this.unsigned);if(t.isZero())return this.unsigned?b:y;if(this.eq(T))return t.isOdd()?T:y;if(t.eq(T))return this.isOdd()?T:y;if(this.isNegative())return t.isNegative()?this.neg().mul(t.neg()):this.neg().mul(t).neg();if(t.isNegative())return this.mul(t.neg()).neg();if(this.lt(h)&&t.lt(h))return s(this.toNumber()*t.toNumber(),this.unsigned);var n=this.high>>>16,o=65535&this.high,i=this.low>>>16,a=65535&this.low,u=t.high>>>16,g=65535&t.high,p=t.low>>>16,d=65535&t.low,f=0,m=0,_=0,v=0;return _+=(v+=a*d)>>>16,m+=(_+=i*d)>>>16,_&=65535,m+=(_+=a*p)>>>16,f+=(m+=o*d)>>>16,m&=65535,f+=(m+=i*p)>>>16,m&=65535,f+=(m+=a*g)>>>16,f+=n*d+o*p+i*g+a*u,l((_&=65535)<<16|(v&=65535),(f&=65535)<<16|(m&=65535),this.unsigned)},A.mul=A.multiply,A.divide=function(t){if(r(t)||(t=c(t)),t.isZero())throw Error("division by zero");var n,o,i;if(e)return this.unsigned||-2147483648!==this.high||-1!==t.low||-1!==t.high?l((this.unsigned?e.div_u:e.div_s)(this.low,this.high,t.low,t.high),e.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?b:y;if(this.unsigned){if(t.unsigned||(t=t.toUnsigned()),t.gt(this))return b;if(t.gt(this.shru(1)))return _;i=b}else{if(this.eq(T))return t.eq(m)||t.eq(v)?T:t.eq(T)?m:(n=this.shr(1).div(t).shl(1)).eq(y)?t.isNegative()?m:v:(o=this.sub(t.mul(n)),i=n.add(o.div(t)));if(t.eq(T))return this.unsigned?b:y;if(this.isNegative())return t.isNegative()?this.neg().div(t.neg()):this.neg().div(t).neg();if(t.isNegative())return this.div(t.neg()).neg();i=y}for(o=this;o.gte(t);){n=Math.max(1,Math.floor(o.toNumber()/t.toNumber()));for(var a=Math.ceil(Math.log(n)/Math.LN2),g=a<=48?1:u(2,a-48),p=s(n),d=p.mul(t);d.isNegative()||d.gt(o);)d=(p=s(n-=g,this.unsigned)).mul(t);p.isZero()&&(p=m),i=i.add(p),o=o.sub(d)}return i},A.div=A.divide,A.modulo=function(t){return r(t)||(t=c(t)),e?l((this.unsigned?e.rem_u:e.rem_s)(this.low,this.high,t.low,t.high),e.get_high(),this.unsigned):this.sub(this.div(t).mul(t))},A.mod=A.modulo,A.rem=A.modulo,A.not=function(){return l(~this.low,~this.high,this.unsigned)},A.countLeadingZeros=function(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32},A.clz=A.countLeadingZeros,A.countTrailingZeros=function(){return this.low?n(this.low):n(this.high)+32},A.ctz=A.countTrailingZeros,A.and=function(e){return r(e)||(e=c(e)),l(this.low&e.low,this.high&e.high,this.unsigned)},A.or=function(e){return r(e)||(e=c(e)),l(this.low|e.low,this.high|e.high,this.unsigned)},A.xor=function(e){return r(e)||(e=c(e)),l(this.low^e.low,this.high^e.high,this.unsigned)},A.shiftLeft=function(e){return r(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?l(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):l(0,this.low<<e-32,this.unsigned)},A.shl=A.shiftLeft,A.shiftRight=function(e){return r(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?l(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):l(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},A.shr=A.shiftRight,A.shiftRightUnsigned=function(e){return r(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?l(this.low>>>e|this.high<<32-e,this.high>>>e,this.unsigned):l(32===e?this.high:this.high>>>e-32,0,this.unsigned)},A.shru=A.shiftRightUnsigned,A.shr_u=A.shiftRightUnsigned,A.rotateLeft=function(e){var t;return r(e)&&(e=e.toInt()),0==(e&=63)?this:32===e?l(this.high,this.low,this.unsigned):e<32?(t=32-e,l(this.low<<e|this.high>>>t,this.high<<e|this.low>>>t,this.unsigned)):(t=32-(e-=32),l(this.high<<e|this.low>>>t,this.low<<e|this.high>>>t,this.unsigned))},A.rotl=A.rotateLeft,A.rotateRight=function(e){var t;return r(e)&&(e=e.toInt()),0==(e&=63)?this:32===e?l(this.high,this.low,this.unsigned):e<32?(t=32-e,l(this.high<<t|this.low>>>e,this.low<<t|this.high>>>e,this.unsigned)):(t=32-(e-=32),l(this.low<<t|this.high>>>e,this.high<<t|this.low>>>e,this.unsigned))},A.rotr=A.rotateRight,A.toSigned=function(){return this.unsigned?l(this.low,this.high,!1):this},A.toUnsigned=function(){return this.unsigned?this:l(this.low,this.high,!0)},A.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},A.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]},A.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,255&e,t>>>24,t>>>16&255,t>>>8&255,255&t]},t.fromBytes=function(e,r,n){return n?t.fromBytesLE(e,r):t.fromBytesBE(e,r)},t.fromBytesLE=function(e,r){return new t(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,r)},t.fromBytesBE=function(e,r){return new t(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],r)}},4629:(e,t,r)=>{"use strict";r.d(t,{Cg:()=>n});function n(e,t,r,n){var o,i=arguments.length,a=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,r,a):o(t,r))||a);return i>3&&a&&Object.defineProperty(t,r,a),a}Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError}}]);