---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-56-58\console.log started at 2025-07-27 20:56:58 (2025-07-28 00:56:58 UTC)

20:56:58.777 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-56-58' to filesystem under name logs
20:56:58.777 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
20:56:58.780 ENGINE       : Initializing engine, version 184702
20:56:58.781 ENGINE       : CLI Params: -config C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\server.json -profile C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves -addonsDir addons -logLevel 3 -logStats 
20:56:58.783 ENGINE       : Addon dirs:
20:56:58.783  ENGINE       : dir: 'addons'
20:56:58.784  ENGINE       : dir: './addons'
20:56:58.784  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
20:56:58.784 ENGINE       : Available addons:
20:56:58.784  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
20:56:58.784  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
20:56:58.856 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
20:56:58.859 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
20:56:58.859 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
20:56:59.018 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
20:56:59.020 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
20:56:59.020 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-56-58/resourceDatabase.rdb)
20:56:59.120 ENGINE       : GameProject load
20:56:59.120  ENGINE       : Loaded addons:
20:56:59.121   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
20:56:59.121   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
20:56:59.122 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
20:56:59.122 INIT         : GameProject engine user settings load @"$profile:.save/settings/ReforgerEngineSettings.conf"
20:56:59.122  ENGINE       : Engine user settings config loaded
20:56:59.122 PROFILING    : Settings changed took: 0.001000 ms
20:56:59.122 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
20:56:59.122 RENDER       : Basic screen setup:
20:56:59.122 RENDER       :   Back buffer width x height: 0x0
20:56:59.122 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
20:56:59.123 RENDER       :   MSAA                      : none
20:56:59.123 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
20:56:59.123 RENDER       : Detected output devices:
20:56:59.123 RENDER       :  * 1 device/s connected to Dummy:
20:56:59.123 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
20:56:59.123 RENDER       :   Concurrent frames limit 2
20:56:59.123 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
20:56:59.177 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
20:56:59.181 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
20:56:59.181 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
20:56:59.181 PROFILING    : Settings changed took: 0.020700 ms
20:56:59.184 ENGINE       : Initializing inputs.
20:56:59.190 NETWORK      : Initializing networking.
20:56:59.190 SCRIPT       : SCRIPT       : Initializing scripts
20:56:59.190  SCRIPT       : SCRIPT       : ScriptProjectManager init
20:56:59.190  PROFILING    : ScriptProjectManager init took: 0.023500 ms
20:56:59.210 PROFILING    : Initializing scripts took: 19.099000 ms
20:56:59.211 ENGINE       : Enfusion engine successfully created.
20:56:59.217 GUI          : Using default language (en_us)
20:56:59.217 GUI          : Loading 'en_us' localization file.
20:56:59.217 INIT         : INIT         : Loading StringTable
20:56:59.238 PROFILING    : Loading StringTable took: 21.153100 ms
20:56:59.238 SCRIPT       : SCRIPT       : Compiling GameLib scripts
20:56:59.259  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
20:56:59.263 PROFILING    : Compiling GameLib scripts took: 25.032500 ms
20:56:59.263 SCRIPT       : SCRIPT       : Compiling Game scripts
20:56:59.722 SCRIPT       : Compiling Game scripts
20:56:59.723  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
20:56:59.758  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
20:56:59.957  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:56:59.957  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:56:59.957  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:56:59.972  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
20:57:00.003  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
20:57:00.081  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
20:57:00.140  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:57:00.181  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
20:57:00.182  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
20:57:00.249  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
20:57:00.249  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:57:00.249  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:57:00.250  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:57:00.250  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:57:00.250  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:57:00.285  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:57:00.286  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:57:00.286  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:57:00.287  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:57:00.287  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:57:00.287  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:57:00.299  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:57:00.311  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:57:00.311  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:57:00.311  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:57:00.337  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
20:57:00.337  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
20:57:00.341  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:57:00.341  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:57:00.342  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:57:00.343  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
20:57:00.343  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:57:00.388  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
20:57:01.133 PROFILING    : Compiling Game scripts took: 1869.152300 ms
20:57:01.134 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
20:57:01.147 BACKEND      : Loading dedicated server config.
20:57:01.147  BACKEND      : Server config loaded.
20:57:01.148  BACKEND      : JSON Schema Validation:
20:57:01.148   BACKEND      : JSON is Valid
20:57:01.148 BACKEND      : Loading dedicated server config.
20:57:01.148  RESOURCES    : GetResourceObject @"{F8B2E3A14C5D6E7F}Missions/EdenRP.conf"
20:57:01.148   RESOURCES (E): Failed to open
20:57:01.148  RESOURCES (E): MissionHeader::ReadMissionHeader cannot load the resource 'Missions/EdenRP.conf'!
20:57:01.153 ENGINE       : Game successfully created.
20:57:01.200 PLATFORM     : Save data from container 'settings' have been loaded
20:57:01.299 PLATFORM     : Save data from container 'sessions' have been loaded
20:57:01.301 PLATFORM     : GameProject user settings load from profile @"ReforgerGameSettings.conf"
20:57:01.302  ENGINE       : Game user settings config loaded
20:57:01.302 NETWORK      : Starting dedicated server using command line args.
20:57:02.791 BACKEND   (E): Addon F8B2E3A14C5D6E7F - Addon was not found on workshop.
20:57:02.791 BACKEND   (E): 1 addons are not downloadable! Cannot start until they are removed from server config.
20:57:02.791 BACKEND   (E): Failed to fetch addon details from workshop API! Repeat later or try different mods.
20:57:02.991 ENGINE    (E): Unable to initialize the game
20:57:03.019 ENGINE       : Game destroyed.
20:57:03.037 RPL          : Pip::Destroy
20:57:03.045 RESOURCES (E): ==== Resource leaks ====
20:57:03.045 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
