//  File: fn_robAction.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Starts the robbing process?
private["_target"];
_target = cursorTarget;

//Error checks
if(isNull _target) exitWith {};
if(!isPlayer _target) exitWith {};

if(_target getVariable["robbed",false]) exitWith {};
[[player],"OEC_fnc_robPerson",_target,false] spawn OEC_fnc_MP;
_target setVariable["robbed",TRUE,TRUE];
hint "Robbing player...";