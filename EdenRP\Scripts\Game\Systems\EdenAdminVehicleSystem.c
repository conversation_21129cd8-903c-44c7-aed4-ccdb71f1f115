//! Eden Admin Vehicle System
//! Handles admin vehicle spawning and management
class EdenAdminVehicleSystem
{
    protected ref EdenAdminManager m_AdminManager;
    protected ref EdenVehicleManager m_VehicleManager;
    protected ref map<string, ref array<string>> m_VehicleCategories;
    protected ref array<string> m_SpawnedAdminVehicles;
    
    void EdenAdminVehicleSystem()
    {
        m_VehicleCategories = new map<string, ref array<string>>();
        m_SpawnedAdminVehicles = new array<string>();
    }
    
    void Initialize(EdenAdminManager adminManager, EdenVehicleManager vehicleManager)
    {
        m_AdminManager = adminManager;
        m_VehicleManager = vehicleManager;
        
        InitializeVehicleCategories();
        
        Print("[EdenAdminVehicleSystem] Admin vehicle system initialized");
    }
    
    //! Initialize vehicle categories
    protected void InitializeVehicleCategories()
    {
        // Civilian vehicles
        ref array<string> civilianVehicles = new array<string>();
        civilianVehicles.Insert("US_M1025_armed_M2");
        civilianVehicles.Insert("US_M998_transport");
        civilianVehicles.Insert("US_M1025_unarmed");
        m_VehicleCategories.Set("Civilian", civilianVehicles);
        
        // Police vehicles
        ref array<string> policeVehicles = new array<string>();
        policeVehicles.Insert("US_M1025_armed_M2");
        policeVehicles.Insert("US_M998_transport");
        m_VehicleCategories.Set("Police", policeVehicles);
        
        // Medical vehicles
        ref array<string> medicalVehicles = new array<string>();
        medicalVehicles.Insert("US_M997_transport");
        medicalVehicles.Insert("US_M1025_unarmed");
        m_VehicleCategories.Set("Medical", medicalVehicles);
        
        // Military vehicles
        ref array<string> militaryVehicles = new array<string>();
        militaryVehicles.Insert("US_M1025_armed_M2");
        militaryVehicles.Insert("US_M1025_armed_Mk19");
        militaryVehicles.Insert("US_M998_transport");
        m_VehicleCategories.Set("Military", militaryVehicles);
        
        // Aircraft
        ref array<string> aircraft = new array<string>();
        aircraft.Insert("US_UH60M");
        aircraft.Insert("US_UH60M_MEV");
        m_VehicleCategories.Set("Aircraft", aircraft);
        
        Print("[EdenAdminVehicleSystem] Vehicle categories initialized");
    }
    
    //! Spawn vehicle for admin
    bool SpawnVehicle(string adminId, string vehicleClass, vector position, float direction = 0)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminVehicleSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        if (!IsValidVehicleClass(vehicleClass))
        {
            Print("[EdenAdminVehicleSystem] Invalid vehicle class: " + vehicleClass);
            return false;
        }
        
        // Spawn the vehicle
        EntitySpawnParams spawnParams = new EntitySpawnParams();
        spawnParams.TransformMode = ETransformMode.WORLD;
        spawnParams.Transform[3] = position;
        
        // Set rotation
        vector angles = Vector(0, direction, 0);
        spawnParams.Transform[0] = angles.AnglesToVector();
        spawnParams.Transform[1] = vector.Up;
        spawnParams.Transform[2] = spawnParams.Transform[0] * spawnParams.Transform[1];
        
        IEntity vehicleEntity = GetGame().SpawnEntityPrefab(Resource.Load(vehicleClass), GetGame().GetWorld(), spawnParams);
        
        if (!vehicleEntity)
        {
            Print("[EdenAdminVehicleSystem] Failed to spawn vehicle: " + vehicleClass);
            return false;
        }
        
        // Track spawned admin vehicle
        string vehicleId = vehicleEntity.GetID().ToString();
        m_SpawnedAdminVehicles.Insert(vehicleId);
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "SpawnVehicle", "", "Spawned vehicle: " + vehicleClass + " at " + position.ToString());
        
        Print("[EdenAdminVehicleSystem] Vehicle spawned successfully: " + vehicleClass + " ID: " + vehicleId);
        return true;
    }
    
    //! Spawn vehicle at player position
    bool SpawnVehicleAtPlayer(string adminId, string targetPlayerId, string vehicleClass)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminVehicleSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        // Get player position
        IEntity playerEntity = GetGame().GetPlayerManager().GetPlayerControlledEntity(GetGame().GetPlayerManager().GetPlayerIdFromName(targetPlayerId));
        if (!playerEntity)
        {
            Print("[EdenAdminVehicleSystem] Player not found: " + targetPlayerId);
            return false;
        }
        
        vector playerPos = playerEntity.GetOrigin();
        vector spawnPos = playerPos + Vector(5, 0, 0); // Spawn 5 meters away
        
        return SpawnVehicle(adminId, vehicleClass, spawnPos);
    }
    
    //! Delete vehicle
    bool DeleteVehicle(string adminId, string vehicleId)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminVehicleSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        IEntity vehicleEntity = GetGame().GetWorld().FindEntityByID(vehicleId.ToInt());
        if (!vehicleEntity)
        {
            Print("[EdenAdminVehicleSystem] Vehicle not found: " + vehicleId);
            return false;
        }
        
        // Remove from tracking
        int index = m_SpawnedAdminVehicles.Find(vehicleId);
        if (index != -1)
            m_SpawnedAdminVehicles.RemoveOrdered(index);
        
        // Delete the vehicle
        SCR_EntityHelper.DeleteEntityAndChildren(vehicleEntity);
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "DeleteVehicle", "", "Deleted vehicle ID: " + vehicleId);
        
        Print("[EdenAdminVehicleSystem] Vehicle deleted: " + vehicleId);
        return true;
    }
    
    //! Delete all admin spawned vehicles
    void DeleteAllAdminVehicles(string adminId)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminVehicleSystem] Access denied - not an admin: " + adminId);
            return;
        }
        
        int deletedCount = 0;
        
        for (int i = m_SpawnedAdminVehicles.Count() - 1; i >= 0; i--)
        {
            string vehicleId = m_SpawnedAdminVehicles[i];
            IEntity vehicleEntity = GetGame().GetWorld().FindEntityByID(vehicleId.ToInt());
            
            if (vehicleEntity)
            {
                SCR_EntityHelper.DeleteEntityAndChildren(vehicleEntity);
                deletedCount++;
            }
            
            m_SpawnedAdminVehicles.RemoveOrdered(i);
        }
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "DeleteAllAdminVehicles", "", "Deleted " + deletedCount + " admin vehicles");
        
        Print("[EdenAdminVehicleSystem] Deleted " + deletedCount + " admin vehicles");
    }
    
    //! Repair vehicle
    bool RepairVehicle(string adminId, string vehicleId)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminVehicleSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        IEntity vehicleEntity = GetGame().GetWorld().FindEntityByID(vehicleId.ToInt());
        if (!vehicleEntity)
        {
            Print("[EdenAdminVehicleSystem] Vehicle not found: " + vehicleId);
            return false;
        }
        
        // Get damage manager and repair
        SCR_DamageManagerComponent damageManager = SCR_DamageManagerComponent.Cast(vehicleEntity.FindComponent(SCR_DamageManagerComponent));
        if (damageManager)
        {
            damageManager.SetHealth(damageManager.GetMaxHealth());
        }
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "RepairVehicle", "", "Repaired vehicle ID: " + vehicleId);
        
        Print("[EdenAdminVehicleSystem] Vehicle repaired: " + vehicleId);
        return true;
    }
    
    //! Flip vehicle
    bool FlipVehicle(string adminId, string vehicleId)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminVehicleSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        IEntity vehicleEntity = GetGame().GetWorld().FindEntityByID(vehicleId.ToInt());
        if (!vehicleEntity)
        {
            Print("[EdenAdminVehicleSystem] Vehicle not found: " + vehicleId);
            return false;
        }
        
        // Get current position and flip the vehicle
        vector currentPos = vehicleEntity.GetOrigin();
        vector angles = Vector(0, vehicleEntity.GetYawPitchRoll()[0], 0);
        
        vehicleEntity.SetOrigin(currentPos + Vector(0, 2, 0)); // Lift slightly
        vehicleEntity.SetYawPitchRoll(angles);
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "FlipVehicle", "", "Flipped vehicle ID: " + vehicleId);
        
        Print("[EdenAdminVehicleSystem] Vehicle flipped: " + vehicleId);
        return true;
    }
    
    //! Get available vehicle categories
    array<string> GetVehicleCategories()
    {
        array<string> categories = new array<string>();
        
        for (int i = 0; i < m_VehicleCategories.Count(); i++)
        {
            categories.Insert(m_VehicleCategories.GetKey(i));
        }
        
        return categories;
    }
    
    //! Get vehicles in category
    array<string> GetVehiclesInCategory(string category)
    {
        if (!m_VehicleCategories.Contains(category))
            return new array<string>();
        
        return m_VehicleCategories.Get(category);
    }
    
    //! Check if vehicle class is valid
    protected bool IsValidVehicleClass(string vehicleClass)
    {
        for (int i = 0; i < m_VehicleCategories.Count(); i++)
        {
            ref array<string> vehicles = m_VehicleCategories.GetElement(i);
            if (vehicles.Find(vehicleClass) != -1)
                return true;
        }
        
        return false;
    }
    
    //! Get spawned admin vehicles
    array<string> GetSpawnedAdminVehicles() { return m_SpawnedAdminVehicles; }
    
    //! Cleanup
    void Cleanup()
    {
        // Delete all admin spawned vehicles
        for (int i = 0; i < m_SpawnedAdminVehicles.Count(); i++)
        {
            string vehicleId = m_SpawnedAdminVehicles[i];
            IEntity vehicleEntity = GetGame().GetWorld().FindEntityByID(vehicleId.ToInt());
            
            if (vehicleEntity)
                SCR_EntityHelper.DeleteEntityAndChildren(vehicleEntity);
        }
        
        m_SpawnedAdminVehicles.Clear();
        m_VehicleCategories.Clear();
        
        Print("[EdenAdminVehicleSystem] Admin vehicle system cleaned up");
    }
}
