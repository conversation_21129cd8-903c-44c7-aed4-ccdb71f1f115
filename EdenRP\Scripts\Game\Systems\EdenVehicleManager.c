//! Eden Vehicle Manager - Handles all vehicle systems including garages, impound, chop shops, and ownership
//! Converted from original vehicle systems

class EdenVehicleManager
{
    protected ref map<string, ref EdenVehicleData> m_PlayerVehicles;
    protected ref map<string, ref EdenVehicleData> m_GangVehicles;
    protected ref map<string, ref EdenGarageLocation> m_GarageLocations;
    protected ref map<string, ref EdenChopShopLocation> m_ChopShopLocations;
    protected ref array<ref EdenVehicleData> m_ImpoundedVehicles;
    protected ref map<string, ref array<IEntity>> m_PlayerKeys; // Player ID -> Vehicle entities
    protected ref map<string, int> m_VehiclePrices;
    
    // Vehicle configuration
    protected int m_ImpoundCarPrice;
    protected int m_ImpoundBoatPrice;
    protected int m_ImpoundAirPrice;
    protected float m_ChopShopMultiplier;
    protected bool m_VehicleSystemEnabled;
    protected int m_MaxVehiclesPerPlayer;
    
    void EdenVehicleManager()
    {
        m_PlayerVehicles = new map<string, ref EdenVehicleData>();
        m_GangVehicles = new map<string, ref EdenVehicleData>();
        m_GarageLocations = new map<string, ref EdenGarageLocation>();
        m_ChopShopLocations = new map<string, ref EdenChopShopLocation>();
        m_ImpoundedVehicles = new array<ref EdenVehicleData>();
        m_PlayerKeys = new map<string, ref array<IEntity>>();
        m_VehiclePrices = new map<string, int>();
        
        m_ImpoundCarPrice = 1500;
        m_ImpoundBoatPrice = 2500;
        m_ImpoundAirPrice = 5000;
        m_ChopShopMultiplier = 0.4;
        m_VehicleSystemEnabled = true;
        m_MaxVehiclesPerPlayer = 15;
        
        InitializeVehicleSystem();
    }
    
    //! Initialize vehicle system
    void Initialize()
    {
        Print("[EdenVehicleManager] Initializing vehicle system...");
        
        SetupGarageLocations();
        SetupChopShopLocations();
        SetupVehiclePrices();
        LoadVehicleData();
        
        // Set up periodic cleanup
        GetGame().GetCallqueue().CallLater(CleanupDestroyedVehicles, 300000, true); // 5 minutes
        GetGame().GetCallqueue().CallLater(ProcessVehicleInsurance, 60000, true); // 1 minute
        
        Print("[EdenVehicleManager] Vehicle system initialized");
    }
    
    //! Initialize vehicle system configuration
    protected void InitializeVehicleSystem()
    {
        Print("[EdenVehicleManager] Vehicle system configuration initialized");
    }
    
    //! Setup garage locations
    protected void SetupGarageLocations()
    {
        // Main city garages
        SetupGarage("kavala_garage", "Kavala Garage", "0 0 0", "Car");
        SetupGarage("kavala_air_garage", "Kavala Air Garage", "0 0 0", "Air");
        SetupGarage("kavala_boat_garage", "Kavala Boat Garage", "0 0 0", "Ship");
        
        // Additional garages would be added here
        Print(string.Format("[EdenVehicleManager] Set up %1 garage locations", m_GarageLocations.Count()));
    }
    
    //! Setup individual garage
    protected void SetupGarage(string garageId, string garageName, string position, string vehicleType)
    {
        EdenGarageLocation garage = new EdenGarageLocation();
        garage.SetGarageId(garageId);
        garage.SetGarageName(garageName);
        garage.SetPosition(position.ToVector());
        garage.SetVehicleType(vehicleType);
        garage.SetIsActive(true);
        garage.SetSpawnPosition(position.ToVector());
        garage.SetSpawnDirection(0);
        
        m_GarageLocations.Set(garageId, garage);
    }
    
    //! Setup chop shop locations
    protected void SetupChopShopLocations()
    {
        SetupChopShop("blackwater_chop", "Blackwater Chop Shop", "0 0 0", true);
        
        Print(string.Format("[EdenVehicleManager] Set up %1 chop shop locations", m_ChopShopLocations.Count()));
    }
    
    //! Setup individual chop shop
    protected void SetupChopShop(string chopShopId, string chopShopName, string position, bool isActive)
    {
        EdenChopShopLocation chopShop = new EdenChopShopLocation();
        chopShop.SetChopShopId(chopShopId);
        chopShop.SetChopShopName(chopShopName);
        chopShop.SetPosition(position.ToVector());
        chopShop.SetIsActive(isActive);
        chopShop.SetInteractionRadius(25.0);
        
        m_ChopShopLocations.Set(chopShopId, chopShop);
    }
    
    //! Setup vehicle prices
    protected void SetupVehiclePrices()
    {
        // Car prices
        m_VehiclePrices.Set("C_Hatchback_01_F", 9500);
        m_VehiclePrices.Set("C_Hatchback_01_sport_F", 12500);
        m_VehiclePrices.Set("C_SUV_01_F", 15000);
        m_VehiclePrices.Set("C_Van_01_transport_F", 25000);
        m_VehiclePrices.Set("I_Truck_02_transport_F", 49800);
        m_VehiclePrices.Set("I_Truck_02_covered_F", 62000);
        m_VehiclePrices.Set("B_Truck_01_transport_F", 135000);
        m_VehiclePrices.Set("O_Truck_03_transport_F", 200000);
        
        // Boat prices
        m_VehiclePrices.Set("C_Boat_Civil_01_F", 6500);
        m_VehiclePrices.Set("C_Rubberboat", 4500);
        m_VehiclePrices.Set("I_Boat_Transport_01_F", 850000);
        m_VehiclePrices.Set("B_Boat_Transport_01_F", 1275000);
        
        // Aircraft prices
        m_VehiclePrices.Set("B_Heli_Light_01_F", 253000);
        m_VehiclePrices.Set("O_Heli_Light_02_unarmed_F", 750000);
        m_VehiclePrices.Set("I_Heli_light_03_unarmed_F", 1200000);
        m_VehiclePrices.Set("I_Heli_Transport_02_F", 2000000);
        m_VehiclePrices.Set("O_Heli_Transport_04_F", 3000000);
        
        Print(string.Format("[EdenVehicleManager] Set up %1 vehicle prices", m_VehiclePrices.Count()));
    }
    
    //! Purchase vehicle
    bool PurchaseVehicle(string playerId, string vehicleClass, string garageId, int gangId = 0)
    {
        if (playerId == "" || vehicleClass == "" || garageId == "")
            return false;
            
        if (!m_VehicleSystemEnabled)
            return false;
            
        // Check if garage exists
        if (!m_GarageLocations.Contains(garageId))
            return false;
            
        EdenGarageLocation garage = m_GarageLocations.Get(garageId);
        if (!garage.IsActive())
            return false;
            
        // Get vehicle price
        if (!m_VehiclePrices.Contains(vehicleClass))
            return false;
            
        int vehiclePrice = m_VehiclePrices.Get(vehicleClass);
        
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player has enough money
        if (vehiclePrice > playerComp.GetBankAccount())
            return false;
            
        // Check vehicle limit
        if (GetPlayerVehicleCount(playerId) >= m_MaxVehiclesPerPlayer)
            return false;
            
        // Generate unique plate
        string plate = GenerateVehiclePlate();
        
        // Create vehicle data
        EdenVehicleData vehicleData = new EdenVehicleData();
        vehicleData.SetVehicleId(GenerateVehicleId());
        vehicleData.SetOwnerId(playerId);
        vehicleData.SetOwnerName(playerComp.GetPlayerName());
        vehicleData.SetVehicleClass(vehicleClass);
        vehicleData.SetVehicleType(garage.GetVehicleType());
        vehicleData.SetPlate(plate);
        vehicleData.SetIsAlive(true);
        vehicleData.SetIsActive(false); // In garage
        vehicleData.SetIsInsured(0); // No insurance
        vehicleData.SetGangId(gangId);
        vehicleData.SetPurchaseTime(GetGame().GetWorld().GetWorldTime());
        vehicleData.SetColor("[\"Default\",0]");
        vehicleData.SetModifications("[0,0,0,0,0,0,0,0]");
        vehicleData.SetInventory("[]");
        
        // Process purchase
        playerComp.RemoveBankMoney(vehiclePrice);
        
        // Store vehicle
        if (gangId > 0)
        {
            m_GangVehicles.Set(vehicleData.GetVehicleId(), vehicleData);
        }
        else
        {
            m_PlayerVehicles.Set(vehicleData.GetVehicleId(), vehicleData);
        }
        
        // Save to database
        SaveVehicleToDatabase(vehicleData);
        
        Print(string.Format("[EdenVehicleManager] %1 purchased %2 for $%3", playerId, vehicleClass, vehiclePrice));
        return true;
    }
    
    //! Spawn vehicle from garage
    bool SpawnVehicle(string playerId, string vehicleId, string garageId)
    {
        if (playerId == "" || vehicleId == "" || garageId == "")
            return false;
            
        // Check if garage exists
        if (!m_GarageLocations.Contains(garageId))
            return false;
            
        EdenGarageLocation garage = m_GarageLocations.Get(garageId);
        if (!garage.IsActive())
            return false;
            
        // Get vehicle data
        EdenVehicleData vehicleData = GetVehicleData(vehicleId);
        if (!vehicleData)
            return false;
            
        // Check ownership
        if (vehicleData.GetOwnerId() != playerId && vehicleData.GetGangId() == 0)
            return false;
            
        // Check if vehicle is available (not active)
        if (vehicleData.IsActive())
            return false;
            
        if (!vehicleData.IsAlive())
            return false;
            
        // Get player entity
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        // Check spawn area
        vector spawnPos = garage.GetSpawnPosition();
        if (!IsSpawnAreaClear(spawnPos))
            return false;
            
        // Create vehicle entity
        IEntity vehicleEntity = CreateVehicleEntity(vehicleData, spawnPos, garage.GetSpawnDirection());
        if (!vehicleEntity)
            return false;
            
        // Set vehicle as active
        vehicleData.SetIsActive(true);
        vehicleData.SetLastSpawnTime(GetGame().GetWorld().GetWorldTime());
        
        // Give keys to player
        GiveVehicleKeys(playerId, vehicleEntity);
        
        // Apply modifications and color
        ApplyVehicleModifications(vehicleEntity, vehicleData);
        ApplyVehicleColor(vehicleEntity, vehicleData);
        
        // Update database
        UpdateVehicleInDatabase(vehicleData);
        
        Print(string.Format("[EdenVehicleManager] %1 spawned vehicle %2", playerId, vehicleId));
        return true;
    }
    
    //! Store vehicle in garage
    bool StoreVehicle(string playerId, IEntity vehicleEntity, bool isImpound = false)
    {
        if (playerId == "" || !vehicleEntity)
            return false;
            
        // Get vehicle data from entity
        EdenVehicleData vehicleData = GetVehicleDataFromEntity(vehicleEntity);
        if (!vehicleData)
            return false;
            
        // Check ownership for non-impound
        if (!isImpound && vehicleData.GetOwnerId() != playerId)
            return false;
            
        // Update vehicle data
        vehicleData.SetIsActive(false);
        vehicleData.SetLastStoreTime(GetGame().GetWorld().GetWorldTime());
        
        // Save inventory and modifications
        SaveVehicleInventory(vehicleEntity, vehicleData);
        SaveVehicleModifications(vehicleEntity, vehicleData);
        
        // Remove from world
        GetGame().GetWorld().DeleteEntity(vehicleEntity);
        
        // Update database
        if (isImpound)
        {
            vehicleData.SetIsImpounded(true);
            vehicleData.SetImpoundTime(GetGame().GetWorld().GetWorldTime());
            m_ImpoundedVehicles.Insert(vehicleData);
        }
        
        UpdateVehicleInDatabase(vehicleData);
        
        Print(string.Format("[EdenVehicleManager] Vehicle %1 stored (%2)", vehicleData.GetVehicleId(), isImpound ? "impounded" : "garage"));
        return true;
    }
    
    //! Chop vehicle at chop shop
    bool ChopVehicle(string playerId, IEntity vehicleEntity, string chopShopId)
    {
        if (playerId == "" || !vehicleEntity || chopShopId == "")
            return false;
            
        // Check if chop shop exists and is active
        if (!m_ChopShopLocations.Contains(chopShopId))
            return false;
            
        EdenChopShopLocation chopShop = m_ChopShopLocations.Get(chopShopId);
        if (!chopShop.IsActive())
            return false;
            
        // Check if player is in range
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        if (vector.Distance(playerEntity.GetOrigin(), chopShop.GetPosition()) > chopShop.GetInteractionRadius())
            return false;
            
        // Get vehicle data
        EdenVehicleData vehicleData = GetVehicleDataFromEntity(vehicleEntity);
        if (!vehicleData)
            return false;
            
        // Calculate chop price
        string vehicleClass = vehicleData.GetVehicleClass();
        if (!m_VehiclePrices.Contains(vehicleClass))
            return false;
            
        int basePrice = m_VehiclePrices.Get(vehicleClass);
        int chopPrice = Math.Floor(basePrice * m_ChopShopMultiplier);
        
        // Get player component
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Process chop
        playerComp.AddCash(chopPrice);
        
        // Handle insurance
        if (vehicleData.GetInsuranceLevel() == 0)
        {
            // Uninsured - vehicle is destroyed permanently
            vehicleData.SetIsAlive(false);
            UpdateVehicleInDatabase(vehicleData);
        }
        else
        {
            // Insured - reset vehicle to garage
            vehicleData.SetIsActive(false);
            vehicleData.SetIsInsured(0); // Remove insurance
            vehicleData.SetModifications("[0,0,0,0,0,0,0,0]"); // Reset mods
            vehicleData.SetInventory("[]"); // Clear inventory
            vehicleData.SetColor("[\"Default\",0]"); // Reset color
            UpdateVehicleInDatabase(vehicleData);
        }
        
        // Remove vehicle from world
        GetGame().GetWorld().DeleteEntity(vehicleEntity);
        
        // Log transaction
        LogChopShopTransaction(playerId, vehicleClass, chopPrice);
        
        Print(string.Format("[EdenVehicleManager] %1 chopped %2 for $%3", playerId, vehicleClass, chopPrice));
        return true;
    }
    
    //! Claim abandoned vehicle
    bool ClaimVehicle(string playerId, IEntity vehicleEntity)
    {
        if (playerId == "" || !vehicleEntity)
            return false;
            
        // Check if vehicle can be claimed (no owner data)
        EdenVehicleData vehicleData = GetVehicleDataFromEntity(vehicleEntity);
        if (vehicleData)
            return false; // Already owned
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check vehicle limit
        if (GetPlayerVehicleCount(playerId) >= m_MaxVehiclesPerPlayer)
            return false;
            
        // Create new vehicle data
        string vehicleClass = vehicleEntity.GetPrefabData().GetPrefabName();
        string plate = GenerateVehiclePlate();
        
        EdenVehicleData newVehicleData = new EdenVehicleData();
        newVehicleData.SetVehicleId(GenerateVehicleId());
        newVehicleData.SetOwnerId(playerId);
        newVehicleData.SetOwnerName(playerComp.GetPlayerName());
        newVehicleData.SetVehicleClass(vehicleClass);
        newVehicleData.SetVehicleType(GetVehicleTypeFromClass(vehicleClass));
        newVehicleData.SetPlate(plate);
        newVehicleData.SetIsAlive(true);
        newVehicleData.SetIsActive(true); // Currently spawned
        newVehicleData.SetIsInsured(0);
        newVehicleData.SetGangId(0);
        newVehicleData.SetPurchaseTime(GetGame().GetWorld().GetWorldTime());
        newVehicleData.SetColor("[\"Default\",0]");
        newVehicleData.SetModifications("[0,0,0,0,0,0,0,0]");
        newVehicleData.SetInventory("[]");
        
        // Store vehicle
        m_PlayerVehicles.Set(newVehicleData.GetVehicleId(), newVehicleData);
        
        // Give keys to player
        GiveVehicleKeys(playerId, vehicleEntity);
        
        // Save to database
        SaveVehicleToDatabase(newVehicleData);
        
        Print(string.Format("[EdenVehicleManager] %1 claimed vehicle %2", playerId, vehicleClass));
        return true;
    }
    
    //! Get player vehicles
    array<ref EdenVehicleData> GetPlayerVehicles(string playerId, string vehicleType = "")
    {
        array<ref EdenVehicleData> playerVehicles = {};
        
        foreach (string vehicleId, EdenVehicleData vehicleData : m_PlayerVehicles)
        {
            if (vehicleData.GetOwnerId() == playerId && vehicleData.IsAlive())
            {
                if (vehicleType == "" || vehicleData.GetVehicleType() == vehicleType)
                {
                    playerVehicles.Insert(vehicleData);
                }
            }
        }
        
        return playerVehicles;
    }
    
    //! Get gang vehicles
    array<ref EdenVehicleData> GetGangVehicles(int gangId, string vehicleType = "")
    {
        array<ref EdenVehicleData> gangVehicles = {};
        
        foreach (string vehicleId, EdenVehicleData vehicleData : m_GangVehicles)
        {
            if (vehicleData.GetGangId() == gangId && vehicleData.IsAlive())
            {
                if (vehicleType == "" || vehicleData.GetVehicleType() == vehicleType)
                {
                    gangVehicles.Insert(vehicleData);
                }
            }
        }
        
        return gangVehicles;
    }
    
    //! Get impounded vehicles
    array<ref EdenVehicleData> GetImpoundedVehicles(string playerId = "")
    {
        array<ref EdenVehicleData> impoundedVehicles = {};
        
        foreach (EdenVehicleData vehicleData : m_ImpoundedVehicles)
        {
            if (playerId == "" || vehicleData.GetOwnerId() == playerId)
            {
                impoundedVehicles.Insert(vehicleData);
            }
        }
        
        return impoundedVehicles;
    }
    
    //! Pay impound fee
    bool PayImpoundFee(string playerId, string vehicleId)
    {
        if (playerId == "" || vehicleId == "")
            return false;
            
        // Find impounded vehicle
        EdenVehicleData vehicleData = null;
        int impoundIndex = -1;
        
        for (int i = 0; i < m_ImpoundedVehicles.Count(); i++)
        {
            if (m_ImpoundedVehicles[i].GetVehicleId() == vehicleId && m_ImpoundedVehicles[i].GetOwnerId() == playerId)
            {
                vehicleData = m_ImpoundedVehicles[i];
                impoundIndex = i;
                break;
            }
        }
        
        if (!vehicleData)
            return false;
            
        // Calculate impound fee
        int impoundFee = CalculateImpoundFee(vehicleData.GetVehicleType());
        
        // Get player component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player has enough money
        if (impoundFee > playerComp.GetBankAccount())
            return false;
            
        // Process payment
        playerComp.RemoveBankMoney(impoundFee);
        
        // Remove from impound
        vehicleData.SetIsImpounded(false);
        vehicleData.SetImpoundTime(0);
        m_ImpoundedVehicles.Remove(impoundIndex);
        
        // Update database
        UpdateVehicleInDatabase(vehicleData);
        
        Print(string.Format("[EdenVehicleManager] %1 paid impound fee $%2 for vehicle %3", playerId, impoundFee, vehicleId));
        return true;
    }
    
    //! Give vehicle keys
    void GiveVehicleKeys(string playerId, IEntity vehicleEntity)
    {
        if (playerId == "" || !vehicleEntity)
            return;
            
        if (!m_PlayerKeys.Contains(playerId))
        {
            m_PlayerKeys.Set(playerId, new array<IEntity>());
        }
        
        array<IEntity> playerKeys = m_PlayerKeys.Get(playerId);
        if (playerKeys.Find(vehicleEntity) == -1)
        {
            playerKeys.Insert(vehicleEntity);
        }
    }
    
    //! Check if player has keys
    bool PlayerHasKeys(string playerId, IEntity vehicleEntity)
    {
        if (playerId == "" || !vehicleEntity)
            return false;
            
        if (!m_PlayerKeys.Contains(playerId))
            return false;
            
        array<IEntity> playerKeys = m_PlayerKeys.Get(playerId);
        return playerKeys.Find(vehicleEntity) != -1;
    }
    
    //! Helper methods
    protected int GetPlayerVehicleCount(string playerId)
    {
        int count = 0;
        foreach (string vehicleId, EdenVehicleData vehicleData : m_PlayerVehicles)
        {
            if (vehicleData.GetOwnerId() == playerId && vehicleData.IsAlive())
                count++;
        }
        return count;
    }
    
    protected int CalculateImpoundFee(string vehicleType)
    {
        switch (vehicleType)
        {
            case "Car": return m_ImpoundCarPrice;
            case "Ship": return m_ImpoundBoatPrice;
            case "Air": return m_ImpoundAirPrice;
            default: return m_ImpoundCarPrice;
        }
    }
    
    protected string GenerateVehiclePlate()
    {
        return string.Format("%1", Math.RandomInt(100000, 999999));
    }
    
    protected string GenerateVehicleId()
    {
        return string.Format("VEH_%1_%2", GetGame().GetWorld().GetWorldTime(), Math.RandomInt(1000, 9999));
    }
    
    protected string GetVehicleTypeFromClass(string vehicleClass)
    {
        // Implementation would determine vehicle type from class name
        if (vehicleClass.Contains("Heli") || vehicleClass.Contains("Plane"))
            return "Air";
        if (vehicleClass.Contains("Boat") || vehicleClass.Contains("Ship"))
            return "Ship";
        return "Car";
    }
    
    protected void LogChopShopTransaction(string playerId, string vehicleClass, int price)
    {
        // Implementation would log to database or file
        Print(string.Format("[EdenVehicleManager] Chop Shop: Player %1, Vehicle %2, Price $%3", playerId, vehicleClass, price));
    }
    
    protected void CleanupDestroyedVehicles()
    {
        // Implementation would clean up destroyed vehicles
        Print("[EdenVehicleManager] Cleaning up destroyed vehicles...");
    }
    
    protected void ProcessVehicleInsurance()
    {
        // Implementation would process insurance claims
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadVehicleData() { }
    protected void SaveVehicleToDatabase(EdenVehicleData vehicleData) { }
    protected void UpdateVehicleInDatabase(EdenVehicleData vehicleData) { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    protected EdenVehicleData GetVehicleData(string vehicleId) { return null; }
    protected EdenVehicleData GetVehicleDataFromEntity(IEntity vehicleEntity) { return null; }
    protected bool IsSpawnAreaClear(vector position) { return true; }
    protected IEntity CreateVehicleEntity(EdenVehicleData vehicleData, vector position, float direction) { return null; }
    protected void ApplyVehicleModifications(IEntity vehicleEntity, EdenVehicleData vehicleData) { }
    protected void ApplyVehicleColor(IEntity vehicleEntity, EdenVehicleData vehicleData) { }
    protected void SaveVehicleInventory(IEntity vehicleEntity, EdenVehicleData vehicleData) { }
    protected void SaveVehicleModifications(IEntity vehicleEntity, EdenVehicleData vehicleData) { }
    
    //! Public configuration methods
    void SetImpoundPrices(int carPrice, int boatPrice, int airPrice)
    {
        m_ImpoundCarPrice = carPrice;
        m_ImpoundBoatPrice = boatPrice;
        m_ImpoundAirPrice = airPrice;
    }
    
    void SetChopShopMultiplier(float multiplier) { m_ChopShopMultiplier = multiplier; }
    float GetChopShopMultiplier() { return m_ChopShopMultiplier; }
    
    void SetVehicleSystemEnabled(bool enabled) { m_VehicleSystemEnabled = enabled; }
    bool IsVehicleSystemEnabled() { return m_VehicleSystemEnabled; }
    
    void SetMaxVehiclesPerPlayer(int maxVehicles) { m_MaxVehiclesPerPlayer = maxVehicles; }
    int GetMaxVehiclesPerPlayer() { return m_MaxVehiclesPerPlayer; }
    
    //! Get garage locations
    array<ref EdenGarageLocation> GetGarageLocations()
    {
        array<ref EdenGarageLocation> garages = {};
        foreach (string garageId, EdenGarageLocation garage : m_GarageLocations)
        {
            garages.Insert(garage);
        }
        return garages;
    }
    
    //! Get chop shop locations
    array<ref EdenChopShopLocation> GetChopShopLocations()
    {
        array<ref EdenChopShopLocation> chopShops = {};
        foreach (string chopShopId, EdenChopShopLocation chopShop : m_ChopShopLocations)
        {
            chopShops.Insert(chopShop);
        }
        return chopShops;
    }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenVehicleManager] Cleaning up vehicle system...");
        
        m_PlayerVehicles.Clear();
        m_GangVehicles.Clear();
        m_GarageLocations.Clear();
        m_ChopShopLocations.Clear();
        m_ImpoundedVehicles.Clear();
        m_PlayerKeys.Clear();
        m_VehiclePrices.Clear();
    }
}
