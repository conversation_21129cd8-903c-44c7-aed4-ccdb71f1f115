---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-56-27\console.log started at 2025-07-27 20:56:27 (2025-07-28 00:56:27 UTC)

20:56:27.419 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-56-27' to filesystem under name logs
20:56:27.419 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
20:56:27.421 ENGINE       : Initializing engine, version 184702
20:56:27.421 ENGINE       : CLI Params: -config ..\..\..\server.json -profile ..\..\..\saves -addonsDir addons -logLevel 3 
20:56:27.425 ENGINE       : Addon dirs:
20:56:27.425  ENGINE       : dir: 'addons'
20:56:27.425  ENGINE       : dir: './addons'
20:56:27.425  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
20:56:27.425 ENGINE       : Available addons:
20:56:27.425  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
20:56:27.425  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
20:56:27.472 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
20:56:27.474 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
20:56:27.474 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
20:56:27.607 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
20:56:27.609 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
20:56:27.609 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-56-27/resourceDatabase.rdb)
20:56:27.717 ENGINE       : GameProject load
20:56:27.717  ENGINE       : Loaded addons:
20:56:27.717   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
20:56:27.717   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
20:56:27.719 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
20:56:27.719 ENGINE       : No Enfusion settings file loaded. Continuing with defaults
20:56:27.719 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
20:56:27.720 RENDER       : Basic screen setup:
20:56:27.720 RENDER       :   Back buffer width x height: 0x0
20:56:27.720 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
20:56:27.720 RENDER       :   MSAA                      : none
20:56:27.720 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
20:56:27.720 RENDER       : Detected output devices:
20:56:27.720 RENDER       :  * 1 device/s connected to Dummy:
20:56:27.720 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
20:56:27.720 RENDER       :   Concurrent frames limit 2
20:56:27.720 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
20:56:27.775 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
20:56:27.779 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
20:56:27.780 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
20:56:27.780 PROFILING    : Settings changed took: 0.022600 ms
20:56:27.783 ENGINE       : Initializing inputs.
20:56:27.789 NETWORK      : Initializing networking.
20:56:27.789 SCRIPT       : SCRIPT       : Initializing scripts
20:56:27.789  SCRIPT       : SCRIPT       : ScriptProjectManager init
20:56:27.789  PROFILING    : ScriptProjectManager init took: 0.024400 ms
20:56:27.806 PROFILING    : Initializing scripts took: 17.469100 ms
20:56:27.807 ENGINE       : Enfusion engine successfully created.
20:56:27.814 GUI          : Using default language (en_us)
20:56:27.814 GUI          : Loading 'en_us' localization file.
20:56:27.814 INIT         : INIT         : Loading StringTable
20:56:27.837 PROFILING    : Loading StringTable took: 22.603600 ms
20:56:27.837 SCRIPT       : SCRIPT       : Compiling GameLib scripts
20:56:27.862  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
20:56:27.867 PROFILING    : Compiling GameLib scripts took: 29.241100 ms
20:56:27.867 SCRIPT       : SCRIPT       : Compiling Game scripts
20:56:28.356 SCRIPT       : Compiling Game scripts
20:56:28.359  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
20:56:28.395  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
20:56:28.594  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:56:28.594  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:56:28.594  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:56:28.610  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
20:56:28.640  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
20:56:28.716  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
20:56:28.773  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:28.814  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
20:56:28.815  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
20:56:28.885  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
20:56:28.885  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:28.885  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:28.886  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:28.886  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:28.886  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:28.921  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:28.921  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:28.922  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:28.922  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:28.922  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:28.922  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:28.937  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:28.948  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:28.948  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:28.948  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:28.974  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
20:56:28.974  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
20:56:28.978  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:56:28.978  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:56:28.979  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:56:28.980  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
20:56:28.980  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:29.025  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
20:56:29.737 PROFILING    : Compiling Game scripts took: 1870.405300 ms
20:56:29.739 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
20:56:29.752 BACKEND      : Loading dedicated server config.
20:56:29.752  BACKEND      : Server config loaded.
20:56:29.753  BACKEND      : JSON Schema Validation:
20:56:29.753   BACKEND      : JSON is Valid
20:56:29.753 BACKEND      : Loading dedicated server config.
20:56:29.753  RESOURCES    : GetResourceObject @"{F8B2E3A14C5D6E7F}Missions/EdenRP.conf"
20:56:29.753   RESOURCES (E): Failed to open
20:56:29.753  RESOURCES (E): MissionHeader::ReadMissionHeader cannot load the resource 'Missions/EdenRP.conf'!
20:56:29.760 ENGINE       : Game successfully created.
20:56:29.799 PLATFORM     : Save data from container 'settings' have been loaded
20:56:29.899 PLATFORM     : Save data from container 'sessions' have been loaded
20:56:29.899 PLATFORM     : System language differs from the saved one - using new language {new: 'en_us'; old: ''}
20:56:29.900 NETWORK      : Starting dedicated server using command line args.
20:56:31.593 BACKEND   (E): Addon F8B2E3A14C5D6E7F - Addon was not found on workshop.
20:56:31.593 BACKEND   (E): 1 addons are not downloadable! Cannot start until they are removed from server config.
20:56:31.593 BACKEND   (E): Failed to fetch addon details from workshop API! Repeat later or try different mods.
20:56:31.793 ENGINE    (E): Unable to initialize the game
20:56:31.815 ENGINE       : Game destroyed.
20:56:31.833 RPL          : Pip::Destroy
20:56:31.840 RESOURCES (E): ==== Resource leaks ====
20:56:31.840 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
