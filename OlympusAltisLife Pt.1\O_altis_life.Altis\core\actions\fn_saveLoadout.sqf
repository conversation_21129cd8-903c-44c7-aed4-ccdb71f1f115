#include "..\..\macro.h"
//  File: fn_saveLoadout.sqf
//	Author: Fusah
//	Modifications: TheCmdrR<PERSON>, <PERSON><PERSON><PERSON>
//	Description: saves the loadout of the person who wants it saved?

params [
	["_slot",-1,[0]]
];

if(scriptAvailable(15)) exitWith {hint "Please wait at least 15 seconds before attempting to do this action again!";};
if (oev_shopType isEqualTo "") exitWith {hint "Invalid Shop Type";};
if (oev_action_inUse) exitWith {titleText ["You are already performing another action!", "PLAIN DOWN"]};
if (__GETC__(oev_restrictions)) exitWith {hint "You are on restrictions and cannot save loadouts!";};
private _loadout = getUnitLoadout [player,true];
private _validLoadout = [_loadout, oev_shopType] call OEC_fnc_validateGear;
private _invPrice = 0;
private _playerInv = [];
private _copItems = [1] call OEC_fnc_factionItems;
private _medicItems = [2] call OEC_fnc_factionItems;
private _vigiItems = [3] call OEC_fnc_factionItems;
private _rebItems =  [4] call OEC_fnc_factionItems;

private _shopName = switch (oev_shopType) do {
	case "rebel": {"rebel"};
	case "vigilante": {"vigilante"};
	case "cop_basic": {"police"};
	case "med_basic": {"medic"};
	default {"null"};
};

switch (oev_shopType) do {
	case "rebel": {
		{
			_playerInv pushBack [[_x,1] call OEC_fnc_varHandle,missionNameSpace getVariable _x]; // ["Item name", amount]
		} forEach _rebItems;

		{
			_invPrice = _invPrice + ([_x select 0,_x select 1,2] call OEC_fnc_invPrice);
		} forEach _playerInv;
	};
	case "vigilante": {
		{
			_playerInv pushBack [[_x,1] call OEC_fnc_varHandle,missionNameSpace getVariable _x]; // ["Item name", amount]
		} forEach _vigiItems;

		{
			_invPrice = _invPrice + ([_x select 0,_x select 1,2] call OEC_fnc_invPrice);
		} forEach _playerInv;
	};
	case "cop_basic": {
		{
			_playerInv pushBack [[_x,1] call OEC_fnc_varHandle,missionNameSpace getVariable _x]; // ["Item name", amount]
		} forEach _copItems;

		{
			_invPrice = _invPrice + ([_x select 0,_x select 1,2] call OEC_fnc_invPrice);
		} forEach _playerInv;
	};
	case "med_basic": {
		{
			_playerInv pushBack [[_x,1] call OEC_fnc_varHandle,missionNameSpace getVariable _x]; // ["Item name", amount]
		} forEach _medicItems;

		{
			_invPrice = _invPrice + ([_x select 0,_x select 1,2] call OEC_fnc_invPrice);
		} forEach _playerInv;
	};
};

if !(_validLoadout) exitWith {closeDialog 0; hint format ["Please be sure that your loadout only has items from the %1 shop!",_shopName]};

closeDialog 0;
private _action = [
		format ["Are you sure you want to save your current %1 loadout to slot #%2? This will overwrite your currently saved %1 loadout. (If you have one)",_shopName, (_slot+1)],
		"Confirmation",
		"Yes",
		"No"
] call BIS_fnc_guiMessage;
if !(_action) exitWith {};
hint "Sending save request to server...";
[[_loadout,1,player,oev_shopType,[],_playerInv,_invPrice, _slot],"OES_fnc_handleLoadouts",false,false] spawn OEC_fnc_MP;
