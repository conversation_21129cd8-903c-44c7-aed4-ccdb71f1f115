//! Eden Civilian Manager - Handles all civilian systems and activities
//! Converted from original civilian systems

class EdenCivilianManager
{
    protected ref map<string, ref EdenJobData> m_ActiveJobs;
    protected ref map<string, ref EdenLicenseData> m_PlayerLicenses;
    protected ref map<string, ref EdenProcessingData> m_ActiveProcessing;
    protected ref array<ref EdenJobLocation> m_JobLocations;
    protected ref array<ref EdenProcessingLocation> m_ProcessingLocations;
    
    // Job configuration
    protected ref map<string, ref EdenJobConfig> m_JobConfigs;
    protected ref map<string, int> m_LicensePrices;
    protected ref array<string> m_AvailableLicenses;
    
    // Civilian interaction systems
    protected ref map<string, ref EdenRobberyData> m_ActiveRobberies;
    protected ref map<string, bool> m_VigilanteStatus;
    
    void EdenCivilianManager()
    {
        m_ActiveJobs = new map<string, ref EdenJobData>();
        m_PlayerLicenses = new map<string, ref EdenLicenseData>();
        m_ActiveProcessing = new map<string, ref EdenProcessingData>();
        m_JobLocations = new array<ref EdenJobLocation>();
        m_ProcessingLocations = new array<ref EdenProcessingLocation>();
        
        m_JobConfigs = new map<string, ref EdenJobConfig>();
        m_LicensePrices = new map<string, int>();
        m_AvailableLicenses = new array<string>();
        
        m_ActiveRobberies = new map<string, ref EdenRobberyData>();
        m_VigilanteStatus = new map<string, bool>();
        
        InitializeJobConfigs();
        InitializeLicenseSystem();
    }
    
    //! Initialize civilian systems
    void Initialize()
    {
        Print("[EdenCivilianManager] Initializing civilian systems...");
        
        SetupJobLocations();
        SetupProcessingLocations();
        
        // Set up periodic updates
        GetGame().GetCallqueue().CallLater(UpdateActiveJobs, 10000, true); // Every 10 seconds
        GetGame().GetCallqueue().CallLater(UpdateProcessing, 5000, true); // Every 5 seconds
        GetGame().GetCallqueue().CallLater(UpdateRobberies, 15000, true); // Every 15 seconds
        
        Print("[EdenCivilianManager] Civilian systems initialized");
    }
    
    //! Initialize job configurations
    protected void InitializeJobConfigs()
    {
        // Mining jobs
        EdenJobConfig miningConfig = new EdenJobConfig();
        miningConfig.SetJobName("Mining");
        miningConfig.SetJobType("gathering");
        miningConfig.SetRequiredTool("pickaxe");
        miningConfig.SetBasePayment(150);
        miningConfig.SetExperienceGain(2);
        miningConfig.SetProcessingTime(3.0);
        miningConfig.AddResource("copperore", 2);
        miningConfig.AddResource("ironore", 2);
        miningConfig.AddResource("salt", 4);
        miningConfig.AddResource("sand", 5);
        miningConfig.AddResource("diamond", 1);
        miningConfig.AddResource("oilu", 1);
        miningConfig.AddResource("rock", 2);
        m_JobConfigs.Set("mining", miningConfig);
        
        // Fishing jobs
        EdenJobConfig fishingConfig = new EdenJobConfig();
        fishingConfig.SetJobName("Fishing");
        fishingConfig.SetJobType("gathering");
        fishingConfig.SetRequiredTool("fishingrod");
        fishingConfig.SetBasePayment(100);
        fishingConfig.SetExperienceGain(1);
        fishingConfig.SetProcessingTime(2.5);
        fishingConfig.AddResource("salema", 1);
        fishingConfig.AddResource("ornate", 1);
        fishingConfig.AddResource("mackerel", 1);
        fishingConfig.AddResource("tuna", 1);
        fishingConfig.AddResource("mullet", 1);
        fishingConfig.AddResource("catshark", 1);
        m_JobConfigs.Set("fishing", fishingConfig);
        
        // Trucking jobs
        EdenJobConfig truckingConfig = new EdenJobConfig();
        truckingConfig.SetJobName("Trucking");
        truckingConfig.SetJobType("transport");
        truckingConfig.SetRequiredLicense("truck");
        truckingConfig.SetBasePayment(500);
        truckingConfig.SetExperienceGain(5);
        truckingConfig.SetProcessingTime(0.0);
        m_JobConfigs.Set("trucking", truckingConfig);
        
        // Oil drilling
        EdenJobConfig oilConfig = new EdenJobConfig();
        oilConfig.SetJobName("Oil Drilling");
        oilConfig.SetJobType("gathering");
        oilConfig.SetRequiredTool("pickaxe");
        oilConfig.SetRequiredLicense("oil");
        oilConfig.SetBasePayment(200);
        oilConfig.SetExperienceGain(3);
        oilConfig.SetProcessingTime(4.0);
        oilConfig.AddResource("oilu", 1);
        m_JobConfigs.Set("oil", oilConfig);
        
        Print(string.Format("[EdenCivilianManager] Initialized %1 job configurations", m_JobConfigs.Count()));
    }
    
    //! Initialize license system
    protected void InitializeLicenseSystem()
    {
        // Basic licenses
        m_LicensePrices.Set("driver", 10000);
        m_LicensePrices.Set("boat", 5000);
        m_LicensePrices.Set("pilot", 50000);
        m_LicensePrices.Set("gun", 25000);
        m_LicensePrices.Set("wpl", 50000); // Workers Protection License
        m_LicensePrices.Set("dive", 3000);
        m_LicensePrices.Set("truck", 20000);
        
        // Processing licenses
        m_LicensePrices.Set("oil", 10000);
        m_LicensePrices.Set("diamond", 35000);
        m_LicensePrices.Set("salt", 12000);
        m_LicensePrices.Set("sand", 14500);
        m_LicensePrices.Set("iron", 9500);
        m_LicensePrices.Set("copper", 8000);
        m_LicensePrices.Set("cement", 6500);
        m_LicensePrices.Set("platinum", 10000);
        m_LicensePrices.Set("silver", 9000);
        
        // Special licenses
        m_LicensePrices.Set("rebel", 75000);
        m_LicensePrices.Set("vigilante", 60000);
        m_LicensePrices.Set("home", 100000);
        
        // Illegal processing licenses
        m_LicensePrices.Set("heroin", 25000);
        m_LicensePrices.Set("marijuana", 17500);
        m_LicensePrices.Set("medmarijuana", 1500);
        m_LicensePrices.Set("cocaine", 30000);
        m_LicensePrices.Set("frog", 24000);
        m_LicensePrices.Set("crystalmeth", 55000);
        m_LicensePrices.Set("moonshine", 54000);
        m_LicensePrices.Set("mushroom", 25000);
        
        // Build available licenses array
        foreach (string licenseType, int price : m_LicensePrices)
        {
            m_AvailableLicenses.Insert(licenseType);
        }
        
        Print(string.Format("[EdenCivilianManager] Initialized %1 license types", m_AvailableLicenses.Count()));
    }
    
    //! Start a job for a player
    bool StartJob(string playerId, string jobType, vector location)
    {
        if (playerId == "" || jobType == "")
            return false;
            
        // Check if player is already working
        if (m_ActiveJobs.Contains(playerId))
            return false;
            
        // Check if job type exists
        if (!m_JobConfigs.Contains(jobType))
            return false;
            
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        EdenJobConfig jobConfig = m_JobConfigs.Get(jobType);
        
        // Check license requirements
        if (jobConfig.GetRequiredLicense() != "")
        {
            if (!HasLicense(playerId, jobConfig.GetRequiredLicense()))
                return false;
        }
        
        // Check tool requirements
        if (jobConfig.GetRequiredTool() != "")
        {
            if (!PlayerHasTool(playerEntity, jobConfig.GetRequiredTool()))
                return false;
        }
        
        // Create job data
        EdenJobData jobData = new EdenJobData();
        jobData.SetPlayerId(playerId);
        jobData.SetJobType(jobType);
        jobData.SetStartTime(GetGame().GetWorld().GetWorldTime());
        jobData.SetLocation(location);
        jobData.SetConfig(jobConfig);
        
        m_ActiveJobs.Set(playerId, jobData);
        
        Print(string.Format("[EdenCivilianManager] Player %1 started %2 job", playerId, jobType));
        return true;
    }
    
    //! Complete a job action
    bool CompleteJobAction(string playerId)
    {
        if (!m_ActiveJobs.Contains(playerId))
            return false;
            
        EdenJobData jobData = m_ActiveJobs.Get(playerId);
        EdenJobConfig jobConfig = jobData.GetConfig();
        
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if enough time has passed
        int currentTime = GetGame().GetWorld().GetWorldTime();
        if (currentTime - jobData.GetLastActionTime() < jobConfig.GetProcessingTime())
            return false;
            
        // Give resources based on job type
        map<string, int> resources = jobConfig.GetResources();
        foreach (string resourceName, int quantity : resources)
        {
            // Add resource to player inventory
            AddResourceToPlayer(playerEntity, resourceName, quantity);
        }
        
        // Give payment
        int payment = CalculateJobPayment(jobData, playerComp);
        playerComp.AddCash(payment);
        
        // Give experience
        int experience = jobConfig.GetExperienceGain();
        playerComp.AddExperience(experience);
        
        // Update job data
        jobData.SetLastActionTime(currentTime);
        jobData.IncrementActionsCompleted();
        
        Print(string.Format("[EdenCivilianManager] Player %1 completed %2 action (Payment: $%3)", 
            playerId, jobData.GetJobType(), payment));
        return true;
    }
    
    //! Stop a job
    void StopJob(string playerId)
    {
        if (m_ActiveJobs.Contains(playerId))
        {
            EdenJobData jobData = m_ActiveJobs.Get(playerId);
            Print(string.Format("[EdenCivilianManager] Player %1 stopped %2 job", playerId, jobData.GetJobType()));
            m_ActiveJobs.Remove(playerId);
        }
    }
    
    //! Buy a license
    bool BuyLicense(string playerId, string licenseType)
    {
        if (!m_LicensePrices.Contains(licenseType))
            return false;
            
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        int price = m_LicensePrices.Get(licenseType);
        
        // Check if player already has license
        if (HasLicense(playerId, licenseType))
            return false;
            
        // Check funds
        int totalMoney = playerComp.GetCash() + playerComp.GetBankAccount();
        if (totalMoney < price)
            return false;
            
        // Handle special license conflicts
        if (!HandleLicenseConflicts(playerId, licenseType))
            return false;
            
        // Deduct money (prefer cash first)
        if (playerComp.GetCash() >= price)
        {
            playerComp.RemoveCash(price);
        }
        else
        {
            int cashAmount = playerComp.GetCash();
            int bankAmount = price - cashAmount;
            playerComp.SetCash(0);
            playerComp.RemoveBankMoney(bankAmount);
        }
        
        // Add license
        AddLicense(playerId, licenseType);
        
        Print(string.Format("[EdenCivilianManager] Player %1 bought %2 license for $%3", playerId, licenseType, price));
        return true;
    }
    
    //! Start processing
    bool StartProcessing(string playerId, string processType, vector location)
    {
        if (playerId == "" || processType == "")
            return false;
            
        // Check if player is already processing
        if (m_ActiveProcessing.Contains(playerId))
            return false;
            
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        // Check if player has required materials
        if (!PlayerHasProcessingMaterials(playerEntity, processType))
            return false;
            
        // Check license requirements for processing
        if (RequiresProcessingLicense(processType))
        {
            if (!HasLicense(playerId, processType))
                return false;
        }
        
        // Create processing data
        EdenProcessingData processingData = new EdenProcessingData();
        processingData.SetPlayerId(playerId);
        processingData.SetProcessType(processType);
        processingData.SetStartTime(GetGame().GetWorld().GetWorldTime());
        processingData.SetLocation(location);
        processingData.SetProcessingTime(GetProcessingTime(processType));
        
        m_ActiveProcessing.Set(playerId, processingData);
        
        Print(string.Format("[EdenCivilianManager] Player %1 started processing %2", playerId, processType));
        return true;
    }
    
    //! Complete processing
    bool CompleteProcessing(string playerId)
    {
        if (!m_ActiveProcessing.Contains(playerId))
            return false;
            
        EdenProcessingData processingData = m_ActiveProcessing.Get(playerId);
        
        // Check if processing time is complete
        int currentTime = GetGame().GetWorld().GetWorldTime();
        if (currentTime - processingData.GetStartTime() < processingData.GetProcessingTime())
            return false;
            
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        // Remove input materials and add output materials
        ProcessMaterials(playerEntity, processingData.GetProcessType());
        
        // Remove from active processing
        m_ActiveProcessing.Remove(playerId);
        
        Print(string.Format("[EdenCivilianManager] Player %1 completed processing %2", playerId, processingData.GetProcessType()));
        return true;
    }
    
    //! Start robbery
    bool StartRobbery(string robberId, string targetId)
    {
        if (robberId == "" || targetId == "" || robberId == targetId)
            return false;
            
        // Check if robbery is already in progress
        if (m_ActiveRobberies.Contains(robberId) || m_ActiveRobberies.Contains(targetId))
            return false;
            
        IEntity robberEntity = GetPlayerEntity(robberId);
        IEntity targetEntity = GetPlayerEntity(targetId);
        
        if (!robberEntity || !targetEntity)
            return false;
            
        // Check distance
        if (vector.Distance(robberEntity.GetOrigin(), targetEntity.GetOrigin()) > 5.0)
            return false;
            
        // Check if target is restrained or vulnerable
        if (!IsPlayerVulnerable(targetId))
            return false;
            
        // Create robbery data
        EdenRobberyData robberyData = new EdenRobberyData();
        robberyData.SetRobberId(robberId);
        robberyData.SetTargetId(targetId);
        robberyData.SetStartTime(GetGame().GetWorld().GetWorldTime());
        robberyData.SetLocation(robberEntity.GetOrigin());
        
        m_ActiveRobberies.Set(robberId, robberyData);
        
        Print(string.Format("[EdenCivilianManager] Player %1 started robbing %2", robberId, targetId));
        return true;
    }
    
    //! Complete robbery
    bool CompleteRobbery(string robberId)
    {
        if (!m_ActiveRobberies.Contains(robberId))
            return false;
            
        EdenRobberyData robberyData = m_ActiveRobberies.Get(robberId);
        
        IEntity robberEntity = GetPlayerEntity(robberId);
        IEntity targetEntity = GetPlayerEntity(robberyData.GetTargetId());
        
        if (!robberEntity || !targetEntity)
            return false;
            
        EdenPlayerComponent robberComp = EdenPlayerComponent.Cast(robberEntity.FindComponent(EdenPlayerComponent));
        EdenPlayerComponent targetComp = EdenPlayerComponent.Cast(targetEntity.FindComponent(EdenPlayerComponent));
        
        if (!robberComp || !targetComp)
            return false;
            
        // Calculate stolen amount (percentage of target's cash)
        int targetCash = targetComp.GetCash();
        int stolenAmount = Math.Min(targetCash, Math.Round(targetCash * 0.75)); // Max 75% of cash
        
        if (stolenAmount > 0)
        {
            targetComp.RemoveCash(stolenAmount);
            robberComp.AddCash(stolenAmount);
        }
        
        // Remove robbery
        m_ActiveRobberies.Remove(robberId);
        
        Print(string.Format("[EdenCivilianManager] Player %1 robbed $%2 from %3", robberId, stolenAmount, robberyData.GetTargetId()));
        return true;
    }
    
    //! Set vigilante status
    void SetVigilanteStatus(string playerId, bool isVigilante)
    {
        m_VigilanteStatus.Set(playerId, isVigilante);
        
        if (isVigilante)
        {
            // Remove conflicting licenses
            RemoveLicense(playerId, "rebel");
            RemoveLicense(playerId, "wpl");
        }
        
        Print(string.Format("[EdenCivilianManager] Player %1 vigilante status: %2", playerId, isVigilante));
    }
    
    //! Update active jobs
    protected void UpdateActiveJobs()
    {
        // Check for expired or invalid jobs
        array<string> toRemove = {};
        
        foreach (string playerId, EdenJobData jobData : m_ActiveJobs)
        {
            IEntity playerEntity = GetPlayerEntity(playerId);
            if (!playerEntity)
            {
                toRemove.Insert(playerId);
                continue;
            }
            
            // Check if player is still at job location
            if (vector.Distance(playerEntity.GetOrigin(), jobData.GetLocation()) > 50.0)
            {
                toRemove.Insert(playerId);
                continue;
            }
        }
        
        foreach (string playerId : toRemove)
        {
            StopJob(playerId);
        }
    }
    
    //! Update processing
    protected void UpdateProcessing()
    {
        array<string> completed = {};
        
        foreach (string playerId, EdenProcessingData processingData : m_ActiveProcessing)
        {
            int currentTime = GetGame().GetWorld().GetWorldTime();
            if (currentTime - processingData.GetStartTime() >= processingData.GetProcessingTime())
            {
                completed.Insert(playerId);
            }
        }
        
        foreach (string playerId : completed)
        {
            CompleteProcessing(playerId);
        }
    }
    
    //! Update robberies
    protected void UpdateRobberies()
    {
        array<string> toRemove = {};
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        foreach (string robberId, EdenRobberyData robberyData : m_ActiveRobberies)
        {
            // Remove robberies older than 2 minutes
            if (currentTime - robberyData.GetStartTime() > 120)
            {
                toRemove.Insert(robberId);
            }
        }
        
        foreach (string robberId : toRemove)
        {
            m_ActiveRobberies.Remove(robberId);
        }
    }
    
    //! Helper methods
    protected IEntity GetPlayerEntity(string playerId)
    {
        // Implementation to get player entity by ID
        return null; // Placeholder
    }
    
    protected bool PlayerHasTool(IEntity playerEntity, string toolName)
    {
        // Check if player has required tool
        return true; // Placeholder
    }
    
    protected void AddResourceToPlayer(IEntity playerEntity, string resourceName, int quantity)
    {
        // Add resource to player inventory
    }
    
    protected int CalculateJobPayment(EdenJobData jobData, EdenPlayerComponent playerComp)
    {
        // Calculate payment based on job config and player stats
        return jobData.GetConfig().GetBasePayment();
    }
    
    protected bool HandleLicenseConflicts(string playerId, string licenseType)
    {
        // Handle special license conflicts (vigilante, rebel, wpl)
        if (licenseType == "vigilante")
        {
            RemoveLicense(playerId, "rebel");
            RemoveLicense(playerId, "wpl");
            SetVigilanteStatus(playerId, true);
        }
        else if (licenseType == "rebel")
        {
            RemoveLicense(playerId, "vigilante");
            RemoveLicense(playerId, "wpl");
            SetVigilanteStatus(playerId, false);
        }
        else if (licenseType == "wpl")
        {
            RemoveLicense(playerId, "vigilante");
            RemoveLicense(playerId, "rebel");
            SetVigilanteStatus(playerId, false);
        }
        
        return true;
    }
    
    protected void AddLicense(string playerId, string licenseType)
    {
        if (!m_PlayerLicenses.Contains(playerId))
        {
            m_PlayerLicenses.Set(playerId, new EdenLicenseData());
        }
        
        EdenLicenseData licenseData = m_PlayerLicenses.Get(playerId);
        licenseData.AddLicense(licenseType);
    }
    
    protected void RemoveLicense(string playerId, string licenseType)
    {
        if (m_PlayerLicenses.Contains(playerId))
        {
            EdenLicenseData licenseData = m_PlayerLicenses.Get(playerId);
            licenseData.RemoveLicense(licenseType);
        }
    }
    
    protected bool PlayerHasProcessingMaterials(IEntity playerEntity, string processType)
    {
        // Check if player has required materials for processing
        return true; // Placeholder
    }
    
    protected bool RequiresProcessingLicense(string processType)
    {
        // Check if process type requires a license
        array<string> licensedProcesses = {"oil", "diamond", "salt", "iron", "copper", "heroin", "marijuana", "cocaine"};
        return licensedProcesses.Find(processType) != -1;
    }
    
    protected int GetProcessingTime(string processType)
    {
        // Return processing time in seconds
        return 30; // Default 30 seconds
    }
    
    protected void ProcessMaterials(IEntity playerEntity, string processType)
    {
        // Remove input materials and add output materials
    }
    
    protected bool IsPlayerVulnerable(string playerId)
    {
        // Check if player can be robbed (restrained, unconscious, etc.)
        return true; // Placeholder
    }
    
    //! Public query methods
    bool HasLicense(string playerId, string licenseType)
    {
        if (m_PlayerLicenses.Contains(playerId))
        {
            EdenLicenseData licenseData = m_PlayerLicenses.Get(playerId);
            return licenseData.HasLicense(licenseType);
        }
        return false;
    }
    
    bool IsPlayerWorking(string playerId)
    {
        return m_ActiveJobs.Contains(playerId);
    }
    
    bool IsPlayerProcessing(string playerId)
    {
        return m_ActiveProcessing.Contains(playerId);
    }
    
    bool IsVigilante(string playerId)
    {
        if (m_VigilanteStatus.Contains(playerId))
            return m_VigilanteStatus.Get(playerId);
        return false;
    }
    
    int GetLicensePrice(string licenseType)
    {
        if (m_LicensePrices.Contains(licenseType))
            return m_LicensePrices.Get(licenseType);
        return 0;
    }
    
    array<string> GetAvailableLicenses() { return m_AvailableLicenses; }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenCivilianManager] Cleaning up civilian systems...");
        
        m_ActiveJobs.Clear();
        m_ActiveProcessing.Clear();
        m_ActiveRobberies.Clear();
        m_PlayerLicenses.Clear();
        m_VigilanteStatus.Clear();
    }
}
