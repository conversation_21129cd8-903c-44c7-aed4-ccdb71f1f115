//! Titles dialog for Eden Reforger
//! Converted from original yMenuTitles.hpp
//! Manages player titles and achievements

class EdenTitlesDialog : EdenBaseDialog
{
    protected Widget m_AvailableTitlesList;
    protected Widget m_UnlockedTitlesList;
    protected Widget m_TitleDetailsPanel;
    protected Widget m_SelectedTitleLabel;
    protected Widget m_TitleDescriptionLabel;
    protected Widget m_RequirementsLabel;
    protected Widget m_ProgressLabel;
    protected Widget m_RewardLabel;
    protected Widget m_CurrentTitleLabel;
    protected Widget m_SetTitleButton;
    protected Widget m_CategoryComboBox;
    protected Widget m_RefreshButton;
    
    protected ref array<ref EdenTitleData> m_AvailableTitles;
    protected ref array<ref EdenTitleData> m_UnlockedTitles;
    protected int m_SelectedTitleIndex;
    protected bool m_ShowingUnlocked;
    protected string m_CurrentCategory;
    protected string m_CurrentPlayerTitle;
    
    //! Constructor
    void EdenTitlesDialog()
    {
        m_AvailableTitles = new array<ref EdenTitleData>();
        m_UnlockedTitles = new array<ref EdenTitleData>();
        m_SelectedTitleIndex = -1;
        m_ShowingUnlocked = false;
        m_CurrentCategory = "All";
        m_CurrentPlayerTitle = "";
    }
    
    //! Initialize the dialog
    override void OnCreate()
    {
        super.OnCreate();
        
        // Get widget references
        m_AvailableTitlesList = m_Root.FindAnyWidget("AvailableTitlesList");
        m_UnlockedTitlesList = m_Root.FindAnyWidget("UnlockedTitlesList");
        m_TitleDetailsPanel = m_Root.FindAnyWidget("TitleDetailsPanel");
        m_SelectedTitleLabel = m_Root.FindAnyWidget("SelectedTitleLabel");
        m_TitleDescriptionLabel = m_Root.FindAnyWidget("TitleDescriptionLabel");
        m_RequirementsLabel = m_Root.FindAnyWidget("RequirementsLabel");
        m_ProgressLabel = m_Root.FindAnyWidget("ProgressLabel");
        m_RewardLabel = m_Root.FindAnyWidget("RewardLabel");
        m_CurrentTitleLabel = m_Root.FindAnyWidget("CurrentTitleLabel");
        m_SetTitleButton = m_Root.FindAnyWidget("SetTitleButton");
        m_CategoryComboBox = m_Root.FindAnyWidget("CategoryComboBox");
        m_RefreshButton = m_Root.FindAnyWidget("RefreshButton");
        
        // Initialize display
        LoadTitleData();
        RefreshTitleDisplay();
        UpdateTitleDetails();
        UpdateCurrentTitle();
    }
    
    //! Get dialog title
    override string GetDialogTitle()
    {
        return "Titles";
    }
    
    //! Get active tab ID
    override int GetActiveTabId()
    {
        return 11; // Titles tab
    }
    
    //! Load title data
    void LoadTitleData()
    {
        EdenGameMode gameMode = EdenGameMode.Cast(GetGame().GetGameMode());
        if (!gameMode)
            return;
            
        EdenDataManager dataManager = gameMode.GetDataManager();
        if (!dataManager)
            return;
            
        // TODO: Load actual title data from data manager
        // For now, create sample data
        CreateSampleTitleData();
    }
    
    //! Create sample title data for testing
    void CreateSampleTitleData()
    {
        m_AvailableTitles.Clear();
        m_UnlockedTitles.Clear();
        
        // Civilian titles
        CreateTitle("Newcomer", "Welcome to Altis Life!", "Civilian", "Join the server", 100, 0, true, false);
        CreateTitle("Entrepreneur", "Master of commerce", "Civilian", "Earn $1,000,000", 75, 1000000, false, false);
        CreateTitle("Millionaire", "Swimming in cash", "Civilian", "Earn $10,000,000", 25, 10000000, false, false);
        CreateTitle("Workaholic", "Always on the grind", "Civilian", "Work 100 hours", 60, 100, false, false);
        
        // Law Enforcement titles
        CreateTitle("Rookie Cop", "Fresh out of the academy", "Police", "Join the police force", 100, 0, true, false);
        CreateTitle("Detective", "Solver of mysteries", "Police", "Solve 50 cases", 40, 50, false, false);
        CreateTitle("Sheriff", "Law and order", "Police", "Reach Police rank 5", 20, 5, false, false);
        CreateTitle("Crime Fighter", "Protector of the innocent", "Police", "Arrest 100 criminals", 30, 100, false, false);
        
        // Medical titles
        CreateTitle("Paramedic", "Saving lives daily", "Medical", "Join the medical service", 100, 0, true, false);
        CreateTitle("Life Saver", "Hero in scrubs", "Medical", "Revive 100 players", 45, 100, false, false);
        CreateTitle("Chief Medical Officer", "Leader of healers", "Medical", "Reach Medical rank 5", 15, 5, false, false);
        
        // Criminal titles
        CreateTitle("Petty Thief", "Small time criminal", "Criminal", "Steal $10,000", 80, 10000, false, false);
        CreateTitle("Drug Dealer", "Pusher of substances", "Criminal", "Sell 100 drug units", 35, 100, false, false);
        CreateTitle("Kingpin", "Master of the underworld", "Criminal", "Control 3 territories", 10, 3, false, false);
        CreateTitle("Most Wanted", "Public enemy #1", "Criminal", "Reach wanted level 5", 5, 5, false, false);
        
        // Special titles
        CreateTitle("Veteran", "Old timer", "Special", "Play for 1000 hours", 25, 1000, false, false);
        CreateTitle("Legend", "Living myth", "Special", "Complete all achievements", 1, 100, false, false);
        
        // Set current player title
        m_CurrentPlayerTitle = "Newcomer";
    }
    
    //! Create a title with specified parameters
    void CreateTitle(string name, string description, string category, string requirement, int progress, int maxProgress, bool isUnlocked, bool isEquipped)
    {
        EdenTitleData title = new EdenTitleData();
        title.SetTitleName(name);
        title.SetDescription(description);
        title.SetCategory(category);
        title.SetRequirement(requirement);
        title.SetProgress(progress);
        title.SetMaxProgress(maxProgress);
        title.SetIsUnlocked(isUnlocked);
        title.SetIsEquipped(isEquipped);
        title.SetUnlockTime(isUnlocked ? GetGame().GetWorld().GetWorldTime() : 0);
        
        if (isUnlocked)
            m_UnlockedTitles.Insert(title);
        else
            m_AvailableTitles.Insert(title);
    }
    
    //! Refresh title display
    void RefreshTitleDisplay()
    {
        if (m_ShowingUnlocked)
        {
            RefreshUnlockedTitlesList();
            if (m_AvailableTitlesList) m_AvailableTitlesList.SetVisible(false);
            if (m_UnlockedTitlesList) m_UnlockedTitlesList.SetVisible(true);
        }
        else
        {
            RefreshAvailableTitlesList();
            if (m_AvailableTitlesList) m_AvailableTitlesList.SetVisible(true);
            if (m_UnlockedTitlesList) m_UnlockedTitlesList.SetVisible(false);
        }
    }
    
    //! Refresh available titles list
    void RefreshAvailableTitlesList()
    {
        if (!m_AvailableTitlesList)
            return;
            
        // Clear existing items
        Widget child = m_AvailableTitlesList.GetChildren();
        while (child)
        {
            Widget next = child.GetSibling();
            child.RemoveFromHierarchy();
            child = next;
        }
        
        // Add available titles
        for (int i = 0; i < m_AvailableTitles.Count(); i++)
        {
            EdenTitleData title = m_AvailableTitles[i];
            if (!title)
                continue;
                
            // Filter by category
            if (m_CurrentCategory != "All" && title.GetCategory() != m_CurrentCategory)
                continue;
                
            string titleText = FormatTitleDisplay(title, i, false);
            
            // Create list item widget
            Widget titleItem = GetGame().GetWorkspace().CreateWidgets("{2EFEA2AF1F38E7F0}UI/layouts/Menus/ContentBrowser/ContentBrowserEntry.layout", m_AvailableTitlesList);
            if (titleItem)
            {
                TextWidget titleLabel = TextWidget.Cast(titleItem.FindAnyWidget("Name"));
                if (titleLabel)
                    titleLabel.SetText(titleText);
            }
        }
    }
    
    //! Refresh unlocked titles list
    void RefreshUnlockedTitlesList()
    {
        if (!m_UnlockedTitlesList)
            return;
            
        // Clear existing items
        Widget child = m_UnlockedTitlesList.GetChildren();
        while (child)
        {
            Widget next = child.GetSibling();
            child.RemoveFromHierarchy();
            child = next;
        }
        
        // Add unlocked titles
        for (int i = 0; i < m_UnlockedTitles.Count(); i++)
        {
            EdenTitleData title = m_UnlockedTitles[i];
            if (!title)
                continue;
                
            // Filter by category
            if (m_CurrentCategory != "All" && title.GetCategory() != m_CurrentCategory)
                continue;
                
            string titleText = FormatTitleDisplay(title, i, true);
            
            // Create list item widget
            Widget titleItem = GetGame().GetWorkspace().CreateWidgets("{2EFEA2AF1F38E7F0}UI/layouts/Menus/ContentBrowser/ContentBrowserEntry.layout", m_UnlockedTitlesList);
            if (titleItem)
            {
                TextWidget titleLabel = TextWidget.Cast(titleItem.FindAnyWidget("Name"));
                if (titleLabel)
                    titleLabel.SetText(titleText);
            }
        }
    }
    
    //! Format title display text
    string FormatTitleDisplay(EdenTitleData title, int index, bool isUnlocked)
    {
        string prefix = isUnlocked ? "✓" : "○";
        string equipped = title.IsEquipped() ? " [EQUIPPED]" : "";
        
        return string.Format("[%1] %2 %3%4", index + 1, prefix, title.GetTitleName(), equipped);
    }
    
    //! Update title details panel
    void UpdateTitleDetails()
    {
        ref array<ref EdenTitleData> currentList = m_ShowingUnlocked ? m_UnlockedTitles : m_AvailableTitles;
        
        if (m_SelectedTitleIndex < 0 || m_SelectedTitleIndex >= currentList.Count())
        {
            if (m_SelectedTitleLabel) m_SelectedTitleLabel.SetText("No title selected");
            if (m_TitleDescriptionLabel) m_TitleDescriptionLabel.SetText("Select a title to view details");
            if (m_RequirementsLabel) m_RequirementsLabel.SetText("Requirements: N/A");
            if (m_ProgressLabel) m_ProgressLabel.SetText("Progress: N/A");
            if (m_RewardLabel) m_RewardLabel.SetText("Reward: N/A");
            return;
        }
        
        EdenTitleData title = currentList[m_SelectedTitleIndex];
        if (!title)
            return;
            
        if (m_SelectedTitleLabel)
            m_SelectedTitleLabel.SetText(string.Format("Title: %1", title.GetTitleName()));
            
        if (m_TitleDescriptionLabel)
            m_TitleDescriptionLabel.SetText(title.GetDescription());
            
        if (m_RequirementsLabel)
            m_RequirementsLabel.SetText(string.Format("Requirements: %1", title.GetRequirement()));
            
        if (m_ProgressLabel)
        {
            if (title.IsUnlocked())
            {
                m_ProgressLabel.SetText("Progress: COMPLETED");
            }
            else
            {
                float progressPercent = 0.0;
                if (title.GetMaxProgress() > 0)
                    progressPercent = (float)title.GetProgress() / (float)title.GetMaxProgress() * 100.0;
                    
                m_ProgressLabel.SetText(string.Format("Progress: %1/%2 (%.1f%%)", 
                    title.GetProgress(), 
                    title.GetMaxProgress(), 
                    progressPercent));
            }
        }
        
        if (m_RewardLabel)
            m_RewardLabel.SetText("Reward: Title unlock + bragging rights");
    }
    
    //! Update current title display
    void UpdateCurrentTitle()
    {
        if (m_CurrentTitleLabel)
            m_CurrentTitleLabel.SetText(string.Format("Current Title: %1", m_CurrentPlayerTitle));
    }
    
    //! Handle widget click events
    override bool OnClick(Widget w, int x, int y, int button)
    {
        if (super.OnClick(w, x, y, button))
            return true;
            
        if (w == m_SetTitleButton)
        {
            OnSetTitleButtonClick();
            return true;
        }
        else if (w == m_CategoryComboBox)
        {
            OnCategoryChanged();
            return true;
        }
        else if (w == m_RefreshButton)
        {
            OnRefreshButtonClick();
            return true;
        }
        
        return false;
    }
    
    //! Handle set title button click
    void OnSetTitleButtonClick()
    {
        if (!m_ShowingUnlocked || m_SelectedTitleIndex < 0 || m_SelectedTitleIndex >= m_UnlockedTitles.Count())
        {
            Print("EdenTitlesDialog: No unlocked title selected");
            return;
        }
        
        EdenTitleData title = m_UnlockedTitles[m_SelectedTitleIndex];
        if (!title)
            return;
            
        // Unequip current title
        for (int i = 0; i < m_UnlockedTitles.Count(); i++)
        {
            if (m_UnlockedTitles[i])
                m_UnlockedTitles[i].SetIsEquipped(false);
        }
        
        // Equip selected title
        title.SetIsEquipped(true);
        m_CurrentPlayerTitle = title.GetTitleName();
        
        RefreshTitleDisplay();
        UpdateCurrentTitle();
        
        Print(string.Format("EdenTitlesDialog: Title set to '%1'", title.GetTitleName()));
    }
    
    //! Handle category change
    void OnCategoryChanged()
    {
        // TODO: Get selected category from combobox
        // Cycle through categories for now
        if (m_CurrentCategory == "All")
            m_CurrentCategory = "Civilian";
        else if (m_CurrentCategory == "Civilian")
            m_CurrentCategory = "Police";
        else if (m_CurrentCategory == "Police")
            m_CurrentCategory = "Medical";
        else if (m_CurrentCategory == "Medical")
            m_CurrentCategory = "Criminal";
        else if (m_CurrentCategory == "Criminal")
            m_CurrentCategory = "Special";
        else
            m_CurrentCategory = "All";
            
        RefreshTitleDisplay();
    }
    
    //! Handle refresh button click
    void OnRefreshButtonClick()
    {
        LoadTitleData();
        RefreshTitleDisplay();
        UpdateTitleDetails();
        UpdateCurrentTitle();
        Print("EdenTitlesDialog: Title data refreshed");
    }
    
    //! Toggle between available and unlocked titles
    void ToggleTitleView()
    {
        m_ShowingUnlocked = !m_ShowingUnlocked;
        m_SelectedTitleIndex = -1;
        RefreshTitleDisplay();
        UpdateTitleDetails();
    }
}
