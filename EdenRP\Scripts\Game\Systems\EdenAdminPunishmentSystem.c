//! Eden Admin Punishment System - Handles player punishments (kicks, bans, warnings)
//! Converted from original admin punishment and ban systems

class EdenAdminPunishmentSystem
{
    protected ref map<string, ref EdenBanData> m_ActiveBans; // Player ID -> Ban Data
    protected ref array<ref EdenPunishmentRecord> m_PunishmentHistory;
    protected ref map<string, ref array<ref EdenWarning>> m_PlayerWarnings; // Player ID -> Warnings
    protected EdenAdminManager m_AdminManager;
    
    // Punishment configuration
    protected bool m_PunishmentSystemEnabled;
    protected int m_MaxWarningsBeforeKick;
    protected int m_MaxKicksBeforeBan;
    protected int m_DefaultBanDuration;
    protected int m_MaxPunishmentRecords;
    protected bool m_LogAllPunishments;
    
    // Ban escalation settings
    protected ref array<int> m_BanEscalationDurations; // Progressive ban durations
    protected int m_BanEscalationResetTime; // Time before escalation resets
    
    void EdenAdminPunishmentSystem()
    {
        m_ActiveBans = new map<string, ref EdenBanData>();
        m_PunishmentHistory = new array<ref EdenPunishmentRecord>();
        m_PlayerWarnings = new map<string, ref array<ref EdenWarning>>();
        
        m_PunishmentSystemEnabled = true;
        m_MaxWarningsBeforeKick = 3;
        m_MaxKicksBeforeBan = 2;
        m_DefaultBanDuration = 3600; // 1 hour
        m_MaxPunishmentRecords = 10000;
        m_LogAllPunishments = true;
        
        // Progressive ban durations: 1h, 6h, 24h, 7d, 30d, permanent
        m_BanEscalationDurations = {3600, 21600, 86400, 604800, 2592000, -1};
        m_BanEscalationResetTime = 2592000; // 30 days
        
        InitializePunishmentSystem();
    }
    
    //! Initialize punishment system
    void Initialize(EdenAdminManager adminManager)
    {
        Print("[EdenAdminPunishmentSystem] Initializing admin punishment system...");
        
        m_AdminManager = adminManager;
        
        LoadActiveBans();
        LoadPunishmentHistory();
        
        // Set up periodic processing
        GetGame().GetCallqueue().CallLater(ProcessBanExpirations, 60000, true); // 1 minute
        GetGame().GetCallqueue().CallLater(ProcessWarningExpirations, 300000, true); // 5 minutes
        GetGame().GetCallqueue().CallLater(SavePunishmentData, 600000, true); // 10 minutes
        
        Print("[EdenAdminPunishmentSystem] Admin punishment system initialized");
    }
    
    //! Initialize punishment system configuration
    protected void InitializePunishmentSystem()
    {
        Print("[EdenAdminPunishmentSystem] Punishment system configuration initialized");
    }
    
    //! Warn player
    bool WarnPlayer(string adminId, string targetId, string reason, int duration = 3600)
    {
        if (!m_PunishmentSystemEnabled || !m_AdminManager)
            return false;
            
        if (!m_AdminManager.HasAdminPermission(adminId, 1))
            return false;
            
        if (adminId == "" || targetId == "" || reason == "")
            return false;
            
        // Create warning
        EdenWarning warning = new EdenWarning();
        warning.SetPlayerId(targetId);
        warning.SetAdminId(adminId);
        warning.SetReason(reason);
        warning.SetTimestamp(GetGame().GetWorld().GetWorldTime());
        warning.SetExpirationTime(GetGame().GetWorld().GetWorldTime() + duration);
        
        // Add to player warnings
        if (!m_PlayerWarnings.Contains(targetId))
        {
            m_PlayerWarnings.Set(targetId, new array<ref EdenWarning>());
        }
        
        array<ref EdenWarning> playerWarnings = m_PlayerWarnings.Get(targetId);
        playerWarnings.Insert(warning);
        
        // Send warning message to player
        string warningMessage = string.Format("WARNING: %1", reason);
        SendMessageToPlayer(targetId, warningMessage);
        
        // Notify admins
        string adminMessage = string.Format("Player %1 warned by %2: %3", targetId, adminId, reason);
        NotifyAdmins(adminMessage);
        
        // Log punishment
        LogPunishment(PunishmentType.WARNING, adminId, targetId, reason, duration);
        
        // Check if player should be kicked for too many warnings
        int activeWarnings = GetActiveWarningCount(targetId);
        if (activeWarnings >= m_MaxWarningsBeforeKick)
        {
            KickPlayer(adminId, targetId, "Too many warnings");
        }
        
        Print(string.Format("[EdenAdminPunishmentSystem] %1 warned %2: %3", adminId, targetId, reason));
        return true;
    }
    
    //! Kick player
    bool KickPlayer(string adminId, string targetId, string reason)
    {
        if (!m_PunishmentSystemEnabled || !m_AdminManager)
            return false;
            
        if (!m_AdminManager.HasAdminPermission(adminId, 1))
            return false;
            
        if (adminId == "" || targetId == "" || reason == "")
            return false;
            
        // Check if target is admin with higher level
        int adminLevel = m_AdminManager.GetAdminLevel(adminId);
        int targetAdminLevel = m_AdminManager.GetAdminLevel(targetId);
        
        if (targetAdminLevel >= adminLevel && adminLevel < 5)
        {
            SendMessageToPlayer(adminId, "Cannot kick player with equal or higher admin level");
            return false;
        }
        
        // Perform kick
        IEntity targetEntity = GetPlayerEntity(targetId);
        if (targetEntity)
        {
            // Send kick message
            string kickMessage = string.Format("You have been kicked: %1", reason);
            SendMessageToPlayer(targetId, kickMessage);
            
            // Disconnect player
            DisconnectPlayer(targetId, reason);
        }
        
        // Notify admins
        string adminMessage = string.Format("Player %1 kicked by %2: %3", targetId, adminId, reason);
        NotifyAdmins(adminMessage);
        
        // Log punishment
        LogPunishment(PunishmentType.KICK, adminId, targetId, reason, 0);
        
        // Check if player should be banned for too many kicks
        int recentKicks = GetRecentKickCount(targetId, 86400); // 24 hours
        if (recentKicks >= m_MaxKicksBeforeBan)
        {
            BanPlayer(adminId, targetId, "Too many kicks", GetEscalatedBanDuration(targetId));
        }
        
        Print(string.Format("[EdenAdminPunishmentSystem] %1 kicked %2: %3", adminId, targetId, reason));
        return true;
    }
    
    //! Ban player
    bool BanPlayer(string adminId, string targetId, string reason, int duration = -1)
    {
        if (!m_PunishmentSystemEnabled || !m_AdminManager)
            return false;
            
        if (!m_AdminManager.HasAdminPermission(adminId, 2))
            return false;
            
        if (adminId == "" || targetId == "" || reason == "")
            return false;
            
        // Check if target is admin with higher level
        int adminLevel = m_AdminManager.GetAdminLevel(adminId);
        int targetAdminLevel = m_AdminManager.GetAdminLevel(targetId);
        
        if (targetAdminLevel >= adminLevel && adminLevel < 5)
        {
            SendMessageToPlayer(adminId, "Cannot ban player with equal or higher admin level");
            return false;
        }
        
        // Use default duration if not specified
        if (duration == -1)
        {
            duration = m_DefaultBanDuration;
        }
        
        // Create ban data
        EdenBanData banData = new EdenBanData();
        banData.SetPlayerId(targetId);
        banData.SetAdminId(adminId);
        banData.SetReason(reason);
        banData.SetBanTime(GetGame().GetWorld().GetWorldTime());
        banData.SetDuration(duration);
        
        if (duration > 0)
        {
            banData.SetExpirationTime(GetGame().GetWorld().GetWorldTime() + duration);
        }
        else
        {
            banData.SetExpirationTime(-1); // Permanent ban
        }
        
        // Add to active bans
        m_ActiveBans.Set(targetId, banData);
        
        // Kick player if online
        IEntity targetEntity = GetPlayerEntity(targetId);
        if (targetEntity)
        {
            string banMessage;
            if (duration > 0)
            {
                banMessage = string.Format("You have been banned for %1 seconds: %2", duration, reason);
            }
            else
            {
                banMessage = string.Format("You have been permanently banned: %1", reason);
            }
            
            SendMessageToPlayer(targetId, banMessage);
            DisconnectPlayer(targetId, reason);
        }
        
        // Notify admins
        string adminMessage;
        if (duration > 0)
        {
            adminMessage = string.Format("Player %1 banned by %2 for %3 seconds: %4", targetId, adminId, duration, reason);
        }
        else
        {
            adminMessage = string.Format("Player %1 permanently banned by %2: %3", targetId, adminId, reason);
        }
        NotifyAdmins(adminMessage);
        
        // Log punishment
        LogPunishment(PunishmentType.BAN, adminId, targetId, reason, duration);
        
        Print(string.Format("[EdenAdminPunishmentSystem] %1 banned %2: %3", adminId, targetId, reason));
        return true;
    }
    
    //! Unban player
    bool UnbanPlayer(string adminId, string targetId, string reason = "")
    {
        if (!m_PunishmentSystemEnabled || !m_AdminManager)
            return false;
            
        if (!m_AdminManager.HasAdminPermission(adminId, 2))
            return false;
            
        if (adminId == "" || targetId == "")
            return false;
            
        if (!m_ActiveBans.Contains(targetId))
        {
            SendMessageToPlayer(adminId, "Player is not banned");
            return false;
        }
        
        // Remove ban
        m_ActiveBans.Remove(targetId);
        
        // Notify admins
        string adminMessage = string.Format("Player %1 unbanned by %2", targetId, adminId);
        if (reason != "")
        {
            adminMessage += string.Format(": %1", reason);
        }
        NotifyAdmins(adminMessage);
        
        // Log punishment
        LogPunishment(PunishmentType.UNBAN, adminId, targetId, reason, 0);
        
        Print(string.Format("[EdenAdminPunishmentSystem] %1 unbanned %2", adminId, targetId));
        return true;
    }
    
    //! Check if player is banned
    bool IsPlayerBanned(string playerId)
    {
        if (!m_ActiveBans.Contains(playerId))
            return false;
            
        EdenBanData banData = m_ActiveBans.Get(playerId);
        if (!banData)
            return false;
            
        // Check if ban has expired
        if (banData.GetExpirationTime() > 0)
        {
            int currentTime = GetGame().GetWorld().GetWorldTime();
            if (currentTime >= banData.GetExpirationTime())
            {
                // Ban expired, remove it
                m_ActiveBans.Remove(playerId);
                return false;
            }
        }
        
        return true;
    }
    
    //! Get ban data for player
    EdenBanData GetPlayerBanData(string playerId)
    {
        if (!m_ActiveBans.Contains(playerId))
            return null;
            
        return m_ActiveBans.Get(playerId);
    }
    
    //! Get active warning count for player
    int GetActiveWarningCount(string playerId)
    {
        if (!m_PlayerWarnings.Contains(playerId))
            return 0;
            
        array<ref EdenWarning> playerWarnings = m_PlayerWarnings.Get(playerId);
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int activeCount = 0;
        
        foreach (EdenWarning warning : playerWarnings)
        {
            if (warning.GetExpirationTime() > currentTime)
            {
                activeCount++;
            }
        }
        
        return activeCount;
    }
    
    //! Get recent kick count for player
    int GetRecentKickCount(string playerId, int timeWindow)
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int kickCount = 0;
        
        foreach (EdenPunishmentRecord record : m_PunishmentHistory)
        {
            if (record.GetTargetId() == playerId && 
                record.GetPunishmentType() == PunishmentType.KICK &&
                (currentTime - record.GetTimestamp()) <= timeWindow)
            {
                kickCount++;
            }
        }
        
        return kickCount;
    }
    
    //! Get escalated ban duration based on player's ban history
    int GetEscalatedBanDuration(string playerId)
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int banCount = 0;
        
        // Count recent bans within escalation reset time
        foreach (EdenPunishmentRecord record : m_PunishmentHistory)
        {
            if (record.GetTargetId() == playerId && 
                record.GetPunishmentType() == PunishmentType.BAN &&
                (currentTime - record.GetTimestamp()) <= m_BanEscalationResetTime)
            {
                banCount++;
            }
        }
        
        // Return escalated duration
        if (banCount < m_BanEscalationDurations.Count())
        {
            return m_BanEscalationDurations[banCount];
        }
        else
        {
            return -1; // Permanent ban
        }
    }
    
    //! Log punishment
    protected void LogPunishment(PunishmentType punishmentType, string adminId, string targetId, string reason, int duration)
    {
        if (!m_LogAllPunishments)
            return;
            
        EdenPunishmentRecord record = new EdenPunishmentRecord();
        record.SetPunishmentType(punishmentType);
        record.SetAdminId(adminId);
        record.SetTargetId(targetId);
        record.SetReason(reason);
        record.SetDuration(duration);
        record.SetTimestamp(GetGame().GetWorld().GetWorldTime());
        
        // Get admin position
        IEntity adminEntity = GetPlayerEntity(adminId);
        if (adminEntity)
        {
            record.SetPosition(adminEntity.GetOrigin());
        }
        
        m_PunishmentHistory.Insert(record);
        
        // Keep only recent records
        if (m_PunishmentHistory.Count() > m_MaxPunishmentRecords)
        {
            m_PunishmentHistory.RemoveOrdered(0);
        }
        
        Print(string.Format("[EdenAdminPunishmentSystem] Logged punishment: %1 by %2 on %3", 
            EnumToString(PunishmentType, punishmentType), adminId, targetId));
    }
    
    //! Process ban expirations
    protected void ProcessBanExpirations()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        array<string> expiredBans = {};
        
        foreach (string playerId, EdenBanData banData : m_ActiveBans)
        {
            if (banData.GetExpirationTime() > 0 && currentTime >= banData.GetExpirationTime())
            {
                expiredBans.Insert(playerId);
            }
        }
        
        foreach (string playerId : expiredBans)
        {
            m_ActiveBans.Remove(playerId);
            Print(string.Format("[EdenAdminPunishmentSystem] Ban expired for player %1", playerId));
        }
    }
    
    //! Process warning expirations
    protected void ProcessWarningExpirations()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        foreach (string playerId, array<ref EdenWarning> warnings : m_PlayerWarnings)
        {
            array<int> expiredIndices = {};
            
            for (int i = 0; i < warnings.Count(); i++)
            {
                if (currentTime >= warnings[i].GetExpirationTime())
                {
                    expiredIndices.Insert(i);
                }
            }
            
            // Remove expired warnings in reverse order
            for (int i = expiredIndices.Count() - 1; i >= 0; i--)
            {
                warnings.RemoveOrdered(expiredIndices[i]);
            }
        }
    }
    
    //! Get punishment history
    array<ref EdenPunishmentRecord> GetPunishmentHistory(int maxRecords = 100)
    {
        array<ref EdenPunishmentRecord> recentRecords = {};
        
        int startIndex = Math.Max(0, m_PunishmentHistory.Count() - maxRecords);
        for (int i = startIndex; i < m_PunishmentHistory.Count(); i++)
        {
            recentRecords.Insert(m_PunishmentHistory[i]);
        }
        
        return recentRecords;
    }
    
    //! Get player punishment history
    array<ref EdenPunishmentRecord> GetPlayerPunishmentHistory(string playerId, int maxRecords = 50)
    {
        array<ref EdenPunishmentRecord> playerRecords = {};
        
        foreach (EdenPunishmentRecord record : m_PunishmentHistory)
        {
            if (record.GetTargetId() == playerId)
            {
                playerRecords.Insert(record);
                
                if (playerRecords.Count() >= maxRecords)
                    break;
            }
        }
        
        return playerRecords;
    }
    
    //! Get player warnings
    array<ref EdenWarning> GetPlayerWarnings(string playerId)
    {
        if (!m_PlayerWarnings.Contains(playerId))
            return {};
            
        return m_PlayerWarnings.Get(playerId);
    }
    
    //! Get active bans
    array<ref EdenBanData> GetActiveBans()
    {
        array<ref EdenBanData> activeBans = {};
        
        foreach (string playerId, EdenBanData banData : m_ActiveBans)
        {
            activeBans.Insert(banData);
        }
        
        return activeBans;
    }
    
    //! Helper methods - placeholders for actual implementation
    protected void LoadActiveBans() { }
    protected void LoadPunishmentHistory() { }
    protected void SavePunishmentData() { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    protected void SendMessageToPlayer(string playerId, string message) { }
    protected void DisconnectPlayer(string playerId, string reason) { }
    protected void NotifyAdmins(string message) { }
    
    //! Configuration methods
    void SetPunishmentSystemEnabled(bool enabled) { m_PunishmentSystemEnabled = enabled; }
    bool IsPunishmentSystemEnabled() { return m_PunishmentSystemEnabled; }
    
    void SetMaxWarningsBeforeKick(int maxWarnings) { m_MaxWarningsBeforeKick = maxWarnings; }
    int GetMaxWarningsBeforeKick() { return m_MaxWarningsBeforeKick; }
    
    void SetMaxKicksBeforeBan(int maxKicks) { m_MaxKicksBeforeBan = maxKicks; }
    int GetMaxKicksBeforeBan() { return m_MaxKicksBeforeBan; }
    
    void SetDefaultBanDuration(int duration) { m_DefaultBanDuration = duration; }
    int GetDefaultBanDuration() { return m_DefaultBanDuration; }
    
    void SetBanEscalationResetTime(int resetTime) { m_BanEscalationResetTime = resetTime; }
    int GetBanEscalationResetTime() { return m_BanEscalationResetTime; }
    
    void SetLogAllPunishments(bool logAll) { m_LogAllPunishments = logAll; }
    bool ShouldLogAllPunishments() { return m_LogAllPunishments; }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenAdminPunishmentSystem] Cleaning up admin punishment system...");
        
        m_ActiveBans.Clear();
        m_PunishmentHistory.Clear();
        m_PlayerWarnings.Clear();
        m_AdminManager = null;
    }
}
