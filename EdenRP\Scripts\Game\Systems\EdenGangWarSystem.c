//! Eden Gang War System - Handles gang warfare mechanics and point tracking
//! Converted from original gang war systems

class EdenGangWarSystem
{
    protected ref map<string, ref EdenGangWarData> m_ActiveWars;
    protected ref map<string, ref EdenWarKillData> m_RecentKills;
    protected ref map<string, int> m_PlayerKillStreaks;
    protected ref map<string, int> m_PlayerWarPoints;
    
    // War configuration
    protected int m_MaxWarDuration;
    protected int m_KillCooldownTime;
    protected int m_BaseKillPoints;
    protected int m_MaxKillStreak;
    protected int m_DistanceBonusThreshold;
    protected bool m_TimeFucked; // Server time issues flag
    
    void EdenGangWarSystem()
    {
        m_ActiveWars = new map<string, ref EdenGangWarData>();
        m_RecentKills = new map<string, ref EdenWarKillData>();
        m_PlayerKillStreaks = new map<string, int>();
        m_PlayerWarPoints = new map<string, int>();
        
        m_MaxWarDuration = 86400; // 24 hours
        m_KillCooldownTime = 900; // 15 minutes
        m_BaseKillPoints = 3;
        m_MaxKillStreak = 15;
        m_DistanceBonusThreshold = 300; // meters
        m_TimeFucked = false;
        
        InitializeWarSystem();
    }
    
    //! Initialize war system
    void Initialize()
    {
        Print("[EdenGangWarSystem] Initializing gang war system...");
        
        LoadActiveWars();
        LoadPlayerWarPoints();
        
        // Start periodic updates
        GetGame().GetCallqueue().CallLater(UpdateWars, 60000, true); // Every minute
        GetGame().GetCallqueue().CallLater(CleanupRecentKills, 30000, true); // Every 30 seconds
        
        Print("[EdenGangWarSystem] Gang war system initialized");
    }
    
    //! Initialize war system configuration
    protected void InitializeWarSystem()
    {
        // Load war configuration from database or config files
        // Set up war zones and special areas
    }
    
    //! Start a gang war
    bool StartGangWar(int attackerGangId, int defenderGangId, string attackerName = "", string defenderName = "")
    {
        if (attackerGangId <= 0 || defenderGangId <= 0 || attackerGangId == defenderGangId)
            return false;
            
        // Check if war already exists
        string warKey = GetWarKey(attackerGangId, defenderGangId);
        string reverseWarKey = GetWarKey(defenderGangId, attackerGangId);
        
        if (m_ActiveWars.Contains(warKey) || m_ActiveWars.Contains(reverseWarKey))
            return false;
            
        // Create war data
        EdenGangWarData warData = new EdenGangWarData();
        warData.SetAttackerGangId(attackerGangId);
        warData.SetDefenderGangId(defenderGangId);
        warData.SetAttackerGangName(attackerName);
        warData.SetDefenderGangName(defenderName);
        warData.SetStartTime(GetGame().GetWorld().GetWorldTime());
        warData.SetDuration(m_MaxWarDuration);
        warData.SetActive(true);
        
        m_ActiveWars.Set(warKey, warData);
        
        // Notify gang members
        NotifyGangWar(attackerGangId, defenderGangId, true);
        
        // Save to database
        SaveWarToDatabase(warData);
        
        Print(string.Format("[EdenGangWarSystem] Gang war started: %1 vs %2", attackerGangId, defenderGangId));
        return true;
    }
    
    //! End a gang war
    bool EndGangWar(int gangId1, int gangId2)
    {
        string warKey = GetWarKey(gangId1, gangId2);
        string reverseWarKey = GetWarKey(gangId2, gangId1);
        
        EdenGangWarData warData = null;
        string activeWarKey = "";
        
        if (m_ActiveWars.Contains(warKey))
        {
            warData = m_ActiveWars.Get(warKey);
            activeWarKey = warKey;
        }
        else if (m_ActiveWars.Contains(reverseWarKey))
        {
            warData = m_ActiveWars.Get(reverseWarKey);
            activeWarKey = reverseWarKey;
        }
        
        if (!warData)
            return false;
            
        // Set war as inactive
        warData.SetActive(false);
        warData.SetEndTime(GetGame().GetWorld().GetWorldTime());
        
        // Notify gang members
        NotifyGangWar(warData.GetAttackerGangId(), warData.GetDefenderGangId(), false);
        
        // Remove from active wars
        m_ActiveWars.Remove(activeWarKey);
        
        // Update database
        UpdateWarInDatabase(warData);
        
        Print(string.Format("[EdenGangWarSystem] Gang war ended: %1 vs %2", gangId1, gangId2));
        return true;
    }
    
    //! Award war points for a kill
    void AwardWarPoints(string killerId, string victimId, vector killPosition, float killDistance)
    {
        if (killerId == "" || victimId == "" || killerId == victimId)
            return;
            
        // Get gang IDs
        int killerGangId = GetPlayerGangId(killerId);
        int victimGangId = GetPlayerGangId(victimId);
        
        if (killerGangId <= 0 || victimGangId <= 0 || killerGangId == victimGangId)
            return;
            
        // Check if there's an active war
        if (!IsWarActive(killerGangId, victimGangId))
            return;
            
        // Check for recent kill cooldown
        if (IsRecentKill(killerId, victimId))
        {
            // Reduce kill streak for spam killing
            if (m_PlayerKillStreaks.Contains(killerId))
            {
                int currentStreak = m_PlayerKillStreaks.Get(killerId);
                if (currentStreak > 0)
                {
                    m_PlayerKillStreaks.Set(killerId, currentStreak - 1);
                }
            }
            return;
        }
        
        // Calculate points
        int killerPoints = CalculateKillPoints(killerId, killDistance);
        int victimPoints = CalculateDeathPenalty(victimId);
        
        // Update kill streak
        UpdateKillStreak(killerId);
        
        // Award points
        AwardPlayerWarPoints(killerId, killerPoints);
        DeductPlayerWarPoints(victimId, victimPoints);
        
        // Update war statistics
        UpdateWarStats(killerGangId, victimGangId, killerPoints, victimPoints);
        
        // Record kill for cooldown
        RecordKill(killerId, victimId);
        
        // Notify players
        NotifyKillPoints(killerId, victimId, killerPoints, victimPoints, killDistance);
        
        Print(string.Format("[EdenGangWarSystem] War kill: %1 (+%2) killed %3 (-%4)", killerId, killerPoints, victimId, victimPoints));
    }
    
    //! Award points for cop kills in war zones
    void AwardCopKillPoints(string killerId, string victimId, int copRank)
    {
        if (killerId == "" || victimId == "")
            return;
            
        int killerGangId = GetPlayerGangId(killerId);
        if (killerGangId <= 0)
            return;
            
        // Calculate points based on cop rank
        int points = Math.Min(copRank, 5);
        
        // Award points
        AwardPlayerWarPoints(killerId, points);
        
        // Notify player
        NotifyCopKillPoints(killerId, points);
        
        Print(string.Format("[EdenGangWarSystem] Cop kill in warzone: %1 (+%2 points)", killerId, points));
    }
    
    //! Calculate kill points based on distance and kill streak
    protected int CalculateKillPoints(string killerId, float killDistance)
    {
        int basePoints = m_BaseKillPoints;
        int distanceBonus = 0;
        int streakBonus = 0;
        
        // Distance bonus
        if (killDistance >= m_DistanceBonusThreshold)
        {
            distanceBonus = 1;
        }
        
        // Kill streak bonus
        if (m_PlayerKillStreaks.Contains(killerId))
        {
            int killStreak = m_PlayerKillStreaks.Get(killerId);
            if (killStreak >= 15)
                streakBonus = 5;
            else if (killStreak >= 12)
                streakBonus = 4;
            else if (killStreak >= 9)
                streakBonus = 3;
            else if (killStreak >= 6)
                streakBonus = 2;
            else if (killStreak >= 3)
                streakBonus = 1;
        }
        
        return basePoints + distanceBonus + streakBonus;
    }
    
    //! Calculate death penalty points
    protected int CalculateDeathPenalty(string victimId)
    {
        // Base penalty
        int penalty = 2;
        
        // Additional penalty based on war points (prevent point farming)
        if (m_PlayerWarPoints.Contains(victimId))
        {
            int currentPoints = m_PlayerWarPoints.Get(victimId);
            if (currentPoints > 100)
                penalty = 3;
            else if (currentPoints > 50)
                penalty = 2;
            else
                penalty = 1;
        }
        
        return penalty;
    }
    
    //! Update player kill streak
    protected void UpdateKillStreak(string killerId)
    {
        if (m_PlayerKillStreaks.Contains(killerId))
        {
            int currentStreak = m_PlayerKillStreaks.Get(killerId);
            m_PlayerKillStreaks.Set(killerId, currentStreak + 1);
        }
        else
        {
            m_PlayerKillStreaks.Set(killerId, 1);
        }
    }
    
    //! Reset player kill streak
    void ResetKillStreak(string playerId)
    {
        if (m_PlayerKillStreaks.Contains(playerId))
        {
            m_PlayerKillStreaks.Set(playerId, 0);
        }
    }
    
    //! Award war points to player
    protected void AwardPlayerWarPoints(string playerId, int points)
    {
        if (m_PlayerWarPoints.Contains(playerId))
        {
            int currentPoints = m_PlayerWarPoints.Get(playerId);
            m_PlayerWarPoints.Set(playerId, currentPoints + points);
        }
        else
        {
            m_PlayerWarPoints.Set(playerId, points);
        }
        
        // Update database
        UpdatePlayerWarPointsInDatabase(playerId, points, true);
    }
    
    //! Deduct war points from player
    protected void DeductPlayerWarPoints(string playerId, int points)
    {
        if (m_PlayerWarPoints.Contains(playerId))
        {
            int currentPoints = m_PlayerWarPoints.Get(playerId);
            int newPoints = Math.Max(0, currentPoints - points);
            m_PlayerWarPoints.Set(playerId, newPoints);
        }
        
        // Update database
        UpdatePlayerWarPointsInDatabase(playerId, points, false);
    }
    
    //! Check if there's an active war between gangs
    protected bool IsWarActive(int gangId1, int gangId2)
    {
        string warKey = GetWarKey(gangId1, gangId2);
        string reverseWarKey = GetWarKey(gangId2, gangId1);
        
        return m_ActiveWars.Contains(warKey) || m_ActiveWars.Contains(reverseWarKey);
    }
    
    //! Check if kill is on cooldown
    protected bool IsRecentKill(string killerId, string victimId)
    {
        if (m_TimeFucked)
            return false;
            
        string killKey = string.Format("%1_%2", killerId, victimId);
        
        if (m_RecentKills.Contains(killKey))
        {
            EdenWarKillData killData = m_RecentKills.Get(killKey);
            int currentTime = GetGame().GetWorld().GetWorldTime();
            
            return currentTime < killData.GetExpirationTime();
        }
        
        return false;
    }
    
    //! Record a kill for cooldown tracking
    protected void RecordKill(string killerId, string victimId)
    {
        if (m_TimeFucked)
            return;
            
        string killKey = string.Format("%1_%2", killerId, victimId);
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        EdenWarKillData killData = new EdenWarKillData();
        killData.SetKillerId(killerId);
        killData.SetVictimId(victimId);
        killData.SetKillTime(currentTime);
        killData.SetExpirationTime(currentTime + m_KillCooldownTime);
        
        m_RecentKills.Set(killKey, killData);
    }
    
    //! Update war statistics
    protected void UpdateWarStats(int killerGangId, int victimGangId, int killerPoints, int victimPoints)
    {
        string warKey = GetWarKey(killerGangId, victimGangId);
        string reverseWarKey = GetWarKey(victimGangId, killerGangId);
        
        EdenGangWarData warData = null;
        bool isAttacker = false;
        
        if (m_ActiveWars.Contains(warKey))
        {
            warData = m_ActiveWars.Get(warKey);
            isAttacker = true;
        }
        else if (m_ActiveWars.Contains(reverseWarKey))
        {
            warData = m_ActiveWars.Get(reverseWarKey);
            isAttacker = false;
        }
        
        if (warData)
        {
            if (isAttacker)
            {
                warData.AddAttackerPoints(killerPoints);
            }
            else
            {
                warData.AddDefenderPoints(killerPoints);
            }
        }
    }
    
    //! Update active wars
    protected void UpdateWars()
    {
        array<string> expiredWars = {};
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        foreach (string warKey, EdenGangWarData warData : m_ActiveWars)
        {
            if (warData.IsExpired(currentTime))
            {
                expiredWars.Insert(warKey);
            }
        }
        
        // Remove expired wars
        foreach (string warKey : expiredWars)
        {
            EdenGangWarData warData = m_ActiveWars.Get(warKey);
            EndGangWar(warData.GetAttackerGangId(), warData.GetDefenderGangId());
        }
    }
    
    //! Clean up recent kills
    protected void CleanupRecentKills()
    {
        array<string> expiredKills = {};
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        foreach (string killKey, EdenWarKillData killData : m_RecentKills)
        {
            if (currentTime >= killData.GetExpirationTime())
            {
                expiredKills.Insert(killKey);
            }
        }
        
        foreach (string killKey : expiredKills)
        {
            m_RecentKills.Remove(killKey);
        }
    }
    
    //! Helper methods
    protected string GetWarKey(int gangId1, int gangId2)
    {
        // Ensure consistent key format
        if (gangId1 < gangId2)
            return string.Format("%1_%2", gangId1, gangId2);
        else
            return string.Format("%1_%2", gangId2, gangId1);
    }
    
    //! Get player war points
    int GetPlayerWarPoints(string playerId)
    {
        if (m_PlayerWarPoints.Contains(playerId))
            return m_PlayerWarPoints.Get(playerId);
        return 0;
    }
    
    //! Get player kill streak
    int GetPlayerKillStreak(string playerId)
    {
        if (m_PlayerKillStreaks.Contains(playerId))
            return m_PlayerKillStreaks.Get(playerId);
        return 0;
    }
    
    //! Get active war data
    EdenGangWarData GetWarData(int gangId1, int gangId2)
    {
        string warKey = GetWarKey(gangId1, gangId2);
        if (m_ActiveWars.Contains(warKey))
            return m_ActiveWars.Get(warKey);
        return null;
    }
    
    //! Get all active wars
    array<ref EdenGangWarData> GetActiveWars()
    {
        array<ref EdenGangWarData> wars = {};
        foreach (string warKey, EdenGangWarData warData : m_ActiveWars)
        {
            wars.Insert(warData);
        }
        return wars;
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadActiveWars() { }
    protected void LoadPlayerWarPoints() { }
    protected void SaveWarToDatabase(EdenGangWarData warData) { }
    protected void UpdateWarInDatabase(EdenGangWarData warData) { }
    protected void UpdatePlayerWarPointsInDatabase(string playerId, int points, bool add) { }
    protected int GetPlayerGangId(string playerId) { return -1; }
    protected void NotifyGangWar(int gangId1, int gangId2, bool started) { }
    protected void NotifyKillPoints(string killerId, string victimId, int killerPoints, int victimPoints, float distance) { }
    protected void NotifyCopKillPoints(string killerId, int points) { }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenGangWarSystem] Cleaning up gang war system...");
        
        m_ActiveWars.Clear();
        m_RecentKills.Clear();
        m_PlayerKillStreaks.Clear();
        m_PlayerWarPoints.Clear();
    }
}

//! War kill data for cooldown tracking
class EdenWarKillData
{
    protected string m_KillerId;
    protected string m_VictimId;
    protected int m_KillTime;
    protected int m_ExpirationTime;
    
    void SetKillerId(string killerId) { m_KillerId = killerId; }
    void SetVictimId(string victimId) { m_VictimId = victimId; }
    void SetKillTime(int killTime) { m_KillTime = killTime; }
    void SetExpirationTime(int expirationTime) { m_ExpirationTime = expirationTime; }
    
    string GetKillerId() { return m_KillerId; }
    string GetVictimId() { return m_VictimId; }
    int GetKillTime() { return m_KillTime; }
    int GetExpirationTime() { return m_ExpirationTime; }
}
