//! Airdrop Manager for Eden Reforger
//! Converted from original Eden Altis Life airdrop systems
//! Manages supply airdrops with random loot spawning and claiming mechanics

[ComponentEditorProps(category: "Eden Systems", description: "Manages airdrop events and supply drops")]
class EdenAirdropManagerComponent : ScriptComponent
{
    [Attribute("3600", UIWidgets.EditBox, "Airdrop cooldown in seconds")]
    protected int m_iAirdropCooldown;
    
    [Attribute("1800", UIWidgets.EditBox, "Airdrop duration in seconds")]
    protected int m_iAirdropDuration;
    
    [Attribute("500", UIWidgets.EditBox, "Minimum distance from players for airdrop")]
    protected float m_fMinPlayerDistance;
    
    [RplProp()]
    protected bool m_bAirdropActive;
    
    [RplProp()]
    protected int m_iLastAirdropTime;
    
    [RplProp()]
    protected vector m_vCurrentAirdropLocation;
    
    protected ref array<ref EdenAirdropData> m_aActiveAirdrops;
    protected ref array<ref EdenAirdropLoot> m_aAirdropLootTable;
    
    //! Constructor
    void EdenAirdropManagerComponent(IEntityComponentSource src, IEntity ent, IEntity parent)
    {
        m_bAirdropActive = false;
        m_iLastAirdropTime = 0;
        m_vCurrentAirdropLocation = "0 0 0";
        m_aActiveAirdrops = new array<ref EdenAirdropData>();
        m_aAirdropLootTable = new array<ref EdenAirdropLoot>();
        
        InitializeAirdropLootTable();
    }
    
    //! Initialize airdrop loot table
    protected void InitializeAirdropLootTable()
    {
        // Weapons
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("MX", "weapon", 15, 1, 2));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("MXM", "weapon", 10, 1, 1));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("MX SW", "weapon", 8, 1, 1));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Mk20", "weapon", 20, 1, 3));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("TRG-20", "weapon", 18, 1, 2));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("SDAR", "weapon", 12, 1, 2));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Mk18 ABR", "weapon", 5, 1, 1));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("MAR-10", "weapon", 3, 1, 1));
        
        // Ammunition
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("6.5mm Mag", "ammo", 25, 5, 15));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("5.56mm Mag", "ammo", 30, 5, 20));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("20mm Mag", "ammo", 15, 2, 8));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot(".338 Mag", "ammo", 8, 1, 5));
        
        // Equipment
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Carrier Rig", "equipment", 20, 1, 3));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Combat Helmet", "equipment", 25, 1, 4));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Night Vision", "equipment", 10, 1, 2));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("GPS", "equipment", 15, 1, 3));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("First Aid Kit", "medical", 30, 2, 8));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Medikit", "medical", 15, 1, 3));
        
        // Explosives
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Explosive Charge", "explosive", 5, 1, 2));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Hand Grenade", "explosive", 12, 2, 5));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Smoke Grenade", "explosive", 20, 3, 8));
        
        // Special items
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Lockpick", "tool", 25, 5, 15));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Bolt Cutter", "tool", 15, 1, 3));
        m_aAirdropLootTable.Insert(new EdenAirdropLoot("Repair Kit", "tool", 20, 2, 6));
    }
    
    //! Start airdrop event
    bool StartAirdropEvent(vector location = "0 0 0")
    {
        if (m_bAirdropActive)
            return false;
            
        // Check cooldown
        int currentTime = GetGame().GetWorld().GetWorldTime();
        if (currentTime - m_iLastAirdropTime < m_iAirdropCooldown)
            return false;
            
        // Generate random location if not specified
        if (location == "0 0 0")
        {
            location = GenerateRandomAirdropLocation();
        }
        
        // Validate location
        if (!IsValidAirdropLocation(location))
        {
            Print("EdenAirdropManager: Invalid airdrop location, trying again");
            location = GenerateRandomAirdropLocation();
        }
        
        m_bAirdropActive = true;
        m_vCurrentAirdropLocation = location;
        m_iLastAirdropTime = currentTime;
        
        // Create airdrop data
        EdenAirdropData airdropData = new EdenAirdropData();
        airdropData.SetLocation(location);
        airdropData.SetStartTime(currentTime);
        airdropData.SetDuration(m_iAirdropDuration);
        airdropData.SetIsActive(true);
        airdropData.SetIsClaimed(false);
        
        // Generate loot
        ref array<ref EdenAirdropLoot> loot = GenerateAirdropLoot();
        airdropData.SetLoot(loot);
        
        m_aActiveAirdrops.Insert(airdropData);
        
        // Spawn the actual airdrop
        SpawnAirdrop(location, loot);
        
        // Broadcast event
        string gridRef = VectorToGrid(location);
        BroadcastAirdropMessage(string.Format("Supply airdrop incoming at grid %1! Contains high-value equipment and weapons.", gridRef));
        
        Print(string.Format("EdenAirdropManager: Airdrop started at %1 (Grid: %2)", location, gridRef));
        
        // Auto-cleanup after duration
        GetGame().GetCallqueue().CallLater(CleanupAirdrop, m_iAirdropDuration * 1000, false, airdropData);
        
        return true;
    }
    
    //! Generate random airdrop location
    protected vector GenerateRandomAirdropLocation()
    {
        int attempts = 0;
        vector location;
        
        do {
            // Generate random coordinates within map bounds
            float x = Math.RandomFloat(2000, 23000);
            float z = Math.RandomFloat(2000, 23000);
            float y = GetGame().GetWorld().GetSurfaceY(x, z);
            
            location = Vector(x, y, z);
            attempts++;
            
        } while (!IsValidAirdropLocation(location) && attempts < 10);
        
        return location;
    }
    
    //! Check if location is valid for airdrop
    protected bool IsValidAirdropLocation(vector location)
    {
        // Check if location is on land (not water)
        if (location[1] < 0)
            return false;
            
        // Check minimum distance from players
        // TODO: Implement actual player distance checking
        
        // Check if location is not in restricted areas
        // TODO: Implement restricted area checking
        
        return true;
    }
    
    //! Generate airdrop loot
    protected ref array<ref EdenAirdropLoot> GenerateAirdropLoot()
    {
        ref array<ref EdenAirdropLoot> loot = new array<ref EdenAirdropLoot>();
        
        // Determine number of items (8-15 items)
        int itemCount = Math.RandomInt(8, 16);
        
        for (int i = 0; i < itemCount; i++)
        {
            // Select random item from loot table
            int totalWeight = 0;
            foreach (EdenAirdropLoot item : m_aAirdropLootTable)
            {
                totalWeight += item.GetSpawnChance();
            }
            
            int randomWeight = Math.RandomInt(0, totalWeight);
            int currentWeight = 0;
            
            foreach (EdenAirdropLoot item : m_aAirdropLootTable)
            {
                currentWeight += item.GetSpawnChance();
                if (randomWeight < currentWeight)
                {
                    // Create loot item with random quantity
                    EdenAirdropLoot lootItem = new EdenAirdropLoot(
                        item.GetItemName(),
                        item.GetItemType(),
                        item.GetSpawnChance(),
                        Math.RandomInt(item.GetMinQuantity(), item.GetMaxQuantity() + 1),
                        item.GetMaxQuantity()
                    );
                    loot.Insert(lootItem);
                    break;
                }
            }
        }
        
        return loot;
    }
    
    //! Spawn airdrop crate
    protected void SpawnAirdrop(vector location, array<ref EdenAirdropLoot> loot)
    {
        // TODO: Implement actual airdrop spawning with parachute and crate
        // This would involve:
        // 1. Spawning a parachute at high altitude above the location
        // 2. Creating a supply crate attached to the parachute
        // 3. Implementing physics for the parachute descent
        // 4. Spawning the loot items in the crate when it lands
        // 5. Adding interaction to claim the crate
        
        Print(string.Format("EdenAirdropManager: Spawning airdrop crate at %1 with %2 items", location, loot.Count()));
    }
    
    //! Claim airdrop
    bool ClaimAirdrop(string playerId, vector location)
    {
        // Find airdrop at location
        EdenAirdropData airdrop = GetAirdropAtLocation(location);
        if (!airdrop || !airdrop.IsActive() || airdrop.IsClaimed())
            return false;
            
        // Mark as claimed
        airdrop.SetIsClaimed(true);
        airdrop.SetClaimedBy(playerId);
        airdrop.SetClaimTime(GetGame().GetWorld().GetWorldTime());
        
        // Give loot to player
        GiveLootToPlayer(playerId, airdrop.GetLoot());
        
        BroadcastAirdropMessage(string.Format("Supply airdrop at grid %1 has been claimed!", VectorToGrid(location)));
        Print(string.Format("EdenAirdropManager: Airdrop claimed by %1 at %2", playerId, location));
        
        return true;
    }
    
    //! Get airdrop at location
    protected EdenAirdropData GetAirdropAtLocation(vector location)
    {
        foreach (EdenAirdropData airdrop : m_aActiveAirdrops)
        {
            if (airdrop && airdrop.IsActive())
            {
                float distance = vector.Distance(airdrop.GetLocation(), location);
                if (distance < 50) // Within 50 meters
                    return airdrop;
            }
        }
        return null;
    }
    
    //! Give loot to player
    protected void GiveLootToPlayer(string playerId, array<ref EdenAirdropLoot> loot)
    {
        // TODO: Implement actual item giving to player inventory
        Print(string.Format("EdenAirdropManager: Giving %1 items to player %2", loot.Count(), playerId));
        
        foreach (EdenAirdropLoot item : loot)
        {
            Print(string.Format("  - %1x %2", item.GetQuantity(), item.GetItemName()));
        }
    }
    
    //! Cleanup airdrop
    protected void CleanupAirdrop(EdenAirdropData airdrop)
    {
        if (!airdrop)
            return;
            
        airdrop.SetIsActive(false);
        
        // Remove from active airdrops
        int index = m_aActiveAirdrops.Find(airdrop);
        if (index >= 0)
            m_aActiveAirdrops.RemoveOrdered(index);
            
        // If this was the current airdrop, clear the flag
        if (vector.Distance(airdrop.GetLocation(), m_vCurrentAirdropLocation) < 10)
        {
            m_bAirdropActive = false;
            m_vCurrentAirdropLocation = "0 0 0";
        }
        
        // Remove physical crate
        // TODO: Implement actual crate removal
        
        if (!airdrop.IsClaimed())
        {
            BroadcastAirdropMessage(string.Format("Unclaimed supply airdrop at grid %1 has been removed.", VectorToGrid(airdrop.GetLocation())));
        }
        
        Print(string.Format("EdenAirdropManager: Airdrop cleaned up at %1", airdrop.GetLocation()));
    }
    
    //! Convert vector to grid reference
    protected string VectorToGrid(vector pos)
    {
        // Simple grid conversion (can be improved with proper map grid system)
        int gridX = Math.Floor(pos[0] / 1000);
        int gridZ = Math.Floor(pos[2] / 1000);
        return string.Format("%1%2", 
            string.Format("%c", 65 + (gridX % 26)), // A-Z
            gridZ.ToString().PadLeft(2, "0")); // 00-99
    }
    
    //! Broadcast airdrop message
    protected void BroadcastAirdropMessage(string message)
    {
        // TODO: Implement proper broadcasting to all players
        Print(string.Format("AIRDROP BROADCAST: %1", message));
    }
    
    //! Get cooldown remaining
    int GetCooldownRemaining()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int remaining = (m_iLastAirdropTime + m_iAirdropCooldown) - currentTime;
        return Math.Max(0, remaining);
    }
    
    //! Getters
    bool IsAirdropActive() { return m_bAirdropActive; }
    vector GetCurrentAirdropLocation() { return m_vCurrentAirdropLocation; }
    array<ref EdenAirdropData> GetActiveAirdrops() { return m_aActiveAirdrops; }
    array<ref EdenAirdropLoot> GetAirdropLootTable() { return m_aAirdropLootTable; }
}

//! Airdrop Manager class for easy access
class EdenAirdropManager
{
    protected static EdenAirdropManager s_Instance;
    protected EdenAirdropManagerComponent m_Component;
    
    //! Get singleton instance
    static EdenAirdropManager GetInstance()
    {
        if (!s_Instance)
            s_Instance = new EdenAirdropManager();
        return s_Instance;
    }
    
    //! Initialize with component
    void Initialize(EdenAirdropManagerComponent component)
    {
        m_Component = component;
    }
    
    //! Get component
    EdenAirdropManagerComponent GetComponent()
    {
        return m_Component;
    }
}
