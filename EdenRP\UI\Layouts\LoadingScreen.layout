FrameWidgetClass {
 Name "LoadingScreenRoot"
 {
  FrameWidgetClass {
   Name "BackgroundImage"
   {
    ImageWidgetClass {
     Name "BackgroundImg"
     Texture "{UI_LOADING_BACKGROUND}"
     "Size Mode" fill
     Stretch 1
    }
   }
  }
  
  FrameWidgetClass {
   Name "LoadingPanel"
   Pos 50 50
   Size 400 300
   {
    TextWidgetClass {
     Name "LoadingStatusText"
     Text "Loading..."
     Pos 10 10
     Size 380 30
     "Font Size" 16
     "Text Color" RGBA 255 255 255 255
    }
    
    ProgressBarWidgetClass {
     Name "ProgressBar"
     Pos 10 50
     Size 380 20
     Min 0
     Max 1
     Step 0.01
    }
    
    TextWidgetClass {
     Name "LoadingProgressText"
     Text "0%"
     Pos 10 80
     Size 100 20
     "Font Size" 14
     "Text Color" RGBA 255 255 255 255
    }
    
    ImageWidgetClass {
     Name "LoadingIcon"
     Texture "{UI_LOADING_ICON_1}"
     Pos 320 80
     Size 32 32
    }
   }
  }
  
  FrameWidgetClass {
   Name "ContentPanel"
   Pos 500 50
   Size 600 500
   {
    FrameWidgetClass {
     Name "TreePanel"
     Pos 0 0
     Size 250 500
     {
      TreeWidgetClass {
       Name "ContentTree"
       Pos 5 5
       Size 240 490
      }
     }
    }
    
    FrameWidgetClass {
     Name "ContentDisplayPanel"
     Pos 260 0
     Size 340 500
     {
      RichTextWidgetClass {
       Name "ContentDisplay"
       Pos 5 5
       Size 330 490
       "Font Size" 12
       "Text Color" RGBA 255 255 255 255
       "Word Wrap" 1
      }
     }
    }
   }
  }
  
  FrameWidgetClass {
   Name "ButtonPanel"
   Pos 50 600
   Size 400 50
   {
    ButtonWidgetClass {
     Name "ReturnToLobbyButton"
     Text "Return to Lobby"
     Pos 10 10
     Size 120 30
    }
    
    ButtonWidgetClass {
     Name "PlayButton"
     Text "Play"
     Pos 270 10
     Size 120 30
     Enabled 0
    }
   }
  }
 }
}
