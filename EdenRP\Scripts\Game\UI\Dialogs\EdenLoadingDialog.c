//! Eden Loading Screen Dialog for Arma Reforger
//! Converted from original Eden Altis Life loading screen system
//! Displays loading progress, server information, and changelog content

class EdenLoadingDialog : EdenBaseDialog
{
    protected Widget m_wBackgroundImage;
    protected TextWidget m_wLoadingStatusText;
    protected TextWidget m_wLoadingProgressText;
    protected ImageWidget m_wLoadingIcon;
    protected ProgressBarWidget m_wProgressBar;
    
    // Content panels
    protected Widget m_wContentPanel;
    protected Widget m_wTreePanel;
    protected Widget m_wContentDisplayPanel;
    protected TreeWidget m_wContentTree;
    protected RichTextWidget m_wContentDisplay;
    
    // Control buttons
    protected ButtonWidget m_wReturnToLobbyButton;
    protected ButtonWidget m_wPlayButton;
    
    protected EdenLoadingScreenManager m_LoadingManager;
    protected int m_iCurrentIconFrame;
    protected bool m_bIconAnimationActive;
    
    //! Constructor
    void EdenLoadingDialog()
    {
        m_iCurrentIconFrame = 1;
        m_bIconAnimationActive = true;
    }
    
    //! Initialize dialog
    void Initialize(EdenLoadingScreenManager loadingManager)
    {
        m_LoadingManager = loadingManager;
    }
    
    //! Called when dialog is opened
    override void OnMenuOpen()
    {
        super.OnMenuOpen();
        
        // Initialize widgets
        InitializeWidgets();
        
        // Setup content tree
        SetupContentTree();
        
        // Start icon animation
        StartIconAnimation();
        
        // Initial update
        UpdateDisplay();
    }
    
    //! Initialize all widgets
    protected void InitializeWidgets()
    {
        // Background and main elements
        m_wBackgroundImage = m_wRoot.FindAnyWidget("BackgroundImage");
        m_wLoadingStatusText = TextWidget.Cast(m_wRoot.FindAnyWidget("LoadingStatusText"));
        m_wLoadingProgressText = TextWidget.Cast(m_wRoot.FindAnyWidget("LoadingProgressText"));
        m_wLoadingIcon = ImageWidget.Cast(m_wRoot.FindAnyWidget("LoadingIcon"));
        m_wProgressBar = ProgressBarWidget.Cast(m_wRoot.FindAnyWidget("ProgressBar"));
        
        // Content panels
        m_wContentPanel = m_wRoot.FindAnyWidget("ContentPanel");
        m_wTreePanel = m_wRoot.FindAnyWidget("TreePanel");
        m_wContentDisplayPanel = m_wRoot.FindAnyWidget("ContentDisplayPanel");
        m_wContentTree = TreeWidget.Cast(m_wRoot.FindAnyWidget("ContentTree"));
        m_wContentDisplay = RichTextWidget.Cast(m_wRoot.FindAnyWidget("ContentDisplay"));
        
        // Control buttons
        m_wReturnToLobbyButton = ButtonWidget.Cast(m_wRoot.FindAnyWidget("ReturnToLobbyButton"));
        m_wPlayButton = ButtonWidget.Cast(m_wRoot.FindAnyWidget("PlayButton"));
        
        // Set up button handlers
        if (m_wReturnToLobbyButton)
            m_wReturnToLobbyButton.SetHandler(this);
        if (m_wPlayButton)
            m_wPlayButton.SetHandler(this);
        if (m_wContentTree)
            m_wContentTree.SetHandler(this);
        
        // Set background image
        if (m_wBackgroundImage)
        {
            // Set loading background image
            ImageWidget backgroundImg = ImageWidget.Cast(m_wBackgroundImage);
            if (backgroundImg)
                backgroundImg.LoadImageTexture(0, "images/loadingBackground.jpg");
        }
        
        // Initially disable play button
        if (m_wPlayButton)
            m_wPlayButton.SetEnabled(false);
    }
    
    //! Setup content tree with changelogs and server information
    protected void SetupContentTree()
    {
        if (!m_wContentTree || !m_LoadingManager)
            return;
            
        m_wContentTree.ClearAll();
        
        // Add Development Changelog section
        TreeViewItem changelogRoot = m_wContentTree.CreateItem(null, "Development Changelog");
        changelogRoot.SetColor(Color.WHITE);
        
        array<ref EdenChangelogEntry> changelogs = m_LoadingManager.GetChangelogs();
        foreach (EdenChangelogEntry changelog : changelogs)
        {
            TreeViewItem changelogItem = m_wContentTree.CreateItem(changelogRoot, changelog.GetTitle());
            changelogItem.SetColor(Color.FromInt(0x90909090)); // Light gray
            changelogItem.SetData(changelog);
        }
        
        // Add Server Information section
        TreeViewItem serverInfoRoot = m_wContentTree.CreateItem(null, "Server Information");
        serverInfoRoot.SetColor(Color.WHITE);
        
        array<ref EdenContentCategory> categories = m_LoadingManager.GetContentCategories();
        foreach (EdenContentCategory category : categories)
        {
            TreeViewItem categoryItem = m_wContentTree.CreateItem(serverInfoRoot, category.GetTitle());
            categoryItem.SetColor(category.GetColor());
            
            array<ref EdenContentItem> items = category.GetItems();
            foreach (EdenContentItem item : items)
            {
                TreeViewItem itemNode = m_wContentTree.CreateItem(categoryItem, item.GetTitle());
                itemNode.SetColor(Color.FromInt(0x80808080)); // Medium gray
                itemNode.SetData(item);
            }
        }
        
        // Expand changelog by default
        changelogRoot.SetCollapsed(false);
        
        // Select first changelog entry
        if (changelogs.Count() > 0)
        {
            TreeViewItem firstChangelog = changelogRoot.GetChildren();
            if (firstChangelog)
            {
                m_wContentTree.SelectItem(firstChangelog);
                DisplayContent(changelogs[0].GetContent());
            }
        }
    }
    
    //! Start loading icon animation
    protected void StartIconAnimation()
    {
        m_bIconAnimationActive = true;
        GetGame().GetCallqueue().CallLater(UpdateLoadingIcon, 75, true);
    }
    
    //! Stop loading icon animation
    protected void StopIconAnimation()
    {
        m_bIconAnimationActive = false;
        GetGame().GetCallqueue().Remove(UpdateLoadingIcon);
    }
    
    //! Update loading icon frame
    protected void UpdateLoadingIcon()
    {
        if (!m_bIconAnimationActive || !m_wLoadingIcon)
        {
            GetGame().GetCallqueue().Remove(UpdateLoadingIcon);
            return;
        }
        
        if (m_LoadingManager)
        {
            m_iCurrentIconFrame = m_LoadingManager.GetNextIconFrame();
            
            // Update icon texture
            string iconPath = string.Format("images/icons/load-%1.paa", m_iCurrentIconFrame);
            m_wLoadingIcon.LoadImageTexture(0, iconPath);
        }
    }
    
    //! Update loading icon with specific frame
    void UpdateLoadingIcon(int frame)
    {
        if (m_wLoadingIcon)
        {
            string iconPath = string.Format("images/icons/load-%1.paa", frame);
            m_wLoadingIcon.LoadImageTexture(0, iconPath);
        }
    }
    
    //! Update display with current loading status
    void UpdateDisplay()
    {
        if (!m_LoadingManager)
            return;
            
        UpdateProgress(m_LoadingManager.GetLoadingProgress());
        UpdateStatus(m_LoadingManager.GetLoadingStatus());
        SetPlayButtonEnabled(m_LoadingManager.IsLoadingSystemReady());
    }
    
    //! Update progress display
    void UpdateProgress(int progress)
    {
        if (m_wLoadingProgressText)
        {
            m_wLoadingProgressText.SetText(progress.ToString() + "%");
        }
        
        if (m_wProgressBar)
        {
            float progressFloat = (float)progress / 100.0;
            m_wProgressBar.SetCurrent(progressFloat);
        }
    }
    
    //! Update status text
    void UpdateStatus(string status)
    {
        if (m_wLoadingStatusText)
        {
            m_wLoadingStatusText.SetText(status);
        }
    }
    
    //! Set play button enabled state
    void SetPlayButtonEnabled(bool enabled)
    {
        if (m_wPlayButton)
        {
            m_wPlayButton.SetEnabled(enabled);
            
            if (enabled)
            {
                m_wPlayButton.SetColor(Color.GREEN);
            }
            else
            {
                m_wPlayButton.SetColor(Color.WHITE);
            }
        }
    }
    
    //! Display content in the content panel
    protected void DisplayContent(string content)
    {
        if (m_wContentDisplay)
        {
            m_wContentDisplay.SetText(content);
        }
    }
    
    //! Handle tree selection change
    override bool OnChange(Widget w, int x, int y, bool finished)
    {
        if (w == m_wContentTree)
        {
            TreeViewItem selectedItem = m_wContentTree.GetSelectedItem();
            if (selectedItem)
            {
                Managed data = selectedItem.GetData();
                
                if (data)
                {
                    // Check if it's a changelog entry
                    EdenChangelogEntry changelog = EdenChangelogEntry.Cast(data);
                    if (changelog)
                    {
                        DisplayContent(changelog.GetContent());
                        return true;
                    }
                    
                    // Check if it's a content item
                    EdenContentItem contentItem = EdenContentItem.Cast(data);
                    if (contentItem)
                    {
                        DisplayContent(contentItem.GetContent());
                        return true;
                    }
                }
            }
            return true;
        }
        
        return super.OnChange(w, x, y, finished);
    }
    
    //! Handle button clicks
    override bool OnClick(Widget w, int x, int y, int button)
    {
        if (w == m_wReturnToLobbyButton)
        {
            OnReturnToLobbyClick();
            return true;
        }
        else if (w == m_wPlayButton)
        {
            OnPlayButtonClick();
            return true;
        }
        
        return super.OnClick(w, x, y, button);
    }
    
    //! Handle return to lobby button click
    protected void OnReturnToLobbyClick()
    {
        if (m_LoadingManager)
        {
            m_LoadingManager.ReturnToLobby();
        }
    }
    
    //! Handle play button click
    protected void OnPlayButtonClick()
    {
        if (m_LoadingManager && m_LoadingManager.IsLoadingSystemReady())
        {
            // Disable play button
            if (m_wPlayButton)
                m_wPlayButton.SetEnabled(false);
                
            m_LoadingManager.ContinueFromLoadingScreen();
        }
    }
    
    //! Close dialog
    void Close()
    {
        StopIconAnimation();
        
        // Close the dialog
        EdenUIManager uiManager = EdenUIManager.GetInstance();
        if (uiManager)
        {
            uiManager.CloseDialog("EdenLoadingDialog");
        }
    }
    
    //! Called when dialog is closed
    override void OnMenuClose()
    {
        StopIconAnimation();
        super.OnMenuClose();
    }
}
