//! Eden Shop Dialog
//! Converted from original shop dialogs (vehicle shop, gear shop, etc.)
//! Generic shop interface for buying and selling items

class EdenShopDialog : EdenBaseDialog
{
    protected ListBoxWidget m_ItemListBox;
    protected ListBoxWidget m_PlayerItemListBox;
    protected TextWidget m_ItemInfoText;
    protected TextWidget m_PlayerCashText;
    protected TextWidget m_SelectedItemPriceText;
    protected ButtonWidget m_BuyButton;
    protected ButtonWidget m_SellButton;
    protected EditBoxWidget m_QuantityEdit;
    protected ComboBoxWidget m_CategoryCombo;
    
    protected string m_PlayerId;
    protected ref EdenPlayerData m_PlayerData;
    protected ref array<ref EdenShopItem> m_ShopItems;
    protected ref array<ref EdenInventoryItem> m_PlayerItems;
    protected int m_SelectedShopItemIndex;
    protected int m_SelectedPlayerItemIndex;
    protected string m_ShopType;
    protected string m_ShopName;
    
    void EdenShopDialog()
    {
        m_PlayerId = "";
        m_PlayerData = null;
        m_ShopItems = new array<ref EdenShopItem>();
        m_PlayerItems = new array<ref EdenInventoryItem>();
        m_SelectedShopItemIndex = -1;
        m_SelectedPlayerItemIndex = -1;
        m_ShopType = "";
        m_ShopName = "";
    }
    
    override void OnCreate(string playerId, string parameter = "")
    {
        super.OnCreate(playerId, parameter);
        
        m_PlayerId = playerId;
        
        // Parse shop type from parameter
        if (parameter != "")
        {
            array<string> params = new array<string>();
            parameter.Split("|", params);
            if (params.Count() >= 2)
            {
                m_ShopType = params[0];
                m_ShopName = params[1];
            }
        }
        
        // Get player data
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenDataManager dataManager = gameMode.GetDataManager();
            if (dataManager)
            {
                m_PlayerData = dataManager.GetPlayerData(playerId);
            }
        }
        
        InitializeShopWidgets();
        LoadShopItems();
        RefreshDisplay();
        
        Print("[EdenShopDialog] Shop dialog created - Type: " + m_ShopType + " Name: " + m_ShopName);
    }
    
    override string GetDialogTitle()
    {
        if (m_ShopName != "")
            return m_ShopName;
        return "Shop";
    }
    
    override int GetActiveTabId()
    {
        return 0; // Shop dialogs don't use tabs
    }
    
    //! Initialize shop-specific widgets
    protected void InitializeShopWidgets()
    {
        Widget rootWidget = GetRootWidget();
        if (!rootWidget)
            return;
        
        // Find shop item list
        m_ItemListBox = ListBoxWidget.Cast(rootWidget.FindAnyWidget("ShopItemList"));
        if (!m_ItemListBox)
        {
            Print("[EdenShopDialog] Warning: ShopItemList widget not found");
        }
        
        // Find player item list
        m_PlayerItemListBox = ListBoxWidget.Cast(rootWidget.FindAnyWidget("PlayerItemList"));
        if (!m_PlayerItemListBox)
        {
            Print("[EdenShopDialog] Warning: PlayerItemList widget not found");
        }
        
        // Find item info text
        m_ItemInfoText = TextWidget.Cast(rootWidget.FindAnyWidget("ItemInfo"));
        if (!m_ItemInfoText)
        {
            Print("[EdenShopDialog] Warning: ItemInfo widget not found");
        }
        
        // Find player cash text
        m_PlayerCashText = TextWidget.Cast(rootWidget.FindAnyWidget("PlayerCash"));
        if (!m_PlayerCashText)
        {
            Print("[EdenShopDialog] Warning: PlayerCash widget not found");
        }
        
        // Find selected item price text
        m_SelectedItemPriceText = TextWidget.Cast(rootWidget.FindAnyWidget("SelectedItemPrice"));
        if (!m_SelectedItemPriceText)
        {
            Print("[EdenShopDialog] Warning: SelectedItemPrice widget not found");
        }
        
        // Find action buttons
        m_BuyButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("BuyButton"));
        m_SellButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("SellButton"));
        
        // Find quantity edit box
        m_QuantityEdit = EditBoxWidget.Cast(rootWidget.FindAnyWidget("QuantityEdit"));
        if (m_QuantityEdit)
        {
            m_QuantityEdit.SetText("1");
        }
        
        // Find category combo box
        m_CategoryCombo = ComboBoxWidget.Cast(rootWidget.FindAnyWidget("CategoryCombo"));
        if (m_CategoryCombo)
        {
            PopulateCategoryCombo();
        }
    }
    
    //! Load shop items based on shop type
    protected void LoadShopItems()
    {
        m_ShopItems.Clear();
        
        // Get shop manager
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (!gameMode)
            return;
        
        EdenShopManager shopManager = gameMode.GetShopManager();
        if (!shopManager)
            return;
        
        // Load items for this shop type
        ref array<ref EdenShopItem> shopItems = shopManager.GetShopItems(m_ShopType);
        if (shopItems)
        {
            for (int i = 0; i < shopItems.Count(); i++)
            {
                m_ShopItems.Insert(shopItems[i]);
            }
        }
        
        Print("[EdenShopDialog] Loaded " + m_ShopItems.Count() + " shop items");
    }
    
    //! Populate category combo box
    protected void PopulateCategoryCombo()
    {
        if (!m_CategoryCombo)
            return;
        
        m_CategoryCombo.ClearAll();
        m_CategoryCombo.AddItem("All Categories");
        
        // Add categories based on shop type
        switch (m_ShopType)
        {
            case "vehicle":
                m_CategoryCombo.AddItem("Cars");
                m_CategoryCombo.AddItem("Trucks");
                m_CategoryCombo.AddItem("Boats");
                m_CategoryCombo.AddItem("Aircraft");
                break;
            case "gear":
                m_CategoryCombo.AddItem("Weapons");
                m_CategoryCombo.AddItem("Clothing");
                m_CategoryCombo.AddItem("Equipment");
                m_CategoryCombo.AddItem("Tools");
                break;
            case "general":
                m_CategoryCombo.AddItem("Food");
                m_CategoryCombo.AddItem("Drinks");
                m_CategoryCombo.AddItem("Medical");
                m_CategoryCombo.AddItem("Misc");
                break;
        }
        
        m_CategoryCombo.SetCurrentItem(0);
    }
    
    //! Refresh all displays
    protected void RefreshDisplay()
    {
        RefreshShopItemList();
        RefreshPlayerItemList();
        UpdatePlayerCashDisplay();
        UpdateSelectedItemInfo();
        UpdateButtonStates();
    }
    
    //! Refresh shop item list
    protected void RefreshShopItemList()
    {
        if (!m_ItemListBox)
            return;
        
        m_ItemListBox.ClearItems();
        
        string selectedCategory = GetSelectedCategory();
        
        for (int i = 0; i < m_ShopItems.Count(); i++)
        {
            ref EdenShopItem item = m_ShopItems[i];
            
            // Filter by category if selected
            if (selectedCategory != "All Categories" && item.GetCategory() != selectedCategory)
                continue;
            
            string displayText = FormatShopItemDisplay(item);
            m_ItemListBox.AddItem(displayText, null, 0);
        }
    }
    
    //! Refresh player item list
    protected void RefreshPlayerItemList()
    {
        if (!m_PlayerItemListBox || !m_PlayerData)
            return;
        
        m_PlayerItemListBox.ClearItems();
        m_PlayerItems.Clear();
        
        ref array<ref EdenInventoryItem> playerInventory = m_PlayerData.GetInventory();
        
        for (int i = 0; i < playerInventory.Count(); i++)
        {
            ref EdenInventoryItem item = playerInventory[i];
            
            // Only show items that can be sold at this shop
            if (CanSellItemAtShop(item))
            {
                m_PlayerItems.Insert(item);
                string displayText = FormatPlayerItemDisplay(item);
                m_PlayerItemListBox.AddItem(displayText, null, 0);
            }
        }
    }
    
    //! Format shop item for display
    protected string FormatShopItemDisplay(EdenShopItem item)
    {
        return item.GetDisplayName() + " - $" + item.GetBuyPrice();
    }
    
    //! Format player item for display
    protected string FormatPlayerItemDisplay(EdenInventoryItem item)
    {
        int sellPrice = GetItemSellPrice(item);
        return GetItemDisplayName(item.GetItemClass()) + " (" + item.GetQuantity() + ") - $" + sellPrice;
    }
    
    //! Get selected category from combo box
    protected string GetSelectedCategory()
    {
        if (!m_CategoryCombo)
            return "All Categories";
        
        int selectedIndex = m_CategoryCombo.GetCurrentItem();
        return m_CategoryCombo.GetItemText(selectedIndex);
    }
    
    //! Check if item can be sold at this shop
    protected bool CanSellItemAtShop(EdenInventoryItem item)
    {
        // Logic to determine if item can be sold at this shop type
        return true; // For now, allow all items
    }
    
    //! Get sell price for item
    protected int GetItemSellPrice(EdenInventoryItem item)
    {
        // Get sell price from shop manager
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenShopManager shopManager = gameMode.GetShopManager();
            if (shopManager)
            {
                return shopManager.GetItemSellPrice(item.GetItemClass(), m_ShopType);
            }
        }
        
        return 0;
    }
    
    //! Get item display name
    protected string GetItemDisplayName(string itemClass)
    {
        // This would map item classes to display names
        return itemClass;
    }
    
    //! Update player cash display
    protected void UpdatePlayerCashDisplay()
    {
        if (!m_PlayerCashText || !m_PlayerData)
            return;
        
        int cash = m_PlayerData.GetCash();
        m_PlayerCashText.SetText("Cash: $" + FormatMoney(cash));
    }
    
    //! Update selected item info
    protected void UpdateSelectedItemInfo()
    {
        if (!m_ItemInfoText)
            return;
        
        string infoText = "";
        
        if (m_SelectedShopItemIndex >= 0 && m_SelectedShopItemIndex < m_ShopItems.Count())
        {
            ref EdenShopItem item = m_ShopItems[m_SelectedShopItemIndex];
            infoText = GetShopItemInfo(item);
        }
        else if (m_SelectedPlayerItemIndex >= 0 && m_SelectedPlayerItemIndex < m_PlayerItems.Count())
        {
            ref EdenInventoryItem item = m_PlayerItems[m_SelectedPlayerItemIndex];
            infoText = GetPlayerItemInfo(item);
        }
        else
        {
            infoText = "Select an item to view details";
        }
        
        m_ItemInfoText.SetText(infoText);
    }
    
    //! Get shop item info
    protected string GetShopItemInfo(EdenShopItem item)
    {
        string info = "Item: " + item.GetDisplayName() + "\n";
        info += "Price: $" + item.GetBuyPrice() + "\n";
        info += "Category: " + item.GetCategory() + "\n";
        info += "Description: " + item.GetDescription();
        
        return info;
    }
    
    //! Get player item info
    protected string GetPlayerItemInfo(EdenInventoryItem item)
    {
        string info = "Item: " + GetItemDisplayName(item.GetItemClass()) + "\n";
        info += "Quantity: " + item.GetQuantity() + "\n";
        info += "Sell Price: $" + GetItemSellPrice(item) + "\n";
        info += "Weight: " + (item.GetWeight() * item.GetQuantity()) + "kg";
        
        return info;
    }
    
    //! Update button states
    protected void UpdateButtonStates()
    {
        bool canBuy = (m_SelectedShopItemIndex >= 0 && m_SelectedShopItemIndex < m_ShopItems.Count());
        bool canSell = (m_SelectedPlayerItemIndex >= 0 && m_SelectedPlayerItemIndex < m_PlayerItems.Count());
        
        if (m_BuyButton)
            m_BuyButton.SetEnabled(canBuy);
        
        if (m_SellButton)
            m_SellButton.SetEnabled(canSell);
        
        // Update price display
        if (m_SelectedItemPriceText)
        {
            if (canBuy)
            {
                ref EdenShopItem shopItem = m_ShopItems[m_SelectedShopItemIndex];
                int quantity = GetQuantityFromEdit();
                int totalPrice = shopItem.GetBuyPrice() * quantity;
                m_SelectedItemPriceText.SetText("Total: $" + FormatMoney(totalPrice));
            }
            else if (canSell)
            {
                ref EdenInventoryItem playerItem = m_PlayerItems[m_SelectedPlayerItemIndex];
                int quantity = GetQuantityFromEdit();
                int sellPrice = GetItemSellPrice(playerItem);
                int totalPrice = sellPrice * quantity;
                m_SelectedItemPriceText.SetText("Total: $" + FormatMoney(totalPrice));
            }
            else
            {
                m_SelectedItemPriceText.SetText("");
            }
        }
    }
    
    //! Format money with commas
    protected string FormatMoney(int amount)
    {
        string amountStr = amount.ToString();
        string formatted = "";
        int length = amountStr.Length();
        
        for (int i = 0; i < length; i++)
        {
            if (i > 0 && (length - i) % 3 == 0)
                formatted += ",";
            formatted += amountStr.Get(i);
        }
        
        return formatted;
    }
    
    //! Get quantity from edit box
    protected int GetQuantityFromEdit()
    {
        if (!m_QuantityEdit)
            return 1;

        string quantityText = m_QuantityEdit.GetText();
        int quantity = quantityText.ToInt();

        if (quantity <= 0)
            quantity = 1;

        return quantity;
    }

    //! Handle shop item list selection
    void OnShopItemSelectionChanged()
    {
        if (!m_ItemListBox)
            return;

        m_SelectedShopItemIndex = m_ItemListBox.GetSelectedItem();
        m_SelectedPlayerItemIndex = -1; // Clear player item selection

        if (m_PlayerItemListBox)
            m_PlayerItemListBox.SelectItem(-1, false);

        UpdateSelectedItemInfo();
        UpdateButtonStates();

        Print("[EdenShopDialog] Shop item selected: " + m_SelectedShopItemIndex);
    }

    //! Handle player item list selection
    void OnPlayerItemSelectionChanged()
    {
        if (!m_PlayerItemListBox)
            return;

        m_SelectedPlayerItemIndex = m_PlayerItemListBox.GetSelectedItem();
        m_SelectedShopItemIndex = -1; // Clear shop item selection

        if (m_ItemListBox)
            m_ItemListBox.SelectItem(-1, false);

        UpdateSelectedItemInfo();
        UpdateButtonStates();

        Print("[EdenShopDialog] Player item selected: " + m_SelectedPlayerItemIndex);
    }

    //! Handle category combo box change
    void OnCategoryChanged()
    {
        RefreshShopItemList();
        m_SelectedShopItemIndex = -1;
        UpdateSelectedItemInfo();
        UpdateButtonStates();

        Print("[EdenShopDialog] Category changed to: " + GetSelectedCategory());
    }

    //! Handle buy button click
    void OnBuyButtonClick()
    {
        if (m_SelectedShopItemIndex < 0 || m_SelectedShopItemIndex >= m_ShopItems.Count())
            return;

        if (!m_PlayerData)
            return;

        ref EdenShopItem item = m_ShopItems[m_SelectedShopItemIndex];
        int quantity = GetQuantityFromEdit();
        int totalPrice = item.GetBuyPrice() * quantity;

        // Check if player has enough money
        if (m_PlayerData.GetCash() < totalPrice)
        {
            Print("[EdenShopDialog] Insufficient funds for purchase");
            return;
        }

        // Process purchase through shop manager
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenShopManager shopManager = gameMode.GetShopManager();
            if (shopManager)
            {
                bool success = shopManager.ProcessPurchase(m_PlayerId, item.GetItemClass(), quantity, totalPrice);
                if (success)
                {
                    Print("[EdenShopDialog] Purchase successful: " + item.GetItemClass() + " x" + quantity);
                    RefreshDisplay();
                }
                else
                {
                    Print("[EdenShopDialog] Purchase failed");
                }
            }
        }
    }

    //! Handle sell button click
    void OnSellButtonClick()
    {
        if (m_SelectedPlayerItemIndex < 0 || m_SelectedPlayerItemIndex >= m_PlayerItems.Count())
            return;

        if (!m_PlayerData)
            return;

        ref EdenInventoryItem item = m_PlayerItems[m_SelectedPlayerItemIndex];
        int quantity = GetQuantityFromEdit();
        int sellPrice = GetItemSellPrice(item);
        int totalPrice = sellPrice * quantity;

        // Check if player has enough items
        if (item.GetQuantity() < quantity)
        {
            Print("[EdenShopDialog] Insufficient items for sale");
            return;
        }

        // Process sale through shop manager
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenShopManager shopManager = gameMode.GetShopManager();
            if (shopManager)
            {
                bool success = shopManager.ProcessSale(m_PlayerId, item.GetItemClass(), quantity, totalPrice);
                if (success)
                {
                    Print("[EdenShopDialog] Sale successful: " + item.GetItemClass() + " x" + quantity);
                    RefreshDisplay();
                }
                else
                {
                    Print("[EdenShopDialog] Sale failed");
                }
            }
        }
    }

    override void OnUpdate()
    {
        super.OnUpdate();

        // Update display periodically
        static int lastUpdate = 0;
        int currentTime = GetGame().GetWorld().GetWorldTime();

        if (currentTime - lastUpdate > 3000) // 3 seconds
        {
            UpdatePlayerCashDisplay();
            lastUpdate = currentTime;
        }
    }

    override bool OnClick(Widget w, int x, int y, int button)
    {
        // Handle base dialog clicks first
        if (super.OnClick(w, x, y, button))
            return true;

        // Handle shop-specific clicks
        if (w == m_BuyButton)
        {
            OnBuyButtonClick();
            return true;
        }
        else if (w == m_SellButton)
        {
            OnSellButtonClick();
            return true;
        }
        else if (w == m_ItemListBox)
        {
            OnShopItemSelectionChanged();
            return true;
        }
        else if (w == m_PlayerItemListBox)
        {
            OnPlayerItemSelectionChanged();
            return true;
        }
        else if (w == m_CategoryCombo)
        {
            OnCategoryChanged();
            return true;
        }

        return false;
    }

    override void OnClose()
    {
        super.OnClose();

        m_PlayerData = null;
        m_ShopItems.Clear();
        m_PlayerItems.Clear();

        Print("[EdenShopDialog] Shop dialog closed for player: " + m_PlayerId);
    }
}
