---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-59-59\console.log started at 2025-07-27 20:59:59 (2025-07-28 00:59:59 UTC)

20:59:59.018 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-59-59' to filesystem under name logs
20:59:59.018 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
20:59:59.020 ENGINE       : Initializing engine, version 184702
20:59:59.020 ENGINE       : CLI Params: -config ..\..\..\server.json -profile ..\..\..\saves -addonsDir addons -logLevel 3 
20:59:59.023 ENGINE       : Addon dirs:
20:59:59.023  ENGINE       : dir: 'addons'
20:59:59.023  ENGINE       : dir: './addons'
20:59:59.023  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
20:59:59.023 ENGINE       : Available addons:
20:59:59.023  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
20:59:59.023  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
20:59:59.072 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
20:59:59.073 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
20:59:59.073 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
20:59:59.209 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
20:59:59.211 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
20:59:59.211 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-59-59/resourceDatabase.rdb)
20:59:59.315 ENGINE       : GameProject load
20:59:59.315  ENGINE       : Loaded addons:
20:59:59.315   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
20:59:59.315   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
20:59:59.316 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
20:59:59.317 INIT         : GameProject engine user settings load @"$profile:.save/settings/ReforgerEngineSettings.conf"
20:59:59.317  ENGINE       : Engine user settings config loaded
20:59:59.317 PROFILING    : Settings changed took: 0.001100 ms
20:59:59.317 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
20:59:59.317 RENDER       : Basic screen setup:
20:59:59.318 RENDER       :   Back buffer width x height: 0x0
20:59:59.318 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
20:59:59.318 RENDER       :   MSAA                      : none
20:59:59.318 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
20:59:59.318 RENDER       : Detected output devices:
20:59:59.318 RENDER       :  * 1 device/s connected to Dummy:
20:59:59.318 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
20:59:59.318 RENDER       :   Concurrent frames limit 2
20:59:59.318 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
20:59:59.372 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
20:59:59.377 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
20:59:59.377 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
20:59:59.377 PROFILING    : Settings changed took: 0.021100 ms
20:59:59.381 ENGINE       : Initializing inputs.
20:59:59.387 NETWORK      : Initializing networking.
20:59:59.387 SCRIPT       : SCRIPT       : Initializing scripts
20:59:59.388  SCRIPT       : SCRIPT       : ScriptProjectManager init
20:59:59.388  PROFILING    : ScriptProjectManager init took: 0.030200 ms
20:59:59.407 PROFILING    : Initializing scripts took: 19.530100 ms
20:59:59.407 ENGINE       : Enfusion engine successfully created.
20:59:59.417 GUI          : Using default language (en_us)
20:59:59.417 GUI          : Loading 'en_us' localization file.
20:59:59.417 INIT         : INIT         : Loading StringTable
20:59:59.441 PROFILING    : Loading StringTable took: 24.414500 ms
20:59:59.441 SCRIPT       : SCRIPT       : Compiling GameLib scripts
20:59:59.469  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
20:59:59.473 PROFILING    : Compiling GameLib scripts took: 31.504200 ms
20:59:59.473 SCRIPT       : SCRIPT       : Compiling Game scripts
20:59:59.966 SCRIPT       : Compiling Game scripts
20:59:59.969  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
21:00:00.004  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
21:00:00.203  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:00:00.203  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:00:00.203  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:00:00.218  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
21:00:00.249  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
21:00:00.323  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
21:00:00.385  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:00.428  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
21:00:00.429  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
21:00:00.496  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
21:00:00.496  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:00.496  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:00.497  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:00.497  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:00.497  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:00.532  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:00.532  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:00.534  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:00.534  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:00.534  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:00.534  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:00.547  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:00.561  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:00.561  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:00.561  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:00.586  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
21:00:00.586  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
21:00:00.590  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:00:00.590  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:00:00.591  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:00:00.592  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
21:00:00.592  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:00.636  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
21:00:01.345 PROFILING    : Compiling Game scripts took: 1871.908500 ms
21:00:01.347 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
21:00:01.362 BACKEND      : Loading dedicated server config.
21:00:01.362  BACKEND      : Server config loaded.
21:00:01.362  BACKEND      : JSON Schema Validation:
21:00:01.362   BACKEND      : JSON is Valid
21:00:01.368 ENGINE       : Game successfully created.
21:00:01.400 PLATFORM     : Save data from container 'settings' have been loaded
21:00:01.500 PLATFORM     : Save data from container 'sessions' have been loaded
21:00:01.502 PLATFORM     : GameProject user settings load from profile @"ReforgerGameSettings.conf"
21:00:01.502  ENGINE       : Game user settings config loaded
21:00:01.502 NETWORK      : Starting dedicated server using command line args.
21:00:01.599 BACKEND      : Required addons are ready to use.
21:00:01.897 PLATFORM     : Save data from container 'settings' have been loaded
21:00:01.996 PLATFORM     : Save data from container 'sessions' have been loaded
21:00:01.996 PLATFORM     : GameProject user settings load from profile @"ReforgerGameSettings.conf"
21:00:01.996  ENGINE       : Game user settings config loaded
21:00:01.996 WORLD        : WORLD        : Entities load @"{9DF143A76F5C6460}worlds/MP/CTI_Campaign_Eden.ent"
21:00:02.707 WORLD        : Entities load @"{9DF143A76F5C6460}worlds/MP/CTI_Campaign_Eden.ent"
21:00:02.707  WORLD        : Subscene load @"{853E92315D1D9EFE}worlds/Eden/Eden.ent"
21:00:02.707   RESOURCES    : GetResourceObject @"{C8FAB83670BCF7C3}Prefabs/Structures/Cultural/Calvaries/CalvaryLarge_01.et"
21:00:02.707    WORLD        : Entity prefab load @"{C8FAB83670BCF7C3}Prefabs/Structures/Cultural/Calvaries/CalvaryLarge_01.et"
21:00:02.708     RESOURCES (E): Wrong GUID/name for resource @"{45ECF6B1797D3CD9}system/wbdata/PreviewWindow/TransparentMat.emat" in property "AssignedMaterial"
21:00:02.918 PROFILING    : Entities load took: 920.677000 ms
21:00:02.918 WORLD        : WORLD        : Game::LoadEntities
21:00:02.932  NETWORK      : Enabled lag compensation, 29 frames with 100 FPS and 280 ms rewind limits.
21:00:02.932  WORLD        : WORLD        : Preload
21:00:03.487  PROFILING    : Preload took: 555.743000 ms
21:00:03.487  WORLD        : WORLD        : Wait for preloading data
21:00:03.490 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine_LOD1.ptc"
21:00:03.490  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:00:03.490 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine.ptc"
21:00:03.490  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:00:03.490 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine_LOD2.ptc"
21:00:03.490  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:00:04.500  PROFILING    : Wait for preloading data took: 1011.743100 ms
21:00:04.500  WORLD        : WORLD        : CreateEntities
21:00:04.500   WORLD        : WORLD        : CreateEntities
21:00:04.500    WORLD        : WORLD        : NotifyBeforeEntitiesCreated
21:00:04.501     RESOURCES    : Loaded mission headers from 2 addon(s)!
21:00:04.502    PROFILING    : NotifyBeforeEntitiesCreated took: 1.744500 ms
21:00:04.533 WORLD        : Game::LoadEntities
21:00:04.533  WORLD        : CreateEntities
21:00:04.533   WORLD        : CreateEntities
21:00:04.533    ENTITY       : Create entity @"ENTITY:1" ('world', GenericWorldEntity) at <0.000000 0.000000 0.000000>
21:00:04.533     RESOURCES (E):   Failed to decompress memory image data. Data is not DXT.
21:00:04.533     WORLD     (E): Shore map CPU data creation failed - decompression of DF near atlas texture failed.
21:00:04.534     WORLD     (E): Loading of shore map failed - creation of CPU data failed
21:00:04.534     WORLD     (E): Loading of shore map failed - post load checks failed
21:00:04.535    ENTITY       : Create entity @"ENTITY:2" ('Eden', GenericTerrainEntity) at <0.000000 0.000000 0.000000>
21:00:04.535     PROFILING    : PROFILING    : Terrain load
21:00:07.070     PROFILING    : Terrain load took: 2536.031000 ms
21:00:10.347    ENTITY       : Create entity @"ENTITY:610688" ('StaticModelEntity','Assets/Structures/Infrastructure/Towers/TransmitterTower_01/TransmitterTower_01_small.xob') at <1931.796021 81.777000 5138.725098> @"{6A004A8F0571D456}Prefabs/Structures/Infrastructure/Towers/TransmitterTower_01/TransmitterTower_01_small.et"
21:00:10.348     DEFAULT   (W): World doesn't contain RadioManagerEntity to support any BaseRadioComponent.
21:00:12.838    ENTITY       : Create entity @"ENTITY:1055324" ('GenericEntity') at <6225.922852 107.562500 5118.536133>
21:00:12.838     ENTITY    (E): Door action not set for @"ENTITY:1055324" ('GenericEntity') at <6225.922852 107.562500 5118.536133>
21:00:12.839    ENTITY       : Create entity @"ENTITY:1055357" ('GenericEntity') at <5252.018066 38.411251 11397.515625>
21:00:12.839     ENTITY    (E): Door action not set for @"ENTITY:1055357" ('GenericEntity') at <5252.018066 38.411251 11397.515625>
21:00:12.839    ENTITY       : Create entity @"ENTITY:1055390" ('GenericEntity') at <3274.975098 114.904022 5189.225098>
21:00:12.839     ENTITY    (E): Door action not set for @"ENTITY:1055390" ('GenericEntity') at <3274.975098 114.904022 5189.225098>
21:00:12.840    ENTITY       : Create entity @"ENTITY:1055423" ('GenericEntity') at <3741.502930 80.400497 6630.824219>
21:00:12.840     ENTITY    (E): Door action not set for @"ENTITY:1055423" ('GenericEntity') at <3741.502930 80.400497 6630.824219>
21:00:12.840    ENTITY       : Create entity @"ENTITY:1055458" ('GenericEntity') at <5642.888184 89.763420 5352.812012>
21:00:12.840     ENTITY    (E): Door action not set for @"ENTITY:1055458" ('GenericEntity') at <5642.888184 89.763420 5352.812012>
21:00:12.841    ENTITY       : Create entity @"ENTITY:1055491" ('GenericEntity') at <1365.465942 41.945751 5944.875000>
21:00:12.841     ENTITY    (E): Door action not set for @"ENTITY:1055491" ('GenericEntity') at <1365.465942 41.945751 5944.875000>
21:00:12.841    ENTITY       : Create entity @"ENTITY:1055524" ('GenericEntity') at <4556.948242 20.443750 9608.875000>
21:00:12.841     ENTITY    (E): Door action not set for @"ENTITY:1055524" ('GenericEntity') at <4556.948242 20.443750 9608.875000>
21:00:12.841    ENTITY       : Create entity @"ENTITY:1055557" ('GenericEntity') at <4612.849121 158.267273 7012.264160>
21:00:12.841     ENTITY    (E): Door action not set for @"ENTITY:1055557" ('GenericEntity') at <4612.849121 158.267273 7012.264160>
21:00:12.842    ENTITY       : Create entity @"ENTITY:1055590" ('GenericEntity') at <5782.269043 28.741789 10179.600586>
21:00:12.842     ENTITY    (E): Door action not set for @"ENTITY:1055590" ('GenericEntity') at <5782.269043 28.741789 10179.600586>
21:00:12.842    ENTITY       : Create entity @"ENTITY:1055623" ('GenericEntity') at <5571.204102 91.851639 5976.666992>
21:00:12.842     ENTITY    (E): Door action not set for @"ENTITY:1055623" ('GenericEntity') at <5571.204102 91.851639 5976.666992>
21:00:12.842    ENTITY       : Create entity @"ENTITY:1055656" ('GenericEntity') at <7223.090820 136.826721 2315.154053>
21:00:12.842     ENTITY    (E): Door action not set for @"ENTITY:1055656" ('GenericEntity') at <7223.090820 136.826721 2315.154053>
21:00:12.843    ENTITY       : Create entity @"ENTITY:1055689" ('GenericEntity') at <7223.857910 137.444275 2372.346924>
21:00:12.843     ENTITY    (E): Door action not set for @"ENTITY:1055689" ('GenericEntity') at <7223.857910 137.444275 2372.346924>
21:00:12.843    ENTITY       : Create entity @"ENTITY:1055722" ('GenericEntity') at <4522.501953 15.952277 10653.205078>
21:00:12.843     ENTITY    (E): Door action not set for @"ENTITY:1055722" ('GenericEntity') at <4522.501953 15.952277 10653.205078>
21:00:12.844    ENTITY       : Create entity @"ENTITY:1055755" ('GenericEntity') at <9417.439453 45.156071 1688.795044>
21:00:12.844     ENTITY    (E): Door action not set for @"ENTITY:1055755" ('GenericEntity') at <9417.439453 45.156071 1688.795044>
21:00:13.005    ENTITY       : Create entity @"ENTITY:1104299" ('StaticModelEntity') at <8831.291016 95.934265 2673.487061>
21:00:13.005     MATERIAL     : Material load @"{45ECF6B1797D3CD9}system/wbdata/PreviewWindow/TransparentMat.emat"
21:00:13.005      MATERIAL  (E): Material file not found
21:00:13.005     MATERIAL  (E): Object @"Assets/Structures/Cultural/Calvaries/CalvaryLarge_01/CalvaryLarge_01.xob" - cannot load material @"system/wbdata/PreviewWindow/TransparentMat.emat", creating default one (class MatPBRBasic)
21:00:16.447    ENTITY       : Create entity @"ENTITY:2305843009213698652" ('GenericEntity','Assets/Props/Military/Antennas/Antenna_R161_01/Antenna_R161_01.xob') at <9354.231445 204.427994 1162.372070>
21:00:16.447     DEFAULT   (W): BaseRadioComponent does not have any transceiver. (Entity: @"ENTITY:2305843009213698652" ('GenericEntity','Assets/Props/Military/Antennas/Antenna_R161_01/Antenna_R161_01.xob') at <9354.231445 204.427994 1162.372070>). Only printing this message once. There're possibly more nodes with this requirement.
21:00:17.089    WORLD        : WORLD        : NotifyBeforeEntitiesInitialized
21:00:17.090    PROFILING    : NotifyBeforeEntitiesInitialized took: 0.028800 ms
21:00:17.090    WORLD        : InitEntities 'world'
21:00:17.090     ENTITY       : Init entity @"ENTITY:3" ('SCR_MapEntity1', SCR_MapEntity) at <6400.000000 0.000000 6400.000000> @"{E1B88C66BF6CA1F9}Prefabs/World/Game/MapEntity_Everon.et"
21:00:17.090      SCRIPT    (W): SCR_MapEntity: Cannot get the size from terrain. Using default.
21:00:19.179     ENTITY       : Init entity @"ENTITY:2305843009213695743" ('SCR_BaseTaskManager') at <20.724001 0.000000 7.936000> @"{17E3EF2CF455460F}Prefabs/MP/Campaign/CampaignTaskManager.et"
21:00:19.179      GUI          : WidgetManager: CrateWidgets @"{ACCF501DD69CAF7B}UI/layouts/Tasks/TaskList.layout"
21:00:19.179       SCRIPT    (E): No data found for keyboard:KC_ESCAPE !! Check 'chimeraMapping.conf' and add data if necessary! Provided Actioname: TasksClose.
21:00:19.526    WORLD        : WORLD        : NotifyEntitiesInitialized
21:00:19.527     TERRAIN      : TERRAIN      : Initializing road networks...
21:00:19.529     PROFILING    : Initializing road networks... took: 2.349600 ms
21:00:19.625     INIT         : INIT         : SoundWorldInit
21:00:19.662     PROFILING    : SoundWorldInit took: 37.473900 ms
21:00:19.662    PROFILING    : NotifyEntitiesInitialized took: 135.198500 ms
21:00:19.662   PROFILING    : CreateEntities took: 15162.390500 ms
21:00:19.662  PROFILING    : CreateEntities took: 15162.675300 ms
21:00:19.663  WORLD        : WORLD        : EOnActivate
21:00:19.699  PROFILING    : EOnActivate took: 36.688000 ms
21:00:19.700 PROFILING    : Game::LoadEntities took: 16783.199800 ms
21:00:19.700 PROFILING    : * LoadEntities: 16783.325300 ms
21:00:19.815 NETWORK      : RPL listen address not specified. Using default fallback.
21:00:19.830 NETWORK      : Starting RPL server, listening on address 0.0.0.0:2001, fastValidation=true
21:00:21.228 BACKEND      : Server registered with address: *************:2001
21:00:21.228 BACKEND      : Direct Join Code: 0023180599
21:00:21.228 BACKEND      : Ping Site: new_york
21:00:21.616 DEFAULT      : Entered online game state.
21:00:21.768 WORLD        : Frame start
21:00:21.768  NETWORK      : Projectiles will simulate using: EOnFrame
21:00:21.768  SCRIPT       : ScenarioFramework: Available tasks are empty, no new tasks will be generated.
21:00:21.782 WORLD        : UpdateEntities
21:00:21.783  WORLD        : Frame
21:00:21.783   SCRIPT       : SCR_BaseGameMode::OnGameStateChanged = GAME
21:00:50.391 RPL          : IReplication:: Finishing...
21:00:50.491 RPL          : rpl::Pip::ProcessNetToGame
21:00:50.491  RPL          : IReplication: Finished
21:00:50.493 RPL          : Pip::Destroy
21:00:53.318 SCRIPT    (E): Leaked 'Road' script instance (1x)!
21:00:53.320 ENGINE       : Game destroyed.
21:00:53.545 RPL          : Pip::Destroy
21:00:53.558 RESOURCES (E): ==== Resource leaks ====
21:00:53.558 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
