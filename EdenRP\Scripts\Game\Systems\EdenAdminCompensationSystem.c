//! Eden Admin Compensation System
//! Handles admin item compensation and player assistance
class EdenAdminCompensationSystem
{
    protected ref EdenAdminManager m_AdminManager;
    protected ref map<string, ref array<ref EdenInventoryItem>> m_CompensationTemplates;
    protected ref array<ref EdenCompensationRequest> m_PendingRequests;
    protected ref array<ref EdenCompensationLog> m_CompensationHistory;
    
    void EdenAdminCompensationSystem()
    {
        m_CompensationTemplates = new map<string, ref array<ref EdenInventoryItem>>();
        m_PendingRequests = new array<ref EdenCompensationRequest>();
        m_CompensationHistory = new array<ref EdenCompensationLog>();
    }
    
    void Initialize(EdenAdminManager adminManager)
    {
        m_AdminManager = adminManager;
        
        InitializeCompensationTemplates();
        
        Print("[EdenAdminCompensationSystem] Admin compensation system initialized");
    }
    
    //! Initialize compensation templates
    protected void InitializeCompensationTemplates()
    {
        // Police compensation template
        ref array<ref EdenInventoryItem> policeItems = new array<ref EdenInventoryItem>();
        
        ref EdenInventoryItem pistol = new EdenInventoryItem();
        pistol.SetItemClass("Weapon_M9");
        pistol.SetQuantity(1);
        pistol.SetWeight(1.5);
        policeItems.Insert(pistol);
        
        ref EdenInventoryItem ammo = new EdenInventoryItem();
        ammo.SetItemClass("Magazine_9x19_M9_15rnd");
        ammo.SetQuantity(3);
        ammo.SetWeight(0.5);
        policeItems.Insert(ammo);
        
        ref EdenInventoryItem restraints = new EdenInventoryItem();
        restraints.SetItemClass("Restraints");
        restraints.SetQuantity(5);
        restraints.SetWeight(0.2);
        policeItems.Insert(restraints);
        
        m_CompensationTemplates.Set("Police", policeItems);
        
        // Medical compensation template
        ref array<ref EdenInventoryItem> medicalItems = new array<ref EdenInventoryItem>();
        
        ref EdenInventoryItem medkit = new EdenInventoryItem();
        medkit.SetItemClass("MedicalKit");
        medkit.SetQuantity(3);
        medkit.SetWeight(2.0);
        medicalItems.Insert(medkit);
        
        ref EdenInventoryItem defibrillator = new EdenInventoryItem();
        defibrillator.SetItemClass("Defibrillator");
        defibrillator.SetQuantity(1);
        defibrillator.SetWeight(3.0);
        medicalItems.Insert(defibrillator);
        
        m_CompensationTemplates.Set("Medical", medicalItems);
        
        // Civilian starter template
        ref array<ref EdenInventoryItem> civilianItems = new array<ref EdenInventoryItem>();
        
        ref EdenInventoryItem food = new EdenInventoryItem();
        food.SetItemClass("Food_Apple");
        food.SetQuantity(5);
        food.SetWeight(0.3);
        civilianItems.Insert(food);
        
        ref EdenInventoryItem water = new EdenInventoryItem();
        water.SetItemClass("Consumable_Water");
        water.SetQuantity(3);
        water.SetWeight(0.5);
        civilianItems.Insert(water);
        
        m_CompensationTemplates.Set("Civilian", civilianItems);
        
        Print("[EdenAdminCompensationSystem] Compensation templates initialized");
    }
    
    //! Give compensation package to player
    bool GiveCompensationPackage(string adminId, string targetPlayerId, string packageType)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminCompensationSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        if (!m_CompensationTemplates.Contains(packageType))
        {
            Print("[EdenAdminCompensationSystem] Invalid package type: " + packageType);
            return false;
        }
        
        ref array<ref EdenInventoryItem> items = m_CompensationTemplates.Get(packageType);
        
        // Give items to player
        for (int i = 0; i < items.Count(); i++)
        {
            ref EdenInventoryItem item = items[i];
            GiveItemToPlayer(targetPlayerId, item.GetItemClass(), item.GetQuantity());
        }
        
        // Log compensation
        ref EdenCompensationLog log = new EdenCompensationLog();
        log.SetAdminId(adminId);
        log.SetTargetPlayerId(targetPlayerId);
        log.SetPackageType(packageType);
        log.SetTimestamp(GetGame().GetWorld().GetWorldTime());
        log.SetReason("Admin compensation package");
        
        m_CompensationHistory.Insert(log);
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "GiveCompensation", targetPlayerId, "Gave " + packageType + " compensation package");
        
        Print("[EdenAdminCompensationSystem] Compensation package given: " + packageType + " to " + targetPlayerId);
        return true;
    }
    
    //! Give custom item to player
    bool GiveCustomItem(string adminId, string targetPlayerId, string itemClass, int quantity, string reason = "")
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminCompensationSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        if (quantity <= 0)
        {
            Print("[EdenAdminCompensationSystem] Invalid quantity: " + quantity);
            return false;
        }
        
        // Give item to player
        bool success = GiveItemToPlayer(targetPlayerId, itemClass, quantity);
        
        if (success)
        {
            // Log compensation
            ref EdenCompensationLog log = new EdenCompensationLog();
            log.SetAdminId(adminId);
            log.SetTargetPlayerId(targetPlayerId);
            log.SetPackageType("Custom");
            log.SetTimestamp(GetGame().GetWorld().GetWorldTime());
            log.SetReason(reason);
            log.SetCustomItems(itemClass + ":" + quantity);
            
            m_CompensationHistory.Insert(log);
            
            // Log admin action
            m_AdminManager.LogAdminAction(adminId, "GiveCustomItem", targetPlayerId, 
                "Gave " + quantity + "x " + itemClass + " - Reason: " + reason);
            
            Print("[EdenAdminCompensationSystem] Custom item given: " + quantity + "x " + itemClass + " to " + targetPlayerId);
        }
        
        return success;
    }
    
    //! Submit compensation request
    bool SubmitCompensationRequest(string playerId, string reason, string itemsLost, string evidence = "")
    {
        // Check if player already has pending request
        for (int i = 0; i < m_PendingRequests.Count(); i++)
        {
            ref EdenCompensationRequest request = m_PendingRequests[i];
            if (request.GetPlayerId() == playerId && request.GetStatus() == "Pending")
            {
                Print("[EdenAdminCompensationSystem] Player already has pending compensation request: " + playerId);
                return false;
            }
        }
        
        // Create new request
        ref EdenCompensationRequest newRequest = new EdenCompensationRequest();
        newRequest.SetPlayerId(playerId);
        newRequest.SetReason(reason);
        newRequest.SetItemsLost(itemsLost);
        newRequest.SetEvidence(evidence);
        newRequest.SetSubmissionTime(GetGame().GetWorld().GetWorldTime());
        newRequest.SetStatus("Pending");
        
        m_PendingRequests.Insert(newRequest);
        
        Print("[EdenAdminCompensationSystem] Compensation request submitted by: " + playerId);
        return true;
    }
    
    //! Process compensation request
    bool ProcessCompensationRequest(string adminId, int requestIndex, bool approved, string adminNotes = "")
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminCompensationSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        if (requestIndex < 0 || requestIndex >= m_PendingRequests.Count())
        {
            Print("[EdenAdminCompensationSystem] Invalid request index: " + requestIndex);
            return false;
        }
        
        ref EdenCompensationRequest request = m_PendingRequests[requestIndex];
        
        if (request.GetStatus() != "Pending")
        {
            Print("[EdenAdminCompensationSystem] Request already processed: " + requestIndex);
            return false;
        }
        
        // Update request
        request.SetStatus(approved ? "Approved" : "Denied");
        request.SetProcessingAdminId(adminId);
        request.SetProcessingTime(GetGame().GetWorld().GetWorldTime());
        request.SetAdminNotes(adminNotes);
        
        // Log admin action
        string action = approved ? "ApproveCompensation" : "DenyCompensation";
        m_AdminManager.LogAdminAction(adminId, action, request.GetPlayerId(), 
            "Request: " + request.GetReason() + " - Notes: " + adminNotes);
        
        Print("[EdenAdminCompensationSystem] Compensation request " + (approved ? "approved" : "denied") + 
              " by admin: " + adminId);
        
        return true;
    }
    
    //! Give item to player (implementation would depend on inventory system)
    protected bool GiveItemToPlayer(string playerId, string itemClass, int quantity)
    {
        // This would integrate with the actual inventory system
        // For now, just log the action
        Print("[EdenAdminCompensationSystem] Giving " + quantity + "x " + itemClass + " to " + playerId);
        return true;
    }
    
    //! Get pending compensation requests
    array<ref EdenCompensationRequest> GetPendingRequests()
    {
        ref array<ref EdenCompensationRequest> pending = new array<ref EdenCompensationRequest>();
        
        for (int i = 0; i < m_PendingRequests.Count(); i++)
        {
            ref EdenCompensationRequest request = m_PendingRequests[i];
            if (request.GetStatus() == "Pending")
                pending.Insert(request);
        }
        
        return pending;
    }
    
    //! Get compensation history
    array<ref EdenCompensationLog> GetCompensationHistory() { return m_CompensationHistory; }
    
    //! Get available compensation packages
    array<string> GetAvailablePackages()
    {
        array<string> packages = new array<string>();
        
        for (int i = 0; i < m_CompensationTemplates.Count(); i++)
        {
            packages.Insert(m_CompensationTemplates.GetKey(i));
        }
        
        return packages;
    }
    
    //! Cleanup
    void Cleanup()
    {
        m_CompensationTemplates.Clear();
        m_PendingRequests.Clear();
        m_CompensationHistory.Clear();
        
        Print("[EdenAdminCompensationSystem] Admin compensation system cleaned up");
    }
}

//! Compensation request data class
class EdenCompensationRequest
{
    protected string m_PlayerId;
    protected string m_Reason;
    protected string m_ItemsLost;
    protected string m_Evidence;
    protected int m_SubmissionTime;
    protected string m_Status;
    protected string m_ProcessingAdminId;
    protected int m_ProcessingTime;
    protected string m_AdminNotes;
    
    void EdenCompensationRequest()
    {
        m_Status = "Pending";
        m_ProcessingAdminId = "";
        m_ProcessingTime = 0;
        m_AdminNotes = "";
    }
    
    // Getters and setters
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    string GetPlayerId() { return m_PlayerId; }
    
    void SetReason(string reason) { m_Reason = reason; }
    string GetReason() { return m_Reason; }
    
    void SetItemsLost(string itemsLost) { m_ItemsLost = itemsLost; }
    string GetItemsLost() { return m_ItemsLost; }
    
    void SetEvidence(string evidence) { m_Evidence = evidence; }
    string GetEvidence() { return m_Evidence; }
    
    void SetSubmissionTime(int time) { m_SubmissionTime = time; }
    int GetSubmissionTime() { return m_SubmissionTime; }
    
    void SetStatus(string status) { m_Status = status; }
    string GetStatus() { return m_Status; }
    
    void SetProcessingAdminId(string adminId) { m_ProcessingAdminId = adminId; }
    string GetProcessingAdminId() { return m_ProcessingAdminId; }
    
    void SetProcessingTime(int time) { m_ProcessingTime = time; }
    int GetProcessingTime() { return m_ProcessingTime; }
    
    void SetAdminNotes(string notes) { m_AdminNotes = notes; }
    string GetAdminNotes() { return m_AdminNotes; }
}

//! Compensation log data class
class EdenCompensationLog
{
    protected string m_AdminId;
    protected string m_TargetPlayerId;
    protected string m_PackageType;
    protected int m_Timestamp;
    protected string m_Reason;
    protected string m_CustomItems;
    
    void EdenCompensationLog()
    {
        m_CustomItems = "";
    }
    
    // Getters and setters
    void SetAdminId(string adminId) { m_AdminId = adminId; }
    string GetAdminId() { return m_AdminId; }
    
    void SetTargetPlayerId(string playerId) { m_TargetPlayerId = playerId; }
    string GetTargetPlayerId() { return m_TargetPlayerId; }
    
    void SetPackageType(string packageType) { m_PackageType = packageType; }
    string GetPackageType() { return m_PackageType; }
    
    void SetTimestamp(int timestamp) { m_Timestamp = timestamp; }
    int GetTimestamp() { return m_Timestamp; }
    
    void SetReason(string reason) { m_Reason = reason; }
    string GetReason() { return m_Reason; }
    
    void SetCustomItems(string customItems) { m_CustomItems = customItems; }
    string GetCustomItems() { return m_CustomItems; }
}
