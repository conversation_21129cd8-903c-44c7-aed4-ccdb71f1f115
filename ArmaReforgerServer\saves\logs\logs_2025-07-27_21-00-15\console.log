---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-00-15\console.log started at 2025-07-27 21:00:15 (2025-07-28 01:00:15 UTC)

21:00:15.901 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-00-15' to filesystem under name logs
21:00:15.901 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
21:00:15.905 ENGINE       : Initializing engine, version 184702
21:00:15.907 ENGINE       : CLI Params: -config C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\server.json -profile C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves -addonsDir addons -logLevel 3 -logStats 
21:00:15.910 ENGINE       : Addon dirs:
21:00:15.910  ENGINE       : dir: 'addons'
21:00:15.910  ENGINE       : dir: './addons'
21:00:15.910  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
21:00:15.910 ENGINE       : Available addons:
21:00:15.910  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
21:00:15.910  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
21:00:15.985 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
21:00:15.987 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
21:00:15.987 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
21:00:16.156 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
21:00:16.157 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
21:00:16.157 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-00-15/resourceDatabase.rdb)
21:00:16.264 ENGINE       : GameProject load
21:00:16.265  ENGINE       : Loaded addons:
21:00:16.265   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
21:00:16.265   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
21:00:16.266 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
21:00:16.266 INIT         : GameProject engine user settings load @"$profile:.save/settings/ReforgerEngineSettings.conf"
21:00:16.267  ENGINE       : Engine user settings config loaded
21:00:16.267 PROFILING    : Settings changed took: 0.001200 ms
21:00:16.267 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
21:00:16.267 RENDER       : Basic screen setup:
21:00:16.268 RENDER       :   Back buffer width x height: 0x0
21:00:16.268 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
21:00:16.268 RENDER       :   MSAA                      : none
21:00:16.268 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
21:00:16.268 RENDER       : Detected output devices:
21:00:16.268 RENDER       :  * 1 device/s connected to Dummy:
21:00:16.268 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
21:00:16.268 RENDER       :   Concurrent frames limit 2
21:00:16.268 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
21:00:16.330 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
21:00:16.334 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
21:00:16.334 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
21:00:16.335 PROFILING    : Settings changed took: 0.017400 ms
21:00:16.338 ENGINE       : Initializing inputs.
21:00:16.345 NETWORK      : Initializing networking.
21:00:16.346 SCRIPT       : SCRIPT       : Initializing scripts
21:00:16.346  SCRIPT       : SCRIPT       : ScriptProjectManager init
21:00:16.346  PROFILING    : ScriptProjectManager init took: 0.013800 ms
21:00:16.365 PROFILING    : Initializing scripts took: 18.619800 ms
21:00:16.365 ENGINE       : Enfusion engine successfully created.
21:00:16.371 GUI          : Using default language (en_us)
21:00:16.371 GUI          : Loading 'en_us' localization file.
21:00:16.371 INIT         : INIT         : Loading StringTable
21:00:16.393 PROFILING    : Loading StringTable took: 21.552600 ms
21:00:16.393 SCRIPT       : SCRIPT       : Compiling GameLib scripts
21:00:16.417  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
21:00:16.420 PROFILING    : Compiling GameLib scripts took: 27.099800 ms
21:00:16.420 SCRIPT       : SCRIPT       : Compiling Game scripts
21:00:16.977 SCRIPT       : Compiling Game scripts
21:00:16.980  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
21:00:17.024  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
21:00:17.259  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:00:17.260  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:00:17.260  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:00:17.275  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
21:00:17.310  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
21:00:17.397  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
21:00:17.458  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.501  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
21:00:17.502  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
21:00:17.577  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
21:00:17.578  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.578  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.579  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.579  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.579  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.622  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.622  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.623  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.623  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.623  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.623  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.637  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:17.648  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:17.648  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:17.649  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:17.678  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
21:00:17.679  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
21:00:17.682  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:00:17.682  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:00:17.683  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:00:17.684  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
21:00:17.685  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:17.730  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
21:00:18.522 PROFILING    : Compiling Game scripts took: 2101.055600 ms
21:00:18.523 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
21:00:18.540 BACKEND      : Loading dedicated server config.
21:00:18.540  BACKEND      : Server config loaded.
21:00:18.540  BACKEND      : JSON Schema Validation:
21:00:18.540   BACKEND      : JSON is Valid
21:00:18.546 ENGINE       : Game successfully created.
21:00:18.559 PLATFORM     : Save data from container 'settings' have been loaded
21:00:18.659 PLATFORM     : Save data from container 'sessions' have been loaded
21:00:18.661 PLATFORM     : GameProject user settings load from profile @"ReforgerGameSettings.conf"
21:00:18.661  ENGINE       : Game user settings config loaded
21:00:18.661 NETWORK      : Starting dedicated server using command line args.
21:00:18.759 BACKEND      : Required addons are ready to use.
21:00:19.057 PLATFORM     : Save data from container 'settings' have been loaded
21:00:19.157 PLATFORM     : Save data from container 'sessions' have been loaded
21:00:19.157 PLATFORM     : GameProject user settings load from profile @"ReforgerGameSettings.conf"
21:00:19.157  ENGINE       : Game user settings config loaded
21:00:19.158 WORLD        : WORLD        : Entities load @"{9DF143A76F5C6460}worlds/MP/CTI_Campaign_Eden.ent"
21:00:19.938 WORLD        : Entities load @"{9DF143A76F5C6460}worlds/MP/CTI_Campaign_Eden.ent"
21:00:19.939  WORLD        : Subscene load @"{853E92315D1D9EFE}worlds/Eden/Eden.ent"
21:00:19.939   RESOURCES    : GetResourceObject @"{C8FAB83670BCF7C3}Prefabs/Structures/Cultural/Calvaries/CalvaryLarge_01.et"
21:00:19.939    WORLD        : Entity prefab load @"{C8FAB83670BCF7C3}Prefabs/Structures/Cultural/Calvaries/CalvaryLarge_01.et"
21:00:19.939     RESOURCES (E): Wrong GUID/name for resource @"{45ECF6B1797D3CD9}system/wbdata/PreviewWindow/TransparentMat.emat" in property "AssignedMaterial"
21:00:20.156 PROFILING    : Entities load took: 998.550100 ms
21:00:20.157 WORLD        : WORLD        : Game::LoadEntities
21:00:20.173  NETWORK      : Enabled lag compensation, 29 frames with 100 FPS and 280 ms rewind limits.
21:00:20.173  WORLD        : WORLD        : Preload
21:00:20.756  PROFILING    : Preload took: 583.031300 ms
21:00:20.757  WORLD        : WORLD        : Wait for preloading data
21:00:20.759 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine_LOD1.ptc"
21:00:20.759  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:00:20.759 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine.ptc"
21:00:20.759  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:00:20.759 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine_LOD2.ptc"
21:00:20.759  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:00:21.875  PROFILING    : Wait for preloading data took: 1117.125700 ms
21:00:21.875  WORLD        : WORLD        : CreateEntities
21:00:21.875   WORLD        : WORLD        : CreateEntities
21:00:21.875    WORLD        : WORLD        : NotifyBeforeEntitiesCreated
21:00:21.877     RESOURCES    : Loaded mission headers from 2 addon(s)!
21:00:21.877    PROFILING    : NotifyBeforeEntitiesCreated took: 1.516600 ms
21:00:21.907 WORLD        : Game::LoadEntities
21:00:21.908  WORLD        : CreateEntities
21:00:21.908   WORLD        : CreateEntities
21:00:21.908    ENTITY       : Create entity @"ENTITY:1" ('world', GenericWorldEntity) at <0.000000 0.000000 0.000000>
21:00:21.908     RESOURCES (E):   Failed to decompress memory image data. Data is not DXT.
21:00:21.908     WORLD     (E): Shore map CPU data creation failed - decompression of DF near atlas texture failed.
21:00:21.909     WORLD     (E): Loading of shore map failed - creation of CPU data failed
21:00:21.910     WORLD     (E): Loading of shore map failed - post load checks failed
21:00:21.910    ENTITY       : Create entity @"ENTITY:2" ('Eden', GenericTerrainEntity) at <0.000000 0.000000 0.000000>
21:00:21.910     PROFILING    : PROFILING    : Terrain load
21:00:24.714     PROFILING    : Terrain load took: 2803.215900 ms
21:00:28.712    ENTITY       : Create entity @"ENTITY:610688" ('StaticModelEntity','Assets/Structures/Infrastructure/Towers/TransmitterTower_01/TransmitterTower_01_small.xob') at <1931.796021 81.777000 5138.725098> @"{6A004A8F0571D456}Prefabs/Structures/Infrastructure/Towers/TransmitterTower_01/TransmitterTower_01_small.et"
21:00:28.712     DEFAULT   (W): World doesn't contain RadioManagerEntity to support any BaseRadioComponent.
21:00:31.806    ENTITY       : Create entity @"ENTITY:1055324" ('GenericEntity') at <6225.922852 107.562500 5118.536133>
21:00:31.807     ENTITY    (E): Door action not set for @"ENTITY:1055324" ('GenericEntity') at <6225.922852 107.562500 5118.536133>
21:00:31.807    ENTITY       : Create entity @"ENTITY:1055357" ('GenericEntity') at <5252.018066 38.411251 11397.515625>
21:00:31.807     ENTITY    (E): Door action not set for @"ENTITY:1055357" ('GenericEntity') at <5252.018066 38.411251 11397.515625>
21:00:31.808    ENTITY       : Create entity @"ENTITY:1055390" ('GenericEntity') at <3274.975098 114.904022 5189.225098>
21:00:31.808     ENTITY    (E): Door action not set for @"ENTITY:1055390" ('GenericEntity') at <3274.975098 114.904022 5189.225098>
21:00:31.809    ENTITY       : Create entity @"ENTITY:1055423" ('GenericEntity') at <3741.502930 80.400497 6630.824219>
21:00:31.809     ENTITY    (E): Door action not set for @"ENTITY:1055423" ('GenericEntity') at <3741.502930 80.400497 6630.824219>
21:00:31.809    ENTITY       : Create entity @"ENTITY:1055458" ('GenericEntity') at <5642.888184 89.763420 5352.812012>
21:00:31.809     ENTITY    (E): Door action not set for @"ENTITY:1055458" ('GenericEntity') at <5642.888184 89.763420 5352.812012>
21:00:31.810    ENTITY       : Create entity @"ENTITY:1055491" ('GenericEntity') at <1365.465942 41.945751 5944.875000>
21:00:31.810     ENTITY    (E): Door action not set for @"ENTITY:1055491" ('GenericEntity') at <1365.465942 41.945751 5944.875000>
21:00:31.811    ENTITY       : Create entity @"ENTITY:1055524" ('GenericEntity') at <4556.948242 20.443750 9608.875000>
21:00:31.811     ENTITY    (E): Door action not set for @"ENTITY:1055524" ('GenericEntity') at <4556.948242 20.443750 9608.875000>
21:00:31.811    ENTITY       : Create entity @"ENTITY:1055557" ('GenericEntity') at <4612.849121 158.267273 7012.264160>
21:00:31.811     ENTITY    (E): Door action not set for @"ENTITY:1055557" ('GenericEntity') at <4612.849121 158.267273 7012.264160>
21:00:31.812    ENTITY       : Create entity @"ENTITY:1055590" ('GenericEntity') at <5782.269043 28.741789 10179.600586>
21:00:31.812     ENTITY    (E): Door action not set for @"ENTITY:1055590" ('GenericEntity') at <5782.269043 28.741789 10179.600586>
21:00:31.812    ENTITY       : Create entity @"ENTITY:1055623" ('GenericEntity') at <5571.204102 91.851639 5976.666992>
21:00:31.812     ENTITY    (E): Door action not set for @"ENTITY:1055623" ('GenericEntity') at <5571.204102 91.851639 5976.666992>
21:00:31.814    ENTITY       : Create entity @"ENTITY:1055656" ('GenericEntity') at <7223.090820 136.826721 2315.154053>
21:00:31.814     ENTITY    (E): Door action not set for @"ENTITY:1055656" ('GenericEntity') at <7223.090820 136.826721 2315.154053>
21:00:31.814    ENTITY       : Create entity @"ENTITY:1055689" ('GenericEntity') at <7223.857910 137.444275 2372.346924>
21:00:31.815     ENTITY    (E): Door action not set for @"ENTITY:1055689" ('GenericEntity') at <7223.857910 137.444275 2372.346924>
21:00:31.815    ENTITY       : Create entity @"ENTITY:1055722" ('GenericEntity') at <4522.501953 15.952277 10653.205078>
21:00:31.815     ENTITY    (E): Door action not set for @"ENTITY:1055722" ('GenericEntity') at <4522.501953 15.952277 10653.205078>
21:00:31.816    ENTITY       : Create entity @"ENTITY:1055755" ('GenericEntity') at <9417.439453 45.156071 1688.795044>
21:00:31.816     ENTITY    (E): Door action not set for @"ENTITY:1055755" ('GenericEntity') at <9417.439453 45.156071 1688.795044>
21:00:32.009    ENTITY       : Create entity @"ENTITY:1104299" ('StaticModelEntity') at <8831.291016 95.934265 2673.487061>
21:00:32.009     MATERIAL     : Material load @"{45ECF6B1797D3CD9}system/wbdata/PreviewWindow/TransparentMat.emat"
21:00:32.009      MATERIAL  (E): Material file not found
21:00:32.009     MATERIAL  (E): Object @"Assets/Structures/Cultural/Calvaries/CalvaryLarge_01/CalvaryLarge_01.xob" - cannot load material @"system/wbdata/PreviewWindow/TransparentMat.emat", creating default one (class MatPBRBasic)
21:00:36.003    ENTITY       : Create entity @"ENTITY:2305843009213698652" ('GenericEntity','Assets/Props/Military/Antennas/Antenna_R161_01/Antenna_R161_01.xob') at <9354.231445 204.427994 1162.372070>
21:00:36.003     DEFAULT   (W): BaseRadioComponent does not have any transceiver. (Entity: @"ENTITY:2305843009213698652" ('GenericEntity','Assets/Props/Military/Antennas/Antenna_R161_01/Antenna_R161_01.xob') at <9354.231445 204.427994 1162.372070>). Only printing this message once. There're possibly more nodes with this requirement.
21:00:36.731    WORLD        : WORLD        : NotifyBeforeEntitiesInitialized
21:00:36.732    PROFILING    : NotifyBeforeEntitiesInitialized took: 0.034300 ms
21:00:36.732    WORLD        : InitEntities 'world'
21:00:36.732     ENTITY       : Init entity @"ENTITY:3" ('SCR_MapEntity1', SCR_MapEntity) at <6400.000000 0.000000 6400.000000> @"{E1B88C66BF6CA1F9}Prefabs/World/Game/MapEntity_Everon.et"
21:00:36.732      SCRIPT    (W): SCR_MapEntity: Cannot get the size from terrain. Using default.
21:00:39.247     ENTITY       : Init entity @"ENTITY:2305843009213695743" ('SCR_BaseTaskManager') at <20.724001 0.000000 7.936000> @"{17E3EF2CF455460F}Prefabs/MP/Campaign/CampaignTaskManager.et"
21:00:39.248      GUI          : WidgetManager: CrateWidgets @"{ACCF501DD69CAF7B}UI/layouts/Tasks/TaskList.layout"
21:00:39.248       SCRIPT    (E): No data found for keyboard:KC_ESCAPE !! Check 'chimeraMapping.conf' and add data if necessary! Provided Actioname: TasksClose.
21:00:39.694    WORLD        : WORLD        : NotifyEntitiesInitialized
21:00:39.694     TERRAIN      : TERRAIN      : Initializing road networks...
21:00:39.697     PROFILING    : Initializing road networks... took: 3.124100 ms
21:00:39.827     INIT         : INIT         : SoundWorldInit
21:00:39.872     PROFILING    : SoundWorldInit took: 44.881900 ms
21:00:39.873    PROFILING    : NotifyEntitiesInitialized took: 178.759800 ms
21:00:39.873   PROFILING    : CreateEntities took: 17998.125000 ms
21:00:39.873  PROFILING    : CreateEntities took: 17998.408700 ms
21:00:39.873  WORLD        : WORLD        : EOnActivate
21:00:39.919  PROFILING    : EOnActivate took: 45.961300 ms
21:00:39.921 PROFILING    : Game::LoadEntities took: 19764.129000 ms
21:00:39.921 PROFILING    : * LoadEntities: 19764.703100 ms
21:00:40.069 NETWORK      : RPL listen address not specified. Using default fallback.
21:00:40.094 NETWORK      : Starting RPL server, listening on address 0.0.0.0:2001, fastValidation=true
21:00:40.107 RPL          : Pip::Destroy
21:00:40.295 ENGINE    (E): Unable to initialize the game
21:00:44.261 SCRIPT    (E): Leaked 'NavlinkRoad' script instance (334x)!
21:00:44.262 SCRIPT    (E): Leaked 'BridgeRoad' script instance (81x)!
21:00:44.262 SCRIPT    (E): ==== Total Leaks (415x)! ====
21:00:44.263 ENGINE       : Game destroyed.
21:00:44.568 RPL          : Pip::Destroy
21:00:44.590 RESOURCES (E): ==== Resource leaks ====
21:00:44.591 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
