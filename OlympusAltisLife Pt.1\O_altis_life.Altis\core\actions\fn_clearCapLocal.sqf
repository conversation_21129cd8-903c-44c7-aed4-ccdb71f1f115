private["_flag","_coolDownVal","_uids10m","_uidsOnCap"];
_flag = _this select 0;

if(currentWeapon player == "") exitWith {
	hint "You need a weapon to clear the cap of vehicles!";
};

if(serverTime < (_flag getVariable["clearCooldown",0])) exitWith {
	hint "This cap has already been cleared recently!";
};

_uids10m = [];
_uidsOnCap = [];
{
	if(_x distance _flag <= 10) then {
		_uids10m pushBack getPlayerUID _x;
	};
	if(_x distance _flag <= 150) then {
		_uidsOnCap pushBack getPlayerUID _x;
	};
} forEach playableUnits;

if(count _uids10m < ((count _uidsOnCap)*3.0)/4.0) exitWith {hint "Not enough people on cap are standing by the flag!";};

[_flag] remoteExec["OES_fnc_clearCap",2];
[
	["event","Cartel Cleared"],
	["player",getPlayerUID player],
	["players_near_flag",_uids10m],
	["players_on_cap",_uidsOnCap],
	["cap",_flag]
] call OEC_fnc_logIt;
hint "The cartel has been cleared of all legal vehicles!";
