"Language" "english" 
"Tokens" 
{ 
SteamBootstrapper_UpdatingSteam				"Updating %appname%..."
SteamBootstrapper_ElevationPrompt			"%appname% needs to self-update before launching."
SteamBootstrapper_ReadOnlyInstallOSX		"%appname% is unable to write to its folder. Make sure you're not trying to run from the disk image."
SteamBootstrapper_RunFromDiskImageOSX		"Please drag %appname% into your Applications folder before launching it."
SteamBootstrapper_ReadOnlyInstall			"%appname% is unable to write to its folder."
SteamBootstrapper_InstallDirNotASCII			"%appname% cannot run from a folder path with non-English characters. Please re-install %appname% to the default folder."
SteamBootstrapper_InstallDirIncompatible		"%appname% cannot run from a folder path with non-ASCII characters on this version of Windows. Please re-install %appname% to the default folder, or install on Windows 7 or later."
SteamBootstrapper_InsufficientDiskSpace		"%appname% needs %nMegaBytes%MB of free disk space to update."
SteamBootstrapper_FailedManifestLoad		"Failed to load package manifest."
SteamBootstrapper_FailedManifestVerify		"Failed to verify package manifest."
SteamBootstrapper_FailedManifestOSType		"System OS version is not compatible with package manifest OS type"
SteamBootstrapper_FailedManifestSave		"Failed to save package manifest."
SteamBootstrapper_FailedPackageRead			"Failed to read package (%pkgName%)."
SteamBootstrapper_CorruptPackageFile		"Corrupt package file (%pkgName%)."
SteamBootstrapper_FailedPackageUnzip		"Failed to unzip package (%pkgName%)."
SteamBootstrapper_UpdateApplyFail			"Failed to apply update, reverting..."
SteamBootstrapper_UpdateCleanup				"Cleaning up..."
SteamBootstrapper_UpdateCleanupFail			"Failed to clean up after update, continuing..."
SteamBootstrapper_UpdateFinalizeFail		"Failed to write installation manifest."
SteamBootstrapper_UpdateComplete			"Update complete, launching %appname%..."
SteamBootstrapper_UpdateChecking			"Checking for available updates..."
SteamBootstrapper_UpdatePrepare				"Preparing to update..."
SteamBootstrapper_UpdateDownload			"Downloading update..."
SteamBootstrapper_UpdateDownloading			"Downloading update (%bytes% of %size% KB)..."
SteamBootstrapper_UpdateExtractingPackage	"Extracting package..."
SteamBootstrapper_UpdateInstalling			"Installing update..."
SteamBootstrapper_InstallVerify				"Verifying installation..."
SteamBootstrapper_DownloadComplete			"Download complete."
SteamBootstrapper_PercentComplete			"%percent%%% complete"
SteamBootstrapper_NoNetwork					"%appname% needs to be online to update. Please confirm your network connection and try again."
SteamBootstrapper_FailedMkdir				"Couldn't create directory %dirname%, got error %errno%"
SteamBootstrapper_NotWriteable				"Directory %dirname% not writable."
SteamBootstrapper_FailedDownloadHTTPError	"Download failed: http error %errno%"
SteamBootstrapper_AlreadyRunningError		"An instance of %appname% is already running on this computer, under a different user account."
SteamBootstrapper_MoveContentDir_DestExists "%appname% has changed where it stores game content from '~/Documents/Steam Content' to '~/Library/Application Support/Steam/SteamApps'.  You have game files in the original location, and Steam was not able to move the files because files already exist at the new location.  You may need to move the files manually, or delete the old files and download your games again.  Continue anyway?"
SteamBootstrapper_MoveContentDir_CopyError  "Steam has changed where it stores game content from '~/Documents/Steam Content' to '~/Library/Application Support/Steam/SteamApps'.  You have game files in the original location, and Steam was unable to move the files.  You may need to move the files manually, or delete the old files and download your games again.  Continue anyway?"
SteamBootstrapper_OK				"OK"
SteamBootstrapper_Cancel			"Cancel"
SteamBootstrapper_WindowTitle			"%appname%"
SteamBootstrapper_Error_Title			"%appname% - Fatal Error"
SteamBootstrapper_Warning_Title			"%appname% - Warning"
SteamBootstrapper_OperatingSystemNoLongerSupported "%appname% is no longer supported on your operating system version."
SteamCmd_OperatingSystemNoLongerSupported "%appname% is no longer supported on your operating system version.\nYou may override this check by passing -overrideminos.\n%appname% may not run properly and support for this override may be removed in the future.\nPlease update to a supported OS version for continued usage."
SteamBootstrapper_OperatingSystemNoLongerSupportedOnBeta "%appname% is no longer supported on your operating system version.\n\nClick OK to exit the selected %appname% beta."
SteamBootstrapper_Starting                  "Starting..."
}
