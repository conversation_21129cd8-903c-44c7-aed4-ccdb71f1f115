//! Dispatch Manager for Eden Reforger
//! Converted from original Eden Altis Life dispatch systems
//! Manages emergency dispatch, service calls, and response coordination

[ComponentEditorProps(category: "Eden Systems", description: "Manages emergency dispatch and service coordination")]
class EdenDispatchManagerComponent : ScriptComponent
{
    [Attribute("300", UIWidgets.EditBox, "Call timeout in seconds")]
    protected int m_iCallTimeout;
    
    [Attribute("60", UIWidgets.EditBox, "Dispatch update interval in seconds")]
    protected int m_iDispatchUpdateInterval;
    
    [RplProp()]
    protected ref array<ref EdenDispatchCall> m_aPoliceDispatch;
    
    [RplProp()]
    protected ref array<ref EdenDispatchCall> m_aEMSDispatch;
    
    [RplProp()]
    protected ref array<ref EdenDispatchCall> m_aServiceDispatch;
    
    protected ref map<string, ref EdenDispatchCall> m_mActiveCallsByPlayer;
    protected ref array<string> m_aAvailablePoliceUnits;
    protected ref array<string> m_aAvailableEMSUnits;
    
    //! Constructor
    void EdenDispatchManagerComponent(IEntityComponentSource src, IEntity ent, IEntity parent)
    {
        m_aPoliceDispatch = new array<ref EdenDispatchCall>();
        m_aEMSDispatch = new array<ref EdenDispatchCall>();
        m_aServiceDispatch = new array<ref EdenDispatchCall>();
        m_mActiveCallsByPlayer = new map<string, ref EdenDispatchCall>();
        m_aAvailablePoliceUnits = new array<string>();
        m_aAvailableEMSUnits = new array<string>();
        
        // Start dispatch update timer
        GetGame().GetCallqueue().CallLater(UpdateDispatchSystem, m_iDispatchUpdateInterval * 1000, true);
    }
    
    //! Create emergency dispatch call
    bool CreateEmergencyCall(string playerId, string serviceType, string description, vector location, int priority = 3)
    {
        // Check if player already has an active call
        if (m_mActiveCallsByPlayer.Contains(playerId))
        {
            EdenDispatchCall existingCall = m_mActiveCallsByPlayer.Get(playerId);
            if (existingCall && existingCall.IsActive())
            {
                Print(string.Format("EdenDispatchManager: Player %1 already has active call", playerId));
                return false;
            }
        }
        
        // Create dispatch call
        ref EdenDispatchCall dispatchCall = new EdenDispatchCall();
        dispatchCall.SetCallId(GenerateCallId());
        dispatchCall.SetPlayerId(playerId);
        dispatchCall.SetPlayerName(GetPlayerName(playerId));
        dispatchCall.SetServiceType(serviceType);
        dispatchCall.SetDescription(description);
        dispatchCall.SetLocation(location);
        dispatchCall.SetTimestamp(GetGame().GetWorld().GetWorldTime());
        dispatchCall.SetPriority(priority);
        dispatchCall.SetIsActive(true);
        dispatchCall.SetIsAssigned(false);
        
        // Add to appropriate dispatch queue
        switch (serviceType.ToLower())
        {
            case "police":
                {
                    m_aPoliceDispatch.Insert(dispatchCall);
                    BroadcastToPolice(dispatchCall);
                    break;
                }
            case "ems":
            case "medical":
                {
                    m_aEMSDispatch.Insert(dispatchCall);
                    BroadcastToEMS(dispatchCall);
                    break;
                }
            case "taxi":
            case "mechanic":
                {
                    m_aServiceDispatch.Insert(dispatchCall);
                    BroadcastToService(dispatchCall, serviceType);
                    break;
                }
            default:
                {
                    Print(string.Format("EdenDispatchManager: Unknown service type: %1", serviceType));
                    return false;
                }
        }
        
        // Track active call
        m_mActiveCallsByPlayer.Set(playerId, dispatchCall);
        
        Print(string.Format("EdenDispatchManager: Emergency call created - %1 for %2 (Priority: %3)", 
            serviceType, dispatchCall.GetPlayerName(), priority));
        
        return true;
    }
    
    //! Assign dispatch call to responder
    bool AssignCall(string callId, string responderId)
    {
        EdenDispatchCall call = FindCallById(callId);
        if (!call || !call.IsActive() || call.IsAssigned())
            return false;
            
        call.SetIsAssigned(true);
        call.SetResponderId(responderId);
        call.SetResponderName(GetPlayerName(responderId));
        call.SetAssignedTime(GetGame().GetWorld().GetWorldTime());
        
        // Notify caller and responder
        NotifyCallAssigned(call);
        
        Print(string.Format("EdenDispatchManager: Call %1 assigned to %2", callId, call.GetResponderName()));
        return true;
    }
    
    //! Complete dispatch call
    bool CompleteCall(string callId, string completedBy = "")
    {
        EdenDispatchCall call = FindCallById(callId);
        if (!call || !call.IsActive())
            return false;
            
        call.SetIsActive(false);
        call.SetCompletedTime(GetGame().GetWorld().GetWorldTime());
        
        // Remove from active calls tracking
        if (m_mActiveCallsByPlayer.Contains(call.GetPlayerId()))
            m_mActiveCallsByPlayer.Remove(call.GetPlayerId());
            
        // Notify completion
        NotifyCallCompleted(call, completedBy);
        
        Print(string.Format("EdenDispatchManager: Call %1 completed", callId));
        return true;
    }
    
    //! Cancel dispatch call
    bool CancelCall(string callId, string reason = "")
    {
        EdenDispatchCall call = FindCallById(callId);
        if (!call || !call.IsActive())
            return false;
            
        call.SetIsActive(false);
        call.SetCompletedTime(GetGame().GetWorld().GetWorldTime());
        
        // Remove from active calls tracking
        if (m_mActiveCallsByPlayer.Contains(call.GetPlayerId()))
            m_mActiveCallsByPlayer.Remove(call.GetPlayerId());
            
        // Notify cancellation
        NotifyCallCancelled(call, reason);
        
        Print(string.Format("EdenDispatchManager: Call %1 cancelled - %2", callId, reason));
        return true;
    }
    
    //! Handle panic button activation
    void HandlePanicButton(string playerId, vector location, string additionalInfo = "")
    {
        string description = "PANIC BUTTON ACTIVATED";
        if (additionalInfo != "")
            description += " - " + additionalInfo;
            
        // Create high priority police call
        CreateEmergencyCall(playerId, "police", description, location, 4);
        
        // Create map marker for panic location
        CreatePanicMarker(playerId, location);
        
        // Special panic button broadcast
        BroadcastPanicButton(playerId, location, additionalInfo);
        
        Print(string.Format("EdenDispatchManager: Panic button activated by %1", GetPlayerName(playerId)));
    }
    
    //! Register available unit
    void RegisterUnit(string unitId, string serviceType, bool isAvailable = true)
    {
        switch (serviceType.ToLower())
        {
            case "police":
                {
                    if (isAvailable && m_aAvailablePoliceUnits.Find(unitId) == -1)
                        m_aAvailablePoliceUnits.Insert(unitId);
                    else if (!isAvailable)
                        m_aAvailablePoliceUnits.RemoveItem(unitId);
                    break;
                }
            case "ems":
            case "medical":
                {
                    if (isAvailable && m_aAvailableEMSUnits.Find(unitId) == -1)
                        m_aAvailableEMSUnits.Insert(unitId);
                    else if (!isAvailable)
                        m_aAvailableEMSUnits.RemoveItem(unitId);
                    break;
                }
        }
        
        Print(string.Format("EdenDispatchManager: Unit %1 (%2) %3", 
            unitId, serviceType, isAvailable ? "available" : "unavailable"));
    }
    
    //! Get active calls for service type
    array<ref EdenDispatchCall> GetActiveCallsForService(string serviceType)
    {
        switch (serviceType.ToLower())
        {
            case "police":
                return GetActiveCallsFromArray(m_aPoliceDispatch);
            case "ems":
            case "medical":
                return GetActiveCallsFromArray(m_aEMSDispatch);
            case "service":
                return GetActiveCallsFromArray(m_aServiceDispatch);
            default:
                return new array<ref EdenDispatchCall>();
        }
    }
    
    //! Get active calls from array
    protected array<ref EdenDispatchCall> GetActiveCallsFromArray(array<ref EdenDispatchCall> callArray)
    {
        ref array<ref EdenDispatchCall> activeCalls = new array<ref EdenDispatchCall>();
        foreach (EdenDispatchCall call : callArray)
        {
            if (call && call.IsActive())
                activeCalls.Insert(call);
        }
        return activeCalls;
    }
    
    //! Find call by ID
    protected EdenDispatchCall FindCallById(string callId)
    {
        // Search all dispatch arrays
        ref array<ref array<ref EdenDispatchCall>> allArrays = {
            m_aPoliceDispatch,
            m_aEMSDispatch,
            m_aServiceDispatch
        };
        
        foreach (array<ref EdenDispatchCall> callArray : allArrays)
        {
            foreach (EdenDispatchCall call : callArray)
            {
                if (call && call.GetCallId() == callId)
                    return call;
            }
        }
        
        return null;
    }
    
    //! Update dispatch system
    protected void UpdateDispatchSystem()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        // Check for timed out calls
        CheckCallTimeouts(currentTime);
        
        // Clean up old completed calls
        CleanupOldCalls(currentTime);
        
        // Update call priorities based on age
        UpdateCallPriorities(currentTime);
    }
    
    //! Check for call timeouts
    protected void CheckCallTimeouts(int currentTime)
    {
        ref array<ref array<ref EdenDispatchCall>> allArrays = {
            m_aPoliceDispatch,
            m_aEMSDispatch,
            m_aServiceDispatch
        };
        
        foreach (array<ref EdenDispatchCall> callArray : allArrays)
        {
            foreach (EdenDispatchCall call : callArray)
            {
                if (call && call.IsActive() && !call.IsAssigned())
                {
                    int elapsed = currentTime - call.GetTimestamp();
                    if (elapsed > m_iCallTimeout)
                    {
                        CancelCall(call.GetCallId(), "Timeout - No response");
                    }
                }
            }
        }
    }
    
    //! Clean up old calls
    protected void CleanupOldCalls(int currentTime)
    {
        int maxAge = 24 * 60 * 60; // 24 hours
        
        ref array<ref array<ref EdenDispatchCall>> allArrays = {
            m_aPoliceDispatch,
            m_aEMSDispatch,
            m_aServiceDispatch
        };
        
        foreach (array<ref EdenDispatchCall> callArray : allArrays)
        {
            for (int i = callArray.Count() - 1; i >= 0; i--)
            {
                EdenDispatchCall call = callArray[i];
                if (call && !call.IsActive() && (currentTime - call.GetCompletedTime()) > maxAge)
                {
                    callArray.RemoveOrdered(i);
                }
            }
        }
    }
    
    //! Update call priorities
    protected void UpdateCallPriorities(int currentTime)
    {
        ref array<ref array<ref EdenDispatchCall>> allArrays = {
            m_aPoliceDispatch,
            m_aEMSDispatch,
            m_aServiceDispatch
        };
        
        foreach (array<ref EdenDispatchCall> callArray : allArrays)
        {
            foreach (EdenDispatchCall call : callArray)
            {
                if (call && call.IsActive() && !call.IsAssigned())
                {
                    int elapsed = currentTime - call.GetTimestamp();
                    
                    // Increase priority for old calls
                    if (elapsed > 300 && call.GetPriority() < 4) // 5 minutes
                    {
                        call.SetPriority(call.GetPriority() + 1);
                    }
                }
            }
        }
    }
    
    //! Generate call ID
    protected string GenerateCallId()
    {
        return string.Format("DISPATCH_%1_%2", GetGame().GetWorld().GetWorldTime(), Math.RandomInt(1000, 9999));
    }
    
    //! Get player name
    protected string GetPlayerName(string playerId)
    {
        // TODO: Implement actual player name lookup
        return string.Format("Player_%1", playerId);
    }
    
    //! Broadcast to police
    protected void BroadcastToPolice(EdenDispatchCall call)
    {
        // TODO: Implement police broadcasting
        Print(string.Format("POLICE DISPATCH: %1 - %2", call.GetPlayerName(), call.GetDescription()));
    }
    
    //! Broadcast to EMS
    protected void BroadcastToEMS(EdenDispatchCall call)
    {
        // TODO: Implement EMS broadcasting
        Print(string.Format("EMS DISPATCH: %1 - %2", call.GetPlayerName(), call.GetDescription()));
    }
    
    //! Broadcast to service
    protected void BroadcastToService(EdenDispatchCall call, string serviceType)
    {
        // TODO: Implement service broadcasting
        Print(string.Format("%1 DISPATCH: %2 - %3", serviceType.ToUpper(), call.GetPlayerName(), call.GetDescription()));
    }
    
    //! Broadcast panic button
    protected void BroadcastPanicButton(string playerId, vector location, string info)
    {
        // TODO: Implement panic button broadcasting
        Print(string.Format("PANIC BUTTON: %1 at %2 - %3", GetPlayerName(playerId), location, info));
    }
    
    //! Create panic marker
    protected void CreatePanicMarker(string playerId, vector location)
    {
        // TODO: Implement panic marker creation
        Print(string.Format("Creating panic marker for %1 at %2", playerId, location));
    }
    
    //! Notify call assigned
    protected void NotifyCallAssigned(EdenDispatchCall call)
    {
        // TODO: Implement assignment notifications
        Print(string.Format("Call assigned: %1 -> %2", call.GetCallId(), call.GetResponderName()));
    }
    
    //! Notify call completed
    protected void NotifyCallCompleted(EdenDispatchCall call, string completedBy)
    {
        // TODO: Implement completion notifications
        Print(string.Format("Call completed: %1 by %2", call.GetCallId(), completedBy));
    }
    
    //! Notify call cancelled
    protected void NotifyCallCancelled(EdenDispatchCall call, string reason)
    {
        // TODO: Implement cancellation notifications
        Print(string.Format("Call cancelled: %1 - %2", call.GetCallId(), reason));
    }
    
    //! Getters
    array<ref EdenDispatchCall> GetPoliceDispatch() { return m_aPoliceDispatch; }
    array<ref EdenDispatchCall> GetEMSDispatch() { return m_aEMSDispatch; }
    array<ref EdenDispatchCall> GetServiceDispatch() { return m_aServiceDispatch; }
    array<string> GetAvailablePoliceUnits() { return m_aAvailablePoliceUnits; }
    array<string> GetAvailableEMSUnits() { return m_aAvailableEMSUnits; }
}

//! Dispatch Manager class for easy access
class EdenDispatchManager
{
    protected static EdenDispatchManager s_Instance;
    protected EdenDispatchManagerComponent m_Component;
    
    //! Get singleton instance
    static EdenDispatchManager GetInstance()
    {
        if (!s_Instance)
            s_Instance = new EdenDispatchManager();
        return s_Instance;
    }
    
    //! Initialize with component
    void Initialize(EdenDispatchManagerComponent component)
    {
        m_Component = component;
    }
    
    //! Get component
    EdenDispatchManagerComponent GetComponent()
    {
        return m_Component;
    }
}
