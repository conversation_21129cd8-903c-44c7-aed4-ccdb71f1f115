//! Eden Main Menu Dialog
//! Converted from original yMenuMain.hpp
//! Main menu interface showing player cash, bank, licenses, and basic info

class EdenMainMenuDialog : EdenBaseDialog
{
    protected TextWidget m_CashValueText;
    protected TextWidget m_BankValueText;
    protected TextWidget m_PlaytimeText;
    protected TextWidget m_LicensesText;
    protected TextWidget m_GangText;
    protected TextWidget m_CopLevelText;
    protected TextWidget m_MedicLevelText;
    protected ImageWidget m_CashIcon;
    protected ImageWidget m_BankIcon;
    
    protected string m_PlayerId;
    protected ref EdenPlayerData m_PlayerData;
    
    void EdenMainMenuDialog()
    {
        m_PlayerId = "";
        m_PlayerData = null;
    }
    
    override void OnCreate(string playerId, string parameter = "")
    {
        super.OnCreate(playerId, parameter);
        
        m_PlayerId = playerId;
        
        // Get player data
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenDataManager dataManager = gameMode.GetDataManager();
            if (dataManager)
            {
                m_PlayerData = dataManager.GetPlayerData(playerId);
            }
        }
        
        InitializeWidgets();
        UpdateDisplay();
        
        Print("[EdenMainMenuDialog] Main menu dialog created for player: " + playerId);
    }
    
    override string GetDialogTitle()
    {
        return "Main Menu";
    }
    
    override int GetActiveTabId()
    {
        return 1; // Main menu is tab 1
    }
    
    //! Initialize UI widgets
    protected void InitializeWidgets()
    {
        Widget rootWidget = GetRootWidget();
        if (!rootWidget)
            return;
        
        // Find cash value text widget
        m_CashValueText = TextWidget.Cast(rootWidget.FindAnyWidget("CashValue"));
        if (!m_CashValueText)
        {
            Print("[EdenMainMenuDialog] Warning: CashValue widget not found");
        }
        
        // Find bank value text widget
        m_BankValueText = TextWidget.Cast(rootWidget.FindAnyWidget("BankValue"));
        if (!m_BankValueText)
        {
            Print("[EdenMainMenuDialog] Warning: BankValue widget not found");
        }
        
        // Find playtime text widget
        m_PlaytimeText = TextWidget.Cast(rootWidget.FindAnyWidget("PlaytimeValue"));
        if (!m_PlaytimeText)
        {
            Print("[EdenMainMenuDialog] Warning: PlaytimeValue widget not found");
        }
        
        // Find licenses text widget
        m_LicensesText = TextWidget.Cast(rootWidget.FindAnyWidget("LicensesValue"));
        if (!m_LicensesText)
        {
            Print("[EdenMainMenuDialog] Warning: LicensesValue widget not found");
        }
        
        // Find gang text widget
        m_GangText = TextWidget.Cast(rootWidget.FindAnyWidget("GangValue"));
        if (!m_GangText)
        {
            Print("[EdenMainMenuDialog] Warning: GangValue widget not found");
        }
        
        // Find cop level text widget
        m_CopLevelText = TextWidget.Cast(rootWidget.FindAnyWidget("CopLevelValue"));
        if (!m_CopLevelText)
        {
            Print("[EdenMainMenuDialog] Warning: CopLevelValue widget not found");
        }
        
        // Find medic level text widget
        m_MedicLevelText = TextWidget.Cast(rootWidget.FindAnyWidget("MedicLevelValue"));
        if (!m_MedicLevelText)
        {
            Print("[EdenMainMenuDialog] Warning: MedicLevelValue widget not found");
        }
        
        // Find cash icon
        m_CashIcon = ImageWidget.Cast(rootWidget.FindAnyWidget("CashIcon"));
        if (m_CashIcon)
        {
            m_CashIcon.LoadImageTexture(0, "images/icons/money.paa");
        }
        
        // Find bank icon
        m_BankIcon = ImageWidget.Cast(rootWidget.FindAnyWidget("BankIcon"));
        if (m_BankIcon)
        {
            m_BankIcon.LoadImageTexture(0, "images/icons/bank.paa");
        }
    }
    
    //! Update display with current player data
    protected void UpdateDisplay()
    {
        if (!m_PlayerData)
            return;
        
        // Update cash display
        if (m_CashValueText)
        {
            int cash = m_PlayerData.GetCash();
            m_CashValueText.SetText("$" + FormatMoney(cash));
        }
        
        // Update bank display
        if (m_BankValueText)
        {
            int bankMoney = m_PlayerData.GetBankMoney();
            m_BankValueText.SetText("$" + FormatMoney(bankMoney));
        }
        
        // Update playtime display
        if (m_PlaytimeText)
        {
            int playtime = m_PlayerData.GetPlaytime();
            m_PlaytimeText.SetText(FormatPlaytime(playtime));
        }
        
        // Update licenses display
        if (m_LicensesText)
        {
            array<ref EdenLicense> licenses = m_PlayerData.GetLicenses();
            int validLicenses = 0;
            
            for (int i = 0; i < licenses.Count(); i++)
            {
                if (licenses[i].IsValid())
                    validLicenses++;
            }
            
            m_LicensesText.SetText(validLicenses + " Active");
        }
        
        // Update gang display
        if (m_GangText)
        {
            string gangName = m_PlayerData.GetGangName();
            if (gangName != "")
            {
                m_GangText.SetText(gangName);
            }
            else
            {
                m_GangText.SetText("None");
            }
        }
        
        // Update cop level display
        if (m_CopLevelText)
        {
            int copLevel = m_PlayerData.GetCopLevel();
            string copRank = GetCopRankName(copLevel);
            m_CopLevelText.SetText(copRank);
        }
        
        // Update medic level display
        if (m_MedicLevelText)
        {
            int medicLevel = m_PlayerData.GetMedicLevel();
            string medicRank = GetMedicRankName(medicLevel);
            m_MedicLevelText.SetText(medicRank);
        }
    }
    
    //! Format money with commas
    protected string FormatMoney(int amount)
    {
        string amountStr = amount.ToString();
        string formatted = "";
        int length = amountStr.Length();
        
        for (int i = 0; i < length; i++)
        {
            if (i > 0 && (length - i) % 3 == 0)
                formatted += ",";
            formatted += amountStr.Get(i);
        }
        
        return formatted;
    }
    
    //! Format playtime in hours and minutes
    protected string FormatPlaytime(int minutes)
    {
        int hours = minutes / 60;
        int remainingMinutes = minutes % 60;
        
        return hours + "h " + remainingMinutes + "m";
    }
    
    //! Get cop rank name from level
    protected string GetCopRankName(int level)
    {
        switch (level)
        {
            case 0: return "Civilian";
            case 1: return "Cadet";
            case 2: return "Officer";
            case 3: return "Senior Officer";
            case 4: return "Corporal";
            case 5: return "Sergeant";
            case 6: return "Lieutenant";
            case 7: return "Captain";
            case 8: return "Deputy Chief";
            case 9: return "Chief";
            default: return "Unknown";
        }
    }
    
    //! Get medic rank name from level
    protected string GetMedicRankName(int level)
    {
        switch (level)
        {
            case 0: return "Civilian";
            case 1: return "EMT";
            case 2: return "Paramedic";
            case 3: return "Senior Paramedic";
            case 4: return "Fire Fighter";
            case 5: return "Senior Fire Fighter";
            case 6: return "EMS Lieutenant";
            case 7: return "EMS Captain";
            case 8: return "EMS Deputy Chief";
            case 9: return "EMS Chief";
            default: return "Unknown";
        }
    }
    
    //! Handle refresh button click
    void OnRefreshButtonClick()
    {
        // Refresh player data
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenDataManager dataManager = gameMode.GetDataManager();
            if (dataManager)
            {
                m_PlayerData = dataManager.GetPlayerData(m_PlayerId);
                UpdateDisplay();
            }
        }
        
        Print("[EdenMainMenuDialog] Display refreshed for player: " + m_PlayerId);
    }
    
    //! Handle settings button click
    void OnSettingsButtonClick()
    {
        // Switch to settings tab
        EdenUIManager uiManager = GetUIManager();
        if (uiManager)
        {
            uiManager.SwitchTab(6, m_PlayerId); // Settings is tab 6
        }
    }
    
    //! Handle logout button click
    void OnLogoutButtonClick()
    {
        // Close UI and handle logout
        EdenUIManager uiManager = GetUIManager();
        if (uiManager)
        {
            uiManager.CloseAllDialogs();
        }
        
        // Additional logout logic would go here
        Print("[EdenMainMenuDialog] Logout requested for player: " + m_PlayerId);
    }
    
    override void OnUpdate()
    {
        super.OnUpdate();
        
        // Update display periodically (every 5 seconds)
        static int lastUpdate = 0;
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        if (currentTime - lastUpdate > 5000) // 5 seconds
        {
            UpdateDisplay();
            lastUpdate = currentTime;
        }
    }
    
    override void OnClose()
    {
        super.OnClose();
        
        m_PlayerData = null;
        
        Print("[EdenMainMenuDialog] Main menu dialog closed for player: " + m_PlayerId);
    }
}
