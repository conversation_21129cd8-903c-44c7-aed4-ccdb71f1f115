//  File: fn_escortAction.sqf
private["_target","_weaponArray"];
_target = param [0,Obj<PERSON><PERSON>,[Obj<PERSON><PERSON>]];

if (player distance _target > 3) exitWith {};
if (!isNull (player getVariable ["TransportingPlayer", objNull])) exitWith {hint "You can only escort one person at a time.";};
if (isNil "_target" || isNull _target || !isPlayer _target) exitWith {};
if (_target getVariable ["Escorting",false]) exitWith {hint "That player is already being escorted by someone.";};
if (!isNull (_target getVariable["TransportingPlayer",objNull])) exitWith {hint "That player is already escorting another player.";};
if (oev_action_inUse) exitWith {hint "You're already performing another action.";};
if (animationState _target != "AmovPercMstpSnonWnonDnon_Ease") exitWith {hint "You can not escort a player until they're in the proper animation.";};
oev_action_inUse = true;

_obj_main = player;

_target setPos ((getPos player) vectorAdd (vectorDir player));
_target attachTo [player, [0, 1, 0.1]];//civ_2 attachTo [player, [0, 0.2, -1.2], "leftshoulder"];
waitUntil {_target in (attachedObjects player)};
player reveal _target;

player setVariable ["TransportingPlayer", _target, true];
_target setVariable["Escorting",true,true];

life_stopEscortAction = _obj_main addAction [format ["<t color='#FF0000'>%1</t>", "Stop Escorting"], "player removeAction life_stopEscortAction; life_stopEscortAction = nil;", nil, 20, false, true, "", ""];
waitUntil{uiSleep 0.3; _target = (player getVariable ["TransportingPlayer", objNull]); ((vehicle player != player) || (player getVariable["playerSurrender",false]) || !(_target getVariable["restrained",false]) || (player getVariable["restrained",false]) || (_target != vehicle _target) || (isNull _target) || !(alive player) || !(alive _target) || (isNil "life_stopEscortAction") || (player getVariable["downed",false]))};
_target = (player getVariable ["TransportingPlayer", objNull]);
if(!isNull _target) then {
	detach _target;
	/*
	if(alive _target) then {
		_target setpos (player ModelToWorld [0,1.9,0]);
	};
	*/
	_target setVariable["Escorting",false,true];
};
player setVariable ['TransportingPlayer', objNull, true];

if(!isNil "life_stopEscortAction") then {
	player removeAction life_stopEscortAction;
	life_stopEscortAction = nil;
};

oev_action_inUse = false;