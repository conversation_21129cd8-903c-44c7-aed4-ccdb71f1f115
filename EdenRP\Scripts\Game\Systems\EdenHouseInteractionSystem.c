//! Eden House Interaction System - Handles house doors, lighting, repairs, and interactions
//! Converted from original house interaction systems

class EdenHouseInteractionSystem
{
    protected ref map<int, ref EdenHouseInteraction> m_HouseInteractions; // House ID -> Interaction Data
    protected ref map<string, int> m_PlayerInteractionCooldowns; // Player ID -> Cooldown time
    
    // Interaction system configuration
    protected bool m_InteractionSystemEnabled;
    protected int m_InteractionCooldown;
    protected int m_DoorRepairCost;
    protected int m_LightingCost;
    protected float m_InteractionRange;
    
    void EdenHouseInteractionSystem()
    {
        m_HouseInteractions = new map<int, ref EdenHouseInteraction>();
        m_PlayerInteractionCooldowns = new map<string, int>();
        
        m_InteractionSystemEnabled = true;
        m_InteractionCooldown = 3; // 3 seconds
        m_DoorRepairCost = 1000;
        m_LightingCost = 500;
        m_InteractionRange = 3.6;
        
        InitializeInteractionSystem();
    }
    
    //! Initialize interaction system
    void Initialize()
    {
        Print("[EdenHouseInteractionSystem] Initializing house interaction system...");
        
        LoadHouseInteractions();
        
        // Set up periodic processing
        GetGame().GetCallqueue().CallLater(ProcessInteractionCooldowns, 1000, true); // 1 second
        GetGame().GetCallqueue().CallLater(ProcessLightingSystems, 60000, true); // 1 minute
        
        Print("[EdenHouseInteractionSystem] House interaction system initialized");
    }
    
    //! Initialize interaction system configuration
    protected void InitializeInteractionSystem()
    {
        Print("[EdenHouseInteractionSystem] Interaction system configuration initialized");
    }
    
    //! Lock/unlock house
    bool ToggleHouseLock(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        if (!m_InteractionSystemEnabled)
            return false;
            
        // Check interaction cooldown
        if (IsPlayerOnCooldown(playerId))
            return false;
            
        // Check if player can access house
        EdenHousingManager housingManager = EdenGameMode.GetInstance().GetHousingManager();
        if (!housingManager.CanAccessHouse(playerId, houseId))
            return false;
            
        // Get house data
        EdenDataManager dataManager = EdenDataManager.GetInstance();
        EdenHouseData houseData = dataManager.GetHouseData(houseId);
        if (!houseData)
            return false;
            
        // Toggle lock state
        bool newLockState = !houseData.IsLocked();
        houseData.SetIsLocked(newLockState);
        
        // Get or create house interaction
        if (!m_HouseInteractions.Contains(houseId))
        {
            CreateHouseInteraction(houseId);
        }
        
        EdenHouseInteraction houseInteraction = m_HouseInteractions.Get(houseId);
        houseInteraction.SetIsLocked(newLockState);
        
        // Update house entity
        UpdateHouseEntityLock(houseId, newLockState);
        
        // Set cooldown
        SetPlayerCooldown(playerId);
        
        // Save changes
        dataManager.SaveHouseData(houseData);
        
        Print(string.Format("[EdenHouseInteractionSystem] %1 %2 house %3", 
            playerId, newLockState ? "locked" : "unlocked", houseId));
        return true;
    }
    
    //! Toggle house lighting
    bool ToggleHouseLighting(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        if (!m_InteractionSystemEnabled)
            return false;
            
        // Check interaction cooldown
        if (IsPlayerOnCooldown(playerId))
            return false;
            
        // Check if player can access house
        EdenHousingManager housingManager = EdenGameMode.GetInstance().GetHousingManager();
        if (!housingManager.CanAccessHouse(playerId, houseId))
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Get or create house interaction
        if (!m_HouseInteractions.Contains(houseId))
        {
            CreateHouseInteraction(houseId);
        }
        
        EdenHouseInteraction houseInteraction = m_HouseInteractions.Get(houseId);
        
        // Check current lighting state
        bool currentLightState = houseInteraction.HasLighting();
        
        // If turning on lights, check if player has money for electricity
        if (!currentLightState && m_LightingCost > playerComp.GetCash())
            return false;
            
        // Toggle lighting state
        bool newLightState = !currentLightState;
        houseInteraction.SetHasLighting(newLightState);
        
        // Charge for electricity if turning on
        if (newLightState)
        {
            playerComp.RemoveCash(m_LightingCost);
        }
        
        // Update house entity lighting
        UpdateHouseEntityLighting(houseId, newLightState);
        
        // Set cooldown
        SetPlayerCooldown(playerId);
        
        Print(string.Format("[EdenHouseInteractionSystem] %1 turned %2 lights for house %3", 
            playerId, newLightState ? "on" : "off", houseId));
        return true;
    }
    
    //! Repair house doors
    bool RepairHouseDoors(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        if (!m_InteractionSystemEnabled)
            return false;
            
        // Check interaction cooldown
        if (IsPlayerOnCooldown(playerId))
            return false;
            
        // Check if player can access house
        EdenHousingManager housingManager = EdenGameMode.GetInstance().GetHousingManager();
        if (!housingManager.CanAccessHouse(playerId, houseId))
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player has enough money
        if (m_DoorRepairCost > playerComp.GetCash())
            return false;
            
        // Get or create house interaction
        if (!m_HouseInteractions.Contains(houseId))
        {
            CreateHouseInteraction(houseId);
        }
        
        EdenHouseInteraction houseInteraction = m_HouseInteractions.Get(houseId);
        
        // Check if doors need repair
        if (!houseInteraction.DoorsNeedRepair())
            return false;
            
        // Process payment
        playerComp.RemoveCash(m_DoorRepairCost);
        
        // Repair doors
        houseInteraction.SetDoorsNeedRepair(false);
        houseInteraction.SetDoorHealth(100.0);
        
        // Update house entity doors
        UpdateHouseEntityDoors(houseId, true);
        
        // Set cooldown
        SetPlayerCooldown(playerId);
        
        Print(string.Format("[EdenHouseInteractionSystem] %1 repaired doors for house %2", playerId, houseId));
        return true;
    }
    
    //! Break down house door (police action)
    bool BreakDownHouseDoor(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        if (!m_InteractionSystemEnabled)
            return false;
            
        // Check if player is police
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        if (playerComp.GetFaction() != EdenFaction.POLICE)
            return false;
            
        // Check interaction cooldown
        if (IsPlayerOnCooldown(playerId))
            return false;
            
        // Get or create house interaction
        if (!m_HouseInteractions.Contains(houseId))
        {
            CreateHouseInteraction(houseId);
        }
        
        EdenHouseInteraction houseInteraction = m_HouseInteractions.Get(houseId);
        
        // Break down door
        houseInteraction.SetDoorsNeedRepair(true);
        houseInteraction.SetDoorHealth(0.0);
        houseInteraction.SetIsLocked(false);
        
        // Update house entity doors
        UpdateHouseEntityDoors(houseId, false);
        
        // Set cooldown
        SetPlayerCooldown(playerId);
        
        Print(string.Format("[EdenHouseInteractionSystem] Police %1 broke down door for house %2", playerId, houseId));
        return true;
    }
    
    //! Search house (police action)
    bool SearchHouse(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        if (!m_InteractionSystemEnabled)
            return false;
            
        // Check if player is police
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        if (playerComp.GetFaction() != EdenFaction.POLICE)
            return false;
            
        // Check interaction cooldown
        if (IsPlayerOnCooldown(playerId))
            return false;
            
        // Check range
        if (!IsPlayerInRange(playerId, houseId))
            return false;
            
        // Get house inventory
        EdenHouseInventorySystem inventorySystem = EdenGameMode.GetInstance().GetHouseInventorySystem();
        EdenHouseInventory houseInventory = inventorySystem.GetHouseInventory(houseId);
        if (!houseInventory)
            return false;
            
        // Search for illegal items
        array<ref EdenInventoryItem> illegalItems = SearchForIllegalItems(houseInventory);
        
        // Process search results
        if (illegalItems.Count() > 0)
        {
            ProcessIllegalItemsFound(playerId, houseId, illegalItems);
        }
        
        // Set cooldown
        SetPlayerCooldown(playerId);
        
        Print(string.Format("[EdenHouseInteractionSystem] Police %1 searched house %2, found %3 illegal items", 
            playerId, houseId, illegalItems.Count()));
        return true;
    }
    
    //! Access house garage
    bool AccessHouseGarage(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        if (!m_InteractionSystemEnabled)
            return false;
            
        // Check if player can access house
        EdenHousingManager housingManager = EdenGameMode.GetInstance().GetHousingManager();
        if (!housingManager.CanAccessHouse(playerId, houseId))
            return false;
            
        // Check interaction cooldown
        if (IsPlayerOnCooldown(playerId))
            return false;
            
        // Get house data
        EdenDataManager dataManager = EdenDataManager.GetInstance();
        EdenHouseData houseData = dataManager.GetHouseData(houseId);
        if (!houseData)
            return false;
            
        // Open garage interface
        OpenGarageInterface(playerId, houseData.GetPosition());
        
        // Set cooldown
        SetPlayerCooldown(playerId);
        
        Print(string.Format("[EdenHouseInteractionSystem] %1 accessed garage for house %2", playerId, houseId));
        return true;
    }
    
    //! Store vehicle at house
    bool StoreVehicleAtHouse(string playerId, int houseId, IEntity vehicleEntity)
    {
        if (playerId == "" || houseId <= 0 || !vehicleEntity)
            return false;
            
        if (!m_InteractionSystemEnabled)
            return false;
            
        // Check if player can access house
        EdenHousingManager housingManager = EdenGameMode.GetInstance().GetHousingManager();
        if (!housingManager.CanAccessHouse(playerId, houseId))
            return false;
            
        // Check interaction cooldown
        if (IsPlayerOnCooldown(playerId))
            return false;
            
        // Get house data
        EdenDataManager dataManager = EdenDataManager.GetInstance();
        EdenHouseData houseData = dataManager.GetHouseData(houseId);
        if (!houseData)
            return false;
            
        // Use vehicle manager to store vehicle
        EdenVehicleManager vehicleManager = EdenGameMode.GetInstance().GetVehicleManager();
        bool success = vehicleManager.StoreVehicle(playerId, vehicleEntity, houseData.GetPosition());
        
        if (success)
        {
            // Set cooldown
            SetPlayerCooldown(playerId);
            
            Print(string.Format("[EdenHouseInteractionSystem] %1 stored vehicle at house %2", playerId, houseId));
        }
        
        return success;
    }
    
    //! Get house interaction data
    EdenHouseInteraction GetHouseInteraction(int houseId)
    {
        if (houseId <= 0 || !m_HouseInteractions.Contains(houseId))
            return null;
            
        return m_HouseInteractions.Get(houseId);
    }
    
    //! Check if house doors need repair
    bool DoHouseDoorsNeedRepair(int houseId)
    {
        if (houseId <= 0 || !m_HouseInteractions.Contains(houseId))
            return false;
            
        EdenHouseInteraction houseInteraction = m_HouseInteractions.Get(houseId);
        return houseInteraction.DoorsNeedRepair();
    }
    
    //! Check if house has lighting
    bool DoesHouseHaveLighting(int houseId)
    {
        if (houseId <= 0 || !m_HouseInteractions.Contains(houseId))
            return false;
            
        EdenHouseInteraction houseInteraction = m_HouseInteractions.Get(houseId);
        return houseInteraction.HasLighting();
    }
    
    //! Helper methods
    protected void CreateHouseInteraction(int houseId)
    {
        EdenHouseInteraction houseInteraction = new EdenHouseInteraction();
        houseInteraction.SetHouseId(houseId);
        houseInteraction.SetIsLocked(true);
        houseInteraction.SetHasLighting(false);
        houseInteraction.SetDoorsNeedRepair(false);
        houseInteraction.SetDoorHealth(100.0);
        
        m_HouseInteractions.Set(houseId, houseInteraction);
        
        Print(string.Format("[EdenHouseInteractionSystem] Created interaction data for house %1", houseId));
    }
    
    protected bool IsPlayerOnCooldown(string playerId)
    {
        if (!m_PlayerInteractionCooldowns.Contains(playerId))
            return false;
            
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int cooldownTime = m_PlayerInteractionCooldowns.Get(playerId);
        
        return currentTime < cooldownTime;
    }
    
    protected void SetPlayerCooldown(string playerId)
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int cooldownTime = currentTime + m_InteractionCooldown;
        
        m_PlayerInteractionCooldowns.Set(playerId, cooldownTime);
    }
    
    protected bool IsPlayerInRange(string playerId, int houseId)
    {
        // Implementation would check if player is within interaction range of house
        return true;
    }
    
    protected array<ref EdenInventoryItem> SearchForIllegalItems(EdenHouseInventory houseInventory)
    {
        array<ref EdenInventoryItem> illegalItems = {};
        
        // Implementation would search for illegal items in house inventory
        // For now, return empty array
        
        return illegalItems;
    }
    
    protected void ProcessIllegalItemsFound(string playerId, int houseId, array<ref EdenInventoryItem> illegalItems)
    {
        // Implementation would process illegal items found during search
        Print(string.Format("[EdenHouseInteractionSystem] Processing %1 illegal items found in house %2", 
            illegalItems.Count(), houseId));
    }
    
    protected void ProcessInteractionCooldowns()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        array<string> expiredCooldowns = {};
        
        foreach (string playerId, int cooldownTime : m_PlayerInteractionCooldowns)
        {
            if (currentTime >= cooldownTime)
            {
                expiredCooldowns.Insert(playerId);
            }
        }
        
        foreach (string playerId : expiredCooldowns)
        {
            m_PlayerInteractionCooldowns.Remove(playerId);
        }
    }
    
    protected void ProcessLightingSystems()
    {
        // Implementation would process lighting systems (electricity costs, etc.)
        Print("[EdenHouseInteractionSystem] Processing lighting systems...");
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadHouseInteractions() { }
    protected void UpdateHouseEntityLock(int houseId, bool isLocked) { }
    protected void UpdateHouseEntityLighting(int houseId, bool hasLighting) { }
    protected void UpdateHouseEntityDoors(int houseId, bool doorsIntact) { }
    protected void OpenGarageInterface(string playerId, vector housePosition) { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    
    //! Configuration methods
    void SetInteractionSystemEnabled(bool enabled) { m_InteractionSystemEnabled = enabled; }
    bool IsInteractionSystemEnabled() { return m_InteractionSystemEnabled; }
    
    void SetInteractionCooldown(int cooldown) { m_InteractionCooldown = cooldown; }
    int GetInteractionCooldown() { return m_InteractionCooldown; }
    
    void SetDoorRepairCost(int cost) { m_DoorRepairCost = cost; }
    int GetDoorRepairCost() { return m_DoorRepairCost; }
    
    void SetLightingCost(int cost) { m_LightingCost = cost; }
    int GetLightingCost() { return m_LightingCost; }
    
    void SetInteractionRange(float range) { m_InteractionRange = range; }
    float GetInteractionRange() { return m_InteractionRange; }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenHouseInteractionSystem] Cleaning up house interaction system...");
        
        m_HouseInteractions.Clear();
        m_PlayerInteractionCooldowns.Clear();
    }
}
