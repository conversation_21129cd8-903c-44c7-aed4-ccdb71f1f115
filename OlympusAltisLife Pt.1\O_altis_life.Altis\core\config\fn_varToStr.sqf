//  File: fn_varToStr.sqf
//	Author: <PERSON> "<PERSON>" Boardwine

//	Description: Takes the long-name (variable) and returns a display name for our virtual item.
private["_var"];
_var = param [0,"",[""]];
if(_var == "") exitWith {""};

switch (_var) do
{
	//Virtual Inventory Items
	case "life_inv_lethalinjector": {"Lethal Injector"};
	case "life_inv_oilu": {(localize "STR_Item_OilU")};
	case "life_inv_oilp": {(localize "STR_Item_OilP")};
	case "life_inv_heroinu": {(localize "STR_Item_HeroinU")};
	case "life_inv_heroinp": {"Black Heroin"};
	case "life_inv_cannabis": {(localize "STR_Item_Cannabis")};
	case "life_inv_marijuana": {(localize "STR_Item_Marijuana")};
	case "life_inv_apple": {"Apple"};
	case "life_inv_salema": {(localize "STR_Item_SalemaMeat")};
	case "life_inv_ornate": {(localize "STR_Item_OrnateMeat")};
	case "life_inv_mackerel": {(localize "STR_Item_MackerelMeat")};
	case "life_inv_tuna": {(localize "STR_Item_TunaMeat")};
	case "life_inv_mullet": {(localize "STR_Item_MulletMeat")};
	case "life_inv_catshark": {(localize "STR_Item_CatSharkMeat")};
	case "life_inv_turtle": {(localize "STR_Item_TurtleMeat")};
	case "life_inv_fishingpoles": {(localize "STR_Item_FishingPole")};
	case "life_inv_water": {(localize "STR_Item_WaterBottle")};
	case "life_inv_coffee": {(localize "STR_Item_Coffee")};
	case "life_inv_turtlesoup": {(localize "STR_Item_TurtleSoup")};
	case "life_inv_donuts": {(localize "STR_Item_Donuts")};
	case "life_inv_fuelE": {(localize "STR_Item_FuelE")};
	case "life_inv_fuelF": {(localize "STR_Item_FuelF")};
	case "life_inv_pickaxe": {(localize "STR_Item_Pickaxe")};
	case "life_inv_copperore": {(localize "STR_Item_CopperOre")};
	case "life_inv_ironore": {(localize "STR_Item_IronOre")};
	case "life_inv_ironr": {(localize "STR_Item_IronIngot")};
	case "life_inv_copperr": {(localize "STR_Item_CopperIngot")};
	case "life_inv_sand": {(localize "STR_Item_Sand")};
	case "life_inv_salt": {(localize "STR_Item_Salt")};
	case "life_inv_saltr": {(localize "STR_Item_SaltR")};
	case "life_inv_glass": {(localize "STR_Item_Glass")};
	case "life_inv_diamond": {(localize "STR_Item_DiamondU")};
	case "life_inv_diamondr": {(localize "STR_Item_DiamondC")};
	case "life_inv_tbacon": {(localize "STR_Item_TBacon")};
	case "life_inv_redgull": {(localize "STR_Item_RedGull")};
	case "life_inv_lollypop": {"Medicinal Lollipop"};
	case "life_inv_lockpick": {(localize "STR_Item_Lockpick")};
	case "life_inv_peach": {(localize "STR_Item_Peach")};
	case "life_inv_coke": {"Unprocessed Cocaine"};
	case "life_inv_cokep": {"Processed Cocaine"};
	case "life_inv_spikeStrip": {(localize "STR_Item_SpikeStrip")};
	case "life_inv_rock": {(localize "STR_Item_Rock")};
	case "life_inv_cement": {(localize "STR_Item_CementBag")};
	case "life_inv_goldbar": {(localize "STR_Item_GoldBar")};
	case "life_inv_moneybag": {"Money Bag"};
	case "life_inv_blastingcharge": {(localize "STR_Item_BCharge")};
	case "life_inv_boltcutter": {(localize "STR_Item_BCutter")};
	case "life_inv_fireaxe": {(localize "STR_Item_FAxe")};
	case "life_inv_defusekit": {(localize "STR_Item_DefuseKit")};
	case "life_inv_storagesmall": {(localize "STR_Item_StorageBS")};
	case "life_inv_storagebig": {(localize "STR_Item_StorageBL")};
	case "life_inv_potato": {(localize "STR_Item_Potato")};
	case "life_inv_cream": {(localize "STR_Item_Cream")};
	case "life_inv_frog": {"Frog"};
	case "life_inv_frogp": {"Frog LSD"};
	case "life_inv_crystalmeth": {"Blue Crystal Meth"};
	case "life_inv_methu": {"Unprocessed Meth"};
	case "life_inv_phosphorous": {"Red Phosphorous"};
	case "life_inv_ephedra": {"Ephedra Plant"};
	case "life_inv_lithium": {"Lithium Rocks"};
	case "life_inv_moonshine": {"Moonshine"};
	case "life_inv_rum": {"Rum"};
	case "life_inv_mashu": {"Sour Mash"};
	case "life_inv_corn": {"Corn"};
	case "life_inv_sugar": {"Sugar"};
	case "life_inv_yeast": {"Yeast"};
	case "life_inv_platinum": {"Platinum Nugget"};
	case "life_inv_platinumr": {"Platinum Ingot"};
	case "life_inv_silver": {"Silver Ore"};
	case "life_inv_silverr": {"Silver Ingot"};
	case "life_inv_beer": {"Broweiser"};
	case "life_inv_ziptie": {"Ziptie"};
	case "life_inv_cupcake": {"Cupcake"};
	case "life_inv_pepsi": {"Pepsi"};
	case "life_inv_burger": {"Organic Burger"};
	case "life_inv_mushroom": {"Mushroom"};
	case "life_inv_mmushroom": {"Processed Mushroom"};
	case "life_inv_mmushroomp": {"Mushroom Pizza"};
	case "life_inv_gpstracker": {"GPS Tracker"};
	case "life_inv_egpstracker": {"Enhanced GPS Tracker"};
	case "life_inv_gpsjammer": {"GPS Jammer"};
	case "life_inv_ccocaine": {"Crack Cocaine"};
	case "life_inv_kidney": {"Black Market Kidney"};
	case "life_inv_scalpel": {"Scalpel"};
	case "life_inv_barrier": {"Barrier"};
	case "life_inv_speedbomb": {"Speed Bomb"};
	case "life_inv_foodDiv": 	{"-------- F O O D S --------"};
	case "life_inv_legalDiv": 	{"-------- L E G A L --------"};
	case "life_inv_illegalDiv": {"------- I L L E G A L ------"};
	case "life_inv_fireworks": {"Fireworks"};
	case "life_inv_heliTowHook": {"Tow Hooks and Slings"};
	case "life_inv_chickenRaw": {"Raw Chicken"};
	case "life_inv_roosterRaw": {"Raw Rooster"};
	case "life_inv_goatRaw": {"Raw Goat"};
	case "life_inv_sheepRaw": {"Raw Sheep"};
	case "life_inv_rabbitRaw": {"Raw Rabbit"};
	case "life_inv_snakeRaw": {"Raw Snake"};
	case "life_inv_chicken": {"Processed Chicken"};
	case "life_inv_rooster": {"Processed Rooster"};
	case "life_inv_goat": {"Processed Goat"};
	case "life_inv_sheep": {"Processed Sheep"};
	case "life_inv_rabbit": {"Processed Rabbit"};
	case "life_inv_snake": {"Processed Snake"};
	case "life_inv_bloodbag": {"Blood Bag"};
	case "life_inv_epiPen": {"Epipen"};
	case "life_inv_dopeShot": {"Dopamine Shot"};
	case "life_inv_woodLog": {"Wood Log"};
	case "life_inv_lumber": {"Lumber"};
	case "life_inv_bananau": {"Banana"};
	case "life_inv_bananap": {"Sliced Banana"};
	case "life_inv_topaz": {"Uncut Topaz"};
	case "life_inv_topazr": {"Topaz"};
	case "life_inv_cocoau": {"Raw Cocoa"};
	case "life_inv_cocoap": {"Chocolate"};
	case "life_inv_bananaSplit": {"Banana Split"};
	case "life_inv_sugarp": {"Sugar Cube"};
	case "life_inv_hackingterminal": {"Hacking Terminal"};
	case "life_inv_takeoverterminal": {"Takeover Terminal"};
	case "life_inv_blindfold": {"Blindfold"};
	case "life_inv_panicButton": {"Panic Button"};
	case "life_inv_wplPanicButton": {"Panic Button"};
	case "life_inv_roadKit": {"Road Kit"};
	case "life_inv_oilbarrel": {"Oil Barrel"};
	case "life_inv_vehAmmo": {"Vehicle Ammo"};
	case "life_inv_baitcar": {"Bait Appliance"};
	case "life_inv_wpearl": {"White Pearl"};
	case "life_inv_bpearl": {"Blue Pearl"};
	case "life_inv_amethyst": {"Amethyst"};
	case "life_inv_coin": {"Doubloon"};
	case "life_inv_emerald": {"Emerald"};
	case "life_inv_scrap": {"Scrap Metal"};
	case "life_inv_hash": {"Hash"};
	case "life_inv_acid": {"Acid"};
	case "life_inv_crack": {"Crack Cocaine"};
	case "life_inv_mushroomu": {"Magic Mushrooms"};
	case "life_inv_pheroin": {"Pure Heroin"};
	case "life_inv_painkillers": {"Painkillers"};
	case "life_inv_excavationtools": {"Excavation Tools"};
	case "life_inv_rubber": {"Rubber"};
	case "life_inv_alumore": {"Aluminum Ore"};
	case "life_inv_coal": {"Coal"};
	case "life_inv_fibers": {"Fibers"};
	case "life_inv_stire": {"Small Tire"};
	case "life_inv_ltire": {"Large Tire"};
	case "life_inv_window": {"Window"};
	case "life_inv_rglass": {"Reinforced Glass"};
	case "life_inv_vdoor": {"Vehicle Door"};
	case "life_inv_electronics": {"Electrical Components"};
	case "life_inv_smetal": {"Sheet Metal"};
	case "life_inv_splating": {"Steel Plating"};
	case "life_inv_alumalloy": {"Aluminum Alloy"};
	case "life_inv_bcremote": {"Bait Car Remote"};
	case "life_inv_paintingSm": {"Small Painting"};
	case "life_inv_paintingLg": {"Large Painting"};
	case "life_inv_gokart": {"Pocket Go-Kart"};

	//License Block
	case "license_civ_driver": {(localize "STR_License_Driver")};
	case "license_civ_air": {(localize "STR_License_Pilot")};
	case "license_civ_heroin": {(localize "STR_License_Heroin")};
	case "license_civ_oil": {(localize "STR_License_Oil")};
	case "license_civ_dive": {(localize "STR_License_Diving")};
	case "license_civ_boat": {(localize "STR_License_Boat")};
	case "license_civ_gun": {(localize "STR_License_Firearm")};
	case "license_civ_wpl": {(localize "STR_License_WPL")};
	case "license_cop_air": {(localize "STR_License_Pilot")};
	case "license_cop_swat": {(localize "STR_License_Swat")};
	case "license_cop_cg": {(localize "STR_License_CG")};
	case "license_civ_rebel": {(localize "STR_License_Rebel")};
	case "license_civ_truck": {(localize "STR_License_Truck")};
	case "license_civ_diamond": {(localize "STR_License_Diamond")};
	case "license_civ_copper": {(localize "STR_License_Copper")};
	case "license_civ_iron": {(localize "STR_License_Iron")};
	case "license_civ_sand": {(localize "STR_License_Sand")};
	case "license_civ_salt": {(localize "STR_License_Salt")};
	case "license_civ_coke": {(localize "STR_License_Cocaine")};
	case "license_civ_marijuana": {(localize "STR_License_Marijuana")};
	case "license_civ_cement": {(localize "STR_License_Cement")};
	case "license_med_air": {(localize "STR_License_Pilot")};
	case "license_med_cg": {(localize "STR_License_MCG")};
	case "license_civ_home": {(localize "STR_License_Home")};
	case "license_civ_frog": {"Frog Poaching License"};
	case "license_civ_crystalmeth": {"Crystal Meth Processing"};
	case "license_civ_methu": {"Meth Processing"};
	case "license_civ_moonshine": {"Moonshine Processing"};
	case "license_civ_mashu": {"Sour Mash Processing"};
	case "license_civ_platinum": {"Platinum Processing"};
	case "license_civ_silver": {"Silver Processing"};
	case "license_civ_vigilante": {"Vigilante License"};
	case "license_civ_mushroom": {"Mushroom Processing"};

	case "license_civ_lumber": {"Sawmill Membership"};
	case "license_civ_bananap": {"Banana Factory Membership"};
	case "license_civ_topaz": {"Topaz Processing"};
	case "license_civ_cocoap": {"Cocoa Processing"};
	case "license_civ_bananaSplit": {"Banana Man's Friendship"};
	case "license_civ_sugarp": {"Sugar Processing"};

	//For APD Training Dummy IDK WHERE ELSE TO PUT THIS OKAY
	case "license_fake": {"<t color='#FF0000'><t size='2'>[OS] McDili</t></t><br/><t color='#FFD700'><t size='1.5'>Licenses:</t></t><br/>Driver License<br/>Pilot License<br/>Heroin Training<br/>Firearm License<br/>Sand Processing<br/>Marijuana Training<br/>"};
	case "fake_search": {"<t color='#FF0000'><t size='2'>[OS] McDili</t></t><br/><t color='#FFD700'><t size='1.5'>Weapons</t></t><br/><t color='#FFFFFF'><t color='#FFFFFF'>MAR-10 .338 (Camo)</t><br/>.338 LM 10Rnd Mag</t><br/><br/><t color='#FFD700'><t size='1.5'>Backpack Content</t></t><br/>No items in backpack.<br/><br/><t color='#FFD700'><t size='1.5'>Vest Content</t></t><br/><t color='#FFFFFF'>.338 LM 10Rnd Mag x8</t><br/><br/><t color='#FFD700'><t size='1.5'>Uniform Content</t></t><br/><t color='#FFFFFF'>.338 LM 10Rnd Mag x1</t><br/><br/><t color='#FFD700'><t size='1.5'>Illegal Items</t></t><br/>Lockpick x15<br/>Blasting Charge x1<br/>Hacking Terminal x3<br/>Scalpel x3<br/>Vehicle Ammo x1<br/><br/><br/><br/><br/><t color='#FF0000'></t>"};
};
