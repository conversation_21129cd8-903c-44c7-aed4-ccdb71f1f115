---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-52-59\console.log started at 2025-07-27 20:52:59 (2025-07-28 00:52:59 UTC)

20:52:59.588 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-52-59' to filesystem under name logs
20:52:59.588 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
20:52:59.590 ENGINE       : Initializing engine, version 184702
20:52:59.590 ENGINE       : CLI Params: -config C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\server.json -profile C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves -addonsDir addons -logLevel 3 -logStats 
20:52:59.592 ENGINE       : Addon dirs:
20:52:59.592  ENGINE       : dir: 'addons'
20:52:59.592  ENGINE       : dir: './addons'
20:52:59.592  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
20:52:59.592 ENGINE       : Available addons:
20:52:59.592  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
20:52:59.592  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
20:52:59.657 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
20:52:59.659 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
20:52:59.659 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
20:52:59.805 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
20:52:59.807 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
20:52:59.807 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-52-59/resourceDatabase.rdb)
20:52:59.919 ENGINE       : GameProject load
20:52:59.919  ENGINE       : Loaded addons:
20:52:59.919   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
20:52:59.919   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
20:52:59.923 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
20:52:59.923 ENGINE       : No Enfusion settings file loaded. Continuing with defaults
20:52:59.924 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
20:52:59.924 RENDER       : Basic screen setup:
20:52:59.924 RENDER       :   Back buffer width x height: 0x0
20:52:59.924 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
20:52:59.924 RENDER       :   MSAA                      : none
20:52:59.924 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
20:52:59.924 RENDER       : Detected output devices:
20:52:59.924 RENDER       :  * 1 device/s connected to Dummy:
20:52:59.924 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
20:52:59.924 RENDER       :   Concurrent frames limit 2
20:52:59.924 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
20:52:59.980 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
20:52:59.984 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
20:52:59.984 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
20:52:59.984 PROFILING    : Settings changed took: 0.022600 ms
20:52:59.987 ENGINE       : Initializing inputs.
20:52:59.993 NETWORK      : Initializing networking.
20:52:59.993 SCRIPT       : SCRIPT       : Initializing scripts
20:52:59.993  SCRIPT       : SCRIPT       : ScriptProjectManager init
20:52:59.994  PROFILING    : ScriptProjectManager init took: 0.023000 ms
20:53:00.010 PROFILING    : Initializing scripts took: 16.138600 ms
20:53:00.010 ENGINE       : Enfusion engine successfully created.
20:53:00.017 GUI          : Using default language (en_us)
20:53:00.017 GUI          : Loading 'en_us' localization file.
20:53:00.017 INIT         : INIT         : Loading StringTable
20:53:00.037 PROFILING    : Loading StringTable took: 20.695900 ms
20:53:00.038 SCRIPT       : SCRIPT       : Compiling GameLib scripts
20:53:00.059  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
20:53:00.064 PROFILING    : Compiling GameLib scripts took: 24.938000 ms
20:53:00.064 SCRIPT       : SCRIPT       : Compiling Game scripts
20:53:00.529 SCRIPT       : Compiling Game scripts
20:53:00.533  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
20:53:00.567  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
20:53:00.765  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:53:00.766  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:53:00.766  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:53:00.782  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
20:53:00.812  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
20:53:00.887  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
20:53:00.944  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:53:00.990  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
20:53:00.991  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
20:53:01.058  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
20:53:01.058  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:53:01.059  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:53:01.059  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:53:01.059  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:53:01.059  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:53:01.097  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:53:01.097  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:53:01.098  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:53:01.098  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:53:01.098  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:53:01.098  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:53:01.113  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:53:01.125  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:53:01.125  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:53:01.125  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:53:01.149  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
20:53:01.150  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
20:53:01.153  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:53:01.153  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:53:01.154  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:53:01.155  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
20:53:01.155  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:53:01.199  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
20:53:01.910 PROFILING    : Compiling Game scripts took: 1847.182900 ms
20:53:01.912 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
20:53:01.927 BACKEND      : Loading dedicated server config.
20:53:01.927  BACKEND      : Server config loaded.
20:53:01.927 BACKEND      : Loading dedicated server config.
20:53:01.927  BACKEND      : JSON Schema Validation:
20:53:01.928   BACKEND      : additionalProperties error:
20:53:01.928    BACKEND   (E): Param "#/game/maxPlayerCount" is not allowed and must be removed.
20:53:01.928    BACKEND   (E): Reference in schema: "#/properties/game"
20:53:01.928   BACKEND   (E): JSON is invalid!
20:53:01.928  BACKEND   (E): There are errors in server config!
20:53:01.928 BACKEND   (E): Unable to continue with a broken DS config! Shutdown!
20:53:01.928 ENGINE    (E): Error while initializing game.
20:53:02.103 ENGINE    (E): Unable to initialize the game
20:53:02.122 ENGINE       : Game destroyed.
20:53:02.138 RPL          : Pip::Destroy
20:53:02.144 RESOURCES (E): ==== Resource leaks ====
20:53:02.144 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
