---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-56-06\console.log started at 2025-07-27 20:56:06 (2025-07-28 00:56:06 UTC)

20:56:06.300 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-56-06' to filesystem under name logs
20:56:06.300 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
20:56:06.302 ENGINE       : Initializing engine, version 184702
20:56:06.302 ENGINE       : CLI Params: -config ..\..\..\server.json -profile ..\..\..\saves -addonsDir addons -logLevel 3 
20:56:06.305 ENGINE       : Addon dirs:
20:56:06.305  ENGINE       : dir: 'addons'
20:56:06.305  ENGINE       : dir: './addons'
20:56:06.306  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
20:56:06.306 ENGINE       : Available addons:
20:56:06.306  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
20:56:06.306  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
20:56:06.355 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
20:56:06.357 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
20:56:06.357 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
20:56:06.489 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
20:56:06.491 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
20:56:06.491 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-56-06/resourceDatabase.rdb)
20:56:06.595 ENGINE       : GameProject load
20:56:06.595  ENGINE       : Loaded addons:
20:56:06.595   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
20:56:06.595   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
20:56:06.597 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
20:56:06.597 ENGINE       : No Enfusion settings file loaded. Continuing with defaults
20:56:06.598 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
20:56:06.598 RENDER       : Basic screen setup:
20:56:06.598 RENDER       :   Back buffer width x height: 0x0
20:56:06.598 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
20:56:06.598 RENDER       :   MSAA                      : none
20:56:06.598 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
20:56:06.598 RENDER       : Detected output devices:
20:56:06.598 RENDER       :  * 1 device/s connected to Dummy:
20:56:06.598 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
20:56:06.598 RENDER       :   Concurrent frames limit 2
20:56:06.598 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
20:56:06.656 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
20:56:06.660 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
20:56:06.660 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
20:56:06.660 PROFILING    : Settings changed took: 0.023800 ms
20:56:06.663 ENGINE       : Initializing inputs.
20:56:06.669 NETWORK      : Initializing networking.
20:56:06.669 SCRIPT       : SCRIPT       : Initializing scripts
20:56:06.670  SCRIPT       : SCRIPT       : ScriptProjectManager init
20:56:06.670  PROFILING    : ScriptProjectManager init took: 0.038300 ms
20:56:06.686 PROFILING    : Initializing scripts took: 16.268200 ms
20:56:06.686 ENGINE       : Enfusion engine successfully created.
20:56:06.692 GUI          : Using default language (en_us)
20:56:06.693 GUI          : Loading 'en_us' localization file.
20:56:06.693 INIT         : INIT         : Loading StringTable
20:56:06.716 PROFILING    : Loading StringTable took: 23.273500 ms
20:56:06.716 SCRIPT       : SCRIPT       : Compiling GameLib scripts
20:56:06.741  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
20:56:06.744 PROFILING    : Compiling GameLib scripts took: 26.949900 ms
20:56:06.744 SCRIPT       : SCRIPT       : Compiling Game scripts
20:56:07.200 SCRIPT       : Compiling Game scripts
20:56:07.203  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
20:56:07.237  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
20:56:07.443  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:56:07.443  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:56:07.444  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:56:07.460  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
20:56:07.490  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
20:56:07.565  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
20:56:07.622  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:07.662  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
20:56:07.662  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
20:56:07.740  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
20:56:07.740  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:07.740  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:07.741  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:07.741  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:07.741  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:56:07.777  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:07.777  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:07.778  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:07.778  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:07.778  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:07.779  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:56:07.792  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:07.804  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:07.804  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:07.804  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:07.830  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
20:56:07.830  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
20:56:07.835  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:56:07.835  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:56:07.836  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:56:07.837  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
20:56:07.837  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:56:07.885  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
20:56:08.598 PROFILING    : Compiling Game scripts took: 1853.561000 ms
20:56:08.599 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
20:56:08.613 BACKEND      : Loading dedicated server config.
20:56:08.614  BACKEND      : Server config loaded.
20:56:08.614 BACKEND      : Loading dedicated server config.
20:56:08.614  BACKEND      : JSON Schema Validation:
20:56:08.614   BACKEND      : pattern error:
20:56:08.614    BACKEND   (E): Value of "#/game/mods/0/modId" does not match the required pattern. Value: "{F8B2E3A14C5D6E7F}"
20:56:08.614    BACKEND   (E): RegEx Pattern: "^[0-9a-fA-F]{16}$"
20:56:08.614    BACKEND   (E): Pattern Description: "Param must contain addon ID in format of 16 numbers or letters (A-F)."
20:56:08.614    BACKEND   (E): Reference in schema: "#/properties/game/properties/mods/items/properties/modId"
20:56:08.614   BACKEND   (E): JSON is invalid!
20:56:08.614  BACKEND   (E): There are errors in server config!
20:56:08.614 BACKEND   (E): Unable to continue with a broken DS config! Shutdown!
20:56:08.614 ENGINE    (E): Error while initializing game.
20:56:08.778 ENGINE    (E): Unable to initialize the game
20:56:08.797 ENGINE       : Game destroyed.
20:56:08.812 RPL          : Pip::Destroy
20:56:08.819 RESOURCES (E): ==== Resource leaks ====
20:56:08.819 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
