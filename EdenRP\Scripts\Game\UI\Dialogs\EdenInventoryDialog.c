//! Eden Inventory Dialog
//! Converted from original yMenuInventory.hpp
//! Player inventory management interface

class EdenInventoryDialog : EdenBaseDialog
{
    protected ListBoxWidget m_ItemListBox;
    protected TextWidget m_CarryWeightText;
    protected TextWidget m_SelectedItemText;
    protected ButtonWidget m_UseButton;
    protected ButtonWidget m_DropButton;
    protected ButtonWidget m_GiveButton;
    protected EditBoxWidget m_QuantityEdit;
    
    protected string m_PlayerId;
    protected ref EdenPlayerData m_PlayerData;
    protected ref array<ref EdenInventoryItem> m_InventoryItems;
    protected int m_SelectedItemIndex;
    
    void EdenInventoryDialog()
    {
        m_PlayerId = "";
        m_PlayerData = null;
        m_InventoryItems = new array<ref EdenInventoryItem>();
        m_SelectedItemIndex = -1;
    }
    
    override void OnCreate(string playerId, string parameter = "")
    {
        super.OnCreate(playerId, parameter);
        
        m_PlayerId = playerId;
        
        // Get player data
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenDataManager dataManager = gameMode.GetDataManager();
            if (dataManager)
            {
                m_PlayerData = dataManager.GetPlayerData(playerId);
            }
        }
        
        InitializeInventoryWidgets();
        RefreshInventoryDisplay();
        
        Print("[EdenInventoryDialog] Inventory dialog created for player: " + playerId);
    }
    
    override string GetDialogTitle()
    {
        return "Player Inventory";
    }
    
    override int GetActiveTabId()
    {
        return 2; // Inventory is tab 2
    }
    
    //! Initialize inventory-specific widgets
    protected void InitializeInventoryWidgets()
    {
        Widget rootWidget = GetRootWidget();
        if (!rootWidget)
            return;
        
        // Find item list box
        m_ItemListBox = ListBoxWidget.Cast(rootWidget.FindAnyWidget("ItemList"));
        if (!m_ItemListBox)
        {
            Print("[EdenInventoryDialog] Warning: ItemList widget not found");
        }
        
        // Find carry weight text
        m_CarryWeightText = TextWidget.Cast(rootWidget.FindAnyWidget("CarryWeight"));
        if (!m_CarryWeightText)
        {
            Print("[EdenInventoryDialog] Warning: CarryWeight widget not found");
        }
        
        // Find selected item text
        m_SelectedItemText = TextWidget.Cast(rootWidget.FindAnyWidget("SelectedItem"));
        if (!m_SelectedItemText)
        {
            Print("[EdenInventoryDialog] Warning: SelectedItem widget not found");
        }
        
        // Find action buttons
        m_UseButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("UseButton"));
        m_DropButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("DropButton"));
        m_GiveButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("GiveButton"));
        
        // Find quantity edit box
        m_QuantityEdit = EditBoxWidget.Cast(rootWidget.FindAnyWidget("QuantityEdit"));
        if (m_QuantityEdit)
        {
            m_QuantityEdit.SetText("1");
        }
    }
    
    //! Refresh inventory display
    protected void RefreshInventoryDisplay()
    {
        if (!m_PlayerData || !m_ItemListBox)
            return;
        
        // Clear current list
        m_ItemListBox.ClearItems();
        m_InventoryItems.Clear();
        
        // Get player inventory
        ref array<ref EdenInventoryItem> playerInventory = m_PlayerData.GetInventory();
        
        // Add items to list
        for (int i = 0; i < playerInventory.Count(); i++)
        {
            ref EdenInventoryItem item = playerInventory[i];
            m_InventoryItems.Insert(item);
            
            // Format item display text
            string displayText = FormatItemDisplay(item);
            m_ItemListBox.AddItem(displayText, null, 0);
        }
        
        // Update carry weight display
        UpdateCarryWeightDisplay();
        
        // Clear selection
        m_SelectedItemIndex = -1;
        UpdateSelectedItemDisplay();
        UpdateButtonStates();
        
        Print("[EdenInventoryDialog] Inventory display refreshed - Items: " + m_InventoryItems.Count());
    }
    
    //! Format item for display in list
    protected string FormatItemDisplay(EdenInventoryItem item)
    {
        string displayName = GetItemDisplayName(item.GetItemClass());
        int quantity = item.GetQuantity();
        float weight = item.GetWeight();
        
        if (quantity > 1)
        {
            return displayName + " (" + quantity + ") - " + (weight * quantity) + "kg";
        }
        else
        {
            return displayName + " - " + weight + "kg";
        }
    }
    
    //! Get display name for item class
    protected string GetItemDisplayName(string itemClass)
    {
        // This would map item classes to display names
        // For now, return the class name
        return itemClass;
    }
    
    //! Update carry weight display
    protected void UpdateCarryWeightDisplay()
    {
        if (!m_PlayerData || !m_CarryWeightText)
            return;
        
        float currentWeight = CalculateCurrentWeight();
        float maxWeight = m_PlayerData.GetMaxCarryWeight();
        
        string weightText = "Weight: " + currentWeight + "/" + maxWeight + " kg";
        m_CarryWeightText.SetText(weightText);
        
        // Change color based on weight percentage
        float weightPercentage = currentWeight / maxWeight;
        if (weightPercentage > 0.9)
        {
            // Red for overweight
            m_CarryWeightText.SetColor(Color.RED);
        }
        else if (weightPercentage > 0.7)
        {
            // Yellow for heavy
            m_CarryWeightText.SetColor(Color.YELLOW);
        }
        else
        {
            // White for normal
            m_CarryWeightText.SetColor(Color.WHITE);
        }
    }
    
    //! Calculate current inventory weight
    protected float CalculateCurrentWeight()
    {
        float totalWeight = 0;
        
        for (int i = 0; i < m_InventoryItems.Count(); i++)
        {
            ref EdenInventoryItem item = m_InventoryItems[i];
            totalWeight += item.GetWeight() * item.GetQuantity();
        }
        
        return totalWeight;
    }
    
    //! Update selected item display
    protected void UpdateSelectedItemDisplay()
    {
        if (!m_SelectedItemText)
            return;
        
        if (m_SelectedItemIndex >= 0 && m_SelectedItemIndex < m_InventoryItems.Count())
        {
            ref EdenInventoryItem selectedItem = m_InventoryItems[m_SelectedItemIndex];
            string itemInfo = GetItemInfo(selectedItem);
            m_SelectedItemText.SetText(itemInfo);
        }
        else
        {
            m_SelectedItemText.SetText("No item selected");
        }
    }
    
    //! Get detailed item information
    protected string GetItemInfo(EdenInventoryItem item)
    {
        string info = "Item: " + GetItemDisplayName(item.GetItemClass()) + "\n";
        info += "Quantity: " + item.GetQuantity() + "\n";
        info += "Weight: " + (item.GetWeight() * item.GetQuantity()) + "kg\n";
        info += "Type: " + GetItemType(item.GetItemClass());
        
        return info;
    }
    
    //! Get item type for display
    protected string GetItemType(string itemClass)
    {
        // This would categorize items
        // For now, return generic type
        return "Item";
    }
    
    //! Update button states based on selection
    protected void UpdateButtonStates()
    {
        bool hasSelection = (m_SelectedItemIndex >= 0 && m_SelectedItemIndex < m_InventoryItems.Count());
        
        if (m_UseButton)
            m_UseButton.SetEnabled(hasSelection);
        
        if (m_DropButton)
            m_DropButton.SetEnabled(hasSelection);
        
        if (m_GiveButton)
            m_GiveButton.SetEnabled(hasSelection);
    }
    
    //! Handle item list selection change
    void OnItemListSelectionChanged()
    {
        if (!m_ItemListBox)
            return;
        
        m_SelectedItemIndex = m_ItemListBox.GetSelectedItem();
        UpdateSelectedItemDisplay();
        UpdateButtonStates();
        
        Print("[EdenInventoryDialog] Item selected: " + m_SelectedItemIndex);
    }
    
    //! Handle use button click
    void OnUseButtonClick()
    {
        if (m_SelectedItemIndex < 0 || m_SelectedItemIndex >= m_InventoryItems.Count())
            return;
        
        ref EdenInventoryItem selectedItem = m_InventoryItems[m_SelectedItemIndex];
        int quantity = GetQuantityFromEdit();
        
        // Use item logic would go here
        Print("[EdenInventoryDialog] Using item: " + selectedItem.GetItemClass() + " Quantity: " + quantity);
        
        // Refresh display after use
        RefreshInventoryDisplay();
    }
    
    //! Handle drop button click
    void OnDropButtonClick()
    {
        if (m_SelectedItemIndex < 0 || m_SelectedItemIndex >= m_InventoryItems.Count())
            return;
        
        ref EdenInventoryItem selectedItem = m_InventoryItems[m_SelectedItemIndex];
        int quantity = GetQuantityFromEdit();
        
        // Drop item logic would go here
        Print("[EdenInventoryDialog] Dropping item: " + selectedItem.GetItemClass() + " Quantity: " + quantity);
        
        // Refresh display after drop
        RefreshInventoryDisplay();
    }
    
    //! Handle give button click
    void OnGiveButtonClick()
    {
        if (m_SelectedItemIndex < 0 || m_SelectedItemIndex >= m_InventoryItems.Count())
            return;
        
        ref EdenInventoryItem selectedItem = m_InventoryItems[m_SelectedItemIndex];
        int quantity = GetQuantityFromEdit();
        
        // Give item logic would go here (would open player selection dialog)
        Print("[EdenInventoryDialog] Giving item: " + selectedItem.GetItemClass() + " Quantity: " + quantity);
    }
    
    //! Get quantity from edit box
    protected int GetQuantityFromEdit()
    {
        if (!m_QuantityEdit)
            return 1;
        
        string quantityText = m_QuantityEdit.GetText();
        int quantity = quantityText.ToInt();
        
        if (quantity <= 0)
            quantity = 1;
        
        return quantity;
    }
    
    override void OnUpdate()
    {
        super.OnUpdate();
        
        // Update display periodically
        static int lastUpdate = 0;
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        if (currentTime - lastUpdate > 2000) // 2 seconds
        {
            UpdateCarryWeightDisplay();
            lastUpdate = currentTime;
        }
    }
    
    override bool OnClick(Widget w, int x, int y, int button)
    {
        // Handle base dialog clicks first
        if (super.OnClick(w, x, y, button))
            return true;
        
        // Handle inventory-specific clicks
        if (w == m_UseButton)
        {
            OnUseButtonClick();
            return true;
        }
        else if (w == m_DropButton)
        {
            OnDropButtonClick();
            return true;
        }
        else if (w == m_GiveButton)
        {
            OnGiveButtonClick();
            return true;
        }
        else if (w == m_ItemListBox)
        {
            OnItemListSelectionChanged();
            return true;
        }
        
        return false;
    }
    
    override void OnClose()
    {
        super.OnClose();
        
        m_PlayerData = null;
        m_InventoryItems.Clear();
        
        Print("[EdenInventoryDialog] Inventory dialog closed for player: " + m_PlayerId);
    }
}
