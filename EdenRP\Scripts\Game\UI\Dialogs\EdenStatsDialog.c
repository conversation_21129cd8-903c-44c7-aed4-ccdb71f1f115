//! Eden Stats Dialog
//! Converted from original yMenuStats.hpp
//! Player statistics and playtime display interface

class EdenStatsDialog : EdenBaseDialog
{
    protected TextWidget m_CivPlaytimeText;
    protected TextWidget m_CopPlaytimeText;
    protected TextWidget m_MedicPlaytimeText;
    protected TextWidget m_TotalPlaytimeText;
    protected TextWidget m_CivLevelText;
    protected TextWidget m_CopLevelText;
    protected TextWidget m_MedicLevelText;
    protected TextWidget m_CivExpText;
    protected TextWidget m_CopExpText;
    protected TextWidget m_MedicExpText;
    protected TextWidget m_MoneyEarnedText;
    protected TextWidget m_MoneySpentText;
    protected TextWidget m_ArrestsText;
    protected TextWidget m_DeathsText;
    protected TextWidget m_KillsText;
    
    protected string m_PlayerId;
    protected ref EdenPlayerData m_PlayerData;
    protected ref EdenPlayerStats m_PlayerStats;
    
    void EdenStatsDialog()
    {
        m_PlayerId = "";
        m_PlayerData = null;
        m_PlayerStats = null;
    }
    
    override void OnCreate(string playerId, string parameter = "")
    {
        super.OnCreate(playerId, parameter);
        
        m_PlayerId = playerId;
        
        // Get player data
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenDataManager dataManager = gameMode.GetDataManager();
            if (dataManager)
            {
                m_PlayerData = dataManager.GetPlayerData(playerId);
                if (m_PlayerData)
                {
                    m_PlayerStats = m_PlayerData.GetStats();
                }
            }
        }
        
        InitializeStatsWidgets();
        UpdateStatsDisplay();
        
        Print("[EdenStatsDialog] Stats dialog created for player: " + playerId);
    }
    
    override string GetDialogTitle()
    {
        return "Player Stats";
    }
    
    override int GetActiveTabId()
    {
        return 3; // Stats is tab 3
    }
    
    //! Initialize stats-specific widgets
    protected void InitializeStatsWidgets()
    {
        Widget rootWidget = GetRootWidget();
        if (!rootWidget)
            return;
        
        // Find playtime widgets
        m_CivPlaytimeText = TextWidget.Cast(rootWidget.FindAnyWidget("CivPlaytime"));
        m_CopPlaytimeText = TextWidget.Cast(rootWidget.FindAnyWidget("CopPlaytime"));
        m_MedicPlaytimeText = TextWidget.Cast(rootWidget.FindAnyWidget("MedicPlaytime"));
        m_TotalPlaytimeText = TextWidget.Cast(rootWidget.FindAnyWidget("TotalPlaytime"));
        
        // Find level widgets
        m_CivLevelText = TextWidget.Cast(rootWidget.FindAnyWidget("CivLevel"));
        m_CopLevelText = TextWidget.Cast(rootWidget.FindAnyWidget("CopLevel"));
        m_MedicLevelText = TextWidget.Cast(rootWidget.FindAnyWidget("MedicLevel"));
        
        // Find experience widgets
        m_CivExpText = TextWidget.Cast(rootWidget.FindAnyWidget("CivExp"));
        m_CopExpText = TextWidget.Cast(rootWidget.FindAnyWidget("CopExp"));
        m_MedicExpText = TextWidget.Cast(rootWidget.FindAnyWidget("MedicExp"));
        
        // Find money widgets
        m_MoneyEarnedText = TextWidget.Cast(rootWidget.FindAnyWidget("MoneyEarned"));
        m_MoneySpentText = TextWidget.Cast(rootWidget.FindAnyWidget("MoneySpent"));
        
        // Find activity widgets
        m_ArrestsText = TextWidget.Cast(rootWidget.FindAnyWidget("Arrests"));
        m_DeathsText = TextWidget.Cast(rootWidget.FindAnyWidget("Deaths"));
        m_KillsText = TextWidget.Cast(rootWidget.FindAnyWidget("Kills"));
        
        Print("[EdenStatsDialog] Stats widgets initialized");
    }
    
    //! Update stats display with current data
    protected void UpdateStatsDisplay()
    {
        if (!m_PlayerData || !m_PlayerStats)
            return;
        
        // Update playtime displays
        if (m_CivPlaytimeText)
        {
            int civPlaytime = m_PlayerStats.GetCivPlaytime();
            m_CivPlaytimeText.SetText("Civilian: " + FormatPlaytime(civPlaytime));
        }
        
        if (m_CopPlaytimeText)
        {
            int copPlaytime = m_PlayerStats.GetCopPlaytime();
            m_CopPlaytimeText.SetText("Police: " + FormatPlaytime(copPlaytime));
        }
        
        if (m_MedicPlaytimeText)
        {
            int medicPlaytime = m_PlayerStats.GetMedicPlaytime();
            m_MedicPlaytimeText.SetText("Medic: " + FormatPlaytime(medicPlaytime));
        }
        
        if (m_TotalPlaytimeText)
        {
            int totalPlaytime = m_PlayerData.GetPlaytime();
            m_TotalPlaytimeText.SetText("Total: " + FormatPlaytime(totalPlaytime));
        }
        
        // Update level displays
        if (m_CivLevelText)
        {
            int civLevel = m_PlayerData.GetCivLevel();
            m_CivLevelText.SetText("Level " + civLevel);
        }
        
        if (m_CopLevelText)
        {
            int copLevel = m_PlayerData.GetCopLevel();
            string copRank = GetCopRankName(copLevel);
            m_CopLevelText.SetText(copRank + " (Level " + copLevel + ")");
        }
        
        if (m_MedicLevelText)
        {
            int medicLevel = m_PlayerData.GetMedicLevel();
            string medicRank = GetMedicRankName(medicLevel);
            m_MedicLevelText.SetText(medicRank + " (Level " + medicLevel + ")");
        }
        
        // Update experience displays
        if (m_CivExpText)
        {
            int civExp = m_PlayerStats.GetCivExperience();
            int nextLevelExp = GetExpForNextLevel(m_PlayerData.GetCivLevel());
            m_CivExpText.SetText(civExp + " / " + nextLevelExp + " XP");
        }
        
        if (m_CopExpText)
        {
            int copExp = m_PlayerStats.GetCopExperience();
            int nextLevelExp = GetExpForNextLevel(m_PlayerData.GetCopLevel());
            m_CopExpText.SetText(copExp + " / " + nextLevelExp + " XP");
        }
        
        if (m_MedicExpText)
        {
            int medicExp = m_PlayerStats.GetMedicExperience();
            int nextLevelExp = GetExpForNextLevel(m_PlayerData.GetMedicLevel());
            m_MedicExpText.SetText(medicExp + " / " + nextLevelExp + " XP");
        }
        
        // Update money displays
        if (m_MoneyEarnedText)
        {
            int moneyEarned = m_PlayerStats.GetTotalMoneyEarned();
            m_MoneyEarnedText.SetText("$" + FormatMoney(moneyEarned));
        }
        
        if (m_MoneySpentText)
        {
            int moneySpent = m_PlayerStats.GetTotalMoneySpent();
            m_MoneySpentText.SetText("$" + FormatMoney(moneySpent));
        }
        
        // Update activity displays
        if (m_ArrestsText)
        {
            int arrests = m_PlayerStats.GetTotalArrests();
            m_ArrestsText.SetText(arrests.ToString());
        }
        
        if (m_DeathsText)
        {
            int deaths = m_PlayerStats.GetTotalDeaths();
            m_DeathsText.SetText(deaths.ToString());
        }
        
        if (m_KillsText)
        {
            int kills = m_PlayerStats.GetTotalKills();
            m_KillsText.SetText(kills.ToString());
        }
        
        Print("[EdenStatsDialog] Stats display updated");
    }
    
    //! Format playtime in hours and minutes
    protected string FormatPlaytime(int minutes)
    {
        int hours = minutes / 60;
        int remainingMinutes = minutes % 60;
        
        if (hours > 0)
        {
            return hours + "h " + remainingMinutes + "m";
        }
        else
        {
            return remainingMinutes + "m";
        }
    }
    
    //! Format money with commas
    protected string FormatMoney(int amount)
    {
        string amountStr = amount.ToString();
        string formatted = "";
        int length = amountStr.Length();
        
        for (int i = 0; i < length; i++)
        {
            if (i > 0 && (length - i) % 3 == 0)
                formatted += ",";
            formatted += amountStr.Get(i);
        }
        
        return formatted;
    }
    
    //! Get cop rank name from level
    protected string GetCopRankName(int level)
    {
        switch (level)
        {
            case 0: return "Civilian";
            case 1: return "Cadet";
            case 2: return "Officer";
            case 3: return "Senior Officer";
            case 4: return "Corporal";
            case 5: return "Sergeant";
            case 6: return "Lieutenant";
            case 7: return "Captain";
            case 8: return "Deputy Chief";
            case 9: return "Chief";
            default: return "Unknown";
        }
    }
    
    //! Get medic rank name from level
    protected string GetMedicRankName(int level)
    {
        switch (level)
        {
            case 0: return "Civilian";
            case 1: return "EMT";
            case 2: return "Paramedic";
            case 3: return "Senior Paramedic";
            case 4: return "Fire Fighter";
            case 5: return "Senior Fire Fighter";
            case 6: return "EMS Lieutenant";
            case 7: return "EMS Captain";
            case 8: return "EMS Deputy Chief";
            case 9: return "EMS Chief";
            default: return "Unknown";
        }
    }
    
    //! Get experience required for next level
    protected int GetExpForNextLevel(int currentLevel)
    {
        // Experience formula: level * 1000 + (level * level * 100)
        int nextLevel = currentLevel + 1;
        return nextLevel * 1000 + (nextLevel * nextLevel * 100);
    }
    
    //! Handle refresh button click
    void OnRefreshButtonClick()
    {
        // Refresh player data
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenDataManager dataManager = gameMode.GetDataManager();
            if (dataManager)
            {
                m_PlayerData = dataManager.GetPlayerData(m_PlayerId);
                if (m_PlayerData)
                {
                    m_PlayerStats = m_PlayerData.GetStats();
                }
                UpdateStatsDisplay();
            }
        }
        
        Print("[EdenStatsDialog] Stats refreshed for player: " + m_PlayerId);
    }
    
    override void OnUpdate()
    {
        super.OnUpdate();
        
        // Update display periodically (every 10 seconds)
        static int lastUpdate = 0;
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        if (currentTime - lastUpdate > 10000) // 10 seconds
        {
            UpdateStatsDisplay();
            lastUpdate = currentTime;
        }
    }
    
    override bool OnClick(Widget w, int x, int y, int button)
    {
        // Handle base dialog clicks first
        if (super.OnClick(w, x, y, button))
            return true;
        
        // Handle stats-specific clicks
        ButtonWidget refreshButton = ButtonWidget.Cast(GetRootWidget().FindAnyWidget("RefreshButton"));
        if (w == refreshButton)
        {
            OnRefreshButtonClick();
            return true;
        }
        
        return false;
    }
    
    override void OnClose()
    {
        super.OnClose();
        
        m_PlayerData = null;
        m_PlayerStats = null;
        
        Print("[EdenStatsDialog] Stats dialog closed for player: " + m_PlayerId);
    }
}
