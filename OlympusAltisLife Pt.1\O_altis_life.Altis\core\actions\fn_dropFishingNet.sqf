//  File: fn_dropFishingNet.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Drops a virtual fishing net from the boat.
private["_fish","_type","_typeName"];
if(!(vehicle player isKindOf "Ship")) exitWith {};
if (player getVariable ["restrained",false]) exitWith {};
if (oev_action_inUse) exitWith {hint "You are already performing another action!"};
if (oev_isDowned) exitWith {};
_fish = (nearestObjects[getPos vehicle player,["Fish_Base_F"],20]);
oev_net_dropped = true;
enableEnvironment true;
titleText[localize "STR_NOTF_NetDrop","PLAIN DOWN"];
uiSleep 5;
if(count _fish == 0) exitWith {titleText[localize "STR_NOTF_NetDropFail","PLAIN DOWN"]; oev_net_dropped = false;};
{
	if(_x isKindOf "Fish_Base_F") then
	{
		switch(true) do
		{
			case ((typeOf _x) == "Salema_F"): {_typeName = localize "STR_ANIM_Salema"; _type = "salema";};
			case ((typeOf _x) == "Ornate_random_F") : {_typeName = localize "STR_ANIM_Ornate"; _type = "ornate";};
			case ((typeOf _x) == "Mackerel_F") : {_typeName = localize "STR_ANIM_Mackerel"; _type = "mackerel";};
			case ((typeOf _x) == "Tuna_F") : {_typeName = localize "STR_ANIM_Tuna"; _type = "tuna";};
			case ((typeOf _x) == "Mullet_F") : {_typeName = localize "STR_ANIM_Mullet"; _type = "mullet";};
			case ((typeOf _x) == "CatShark_F") : {_typeName = localize "STR_ANIM_Catshark"; _type = "catshark";};
			default {_type = "";};
		};

		uiSleep 3;

		if(([true,_type,1] call OEC_fnc_handleInv)) then
		{
			deleteVehicle _x;
			titleText[format[(localize "STR_NOTF_Fishing"),_typeName],"PLAIN DOWN"];
		};
	};
} foreach (_fish);

uiSleep 1.5;
titleText[localize "STR_NOTF_NetUp","PLAIN DOWN"];
oev_net_dropped = false;
enableEnvironment life_ambientLife;