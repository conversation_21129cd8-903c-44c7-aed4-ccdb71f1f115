//! Eden Phone Dialog
//! Converted from original yMenuPhone.hpp
//! Phone interface for messaging, contacts, and emergency services

class EdenPhoneDialog : EdenBaseDialog
{
    protected ListBoxWidget m_ContactListBox;
    protected ListBoxWidget m_MessageListBox;
    protected TextWidget m_ContactNameText;
    protected EditBoxWidget m_MessageEdit;
    protected ButtonWidget m_SendButton;
    protected ButtonWidget m_CallButton;
    protected ButtonWidget m_AddContactButton;
    protected ButtonWidget m_DeleteContactButton;
    protected ButtonWidget m_EmergencyButton;
    protected ButtonWidget m_TaxiButton;
    protected ButtonWidget m_MechanicButton;
    
    protected string m_PlayerId;
    protected ref EdenPlayerData m_PlayerData;
    protected ref array<ref EdenContact> m_Contacts;
    protected ref array<ref EdenMessage> m_Messages;
    protected int m_SelectedContactIndex;
    protected string m_SelectedContactId;
    
    void EdenPhoneDialog()
    {
        m_PlayerId = "";
        m_PlayerData = null;
        m_Contacts = new array<ref EdenContact>();
        m_Messages = new array<ref EdenMessage>();
        m_SelectedContactIndex = -1;
        m_SelectedContactId = "";
    }
    
    override void OnCreate(string playerId, string parameter = "")
    {
        super.OnCreate(playerId, parameter);
        
        m_PlayerId = playerId;
        
        // Get player data
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenDataManager dataManager = gameMode.GetDataManager();
            if (dataManager)
            {
                m_PlayerData = dataManager.GetPlayerData(playerId);
            }
        }
        
        InitializePhoneWidgets();
        LoadContacts();
        RefreshDisplay();
        
        Print("[EdenPhoneDialog] Phone dialog created for player: " + playerId);
    }
    
    override string GetDialogTitle()
    {
        return "Phone";
    }
    
    override int GetActiveTabId()
    {
        return 5; // Phone is tab 5
    }
    
    //! Initialize phone-specific widgets
    protected void InitializePhoneWidgets()
    {
        Widget rootWidget = GetRootWidget();
        if (!rootWidget)
            return;
        
        // Find contact list
        m_ContactListBox = ListBoxWidget.Cast(rootWidget.FindAnyWidget("ContactList"));
        if (!m_ContactListBox)
        {
            Print("[EdenPhoneDialog] Warning: ContactList widget not found");
        }
        
        // Find message list
        m_MessageListBox = ListBoxWidget.Cast(rootWidget.FindAnyWidget("MessageList"));
        if (!m_MessageListBox)
        {
            Print("[EdenPhoneDialog] Warning: MessageList widget not found");
        }
        
        // Find contact name text
        m_ContactNameText = TextWidget.Cast(rootWidget.FindAnyWidget("ContactName"));
        if (!m_ContactNameText)
        {
            Print("[EdenPhoneDialog] Warning: ContactName widget not found");
        }
        
        // Find message edit box
        m_MessageEdit = EditBoxWidget.Cast(rootWidget.FindAnyWidget("MessageEdit"));
        if (!m_MessageEdit)
        {
            Print("[EdenPhoneDialog] Warning: MessageEdit widget not found");
        }
        
        // Find action buttons
        m_SendButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("SendButton"));
        m_CallButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("CallButton"));
        m_AddContactButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("AddContactButton"));
        m_DeleteContactButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("DeleteContactButton"));
        
        // Find service buttons
        m_EmergencyButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("EmergencyButton"));
        m_TaxiButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("TaxiButton"));
        m_MechanicButton = ButtonWidget.Cast(rootWidget.FindAnyWidget("MechanicButton"));
        
        Print("[EdenPhoneDialog] Phone widgets initialized");
    }
    
    //! Load player contacts
    protected void LoadContacts()
    {
        m_Contacts.Clear();
        
        if (!m_PlayerData)
            return;
        
        // Get contacts from player data
        ref array<ref EdenContact> playerContacts = m_PlayerData.GetContacts();
        if (playerContacts)
        {
            for (int i = 0; i < playerContacts.Count(); i++)
            {
                m_Contacts.Insert(playerContacts[i]);
            }
        }
        
        // Add default emergency contacts
        AddDefaultContacts();
        
        Print("[EdenPhoneDialog] Loaded " + m_Contacts.Count() + " contacts");
    }
    
    //! Add default emergency contacts
    protected void AddDefaultContacts()
    {
        // Police
        ref EdenContact policeContact = new EdenContact();
        policeContact.SetName("Police Department");
        policeContact.SetPhoneNumber("911");
        policeContact.SetContactType("emergency");
        policeContact.SetIsDefault(true);
        m_Contacts.Insert(policeContact);
        
        // Medical
        ref EdenContact medicContact = new EdenContact();
        medicContact.SetName("Emergency Medical");
        medicContact.SetPhoneNumber("912");
        medicContact.SetContactType("emergency");
        medicContact.SetIsDefault(true);
        m_Contacts.Insert(medicContact);
        
        // Taxi
        ref EdenContact taxiContact = new EdenContact();
        taxiContact.SetName("Taxi Service");
        taxiContact.SetPhoneNumber("913");
        taxiContact.SetContactType("service");
        taxiContact.SetIsDefault(true);
        m_Contacts.Insert(taxiContact);
        
        // Mechanic
        ref EdenContact mechanicContact = new EdenContact();
        mechanicContact.SetName("Mechanic Service");
        mechanicContact.SetPhoneNumber("914");
        mechanicContact.SetContactType("service");
        mechanicContact.SetIsDefault(true);
        m_Contacts.Insert(mechanicContact);
    }
    
    //! Refresh all displays
    protected void RefreshDisplay()
    {
        RefreshContactList();
        RefreshMessageList();
        UpdateContactInfo();
        UpdateButtonStates();
    }
    
    //! Refresh contact list
    protected void RefreshContactList()
    {
        if (!m_ContactListBox)
            return;
        
        m_ContactListBox.ClearItems();
        
        for (int i = 0; i < m_Contacts.Count(); i++)
        {
            ref EdenContact contact = m_Contacts[i];
            string displayText = FormatContactDisplay(contact);
            m_ContactListBox.AddItem(displayText, null, 0);
        }
    }
    
    //! Refresh message list for selected contact
    protected void RefreshMessageList()
    {
        if (!m_MessageListBox)
            return;
        
        m_MessageListBox.ClearItems();
        m_Messages.Clear();
        
        if (m_SelectedContactId == "")
            return;
        
        // Get messages for selected contact
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenCommunicationManager commManager = gameMode.GetCommunicationManager();
            if (commManager)
            {
                ref array<ref EdenMessage> contactMessages = commManager.GetMessagesWithContact(m_PlayerId, m_SelectedContactId);
                if (contactMessages)
                {
                    for (int i = 0; i < contactMessages.Count(); i++)
                    {
                        ref EdenMessage message = contactMessages[i];
                        m_Messages.Insert(message);
                        
                        string displayText = FormatMessageDisplay(message);
                        m_MessageListBox.AddItem(displayText, null, 0);
                    }
                }
            }
        }
        
        // Scroll to bottom
        if (m_Messages.Count() > 0)
        {
            m_MessageListBox.EnsureVisible(m_Messages.Count() - 1);
        }
    }
    
    //! Format contact for display
    protected string FormatContactDisplay(EdenContact contact)
    {
        string displayText = contact.GetName();
        
        if (contact.GetContactType() == "emergency")
        {
            displayText = "[EMERGENCY] " + displayText;
        }
        else if (contact.GetContactType() == "service")
        {
            displayText = "[SERVICE] " + displayText;
        }
        
        return displayText;
    }
    
    //! Format message for display
    protected string FormatMessageDisplay(EdenMessage message)
    {
        string sender = message.GetSenderName();
        string content = message.GetContent();
        string timestamp = FormatTimestamp(message.GetTimestamp());
        
        if (message.GetSenderId() == m_PlayerId)
        {
            return "[" + timestamp + "] You: " + content;
        }
        else
        {
            return "[" + timestamp + "] " + sender + ": " + content;
        }
    }
    
    //! Format timestamp for display
    protected string FormatTimestamp(int timestamp)
    {
        // Convert timestamp to readable format
        // For now, return simple format
        return timestamp.ToString();
    }
    
    //! Update contact info display
    protected void UpdateContactInfo()
    {
        if (!m_ContactNameText)
            return;
        
        if (m_SelectedContactIndex >= 0 && m_SelectedContactIndex < m_Contacts.Count())
        {
            ref EdenContact contact = m_Contacts[m_SelectedContactIndex];
            string contactInfo = contact.GetName() + " (" + contact.GetPhoneNumber() + ")";
            m_ContactNameText.SetText(contactInfo);
        }
        else
        {
            m_ContactNameText.SetText("No contact selected");
        }
    }
    
    //! Update button states
    protected void UpdateButtonStates()
    {
        bool hasSelection = (m_SelectedContactIndex >= 0 && m_SelectedContactIndex < m_Contacts.Count());
        bool hasMessage = (m_MessageEdit && m_MessageEdit.GetText() != "");
        
        if (m_SendButton)
            m_SendButton.SetEnabled(hasSelection && hasMessage);
        
        if (m_CallButton)
            m_CallButton.SetEnabled(hasSelection);
        
        if (m_DeleteContactButton)
        {
            bool canDelete = hasSelection;
            if (hasSelection)
            {
                ref EdenContact contact = m_Contacts[m_SelectedContactIndex];
                canDelete = !contact.IsDefault(); // Can't delete default contacts
            }
            m_DeleteContactButton.SetEnabled(canDelete);
        }
    }
    
    //! Handle contact list selection
    void OnContactSelectionChanged()
    {
        if (!m_ContactListBox)
            return;
        
        m_SelectedContactIndex = m_ContactListBox.GetSelectedItem();
        
        if (m_SelectedContactIndex >= 0 && m_SelectedContactIndex < m_Contacts.Count())
        {
            ref EdenContact contact = m_Contacts[m_SelectedContactIndex];
            m_SelectedContactId = contact.GetContactId();
        }
        else
        {
            m_SelectedContactId = "";
        }
        
        RefreshMessageList();
        UpdateContactInfo();
        UpdateButtonStates();
        
        Print("[EdenPhoneDialog] Contact selected: " + m_SelectedContactIndex);
    }
    
    //! Handle send button click
    void OnSendButtonClick()
    {
        if (m_SelectedContactId == "" || !m_MessageEdit)
            return;
        
        string messageContent = m_MessageEdit.GetText();
        if (messageContent == "")
            return;
        
        // Send message through communication manager
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenCommunicationManager commManager = gameMode.GetCommunicationManager();
            if (commManager)
            {
                bool success = commManager.SendMessage(m_PlayerId, m_SelectedContactId, messageContent);
                if (success)
                {
                    m_MessageEdit.SetText(""); // Clear message box
                    RefreshMessageList(); // Refresh to show new message
                    Print("[EdenPhoneDialog] Message sent to: " + m_SelectedContactId);
                }
                else
                {
                    Print("[EdenPhoneDialog] Failed to send message");
                }
            }
        }
    }
    
    //! Handle call button click
    void OnCallButtonClick()
    {
        if (m_SelectedContactIndex < 0 || m_SelectedContactIndex >= m_Contacts.Count())
            return;
        
        ref EdenContact contact = m_Contacts[m_SelectedContactIndex];
        
        // Handle different call types
        string contactType = contact.GetContactType();
        string phoneNumber = contact.GetPhoneNumber();
        
        if (contactType == "emergency")
        {
            HandleEmergencyCall(phoneNumber);
        }
        else if (contactType == "service")
        {
            HandleServiceCall(phoneNumber);
        }
        else
        {
            HandlePlayerCall(contact.GetContactId());
        }
        
        Print("[EdenPhoneDialog] Calling: " + contact.GetName() + " (" + phoneNumber + ")");
    }

    //! Handle emergency call
    protected void HandleEmergencyCall(string phoneNumber)
    {
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (!gameMode)
            return;

        switch (phoneNumber)
        {
            case "911": // Police
                {
                    EdenPoliceManager policeManager = gameMode.GetPoliceManager();
                    if (policeManager)
                    {
                        policeManager.HandleEmergencyCall(m_PlayerId, "police");
                    }
                    break;
                }
            case "912": // Medical
                {
                    EdenMedicalManager medicalManager = gameMode.GetMedicalManager();
                    if (medicalManager)
                    {
                        medicalManager.HandleEmergencyCall(m_PlayerId, "medical");
                    }
                    break;
                }
        }
    }

    //! Handle service call
    protected void HandleServiceCall(string phoneNumber)
    {
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (!gameMode)
            return;

        switch (phoneNumber)
        {
            case "913": // Taxi
                {
                    EdenVehicleManager vehicleManager = gameMode.GetVehicleManager();
                    if (vehicleManager)
                    {
                        vehicleManager.HandleTaxiCall(m_PlayerId);
                    }
                    break;
                }
            case "914": // Mechanic
                {
                    EdenVehicleManager vehicleManager = gameMode.GetVehicleManager();
                    if (vehicleManager)
                    {
                        vehicleManager.HandleMechanicCall(m_PlayerId);
                    }
                    break;
                }
        }
    }

    //! Handle player call
    protected void HandlePlayerCall(string contactId)
    {
        // Initiate voice call with another player
        EdenGameMode gameMode = EdenGameMode.GetInstance();
        if (gameMode)
        {
            EdenCommunicationManager commManager = gameMode.GetCommunicationManager();
            if (commManager)
            {
                commManager.InitiateCall(m_PlayerId, contactId);
            }
        }
    }

    //! Handle add contact button click
    void OnAddContactButtonClick()
    {
        // This would open a dialog to add a new contact
        Print("[EdenPhoneDialog] Add contact dialog would open here");
    }

    //! Handle delete contact button click
    void OnDeleteContactButtonClick()
    {
        if (m_SelectedContactIndex < 0 || m_SelectedContactIndex >= m_Contacts.Count())
            return;

        ref EdenContact contact = m_Contacts[m_SelectedContactIndex];

        // Can't delete default contacts
        if (contact.IsDefault())
        {
            Print("[EdenPhoneDialog] Cannot delete default contact");
            return;
        }

        // Remove contact
        m_Contacts.RemoveOrdered(m_SelectedContactIndex);

        // Update player data
        if (m_PlayerData)
        {
            m_PlayerData.RemoveContact(contact.GetContactId());
        }

        // Reset selection
        m_SelectedContactIndex = -1;
        m_SelectedContactId = "";

        RefreshDisplay();

        Print("[EdenPhoneDialog] Contact deleted: " + contact.GetName());
    }

    //! Handle emergency button click
    void OnEmergencyButtonClick()
    {
        // Quick dial 911
        for (int i = 0; i < m_Contacts.Count(); i++)
        {
            ref EdenContact contact = m_Contacts[i];
            if (contact.GetPhoneNumber() == "911")
            {
                m_SelectedContactIndex = i;
                m_SelectedContactId = contact.GetContactId();
                OnCallButtonClick();
                break;
            }
        }
    }

    //! Handle taxi button click
    void OnTaxiButtonClick()
    {
        // Quick dial taxi
        for (int i = 0; i < m_Contacts.Count(); i++)
        {
            ref EdenContact contact = m_Contacts[i];
            if (contact.GetPhoneNumber() == "913")
            {
                m_SelectedContactIndex = i;
                m_SelectedContactId = contact.GetContactId();
                OnCallButtonClick();
                break;
            }
        }
    }

    //! Handle mechanic button click
    void OnMechanicButtonClick()
    {
        // Quick dial mechanic
        for (int i = 0; i < m_Contacts.Count(); i++)
        {
            ref EdenContact contact = m_Contacts[i];
            if (contact.GetPhoneNumber() == "914")
            {
                m_SelectedContactIndex = i;
                m_SelectedContactId = contact.GetContactId();
                OnCallButtonClick();
                break;
            }
        }
    }

    //! Handle message edit text change
    void OnMessageEditChanged()
    {
        UpdateButtonStates();
    }

    override void OnUpdate()
    {
        super.OnUpdate();

        // Check for new messages periodically
        static int lastMessageCheck = 0;
        int currentTime = GetGame().GetWorld().GetWorldTime();

        if (currentTime - lastMessageCheck > 5000) // 5 seconds
        {
            if (m_SelectedContactId != "")
            {
                RefreshMessageList();
            }
            lastMessageCheck = currentTime;
        }
    }

    override bool OnClick(Widget w, int x, int y, int button)
    {
        // Handle base dialog clicks first
        if (super.OnClick(w, x, y, button))
            return true;

        // Handle phone-specific clicks
        if (w == m_SendButton)
        {
            OnSendButtonClick();
            return true;
        }
        else if (w == m_CallButton)
        {
            OnCallButtonClick();
            return true;
        }
        else if (w == m_AddContactButton)
        {
            OnAddContactButtonClick();
            return true;
        }
        else if (w == m_DeleteContactButton)
        {
            OnDeleteContactButtonClick();
            return true;
        }
        else if (w == m_EmergencyButton)
        {
            OnEmergencyButtonClick();
            return true;
        }
        else if (w == m_TaxiButton)
        {
            OnTaxiButtonClick();
            return true;
        }
        else if (w == m_MechanicButton)
        {
            OnMechanicButtonClick();
            return true;
        }
        else if (w == m_ContactListBox)
        {
            OnContactSelectionChanged();
            return true;
        }

        return false;
    }

    override bool OnChange(Widget w, int x, int y, bool finished)
    {
        if (w == m_MessageEdit)
        {
            OnMessageEditChanged();
            return true;
        }

        return false;
    }

    override void OnClose()
    {
        super.OnClose();

        m_PlayerData = null;
        m_Contacts.Clear();
        m_Messages.Clear();

        Print("[EdenPhoneDialog] Phone dialog closed for player: " + m_PlayerId);
    }
