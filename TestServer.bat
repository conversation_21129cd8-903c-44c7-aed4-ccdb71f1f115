@echo off
title Eden RP Server Test
color 0A

REM Store the original directory
set "ORIGINAL_DIR=%~dp0"

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
    REM Change back to the script's directory
    cd /d "%ORIGINAL_DIR%"
) else (
    echo This script requires administrator privileges to create symlinks.
    echo Requesting elevation...
    powershell -Command "Start-Process '%~f0' -Verb RunAs -ArgumentList '-WorkingDirectory', '%~dp0'"
    exit /b
)

echo ========================================
echo      EDEN RP SERVER TEST (ADMIN)
echo ========================================
echo.
echo This will test the server startup with
echo detailed error reporting and admin privileges.
echo.

echo Original script directory: %ORIGINAL_DIR%
cd /d "%ORIGINAL_DIR%"
cd ArmaReforgerServer\steamapps\common\Arma Reforger Server

echo Current directory: %CD%
echo.

echo Checking for server executable...
if not exist "ArmaReforgerServerDiag.exe" (
    echo ERROR: ArmaReforgerServerDiag.exe not found!
    echo Please ensure Arma Reforger Server is properly installed.
    pause
    exit /b 1
)
echo ✓ Server executable found

echo.
echo Checking configuration file...
echo Looking for: %ORIGINAL_DIR%ArmaReforgerServer\server.json
if not exist "%ORIGINAL_DIR%ArmaReforgerServer\server.json" (
    echo ERROR: server.json not found!
    echo Expected path: %ORIGINAL_DIR%ArmaReforgerServer\server.json
    pause
    exit /b 1
)
echo ✓ Configuration file found

echo.
echo Checking mod directory...
echo Source mod path: %ORIGINAL_DIR%EdenRP
echo Target mod path: %CD%\addons\EdenRP

if not exist "addons\EdenRP" (
    echo WARNING: EdenRP mod directory not found, creating symlink...

    REM Create addons directory if it doesn't exist
    if not exist "addons" mkdir "addons"

    REM Try to create symlink first
    mklink /D "addons\EdenRP" "%ORIGINAL_DIR%EdenRP"
    if errorlevel 1 (
        echo Symlink failed, copying mod files instead...
        echo This may take a moment...
        xcopy "%ORIGINAL_DIR%EdenRP" "addons\EdenRP" /E /I /Y
        if errorlevel 1 (
            echo ERROR: Failed to copy mod files!
            pause
            exit /b 1
        )
        echo ✓ Mod files copied successfully
    ) else (
        echo ✓ Mod symlink created successfully
    )
) else (
    echo ✓ Mod directory already exists
)

echo.
echo Starting server with verbose logging...
echo Command: ArmaReforgerServerDiag.exe -config "%ORIGINAL_DIR%ArmaReforgerServer\server.json" -profile "%ORIGINAL_DIR%ArmaReforgerServer\saves" -addonsDir "addons" -logLevel 3 -logStats
echo.
echo ========================================
echo           SERVER OUTPUT
echo ========================================

ArmaReforgerServerDiag.exe -config "%ORIGINAL_DIR%ArmaReforgerServer\server.json" -profile "%ORIGINAL_DIR%ArmaReforgerServer\saves" -addonsDir "addons" -logLevel 3 -logStats

echo.
echo ========================================
echo         SERVER STOPPED
echo ========================================
echo Exit code: %ERRORLEVEL%
echo.
echo Check the output above for error details.
echo Log files are saved in: saves\logs\
echo.
pause
