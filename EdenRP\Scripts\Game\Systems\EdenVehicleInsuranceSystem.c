//! Eden Vehicle Insurance System - Handles vehicle insurance, claims, and coverage
//! Converted from original insurance systems

class EdenVehicleInsuranceSystem
{
    protected ref map<string, ref EdenInsurancePolicy> m_InsurancePolicies;
    protected ref array<ref EdenInsuranceClaim> m_InsuranceClaims;
    protected ref map<string, ref EdenInsuranceCompany> m_InsuranceCompanies;
    
    // Insurance configuration
    protected int m_BasicInsurancePrice;
    protected int m_FullInsurancePrice;
    protected float m_BasicCoveragePercent;
    protected float m_FullCoveragePercent;
    protected int m_ClaimCooldownTime;
    protected int m_MaxClaimsPerMonth;
    protected bool m_InsuranceSystemEnabled;
    
    void EdenVehicleInsuranceSystem()
    {
        m_InsurancePolicies = new map<string, ref EdenInsurancePolicy>();
        m_InsuranceClaims = new array<ref EdenInsuranceClaim>();
        m_InsuranceCompanies = new map<string, ref EdenInsuranceCompany>();
        
        m_BasicInsurancePrice = 15000;
        m_FullInsurancePrice = 35000;
        m_BasicCoveragePercent = 0.75;
        m_FullCoveragePercent = 1.0;
        m_ClaimCooldownTime = 3600; // 1 hour
        m_MaxClaimsPerMonth = 3;
        m_InsuranceSystemEnabled = true;
        
        InitializeInsuranceSystem();
    }
    
    //! Initialize insurance system
    void Initialize()
    {
        Print("[EdenVehicleInsuranceSystem] Initializing vehicle insurance system...");
        
        SetupInsuranceCompanies();
        LoadInsurancePolicies();
        LoadInsuranceClaims();
        
        // Set up periodic processing
        GetGame().GetCallqueue().CallLater(ProcessInsuranceClaims, 300000, true); // 5 minutes
        GetGame().GetCallqueue().CallLater(ProcessPolicyRenewals, 3600000, true); // 1 hour
        
        Print("[EdenVehicleInsuranceSystem] Vehicle insurance system initialized");
    }
    
    //! Initialize insurance system configuration
    protected void InitializeInsuranceSystem()
    {
        Print("[EdenVehicleInsuranceSystem] Insurance system configuration initialized");
    }
    
    //! Setup insurance companies
    protected void SetupInsuranceCompanies()
    {
        // Altis Insurance Company
        SetupInsuranceCompany("altis_insurance", "Altis Insurance Co.", 0.95, true);
        
        // Kavala Mutual Insurance
        SetupInsuranceCompany("kavala_mutual", "Kavala Mutual Insurance", 0.90, true);
        
        // Athira Insurance Group
        SetupInsuranceCompany("athira_group", "Athira Insurance Group", 0.85, true);
        
        Print(string.Format("[EdenVehicleInsuranceSystem] Set up %1 insurance companies", m_InsuranceCompanies.Count()));
    }
    
    //! Setup individual insurance company
    protected void SetupInsuranceCompany(string companyId, string companyName, float payoutRate, bool isActive)
    {
        EdenInsuranceCompany company = new EdenInsuranceCompany();
        company.SetCompanyId(companyId);
        company.SetCompanyName(companyName);
        company.SetPayoutRate(payoutRate);
        company.SetIsActive(isActive);
        company.SetClaimsProcessed(0);
        company.SetTotalPayouts(0);
        
        m_InsuranceCompanies.Set(companyId, company);
    }
    
    //! Purchase vehicle insurance
    bool PurchaseInsurance(string playerId, string vehicleId, int insuranceLevel, string companyId = "altis_insurance")
    {
        if (playerId == "" || vehicleId == "" || insuranceLevel < 1 || insuranceLevel > 2)
            return false;
            
        if (!m_InsuranceSystemEnabled)
            return false;
            
        // Check if company exists
        if (!m_InsuranceCompanies.Contains(companyId))
            return false;
            
        EdenInsuranceCompany company = m_InsuranceCompanies.Get(companyId);
        if (!company.IsActive())
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Calculate insurance price
        int insurancePrice = (insuranceLevel == 1) ? m_BasicInsurancePrice : m_FullInsurancePrice;
        
        // Check if player has enough money
        if (insurancePrice > playerComp.GetBankAccount())
            return false;
            
        // Get vehicle data
        EdenVehicleData vehicleData = GetVehicleData(vehicleId);
        if (!vehicleData)
            return false;
            
        // Check ownership
        if (vehicleData.GetOwnerId() != playerId)
            return false;
            
        // Process payment
        playerComp.RemoveBankMoney(insurancePrice);
        
        // Create insurance policy
        string policyId = GeneratePolicyId();
        EdenInsurancePolicy policy = new EdenInsurancePolicy();
        policy.SetPolicyId(policyId);
        policy.SetPlayerId(playerId);
        policy.SetVehicleId(vehicleId);
        policy.SetInsuranceLevel(insuranceLevel);
        policy.SetCompanyId(companyId);
        policy.SetPurchaseTime(GetGame().GetWorld().GetWorldTime());
        policy.SetExpirationTime(GetGame().GetWorld().GetWorldTime() + 2592000); // 30 days
        policy.SetIsActive(true);
        policy.SetClaimsUsed(0);
        policy.SetPremiumPaid(insurancePrice);
        
        // Store policy
        m_InsurancePolicies.Set(policyId, policy);
        
        // Update vehicle data
        vehicleData.SetInsuranceLevel(insuranceLevel);
        UpdateVehicleInDatabase(vehicleData);
        
        // Save policy to database
        SavePolicyToDatabase(policy);
        
        Print(string.Format("[EdenVehicleInsuranceSystem] %1 purchased level %2 insurance for vehicle %3", playerId, insuranceLevel, vehicleId));
        return true;
    }
    
    //! File insurance claim
    bool FileInsuranceClaim(string playerId, string vehicleId, string claimReason, int damageAmount = 0)
    {
        if (playerId == "" || vehicleId == "" || claimReason == "")
            return false;
            
        // Get insurance policy
        EdenInsurancePolicy policy = GetVehicleInsurancePolicy(vehicleId);
        if (!policy || !policy.IsActive())
            return false;
            
        // Check ownership
        if (policy.GetPlayerId() != playerId)
            return false;
            
        // Check if policy is expired
        if (policy.GetExpirationTime() < GetGame().GetWorld().GetWorldTime())
            return false;
            
        // Check claim cooldown
        if (HasRecentClaim(playerId, vehicleId))
            return false;
            
        // Check monthly claim limit
        if (GetMonthlyClaimCount(playerId) >= m_MaxClaimsPerMonth)
            return false;
            
        // Get vehicle data
        EdenVehicleData vehicleData = GetVehicleData(vehicleId);
        if (!vehicleData)
            return false;
            
        // Calculate payout amount
        int vehicleValue = GetVehicleValue(vehicleData.GetVehicleClass());
        float coveragePercent = (policy.GetInsuranceLevel() == 1) ? m_BasicCoveragePercent : m_FullCoveragePercent;
        int payoutAmount = Math.Floor(vehicleValue * coveragePercent);
        
        // Apply company payout rate
        EdenInsuranceCompany company = m_InsuranceCompanies.Get(policy.GetCompanyId());
        if (company)
        {
            payoutAmount = Math.Floor(payoutAmount * company.GetPayoutRate());
        }
        
        // Create insurance claim
        string claimId = GenerateClaimId();
        EdenInsuranceClaim claim = new EdenInsuranceClaim();
        claim.SetClaimId(claimId);
        claim.SetPolicyId(policy.GetPolicyId());
        claim.SetPlayerId(playerId);
        claim.SetVehicleId(vehicleId);
        claim.SetClaimReason(claimReason);
        claim.SetDamageAmount(damageAmount);
        claim.SetPayoutAmount(payoutAmount);
        claim.SetClaimTime(GetGame().GetWorld().GetWorldTime());
        claim.SetClaimStatus("Pending");
        claim.SetIsProcessed(false);
        
        // Store claim
        m_InsuranceClaims.Insert(claim);
        
        // Update policy
        policy.SetClaimsUsed(policy.GetClaimsUsed() + 1);
        UpdatePolicyInDatabase(policy);
        
        // Save claim to database
        SaveClaimToDatabase(claim);
        
        Print(string.Format("[EdenVehicleInsuranceSystem] %1 filed insurance claim for vehicle %2, payout: $%3", playerId, vehicleId, payoutAmount));
        return true;
    }
    
    //! Process insurance claim
    bool ProcessInsuranceClaim(string claimId, bool approved)
    {
        EdenInsuranceClaim claim = GetInsuranceClaim(claimId);
        if (!claim || claim.IsProcessed())
            return false;
            
        // Update claim status
        claim.SetClaimStatus(approved ? "Approved" : "Denied");
        claim.SetIsProcessed(true);
        claim.SetProcessTime(GetGame().GetWorld().GetWorldTime());
        
        if (approved)
        {
            // Get player entity and component
            IEntity playerEntity = GetPlayerEntity(claim.GetPlayerId());
            if (playerEntity)
            {
                EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
                if (playerComp)
                {
                    // Pay out claim
                    playerComp.AddBankMoney(claim.GetPayoutAmount());
                    
                    // Update company statistics
                    EdenInsurancePolicy policy = GetInsurancePolicy(claim.GetPolicyId());
                    if (policy)
                    {
                        EdenInsuranceCompany company = m_InsuranceCompanies.Get(policy.GetCompanyId());
                        if (company)
                        {
                            company.SetClaimsProcessed(company.GetClaimsProcessed() + 1);
                            company.SetTotalPayouts(company.GetTotalPayouts() + claim.GetPayoutAmount());
                        }
                    }
                    
                    // Handle vehicle restoration for full coverage
                    if (policy && policy.GetInsuranceLevel() == 2)
                    {
                        RestoreVehicle(claim.GetVehicleId());
                    }
                }
            }
        }
        
        // Update claim in database
        UpdateClaimInDatabase(claim);
        
        Print(string.Format("[EdenVehicleInsuranceSystem] Insurance claim %1 %2", claimId, approved ? "approved" : "denied"));
        return true;
    }
    
    //! Restore vehicle (full coverage)
    protected void RestoreVehicle(string vehicleId)
    {
        EdenVehicleData vehicleData = GetVehicleData(vehicleId);
        if (!vehicleData)
            return;
            
        // Reset vehicle to garage
        vehicleData.SetIsAlive(true);
        vehicleData.SetIsActive(false);
        vehicleData.SetIsImpounded(false);
        
        // Reset modifications and inventory
        vehicleData.SetModifications("[0,0,0,0,0,0,0,0]");
        vehicleData.SetInventory("[]");
        vehicleData.SetColor("[\"Default\",0]");
        
        // Update database
        UpdateVehicleInDatabase(vehicleData);
        
        Print(string.Format("[EdenVehicleInsuranceSystem] Vehicle %1 restored to garage", vehicleId));
    }
    
    //! Get vehicle insurance policy
    EdenInsurancePolicy GetVehicleInsurancePolicy(string vehicleId)
    {
        foreach (string policyId, EdenInsurancePolicy policy : m_InsurancePolicies)
        {
            if (policy.GetVehicleId() == vehicleId && policy.IsActive())
                return policy;
        }
        return null;
    }
    
    //! Get player insurance policies
    array<ref EdenInsurancePolicy> GetPlayerInsurancePolicies(string playerId)
    {
        array<ref EdenInsurancePolicy> playerPolicies = {};
        
        foreach (string policyId, EdenInsurancePolicy policy : m_InsurancePolicies)
        {
            if (policy.GetPlayerId() == playerId && policy.IsActive())
            {
                playerPolicies.Insert(policy);
            }
        }
        
        return playerPolicies;
    }
    
    //! Get player insurance claims
    array<ref EdenInsuranceClaim> GetPlayerInsuranceClaims(string playerId)
    {
        array<ref EdenInsuranceClaim> playerClaims = {};
        
        foreach (EdenInsuranceClaim claim : m_InsuranceClaims)
        {
            if (claim.GetPlayerId() == playerId)
            {
                playerClaims.Insert(claim);
            }
        }
        
        return playerClaims;
    }
    
    //! Check if player has recent claim
    protected bool HasRecentClaim(string playerId, string vehicleId)
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        foreach (EdenInsuranceClaim claim : m_InsuranceClaims)
        {
            if (claim.GetPlayerId() == playerId && claim.GetVehicleId() == vehicleId)
            {
                int timeSinceClaim = currentTime - claim.GetClaimTime();
                if (timeSinceClaim < m_ClaimCooldownTime)
                    return true;
            }
        }
        
        return false;
    }
    
    //! Get monthly claim count
    protected int GetMonthlyClaimCount(string playerId)
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int monthStart = currentTime - 2592000; // 30 days
        int claimCount = 0;
        
        foreach (EdenInsuranceClaim claim : m_InsuranceClaims)
        {
            if (claim.GetPlayerId() == playerId && claim.GetClaimTime() > monthStart)
            {
                claimCount++;
            }
        }
        
        return claimCount;
    }
    
    //! Process insurance claims
    protected void ProcessInsuranceClaims()
    {
        int processedCount = 0;
        
        foreach (EdenInsuranceClaim claim : m_InsuranceClaims)
        {
            if (!claim.IsProcessed() && claim.GetClaimStatus() == "Pending")
            {
                // Auto-approve claims after 24 hours
                int claimAge = GetGame().GetWorld().GetWorldTime() - claim.GetClaimTime();
                if (claimAge > 86400) // 24 hours
                {
                    ProcessInsuranceClaim(claim.GetClaimId(), true);
                    processedCount++;
                }
            }
        }
        
        if (processedCount > 0)
        {
            Print(string.Format("[EdenVehicleInsuranceSystem] Auto-processed %1 insurance claims", processedCount));
        }
    }
    
    //! Process policy renewals
    protected void ProcessPolicyRenewals()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int expiredCount = 0;
        
        foreach (string policyId, EdenInsurancePolicy policy : m_InsurancePolicies)
        {
            if (policy.IsActive() && policy.GetExpirationTime() < currentTime)
            {
                policy.SetIsActive(false);
                UpdatePolicyInDatabase(policy);
                
                // Update vehicle insurance level
                EdenVehicleData vehicleData = GetVehicleData(policy.GetVehicleId());
                if (vehicleData)
                {
                    vehicleData.SetInsuranceLevel(0);
                    UpdateVehicleInDatabase(vehicleData);
                }
                
                expiredCount++;
            }
        }
        
        if (expiredCount > 0)
        {
            Print(string.Format("[EdenVehicleInsuranceSystem] Expired %1 insurance policies", expiredCount));
        }
    }
    
    //! Helper methods
    protected string GeneratePolicyId()
    {
        return string.Format("POL_%1_%2", GetGame().GetWorld().GetWorldTime(), Math.RandomInt(1000, 9999));
    }
    
    protected string GenerateClaimId()
    {
        return string.Format("CLM_%1_%2", GetGame().GetWorld().GetWorldTime(), Math.RandomInt(1000, 9999));
    }
    
    protected EdenInsuranceClaim GetInsuranceClaim(string claimId)
    {
        foreach (EdenInsuranceClaim claim : m_InsuranceClaims)
        {
            if (claim.GetClaimId() == claimId)
                return claim;
        }
        return null;
    }
    
    protected EdenInsurancePolicy GetInsurancePolicy(string policyId)
    {
        if (m_InsurancePolicies.Contains(policyId))
            return m_InsurancePolicies.Get(policyId);
        return null;
    }
    
    //! Configuration methods
    void SetInsurancePrices(int basicPrice, int fullPrice)
    {
        m_BasicInsurancePrice = basicPrice;
        m_FullInsurancePrice = fullPrice;
    }
    
    void SetCoveragePercents(float basicPercent, float fullPercent)
    {
        m_BasicCoveragePercent = basicPercent;
        m_FullCoveragePercent = fullPercent;
    }
    
    void SetClaimSettings(int cooldownTime, int maxClaimsPerMonth)
    {
        m_ClaimCooldownTime = cooldownTime;
        m_MaxClaimsPerMonth = maxClaimsPerMonth;
    }
    
    void SetInsuranceSystemEnabled(bool enabled) { m_InsuranceSystemEnabled = enabled; }
    bool IsInsuranceSystemEnabled() { return m_InsuranceSystemEnabled; }
    
    //! Get insurance companies
    array<ref EdenInsuranceCompany> GetInsuranceCompanies()
    {
        array<ref EdenInsuranceCompany> companies = {};
        foreach (string companyId, EdenInsuranceCompany company : m_InsuranceCompanies)
        {
            companies.Insert(company);
        }
        return companies;
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadInsurancePolicies() { }
    protected void LoadInsuranceClaims() { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    protected EdenVehicleData GetVehicleData(string vehicleId) { return null; }
    protected int GetVehicleValue(string vehicleClass) { return 10000; }
    protected void UpdateVehicleInDatabase(EdenVehicleData vehicleData) { }
    protected void SavePolicyToDatabase(EdenInsurancePolicy policy) { }
    protected void UpdatePolicyInDatabase(EdenInsurancePolicy policy) { }
    protected void SaveClaimToDatabase(EdenInsuranceClaim claim) { }
    protected void UpdateClaimInDatabase(EdenInsuranceClaim claim) { }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenVehicleInsuranceSystem] Cleaning up vehicle insurance system...");
        
        m_InsurancePolicies.Clear();
        m_InsuranceClaims.Clear();
        m_InsuranceCompanies.Clear();
    }
}
