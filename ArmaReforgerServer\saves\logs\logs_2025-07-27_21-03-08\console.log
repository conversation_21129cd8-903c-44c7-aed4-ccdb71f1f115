---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-03-08\console.log started at 2025-07-27 21:03:08 (2025-07-28 01:03:08 UTC)

21:03:08.009 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-03-08' to filesystem under name logs
21:03:08.009 ENGINE       : FileSystem: Adding relative directory 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile' to filesystem under name profile
21:03:08.010 ENGINE       : Initializing engine, version 184702
21:03:08.010 ENGINE       : CLI Params: -config ..\..\..\server.json -profile ..\..\..\saves -addonsDir addons -logLevel 3 
21:03:08.014 ENGINE       : Addon dirs:
21:03:08.014  ENGINE       : dir: 'addons'
21:03:08.014  ENGINE       : dir: './addons'
21:03:08.015  ENGINE       : dir: 'C:/Users/<USER>/OneDrive/Desktop/Arma Reforger Test/ArmaReforgerServer/saves/addons'
21:03:08.015 ENGINE       : Available addons:
21:03:08.015  ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C' (packed)
21:03:08.015  ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859' (packed)
21:03:08.063 ENGINE       : FileSystem: Adding package './addons/data/' (pak count: 4) to filesystem under name ArmaReforger
21:03:08.065 ENGINE       : FileSystem: Adding package './addons/core/' (pak count: 1) to filesystem under name core
21:03:08.065 RESOURCES    : ResourceDB: loading cache (id=0 name=ArmaReforger path=./addons/data/resourceDatabase.rdb)
21:03:08.209 RESOURCES    : ResourceDB: loading cache (id=1 name=core path=./addons/core/resourceDatabase.rdb)
21:03:08.211 RESOURCES    : ResourceDB: loading cache (id=2 name=profile path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\profile/resourceDatabase.rdb)
21:03:08.211 RESOURCES    : ResourceDB: loading cache (id=3 name=logs path=C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-03-08/resourceDatabase.rdb)
21:03:08.320 ENGINE       : GameProject load
21:03:08.320  ENGINE       : Loaded addons:
21:03:08.321   ENGINE       : gproj: './addons/core/core.gproj' guid: '5614BBCCBB55ED1C'
21:03:08.321   ENGINE       : gproj: './addons/data/ArmaReforger.gproj' guid: '58D0FB3206B6F859'
21:03:08.322 ENGINE       : Loading custom engine user config '$profile:.save/settings/ReforgerEngineSettings.conf'
21:03:08.323 INIT         : GameProject engine user settings load @"$profile:.save/settings/ReforgerEngineSettings.conf"
21:03:08.323  ENGINE       : Engine user settings config loaded
21:03:08.323 PROFILING    : Settings changed took: 0.001500 ms
21:03:08.323 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
21:03:08.323 RENDER       : Basic screen setup:
21:03:08.323 RENDER       :   Back buffer width x height: 0x0
21:03:08.323 RENDER       :   Back buffer format	       : TEXFMT_UNKNOWN (0)
21:03:08.324 RENDER       :   MSAA                      : none
21:03:08.324 RENDER       : GPU_DUMMY (0xFFFF) (Enfusion dummy render) with 1 GPU detected, VRAM 128MB (fast 128MB), shared 128MB, driver version Unknown version
21:03:08.324 RENDER       : Detected output devices:
21:03:08.324 RENDER       :  * 1 device/s connected to Dummy:
21:03:08.324 RENDER       :    Dummy output (Dummy device for our dummy render.), 1920x1080 (offset 0x0), 60Hz
21:03:08.324 RENDER       :   Concurrent frames limit 2
21:03:08.324 ENGINE       : Job system settings: coreCount=16, shortJobContextCount=16, shortJobThreadCount=15, longJobThreadCount=7
21:03:08.383 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
21:03:08.387 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
21:03:08.387 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
21:03:08.387 PROFILING    : Settings changed took: 0.020700 ms
21:03:08.391 ENGINE       : Initializing inputs.
21:03:08.396 NETWORK      : Initializing networking.
21:03:08.397 SCRIPT       : SCRIPT       : Initializing scripts
21:03:08.397  SCRIPT       : SCRIPT       : ScriptProjectManager init
21:03:08.398  PROFILING    : ScriptProjectManager init took: 0.024700 ms
21:03:08.416 PROFILING    : Initializing scripts took: 18.647000 ms
21:03:08.416 ENGINE       : Enfusion engine successfully created.
21:03:08.423 GUI          : Using default language (en_us)
21:03:08.423 GUI          : Loading 'en_us' localization file.
21:03:08.423 INIT         : INIT         : Loading StringTable
21:03:08.448 PROFILING    : Loading StringTable took: 24.488000 ms
21:03:08.448 SCRIPT       : SCRIPT       : Compiling GameLib scripts
21:03:08.471  SCRIPT       : Module: GameLib; loaded 354x files; 541x classes; used 586/2097 kB (27%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE"; CRC32: 57d31b2f
21:03:08.474 PROFILING    : Compiling GameLib scripts took: 26.319300 ms
21:03:08.474 SCRIPT       : SCRIPT       : Compiling Game scripts
21:03:08.946 SCRIPT       : Compiling Game scripts
21:03:08.946  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
21:03:08.980  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
21:03:09.184  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:03:09.184  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:03:09.184  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:03:09.200  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
21:03:09.234  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
21:03:09.317  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
21:03:09.375  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:03:09.416  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
21:03:09.416  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
21:03:09.483  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
21:03:09.483  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:03:09.483  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:03:09.484  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:03:09.484  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:03:09.484  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:03:09.524  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:03:09.524  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:03:09.525  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:03:09.525  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:03:09.525  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:03:09.525  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:03:09.537  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:03:09.549  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:03:09.549  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:03:09.549  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:03:09.574  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
21:03:09.575  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
21:03:09.578  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:03:09.578  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:03:09.579  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:03:09.580  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
21:03:09.580  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:03:09.628  SCRIPT       : Module: Game; loaded 4725x files; 9511x classes; used 25161/41943 kB (59%) of static memory; defines: "ENF_WB,PLATFORM_WINDOWS,ENF_ENABLE_VHC_OVERRIDE,ENF_ENABLE_RPLCOMPONENT_IN_GAMECODE,ENABLE_BASE_DESTRUCTION,BUDGET_OPTIMIZATION_CHECKS,DEBUG,RESPAWN_TIMER_COMPONENT_DEBUG,DEBUG_CONFIGURABLE_DIALOGS,DISABLE_WEAPON_SWITCHING,SCRIPTED_AIM_MODIFIER_DEBUG"; CRC32: 4ae5f8b1
21:03:10.341 PROFILING    : Compiling Game scripts took: 1865.645600 ms
21:03:10.342 INIT         : Creating game instance(ArmaReforgerScripted), version ******** built 2025-07-03 21:02:31 UTC.
21:03:10.357 BACKEND      : Loading dedicated server config.
21:03:10.357  BACKEND      : Server config loaded.
21:03:10.358  BACKEND      : JSON Schema Validation:
21:03:10.358   BACKEND      : JSON is Valid
21:03:10.364 ENGINE       : Game successfully created.
21:03:10.408 PLATFORM     : Save data from container 'settings' have been loaded
21:03:10.508 PLATFORM     : Save data from container 'sessions' have been loaded
21:03:10.510 PLATFORM     : GameProject user settings load from profile @"ReforgerGameSettings.conf"
21:03:10.510  ENGINE       : Game user settings config loaded
21:03:10.510 NETWORK      : Starting dedicated server using command line args.
21:03:10.608 BACKEND      : Required addons are ready to use.
21:03:10.906 PLATFORM     : Save data from container 'settings' have been loaded
21:03:11.006 PLATFORM     : Save data from container 'sessions' have been loaded
21:03:11.006 PLATFORM     : GameProject user settings load from profile @"ReforgerGameSettings.conf"
21:03:11.006  ENGINE       : Game user settings config loaded
21:03:11.006 WORLD        : WORLD        : Entities load @"{9DF143A76F5C6460}worlds/MP/CTI_Campaign_Eden.ent"
21:03:11.735 WORLD        : Entities load @"{9DF143A76F5C6460}worlds/MP/CTI_Campaign_Eden.ent"
21:03:11.735  WORLD        : Subscene load @"{853E92315D1D9EFE}worlds/Eden/Eden.ent"
21:03:11.735   RESOURCES    : GetResourceObject @"{C8FAB83670BCF7C3}Prefabs/Structures/Cultural/Calvaries/CalvaryLarge_01.et"
21:03:11.735    WORLD        : Entity prefab load @"{C8FAB83670BCF7C3}Prefabs/Structures/Cultural/Calvaries/CalvaryLarge_01.et"
21:03:11.735     RESOURCES (E): Wrong GUID/name for resource @"{45ECF6B1797D3CD9}system/wbdata/PreviewWindow/TransparentMat.emat" in property "AssignedMaterial"
21:03:11.953 PROFILING    : Entities load took: 946.093600 ms
21:03:11.953 WORLD        : WORLD        : Game::LoadEntities
21:03:11.967  NETWORK      : Enabled lag compensation, 29 frames with 100 FPS and 280 ms rewind limits.
21:03:11.967  WORLD        : WORLD        : Preload
21:03:12.548  PROFILING    : Preload took: 580.316200 ms
21:03:12.548  WORLD        : WORLD        : Wait for preloading data
21:03:12.549 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine_LOD1.ptc"
21:03:12.550  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:03:12.550 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine.ptc"
21:03:12.550  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:03:12.550 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine_LOD2.ptc"
21:03:12.550  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:03:13.612  PROFILING    : Wait for preloading data took: 1064.386600 ms
21:03:13.612  WORLD        : WORLD        : CreateEntities
21:03:13.612   WORLD        : WORLD        : CreateEntities
21:03:13.612    WORLD        : WORLD        : NotifyBeforeEntitiesCreated
21:03:13.613     RESOURCES    : Loaded mission headers from 2 addon(s)!
21:03:13.613    PROFILING    : NotifyBeforeEntitiesCreated took: 1.483900 ms
21:03:13.643 WORLD        : Game::LoadEntities
21:03:13.643  WORLD        : CreateEntities
21:03:13.643   WORLD        : CreateEntities
21:03:13.643    ENTITY       : Create entity @"ENTITY:1" ('world', GenericWorldEntity) at <0.000000 0.000000 0.000000>
21:03:13.643     RESOURCES (E):   Failed to decompress memory image data. Data is not DXT.
21:03:13.643     WORLD     (E): Shore map CPU data creation failed - decompression of DF near atlas texture failed.
21:03:13.644     WORLD     (E): Loading of shore map failed - creation of CPU data failed
21:03:13.644     WORLD     (E): Loading of shore map failed - post load checks failed
21:03:13.645    ENTITY       : Create entity @"ENTITY:2" ('Eden', GenericTerrainEntity) at <0.000000 0.000000 0.000000>
21:03:13.645     PROFILING    : PROFILING    : Terrain load
21:03:16.136     PROFILING    : Terrain load took: 2490.387300 ms
21:03:19.224    ENTITY       : Create entity @"ENTITY:610688" ('StaticModelEntity','Assets/Structures/Infrastructure/Towers/TransmitterTower_01/TransmitterTower_01_small.xob') at <1931.796021 81.777000 5138.725098> @"{6A004A8F0571D456}Prefabs/Structures/Infrastructure/Towers/TransmitterTower_01/TransmitterTower_01_small.et"
21:03:19.225     DEFAULT   (W): World doesn't contain RadioManagerEntity to support any BaseRadioComponent.
21:03:21.553    ENTITY       : Create entity @"ENTITY:1055324" ('GenericEntity') at <6225.922852 107.562500 5118.536133>
21:03:21.554     ENTITY    (E): Door action not set for @"ENTITY:1055324" ('GenericEntity') at <6225.922852 107.562500 5118.536133>
21:03:21.554    ENTITY       : Create entity @"ENTITY:1055357" ('GenericEntity') at <5252.018066 38.411251 11397.515625>
21:03:21.554     ENTITY    (E): Door action not set for @"ENTITY:1055357" ('GenericEntity') at <5252.018066 38.411251 11397.515625>
21:03:21.554    ENTITY       : Create entity @"ENTITY:1055390" ('GenericEntity') at <3274.975098 114.904022 5189.225098>
21:03:21.554     ENTITY    (E): Door action not set for @"ENTITY:1055390" ('GenericEntity') at <3274.975098 114.904022 5189.225098>
21:03:21.555    ENTITY       : Create entity @"ENTITY:1055423" ('GenericEntity') at <3741.502930 80.400497 6630.824219>
21:03:21.555     ENTITY    (E): Door action not set for @"ENTITY:1055423" ('GenericEntity') at <3741.502930 80.400497 6630.824219>
21:03:21.555    ENTITY       : Create entity @"ENTITY:1055458" ('GenericEntity') at <5642.888184 89.763420 5352.812012>
21:03:21.555     ENTITY    (E): Door action not set for @"ENTITY:1055458" ('GenericEntity') at <5642.888184 89.763420 5352.812012>
21:03:21.556    ENTITY       : Create entity @"ENTITY:1055491" ('GenericEntity') at <1365.465942 41.945751 5944.875000>
21:03:21.556     ENTITY    (E): Door action not set for @"ENTITY:1055491" ('GenericEntity') at <1365.465942 41.945751 5944.875000>
21:03:21.556    ENTITY       : Create entity @"ENTITY:1055524" ('GenericEntity') at <4556.948242 20.443750 9608.875000>
21:03:21.556     ENTITY    (E): Door action not set for @"ENTITY:1055524" ('GenericEntity') at <4556.948242 20.443750 9608.875000>
21:03:21.557    ENTITY       : Create entity @"ENTITY:1055557" ('GenericEntity') at <4612.849121 158.267273 7012.264160>
21:03:21.557     ENTITY    (E): Door action not set for @"ENTITY:1055557" ('GenericEntity') at <4612.849121 158.267273 7012.264160>
21:03:21.557    ENTITY       : Create entity @"ENTITY:1055590" ('GenericEntity') at <5782.269043 28.741789 10179.600586>
21:03:21.557     ENTITY    (E): Door action not set for @"ENTITY:1055590" ('GenericEntity') at <5782.269043 28.741789 10179.600586>
21:03:21.558    ENTITY       : Create entity @"ENTITY:1055623" ('GenericEntity') at <5571.204102 91.851639 5976.666992>
21:03:21.558     ENTITY    (E): Door action not set for @"ENTITY:1055623" ('GenericEntity') at <5571.204102 91.851639 5976.666992>
21:03:21.558    ENTITY       : Create entity @"ENTITY:1055656" ('GenericEntity') at <7223.090820 136.826721 2315.154053>
21:03:21.558     ENTITY    (E): Door action not set for @"ENTITY:1055656" ('GenericEntity') at <7223.090820 136.826721 2315.154053>
21:03:21.559    ENTITY       : Create entity @"ENTITY:1055689" ('GenericEntity') at <7223.857910 137.444275 2372.346924>
21:03:21.559     ENTITY    (E): Door action not set for @"ENTITY:1055689" ('GenericEntity') at <7223.857910 137.444275 2372.346924>
21:03:21.559    ENTITY       : Create entity @"ENTITY:1055722" ('GenericEntity') at <4522.501953 15.952277 10653.205078>
21:03:21.559     ENTITY    (E): Door action not set for @"ENTITY:1055722" ('GenericEntity') at <4522.501953 15.952277 10653.205078>
21:03:21.559    ENTITY       : Create entity @"ENTITY:1055755" ('GenericEntity') at <9417.439453 45.156071 1688.795044>
21:03:21.559     ENTITY    (E): Door action not set for @"ENTITY:1055755" ('GenericEntity') at <9417.439453 45.156071 1688.795044>
21:03:21.718    ENTITY       : Create entity @"ENTITY:1104299" ('StaticModelEntity') at <8831.291016 95.934265 2673.487061>
21:03:21.718     MATERIAL     : Material load @"{45ECF6B1797D3CD9}system/wbdata/PreviewWindow/TransparentMat.emat"
21:03:21.718      MATERIAL  (E): Material file not found
21:03:21.718     MATERIAL  (E): Object @"Assets/Structures/Cultural/Calvaries/CalvaryLarge_01/CalvaryLarge_01.xob" - cannot load material @"system/wbdata/PreviewWindow/TransparentMat.emat", creating default one (class MatPBRBasic)
21:03:24.946    ENTITY       : Create entity @"ENTITY:2305843009213698652" ('GenericEntity','Assets/Props/Military/Antennas/Antenna_R161_01/Antenna_R161_01.xob') at <9354.231445 204.427994 1162.372070>
21:03:24.946     DEFAULT   (W): BaseRadioComponent does not have any transceiver. (Entity: @"ENTITY:2305843009213698652" ('GenericEntity','Assets/Props/Military/Antennas/Antenna_R161_01/Antenna_R161_01.xob') at <9354.231445 204.427994 1162.372070>). Only printing this message once. There're possibly more nodes with this requirement.
21:03:25.545    WORLD        : WORLD        : NotifyBeforeEntitiesInitialized
21:03:25.545    PROFILING    : NotifyBeforeEntitiesInitialized took: 0.028600 ms
21:03:25.546    WORLD        : InitEntities 'world'
21:03:25.546     ENTITY       : Init entity @"ENTITY:3" ('SCR_MapEntity1', SCR_MapEntity) at <6400.000000 0.000000 6400.000000> @"{E1B88C66BF6CA1F9}Prefabs/World/Game/MapEntity_Everon.et"
21:03:25.546      SCRIPT    (W): SCR_MapEntity: Cannot get the size from terrain. Using default.
21:03:27.518     ENTITY       : Init entity @"ENTITY:2305843009213695743" ('SCR_BaseTaskManager') at <20.724001 0.000000 7.936000> @"{17E3EF2CF455460F}Prefabs/MP/Campaign/CampaignTaskManager.et"
21:03:27.518      GUI          : WidgetManager: CrateWidgets @"{ACCF501DD69CAF7B}UI/layouts/Tasks/TaskList.layout"
21:03:27.518       SCRIPT    (E): No data found for keyboard:KC_ESCAPE !! Check 'chimeraMapping.conf' and add data if necessary! Provided Actioname: TasksClose.
21:03:27.877    WORLD        : WORLD        : NotifyEntitiesInitialized
21:03:27.877     TERRAIN      : TERRAIN      : Initializing road networks...
21:03:27.879     PROFILING    : Initializing road networks... took: 2.359700 ms
21:03:27.988     INIT         : INIT         : SoundWorldInit
21:03:28.029     PROFILING    : SoundWorldInit took: 41.366700 ms
21:03:28.030    PROFILING    : NotifyEntitiesInitialized took: 152.784600 ms
21:03:28.030   PROFILING    : CreateEntities took: 14417.755200 ms
21:03:28.030  PROFILING    : CreateEntities took: 14417.907200 ms
21:03:28.030  WORLD        : WORLD        : EOnActivate
21:03:28.070  PROFILING    : EOnActivate took: 39.266700 ms
21:03:28.070 PROFILING    : Game::LoadEntities took: 16117.470400 ms
21:03:28.070 PROFILING    : * LoadEntities: 16117.643400 ms
21:03:28.179 NETWORK      : RPL listen address not specified. Using default fallback.
21:03:28.238 NETWORK      : Starting RPL server, listening on address 0.0.0.0:2001, fastValidation=true
21:03:29.532 BACKEND      : Server registered with address: *************:2001
21:03:29.532 BACKEND      : Direct Join Code: 0546439475
21:03:29.532 BACKEND      : Ping Site: new_york
21:03:29.991 DEFAULT      : Entered online game state.
21:03:30.142 WORLD        : Frame start
21:03:30.142  NETWORK      : Projectiles will simulate using: EOnFrame
21:03:30.143  SCRIPT       : ScenarioFramework: Available tasks are empty, no new tasks will be generated.
21:03:30.157 WORLD        : UpdateEntities
21:03:30.157  WORLD        : Frame
21:03:30.157   SCRIPT       : SCR_BaseGameMode::OnGameStateChanged = GAME
21:03:42.206 RPL          : IReplication:: Finishing...
21:03:42.306 RPL          : rpl::Pip::ProcessNetToGame
21:03:42.306  RPL          : IReplication: Finished
21:03:42.308 RPL          : Pip::Destroy
21:03:46.448 SCRIPT    (E): Leaked 'Road' script instance (1x)!
21:03:46.449 ENGINE       : Game destroyed.
21:03:46.718 RPL          : Pip::Destroy
21:03:46.735 RESOURCES (E): ==== Resource leaks ====
21:03:46.735 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
