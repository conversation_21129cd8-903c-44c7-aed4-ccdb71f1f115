@echo off
title Eden RP Reforger Server Launcher
color 0A

REM Store the original directory
set "ORIGINAL_DIR=%~dp0"

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
    REM Change back to the script's directory
    cd /d "%ORIGINAL_DIR%"
) else (
    echo This script requires administrator privileges to create symlinks.
    echo Requesting elevation...
    powershell -Command "Start-Process '%~f0' -Verb RunAs -ArgumentList '-WorkingDirectory', '%~dp0'"
    exit /b
)

echo ========================================
echo      EDEN RP REFORGER SERVER
echo ========================================
echo.
echo This will start the Eden RP server
echo with all converted systems from the original
echo Arma 3 framework.
echo.
echo Server Features:
echo - Complete roleplay framework
echo - Police, Medical, and Civilian systems
echo - Dynamic economy and housing
echo - Gang territories and warfare
echo - Admin tools and progression system
echo.

REM Check if server files exist
if not exist "ArmaReforgerServer\steamapps\common\Arma Reforger Server\ArmaReforgerServer.exe" (
    echo ERROR: Arma Reforger Server not found!
    echo Please run FetchServerFiles.bat first to download the server.
    echo.
    pause
    exit /b 1
)

REM Check if EdenRP mod exists
if not exist "EdenRP\Scripts\Game\Core\EdenGameMode.c" (
    echo ERROR: EdenRP mod not found!
    echo Please ensure the EdenRP directory contains the converted scripts.
    echo.
    pause
    exit /b 1
)

echo All files found. Starting server...
echo.

REM Change to server directory
cd ArmaReforgerServer

REM Run the server startup script
call StartServer.bat

REM Return to original directory
cd ..

echo.
echo Server launcher finished.
pause
