---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_20-48-02\error.log started at 2025-07-27 20:48:02 (2025-07-28 00:48:02 UTC)

20:48:02.351 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
20:48:02.406 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
20:48:02.409 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
20:48:02.410 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
20:48:02.958 SCRIPT       : Compiling Game scripts
20:48:02.959  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
20:48:03.000  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
20:48:03.195  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:48:03.196  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:48:03.196  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
20:48:03.210  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
20:48:03.240  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
20:48:03.316  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
20:48:03.376  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:48:03.420  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
20:48:03.420  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
20:48:03.492  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
20:48:03.493  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:48:03.493  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:48:03.494  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:48:03.494  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:48:03.494  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
20:48:03.533  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:48:03.534  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:48:03.535  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:48:03.535  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:48:03.535  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:48:03.535  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
20:48:03.548  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:48:03.560  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:48:03.560  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:48:03.560  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:48:03.586  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
20:48:03.587  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
20:48:03.590  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:48:03.590  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:48:03.592  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
20:48:03.592  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
20:48:03.593  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
20:48:04.363 BACKEND      : Loading dedicated server config.
20:48:04.363  BACKEND      : JSON Schema Validation:
20:48:04.363   BACKEND      : additionalProperties error:
20:48:04.363    BACKEND   (E): Param "#/game/playerCountLimit" is not allowed and must be removed.
20:48:04.364    BACKEND   (E): Reference in schema: "#/properties/game"
20:48:04.364   BACKEND   (E): JSON is invalid!
20:48:04.364  BACKEND   (E): There are errors in server config!
20:48:04.364 BACKEND   (E): Unable to continue with a broken DS config! Shutdown!
20:48:04.364 ENGINE    (E): Error while initializing game.
20:48:04.528 ENGINE    (E): Unable to initialize the game
20:48:04.568 RESOURCES (E): ==== Resource leaks ====
20:48:04.568 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
