"Language" "schinese" 
"Tokens" 
{ 
"SteamBootstrapper_UpdatingSteam"				"正在更新 %appname%..."
"SteamBootstrapper_ElevationPrompt"			"%appname% 需要在启动前进行自我更新。"
"SteamBootstrapper_ReadOnlyInstallOSX"		"%appname% 无法向其安装目录写入数据。请确保您没有尝试从磁盘映像中运行。"
"SteamBootstrapper_RunFromDiskImageOSX"		"请在启动 %appname% 前其将拖动到您的“应用程序”文件夹中。"
"SteamBootstrapper_ReadOnlyInstall"			"%appname% 无法向其安装目录写入数据。"
"SteamBootstrapper_InstallDirNotASCII"			"%appname% 无法在以非英文字符文件夹命名的路径下运行。请将  %appname% 重新安装至默认文件夹。"
"SteamBootstrapper_InstallDirIncompatible"		"%appname% 在该 Windows 版本上无法从含有非 ASCII 字符的文件夹路径运行。请重新安装 %appname% 至默认文件夹，或将其安装于 Windows 7 及更高版本上。"
"SteamBootstrapper_InsufficientDiskSpace"		"%appname% 需要 %nMegaBytes%MB 可用磁盘空间用于更新。"
"SteamBootstrapper_FailedManifestLoad"		"无法载入安装包清单。"
"SteamBootstrapper_FailedManifestVerify"		"无法验证安装包清单。"
"SteamBootstrapper_FailedManifestOSType"		"操作系统版本与安装包清单操作系统类型不兼容"
"SteamBootstrapper_FailedManifestSave"		"无法保存安装包清单。"
"SteamBootstrapper_FailedPackageRead"			"无法读取安装包（%pkgName%）。"
"SteamBootstrapper_CorruptPackageFile"		"安装包文件（%pkgName%）已损坏。"
"SteamBootstrapper_FailedPackageUnzip"		"无法解压缩安装包（%pkgName%）。"
"SteamBootstrapper_UpdateApplyFail"			"无法应用更新，正在还原..."
"SteamBootstrapper_UpdateCleanup"				"正在清理..."
"SteamBootstrapper_UpdateCleanupFail"			"更新后无法清理，正在继续..."
"SteamBootstrapper_UpdateFinalizeFail"		"无法写入安装清单。"
"SteamBootstrapper_UpdateComplete"			"更新完成，正在启动 %appname%..."
"SteamBootstrapper_UpdateChecking"			"正在检查可用更新..."
"SteamBootstrapper_UpdatePrepare"				"正在准备更新..."
"SteamBootstrapper_UpdateDownload"			"正在下载更新..."
"SteamBootstrapper_UpdateDownloading"			"正在下载更新 (已下载 %bytes%，共 %size% KB)..."
"SteamBootstrapper_UpdateExtractingPackage"	"正在展开安装包..."
"SteamBootstrapper_UpdateInstalling"			"正在安装更新..."
"SteamBootstrapper_InstallVerify"				"正在验证安装..."
"SteamBootstrapper_DownloadComplete"			"下载完成。"
"SteamBootstrapper_PercentComplete"			"已完成 %percent%%%"
"SteamBootstrapper_NoNetwork"					"%appname% 需要在线进行更新。请确认您的网络连接正常，然后重试。"
"SteamBootstrapper_FailedMkdir"				"无法创建目录 %dirname%，发生错误 %errno%"
"SteamBootstrapper_NotWriteable"				"目录 %dirname% 不可写入"
"SteamBootstrapper_FailedDownloadHTTPError"	"下载失败：http 错误 %errno%"
"SteamBootstrapper_AlreadyRunningError"		"一个 %appname% 的实例已在此计算机上以其他用户帐户运行。"
"SteamBootstrapper_MoveContentDir_DestExists" "%appname% 已经将游戏内容保存位置由“~/Documents/Steam Content”更改为“~/Library/Application Support/Steam/SteamApps”。在原始位置保存的游戏文件，Steam 无法移动，因为新的保存位置上已经有文件存在。您可能需要手动移动这些文件，或者删除旧文件然后重新下载游戏。是否继续？"
"SteamBootstrapper_MoveContentDir_CopyError"  "%appname% 已经将游戏内容保存位置由“~/Documents/Steam Content”更改为“~/Library/Application Support/Steam/SteamApps”。在原始位置保存的游戏文件，Steam 无法移动。您可能需要手动移动这些文件，或者删除旧文件然后重新下载游戏。是否继续？"
"SteamBootstrapper_OK"				"确定"
"SteamBootstrapper_Cancel"			"取消"
"SteamBootstrapper_WindowTitle"			"%appname%"
"SteamBootstrapper_Error_Title"			"%appname% - 致命错误"
"SteamBootstrapper_Warning_Title"			"%appname% - 警告"
"SteamBootstrapper_OperatingSystemNoLongerSupported" "您当前的操作系统版本不再支持 %appname%。"
"SteamCmd_OperatingSystemNoLongerSupported" "您的操作系统版本不再支持 %appname%。\n您可以传入 -overrideminos 以覆盖此检查。\n%appname% 可能无法正确运行，因为将来可能会移除对此覆盖的支持。\n请更新至受支持的操作系统版本，以便继续使用。"
"SteamBootstrapper_OperatingSystemNoLongerSupportedOnBeta" "您的操作系统版本不再支持 %appname%。\n\n点击“确定”以退出所选择的 %appname% 测试版。"
"SteamBootstrapper_Starting"                  "正在开始…"
}
