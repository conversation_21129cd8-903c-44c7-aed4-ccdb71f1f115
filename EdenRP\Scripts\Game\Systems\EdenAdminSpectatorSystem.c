//! Eden Admin Spectator System
//! Handles admin spectator mode functionality
class EdenAdminSpectatorSystem
{
    protected ref EdenAdminManager m_AdminManager;
    protected ref map<string, ref EdenSpectatorData> m_SpectatorSessions;
    protected ref array<string> m_SpectatorTargets;
    
    void EdenAdminSpectatorSystem()
    {
        m_SpectatorSessions = new map<string, ref EdenSpectatorData>();
        m_SpectatorTargets = new array<string>();
    }
    
    void Initialize(EdenAdminManager adminManager)
    {
        m_AdminManager = adminManager;
        
        Print("[EdenAdminSpectatorSystem] Admin spectator system initialized");
    }
    
    //! Start spectating a player
    bool StartSpectating(string adminId, string targetPlayerId)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminSpectatorSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        // Check if target player exists
        if (!IsPlayerOnline(targetPlayerId))
        {
            Print("[EdenAdminSpectatorSystem] Target player not found: " + targetPlayerId);
            return false;
        }
        
        // Stop current spectating session if exists
        if (m_SpectatorSessions.Contains(adminId))
        {
            StopSpectating(adminId);
        }
        
        // Create new spectator session
        ref EdenSpectatorData spectatorData = new EdenSpectatorData();
        spectatorData.SetAdminId(adminId);
        spectatorData.SetTargetPlayerId(targetPlayerId);
        spectatorData.SetStartTime(GetGame().GetWorld().GetWorldTime());
        spectatorData.SetSpectatorMode("Player");
        spectatorData.SetIsActive(true);
        
        m_SpectatorSessions.Set(adminId, spectatorData);
        
        // Add to spectator targets list
        if (m_SpectatorTargets.Find(targetPlayerId) == -1)
            m_SpectatorTargets.Insert(targetPlayerId);
        
        // Enable spectator mode for admin
        EnableSpectatorMode(adminId, targetPlayerId);
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "StartSpectating", targetPlayerId, "Started spectating player");
        
        Print("[EdenAdminSpectatorSystem] Admin " + adminId + " started spectating " + targetPlayerId);
        return true;
    }
    
    //! Stop spectating
    bool StopSpectating(string adminId)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminSpectatorSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        if (!m_SpectatorSessions.Contains(adminId))
        {
            Print("[EdenAdminSpectatorSystem] Admin not currently spectating: " + adminId);
            return false;
        }
        
        ref EdenSpectatorData spectatorData = m_SpectatorSessions.Get(adminId);
        string targetPlayerId = spectatorData.GetTargetPlayerId();
        
        // Disable spectator mode
        DisableSpectatorMode(adminId);
        
        // Update spectator data
        spectatorData.SetEndTime(GetGame().GetWorld().GetWorldTime());
        spectatorData.SetIsActive(false);
        
        // Remove from active sessions
        m_SpectatorSessions.Remove(adminId);
        
        // Remove from targets list if no other admins spectating
        bool otherAdminsSpectating = false;
        for (int i = 0; i < m_SpectatorSessions.Count(); i++)
        {
            ref EdenSpectatorData otherSession = m_SpectatorSessions.GetElement(i);
            if (otherSession.GetTargetPlayerId() == targetPlayerId && otherSession.IsActive())
            {
                otherAdminsSpectating = true;
                break;
            }
        }
        
        if (!otherAdminsSpectating)
        {
            int targetIndex = m_SpectatorTargets.Find(targetPlayerId);
            if (targetIndex != -1)
                m_SpectatorTargets.RemoveOrdered(targetIndex);
        }
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "StopSpectating", targetPlayerId, "Stopped spectating player");
        
        Print("[EdenAdminSpectatorSystem] Admin " + adminId + " stopped spectating " + targetPlayerId);
        return true;
    }
    
    //! Switch spectator target
    bool SwitchSpectatorTarget(string adminId, string newTargetPlayerId)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminSpectatorSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        if (!m_SpectatorSessions.Contains(adminId))
        {
            Print("[EdenAdminSpectatorSystem] Admin not currently spectating: " + adminId);
            return false;
        }
        
        // Check if new target exists
        if (!IsPlayerOnline(newTargetPlayerId))
        {
            Print("[EdenAdminSpectatorSystem] New target player not found: " + newTargetPlayerId);
            return false;
        }
        
        ref EdenSpectatorData spectatorData = m_SpectatorSessions.Get(adminId);
        string oldTargetPlayerId = spectatorData.GetTargetPlayerId();
        
        // Update spectator data
        spectatorData.SetTargetPlayerId(newTargetPlayerId);
        spectatorData.SetLastSwitchTime(GetGame().GetWorld().GetWorldTime());
        
        // Switch spectator camera
        SwitchSpectatorCamera(adminId, newTargetPlayerId);
        
        // Update targets list
        if (m_SpectatorTargets.Find(newTargetPlayerId) == -1)
            m_SpectatorTargets.Insert(newTargetPlayerId);
        
        // Check if old target should be removed from list
        bool otherAdminsSpectatingOld = false;
        for (int i = 0; i < m_SpectatorSessions.Count(); i++)
        {
            ref EdenSpectatorData otherSession = m_SpectatorSessions.GetElement(i);
            if (otherSession.GetAdminId() != adminId && 
                otherSession.GetTargetPlayerId() == oldTargetPlayerId && 
                otherSession.IsActive())
            {
                otherAdminsSpectatingOld = true;
                break;
            }
        }
        
        if (!otherAdminsSpectatingOld)
        {
            int oldTargetIndex = m_SpectatorTargets.Find(oldTargetPlayerId);
            if (oldTargetIndex != -1)
                m_SpectatorTargets.RemoveOrdered(oldTargetIndex);
        }
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "SwitchSpectatorTarget", newTargetPlayerId, 
            "Switched spectator target from " + oldTargetPlayerId);
        
        Print("[EdenAdminSpectatorSystem] Admin " + adminId + " switched spectator target to " + newTargetPlayerId);
        return true;
    }
    
    //! Enable enhanced spectator mode
    bool EnableEnhancedSpectator(string adminId)
    {
        if (!m_AdminManager.IsAdmin(adminId))
        {
            Print("[EdenAdminSpectatorSystem] Access denied - not an admin: " + adminId);
            return false;
        }
        
        if (!m_SpectatorSessions.Contains(adminId))
        {
            Print("[EdenAdminSpectatorSystem] Admin not currently spectating: " + adminId);
            return false;
        }
        
        ref EdenSpectatorData spectatorData = m_SpectatorSessions.Get(adminId);
        spectatorData.SetSpectatorMode("Enhanced");
        spectatorData.SetEnhancedModeEnabled(true);
        
        // Enable enhanced spectator features
        EnableEnhancedSpectatorFeatures(adminId);
        
        // Log admin action
        m_AdminManager.LogAdminAction(adminId, "EnableEnhancedSpectator", "", "Enabled enhanced spectator mode");
        
        Print("[EdenAdminSpectatorSystem] Enhanced spectator mode enabled for admin: " + adminId);
        return true;
    }
    
    //! Get spectator session info
    EdenSpectatorData GetSpectatorSession(string adminId)
    {
        if (m_SpectatorSessions.Contains(adminId))
            return m_SpectatorSessions.Get(adminId);
        
        return null;
    }
    
    //! Check if player is being spectated
    bool IsPlayerBeingSpectated(string playerId)
    {
        return m_SpectatorTargets.Find(playerId) != -1;
    }
    
    //! Get list of admins spectating a player
    array<string> GetAdminsSpectatingPlayer(string playerId)
    {
        array<string> spectatingAdmins = new array<string>();
        
        for (int i = 0; i < m_SpectatorSessions.Count(); i++)
        {
            ref EdenSpectatorData session = m_SpectatorSessions.GetElement(i);
            if (session.GetTargetPlayerId() == playerId && session.IsActive())
            {
                spectatingAdmins.Insert(session.GetAdminId());
            }
        }
        
        return spectatingAdmins;
    }
    
    //! Get all active spectator sessions
    array<ref EdenSpectatorData> GetActiveSpectatorSessions()
    {
        array<ref EdenSpectatorData> activeSessions = new array<ref EdenSpectatorData>();
        
        for (int i = 0; i < m_SpectatorSessions.Count(); i++)
        {
            ref EdenSpectatorData session = m_SpectatorSessions.GetElement(i);
            if (session.IsActive())
                activeSessions.Insert(session);
        }
        
        return activeSessions;
    }
    
    //! Implementation-specific methods (would integrate with actual camera/player systems)
    protected void EnableSpectatorMode(string adminId, string targetPlayerId)
    {
        // This would integrate with the actual camera and player systems
        Print("[EdenAdminSpectatorSystem] Enabling spectator mode for admin: " + adminId + " on target: " + targetPlayerId);
    }
    
    protected void DisableSpectatorMode(string adminId)
    {
        // This would restore normal player control
        Print("[EdenAdminSpectatorSystem] Disabling spectator mode for admin: " + adminId);
    }
    
    protected void SwitchSpectatorCamera(string adminId, string newTargetPlayerId)
    {
        // This would switch the spectator camera to the new target
        Print("[EdenAdminSpectatorSystem] Switching spectator camera for admin: " + adminId + " to target: " + newTargetPlayerId);
    }
    
    protected void EnableEnhancedSpectatorFeatures(string adminId)
    {
        // This would enable enhanced spectator features like player info overlay, etc.
        Print("[EdenAdminSpectatorSystem] Enabling enhanced spectator features for admin: " + adminId);
    }
    
    protected bool IsPlayerOnline(string playerId)
    {
        // This would check if the player is actually online
        // For now, assume all players are online
        return true;
    }
    
    //! Cleanup
    void Cleanup()
    {
        // Stop all active spectator sessions
        for (int i = 0; i < m_SpectatorSessions.Count(); i++)
        {
            ref EdenSpectatorData session = m_SpectatorSessions.GetElement(i);
            if (session.IsActive())
            {
                DisableSpectatorMode(session.GetAdminId());
            }
        }
        
        m_SpectatorSessions.Clear();
        m_SpectatorTargets.Clear();
        
        Print("[EdenAdminSpectatorSystem] Admin spectator system cleaned up");
    }
}

//! Spectator data class
class EdenSpectatorData
{
    protected string m_AdminId;
    protected string m_TargetPlayerId;
    protected int m_StartTime;
    protected int m_EndTime;
    protected string m_SpectatorMode;
    protected bool m_IsActive;
    protected bool m_EnhancedModeEnabled;
    protected int m_LastSwitchTime;
    
    void EdenSpectatorData()
    {
        m_AdminId = "";
        m_TargetPlayerId = "";
        m_StartTime = 0;
        m_EndTime = 0;
        m_SpectatorMode = "Player";
        m_IsActive = false;
        m_EnhancedModeEnabled = false;
        m_LastSwitchTime = 0;
    }
    
    // Getters and setters
    void SetAdminId(string adminId) { m_AdminId = adminId; }
    string GetAdminId() { return m_AdminId; }
    
    void SetTargetPlayerId(string playerId) { m_TargetPlayerId = playerId; }
    string GetTargetPlayerId() { return m_TargetPlayerId; }
    
    void SetStartTime(int time) { m_StartTime = time; }
    int GetStartTime() { return m_StartTime; }
    
    void SetEndTime(int time) { m_EndTime = time; }
    int GetEndTime() { return m_EndTime; }
    
    void SetSpectatorMode(string mode) { m_SpectatorMode = mode; }
    string GetSpectatorMode() { return m_SpectatorMode; }
    
    void SetIsActive(bool active) { m_IsActive = active; }
    bool IsActive() { return m_IsActive; }
    
    void SetEnhancedModeEnabled(bool enabled) { m_EnhancedModeEnabled = enabled; }
    bool IsEnhancedModeEnabled() { return m_EnhancedModeEnabled; }
    
    void SetLastSwitchTime(int time) { m_LastSwitchTime = time; }
    int GetLastSwitchTime() { return m_LastSwitchTime; }
    
    int GetSessionDuration()
    {
        if (m_IsActive)
            return GetGame().GetWorld().GetWorldTime() - m_StartTime;
        else
            return m_EndTime - m_StartTime;
    }
}
