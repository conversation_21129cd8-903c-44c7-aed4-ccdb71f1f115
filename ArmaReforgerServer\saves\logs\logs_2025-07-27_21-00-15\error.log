---------------------------------------------
Log C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\saves\logs\logs_2025-07-27_21-00-15\error.log started at 2025-07-27 21:00:16 (2025-07-28 01:00:16 UTC)

21:00:16.267 ENGINE    (W): No module info used, trying to identify -> 0x6A90000
21:00:16.330 RENDER    (E): GrassMaterialClass: cannot find perlin noise texture (system/textures/perlin.edds)
21:00:16.334 MATERIAL  (E): SMAAEffect area texture not found (system/textures/PP/smaa_area)
21:00:16.334 MATERIAL  (E): SMAAEffect search texture not found (system/textures/PP/smaa_search)
21:00:16.977 SCRIPT       : Compiling Game scripts
21:00:16.978  SCRIPT    (W): @"scripts/GameCode/Effects/SCR_OptimizedMuzzleEffectComponent.c,14": Possible variable name conflict 'inScope'
21:00:17.024  SCRIPT    (W): @"scripts/Game/Editor/UI/Components/Modes/SCR_PhotoSaveModeEditorUIComponent.c,25": Pointer type 'PixelRawData' can only be used with local variables
21:00:17.259  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,28": 'GetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:00:17.260  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,30": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:00:17.260  SCRIPT    (W): @"scripts/Game/Campaign/UI/SCR_CampaignUIMutePlayerComponent.c,35": 'SetPlayerBlockedState' is obsolete: Session blocking is no longer supported.
21:00:17.275  SCRIPT    (W): @"scripts/Game/Components/Damage/SCR_VehicleDamageManagerComponent.c,826": Variable 'geomIndex' is not used
21:00:17.310  SCRIPT    (W): @"scripts/Game/Components/Voting/SCR_VotingKick.c,134": Variable 'outcome' is not used
21:00:17.397  SCRIPT    (W): @"scripts/Game/Entities/GridSpawnerEntity.c,180": 'Randomize' is obsolete: If seed need to be used use RandomGenerator instead
21:00:17.458  SCRIPT    (W): @"scripts/Game/Groups/SCR_GroupTileButton.c,684": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.501  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetCharacterStance.c,23": 'SetStance' is obsolete
21:00:17.502  SCRIPT    (W): @"scripts/Game/ScenarioFramework/Actions/AIActions/SCR_ScenarioFrameworkAIActionSetMovementType.c,23": 'SetMovementType' is obsolete
21:00:17.577  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,436": 'SetPlayerMutedState' is obsolete: Use SocialComponent.SetMuted(playerId, muted).
21:00:17.578  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,438": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.578  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,498": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.579  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1132": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.579  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1135": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.579  SCRIPT    (W): @"scripts/Game/UI/HUD/SCR_PlayerListMenu.c,1139": 'GetPlayerMutedState' is obsolete: Use SocialComponent.IsMuted(playerId).
21:00:17.622  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,7": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.622  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsFilteredSubMenu.c,36": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.623  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,358": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.623  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,380": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.623  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,420": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.623  SCRIPT    (W): @"scripts/Game/UI/Menu/ContentBrowser/MainMenu/SCR_ContentBrowser_AddonsSubMenu.c,444": 'SetPageItems' is obsolete: Use SetPageSize() instead!
21:00:17.637  SCRIPT    (W): @"scripts/Game/UI/Menu/GroupSettingsDialogUI.c,37": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:17.648  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2799": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:17.648  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2832": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:17.649  SCRIPT    (W): @"scripts/Game/UI/Menu/ServerBrowserMenuUI.c,2857": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:17.678  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,206": Comparing unrelated types 'Class' and 'Physics'
21:00:17.679  SCRIPT    (W): @"scripts/Game/Vehicle/SCR_ImpactEffectComponent.c,337": Comparing unrelated types 'Class' and 'Physics'
21:00:17.682  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,432": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:00:17.682  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_2DPIPSightsComponent.c,481": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:00:17.683  SCRIPT    (W): @"scripts/Game/Weapon/Sights/SCR_PIPSightsComponent.c,115": 'SetGUIWidget' is obsolete: Use SetRenderTarget(IEntity ent) instead
21:00:17.684  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,256": 'GetPrivilege' is obsolete: Use SocialComponent.IsPrivilegedTo() or IsMultiplayerAllowed()
21:00:17.684  SCRIPT    (W): @"scripts/Game/Workshop/SCR_AddonManager.c,280": 'GetPrivilegeAsync' is obsolete: Use SocialComponent.RequestSocialPrivilege() or RequestMultiplayerPrivilege()
21:00:19.938 WORLD        : Entities load @"{9DF143A76F5C6460}worlds/MP/CTI_Campaign_Eden.ent"
21:00:19.939  WORLD        : Subscene load @"{853E92315D1D9EFE}worlds/Eden/Eden.ent"
21:00:19.939   RESOURCES    : GetResourceObject @"{C8FAB83670BCF7C3}Prefabs/Structures/Cultural/Calvaries/CalvaryLarge_01.et"
21:00:19.939    WORLD        : Entity prefab load @"{C8FAB83670BCF7C3}Prefabs/Structures/Cultural/Calvaries/CalvaryLarge_01.et"
21:00:19.939     RESOURCES (E): Wrong GUID/name for resource @"{45ECF6B1797D3CD9}system/wbdata/PreviewWindow/TransparentMat.emat" in property "AssignedMaterial"
21:00:20.759 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine_LOD1.ptc"
21:00:20.759  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:00:20.759 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine.ptc"
21:00:20.759  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:00:20.759 RESOURCES    : Particle effect load @"Particles/Weapon/Explosion_TM62M_AT_Mine_LOD2.ptc"
21:00:20.759  RESOURCES (E): Wrong GUID/name for resource @"{29FBEC1991810EEF}system/wbdata/ParticleEditor/defaultPrefab.et" in property "Prefab"
21:00:21.907 WORLD        : Game::LoadEntities
21:00:21.908  WORLD        : CreateEntities
21:00:21.908   WORLD        : CreateEntities
21:00:21.908    ENTITY       : Create entity @"ENTITY:1" ('world', GenericWorldEntity) at <0.000000 0.000000 0.000000>
21:00:21.908     RESOURCES (E):   Failed to decompress memory image data. Data is not DXT.
21:00:21.908     WORLD     (E): Shore map CPU data creation failed - decompression of DF near atlas texture failed.
21:00:21.909     WORLD     (E): Loading of shore map failed - creation of CPU data failed
21:00:21.909     WORLD     (E): Loading of shore map failed - post load checks failed
21:00:28.712    ENTITY       : Create entity @"ENTITY:610688" ('StaticModelEntity','Assets/Structures/Infrastructure/Towers/TransmitterTower_01/TransmitterTower_01_small.xob') at <1931.796021 81.777000 5138.725098> @"{6A004A8F0571D456}Prefabs/Structures/Infrastructure/Towers/TransmitterTower_01/TransmitterTower_01_small.et"
21:00:28.712     DEFAULT   (W): World doesn't contain RadioManagerEntity to support any BaseRadioComponent.
21:00:31.806    ENTITY       : Create entity @"ENTITY:1055324" ('GenericEntity') at <6225.922852 107.562500 5118.536133>
21:00:31.807     ENTITY    (E): Door action not set for @"ENTITY:1055324" ('GenericEntity') at <6225.922852 107.562500 5118.536133>
21:00:31.807    ENTITY       : Create entity @"ENTITY:1055357" ('GenericEntity') at <5252.018066 38.411251 11397.515625>
21:00:31.807     ENTITY    (E): Door action not set for @"ENTITY:1055357" ('GenericEntity') at <5252.018066 38.411251 11397.515625>
21:00:31.808    ENTITY       : Create entity @"ENTITY:1055390" ('GenericEntity') at <3274.975098 114.904022 5189.225098>
21:00:31.808     ENTITY    (E): Door action not set for @"ENTITY:1055390" ('GenericEntity') at <3274.975098 114.904022 5189.225098>
21:00:31.809    ENTITY       : Create entity @"ENTITY:1055423" ('GenericEntity') at <3741.502930 80.400497 6630.824219>
21:00:31.809     ENTITY    (E): Door action not set for @"ENTITY:1055423" ('GenericEntity') at <3741.502930 80.400497 6630.824219>
21:00:31.809    ENTITY       : Create entity @"ENTITY:1055458" ('GenericEntity') at <5642.888184 89.763420 5352.812012>
21:00:31.809     ENTITY    (E): Door action not set for @"ENTITY:1055458" ('GenericEntity') at <5642.888184 89.763420 5352.812012>
21:00:31.810    ENTITY       : Create entity @"ENTITY:1055491" ('GenericEntity') at <1365.465942 41.945751 5944.875000>
21:00:31.810     ENTITY    (E): Door action not set for @"ENTITY:1055491" ('GenericEntity') at <1365.465942 41.945751 5944.875000>
21:00:31.811    ENTITY       : Create entity @"ENTITY:1055524" ('GenericEntity') at <4556.948242 20.443750 9608.875000>
21:00:31.811     ENTITY    (E): Door action not set for @"ENTITY:1055524" ('GenericEntity') at <4556.948242 20.443750 9608.875000>
21:00:31.811    ENTITY       : Create entity @"ENTITY:1055557" ('GenericEntity') at <4612.849121 158.267273 7012.264160>
21:00:31.811     ENTITY    (E): Door action not set for @"ENTITY:1055557" ('GenericEntity') at <4612.849121 158.267273 7012.264160>
21:00:31.812    ENTITY       : Create entity @"ENTITY:1055590" ('GenericEntity') at <5782.269043 28.741789 10179.600586>
21:00:31.812     ENTITY    (E): Door action not set for @"ENTITY:1055590" ('GenericEntity') at <5782.269043 28.741789 10179.600586>
21:00:31.812    ENTITY       : Create entity @"ENTITY:1055623" ('GenericEntity') at <5571.204102 91.851639 5976.666992>
21:00:31.812     ENTITY    (E): Door action not set for @"ENTITY:1055623" ('GenericEntity') at <5571.204102 91.851639 5976.666992>
21:00:31.814    ENTITY       : Create entity @"ENTITY:1055656" ('GenericEntity') at <7223.090820 136.826721 2315.154053>
21:00:31.814     ENTITY    (E): Door action not set for @"ENTITY:1055656" ('GenericEntity') at <7223.090820 136.826721 2315.154053>
21:00:31.814    ENTITY       : Create entity @"ENTITY:1055689" ('GenericEntity') at <7223.857910 137.444275 2372.346924>
21:00:31.815     ENTITY    (E): Door action not set for @"ENTITY:1055689" ('GenericEntity') at <7223.857910 137.444275 2372.346924>
21:00:31.815    ENTITY       : Create entity @"ENTITY:1055722" ('GenericEntity') at <4522.501953 15.952277 10653.205078>
21:00:31.815     ENTITY    (E): Door action not set for @"ENTITY:1055722" ('GenericEntity') at <4522.501953 15.952277 10653.205078>
21:00:31.816    ENTITY       : Create entity @"ENTITY:1055755" ('GenericEntity') at <9417.439453 45.156071 1688.795044>
21:00:31.816     ENTITY    (E): Door action not set for @"ENTITY:1055755" ('GenericEntity') at <9417.439453 45.156071 1688.795044>
21:00:32.008    ENTITY       : Create entity @"ENTITY:1104299" ('StaticModelEntity') at <8831.291016 95.934265 2673.487061>
21:00:32.009     MATERIAL     : Material load @"{45ECF6B1797D3CD9}system/wbdata/PreviewWindow/TransparentMat.emat"
21:00:32.009      MATERIAL  (E): Material file not found
21:00:32.009     MATERIAL  (E): Object @"Assets/Structures/Cultural/Calvaries/CalvaryLarge_01/CalvaryLarge_01.xob" - cannot load material @"system/wbdata/PreviewWindow/TransparentMat.emat", creating default one (class MatPBRBasic)
21:00:36.003    ENTITY       : Create entity @"ENTITY:2305843009213698652" ('GenericEntity','Assets/Props/Military/Antennas/Antenna_R161_01/Antenna_R161_01.xob') at <9354.231445 204.427994 1162.372070>
21:00:36.003     DEFAULT   (W): BaseRadioComponent does not have any transceiver. (Entity: @"ENTITY:2305843009213698652" ('GenericEntity','Assets/Props/Military/Antennas/Antenna_R161_01/Antenna_R161_01.xob') at <9354.231445 204.427994 1162.372070>). Only printing this message once. There're possibly more nodes with this requirement.
21:00:36.732    WORLD        : InitEntities 'world'
21:00:36.732     ENTITY       : Init entity @"ENTITY:3" ('SCR_MapEntity1', SCR_MapEntity) at <6400.000000 0.000000 6400.000000> @"{E1B88C66BF6CA1F9}Prefabs/World/Game/MapEntity_Everon.et"
21:00:36.732      SCRIPT    (W): SCR_MapEntity: Cannot get the size from terrain. Using default.
21:00:39.247     ENTITY       : Init entity @"ENTITY:2305843009213695743" ('SCR_BaseTaskManager') at <20.724001 0.000000 7.936000> @"{17E3EF2CF455460F}Prefabs/MP/Campaign/CampaignTaskManager.et"
21:00:39.248      GUI          : WidgetManager: CrateWidgets @"{ACCF501DD69CAF7B}UI/layouts/Tasks/TaskList.layout"
21:00:39.248       SCRIPT    (E): No data found for keyboard:KC_ESCAPE !! Check 'chimeraMapping.conf' and add data if necessary! Provided Actioname: TasksClose.
21:00:40.295 ENGINE    (E): Unable to initialize the game
21:00:44.261 SCRIPT    (E): Leaked 'NavlinkRoad' script instance (334x)!
21:00:44.262 SCRIPT    (E): Leaked 'BridgeRoad' script instance (81x)!
21:00:44.262 SCRIPT    (E): ==== Total Leaks (415x)! ====
21:00:44.590 RESOURCES (E): ==== Resource leaks ====
21:00:44.591 RESOURCES (E): ui/fonts/robotomono_msdf_28.edds   1
