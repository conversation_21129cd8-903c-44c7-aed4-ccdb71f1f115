@echo off

REM Store the original directory
set "ORIGINAL_DIR=%~dp0"

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
    REM Change back to the script's directory
    cd /d "%ORIGINAL_DIR%"
) else (
    echo This script requires administrator privileges to create symlinks.
    echo Requesting elevation...
    powershell -Command "Start-Process '%~f0' -Verb RunAs -ArgumentList '-WorkingDirectory', '%~dp0'"
    exit /b
)

echo ========================================
echo Starting Eden RP Reforger Server
echo ========================================

REM Set up mod directory
echo Setting up mod directory...
if not exist "steamapps\common\Arma Reforger Server\addons\EdenRP" (
    echo Creating mod symlink...
    mklink /D "steamapps\common\Arma Reforger Server\addons\EdenRP" "..\..\..\EdenRP"
)

REM Change to server directory
cd "steamapps\common\Arma Reforger Server"

echo Starting server with Eden RP mod...
echo Server Config: ..\..\..\server.json
echo Profile Path: ..\..\..\saves
echo Mod Path: addons\EdenRP

REM Start the server with diagnostic mode for better debugging
echo.
echo Starting ArmaReforgerServerDiag.exe...
echo Command: ArmaReforgerServerDiag.exe -config "..\..\..\server.json" -profile "..\..\..\saves" -addonsDir "addons" -logLevel 2
echo.

ArmaReforgerServerDiag.exe -config "..\..\..\server.json" -profile "..\..\..\saves" -addonsDir "addons" -logLevel 2

echo.
echo Server process ended. Exit code: %ERRORLEVEL%
echo Check the output above for any error messages.
echo.
echo Press any key to exit...
pause
