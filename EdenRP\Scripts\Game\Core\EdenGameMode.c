//! Eden RP Game Mode - Main game mode class for Eden RP Reforger
//! Handles initialization and core game loop management

[BaseGameMode(), SCR_BaseGameModeClass()]
class EdenGameMode : SCR_BaseGameMode
{
    [Attribute("1", UIWidgets.SpinBox, "Server ID (1-6)", params: "1 6 1")]
    protected int m_iServerId;
    
    [Attribute("450", UIWidgets.SpinBox, "Paycheck amount", params: "0 10000 50")]
    protected int m_iPaycheckAmount;
    
    [Attribute("75", UIWidgets.SpinBox, "Total crimes threshold", params: "1 200 1")]
    protected int m_iTotalCrimes;
    
    // Core Systems
    protected ref EdenDataManager m_DataManager;
    protected ref EdenConfigManager m_ConfigManager;
    protected ref EdenUIManager m_UIManager;
    protected ref EdenLoadingScreenManager m_LoadingScreenManager;

    // Economy Systems
    protected ref EdenBankingManager m_BankingManager;
    protected ref EdenMarketManager m_MarketManager;
    protected ref EdenShopManager m_ShopManager;

    // Vehicle Systems
    protected ref EdenVehicleManager m_VehicleManager;
    protected ref EdenVehicleModificationSystem m_VehicleModificationSystem;

    // Gang Systems
    protected ref EdenGangManager m_GangManager;
    protected ref EdenGangTerritorySystem m_GangTerritorySystem;

    // Housing Systems
    protected ref EdenHousingManager m_HousingManager;
    protected ref EdenHouseInventorySystem m_HouseInventorySystem;
    protected ref EdenHouseInteractionSystem m_HouseInteractionSystem;

    // Law Enforcement Systems
    protected ref EdenPoliceManager m_PoliceManager;
    protected ref EdenRestraintSystem m_RestraintSystem;

    // Medical Systems
    protected ref EdenMedicalManager m_MedicalManager;
    protected ref EdenMedicalEquipment m_MedicalEquipment;

    // Civilian Systems
    protected ref EdenCivilianManager m_CivilianManager;

    // Event Systems
    protected ref EdenEventManager m_EventManager;
    protected ref EdenFederalManager m_FederalManager;

    // Admin Systems
    protected ref EdenAdminManager m_AdminManager;
    protected ref EdenAdminCommandSystem m_AdminCommandSystem;
    protected ref EdenAdminMonitoringSystem m_AdminMonitoringSystem;
    protected ref EdenAdminPunishmentSystem m_AdminPunishmentSystem;
    protected ref EdenAdminDialog m_AdminDialog;

    // Communication Systems
    protected ref EdenCommunicationManager m_CommunicationManager;
    protected ref EdenProgressionManager m_ProgressionManager;

    protected bool m_bIsInitialized = false;
    protected bool m_bServerReady = false;
    
    //! Called when game mode is created
    override void EOnInit(IEntity owner)
    {
        super.EOnInit(owner);
        
        Print("[EdenGameMode] Initializing Eden Altis Life Reforger...");
        
        // Initialize core systems
        InitializeCoreManagers();
        
        // Set up event handlers
        SetupEventHandlers();
        
        Print("[EdenGameMode] Eden Game Mode initialized");
    }
    
    //! Initialize all core manager systems
    protected void InitializeCoreManagers()
    {
        Print("[EdenGameMode] Initializing core managers...");
        
        // Initialize data manager first (required by all other systems)
        m_DataManager = EdenDataManager.GetInstance();
        m_DataManager.Initialize();
        
        // Initialize configuration manager
        m_ConfigManager = new EdenConfigManager();
        m_ConfigManager.Initialize(m_iServerId);
        
        // Initialize economy systems
        m_BankingManager = new EdenBankingManager();
        m_BankingManager.Initialize();

        m_MarketManager = new EdenMarketManager();
        m_MarketManager.Initialize();

        m_ShopManager = new EdenShopManager();
        m_ShopManager.Initialize();
        
        // Initialize vehicle system
        m_VehicleManager = new EdenVehicleManager();
        m_VehicleManager.Initialize();

        m_VehicleModificationSystem = new EdenVehicleModificationSystem();
        m_VehicleModificationSystem.Initialize();
        
        // Initialize gang system
        m_GangManager = new EdenGangManager();
        m_GangManager.Initialize();

        // Initialize gang territory system
        m_GangTerritorySystem = new EdenGangTerritorySystem();
        m_GangTerritorySystem.Initialize();

        // Initialize housing system
        m_HousingManager = new EdenHousingManager();
        m_HousingManager.Initialize();

        m_HouseInventorySystem = new EdenHouseInventorySystem();
        m_HouseInventorySystem.Initialize();

        m_HouseInteractionSystem = new EdenHouseInteractionSystem();
        m_HouseInteractionSystem.Initialize();
        
        // Initialize police system
        m_PoliceManager = new EdenPoliceManager();
        m_PoliceManager.Initialize();

        // Initialize restraint system
        m_RestraintSystem = new EdenRestraintSystem();
        m_RestraintSystem.Initialize();

        // Initialize medical system
        m_MedicalManager = new EdenMedicalManager();
        m_MedicalManager.Initialize();

        // Initialize medical equipment system
        m_MedicalEquipment = new EdenMedicalEquipment();
        m_MedicalEquipment.Initialize();

        // Initialize civilian system
        m_CivilianManager = new EdenCivilianManager();
        m_CivilianManager.Initialize();

        // Initialize event system
        m_EventManager = new EdenEventManager();
        m_EventManager.Initialize();
        
        // Initialize admin system
        m_AdminManager = new EdenAdminManager();
        m_AdminManager.Initialize();

        m_AdminCommandSystem = new EdenAdminCommandSystem();
        m_AdminCommandSystem.Initialize(m_AdminManager);

        m_AdminMonitoringSystem = new EdenAdminMonitoringSystem();
        m_AdminMonitoringSystem.Initialize();

        m_AdminPunishmentSystem = new EdenAdminPunishmentSystem();
        m_AdminPunishmentSystem.Initialize();

        m_AdminDialog = new EdenAdminDialog();
        m_AdminDialog.Initialize(m_AdminManager, m_AdminCommandSystem, m_AdminMonitoringSystem, m_AdminPunishmentSystem);

        // Initialize UI system
        m_UIManager = new EdenUIManager();
        m_UIManager.Initialize();

        // Initialize Event Manager
        m_EventManager = EdenEventManager.GetInstance();

        // Initialize Federal Manager
        m_FederalManager = EdenFederalManager.GetInstance();

        // Initialize Communication Manager
        m_CommunicationManager = EdenCommunicationManager.GetInstance();

        // Initialize Progression Manager
        m_ProgressionManager = EdenProgressionManager.GetInstance();

        // Initialize Loading Screen Manager
        m_LoadingScreenManager = EdenLoadingScreenManager.GetInstance();

        m_bIsInitialized = true;
        Print("[EdenGameMode] All core managers initialized successfully");
    }
    
    //! Set up event handlers
    protected void SetupEventHandlers()
    {
        // Player connection events
        GetGame().GetCallqueue().CallLater(CheckServerReady, 1000, true);
        
        // Set up periodic saves
        GetGame().GetCallqueue().CallLater(PeriodicSave, 300000, true); // Save every 5 minutes
        
        // Set up paycheck system
        GetGame().GetCallqueue().CallLater(ProcessPaychecks, 600000, true); // Every 10 minutes
        
        // Set up market updates
        GetGame().GetCallqueue().CallLater(UpdateMarketPrices, 60000, true); // Every minute
    }
    
    //! Check if server is ready for players
    protected void CheckServerReady()
    {
        if (!m_bServerReady && m_bIsInitialized)
        {
            m_bServerReady = true;
            Print("[EdenGameMode] Server is ready for players");
            
            // Notify all systems that server is ready
            if (m_MarketManager)
                m_MarketManager.OnServerReady();
            if (m_VehicleManager)
                m_VehicleManager.OnServerReady();
            if (m_GangManager)
                m_GangManager.OnServerReady();
            if (m_HousingManager)
                m_HousingManager.OnServerReady();
            if (m_EventManager)
                m_EventManager.OnServerReady();
        }
    }
    
    //! Periodic data save
    protected void PeriodicSave()
    {
        if (!m_bServerReady)
            return;
            
        Print("[EdenGameMode] Performing periodic data save...");
        
        if (m_DataManager)
            m_DataManager.SaveAllData();
            
        Print("[EdenGameMode] Periodic save completed");
    }
    
    //! Process player paychecks
    protected void ProcessPaychecks()
    {
        if (!m_bServerReady)
            return;
            
        Print("[EdenGameMode] Processing player paychecks...");
        
        array<int> playerIds = {};
        GetGame().GetPlayerManager().GetPlayers(playerIds);
        
        foreach (int playerId : playerIds)
        {
            IEntity playerEntity = GetGame().GetPlayerManager().GetPlayerControlledEntity(playerId);
            if (!playerEntity)
                continue;
                
            EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
            if (!playerComp)
                continue;
                
            // Calculate paycheck based on faction and level
            int paycheckAmount = CalculatePaycheck(playerComp);
            
            if (paycheckAmount > 0)
            {
                playerComp.AddBankMoney(paycheckAmount);
                
                // Notify player
                SCR_NotificationsComponent.SendToPlayer(playerId, 
                    ENotification.GENERIC, 
                    string.Format("Paycheck received: $%1", paycheckAmount));
            }
        }
        
        Print("[EdenGameMode] Paycheck processing completed");
    }
    
    //! Calculate paycheck amount for player
    protected int CalculatePaycheck(EdenPlayerComponent playerComp)
    {
        if (!playerComp)
            return 0;
            
        string faction = playerComp.GetCurrentFaction();
        int level = 0;
        int basePaycheck = m_iPaycheckAmount;
        
        switch (faction)
        {
            case "cop":
                level = playerComp.GetCopLevel();
                basePaycheck = m_iPaycheckAmount + (level * 50); // Bonus for cop level
                break;
                
            case "medic":
                level = playerComp.GetMedicLevel();
                basePaycheck = m_iPaycheckAmount + (level * 40); // Bonus for medic level
                break;
                
            case "civ":
            default:
                basePaycheck = m_iPaycheckAmount;
                break;
        }
        
        // Apply donator multiplier
        int donatorLevel = playerComp.GetDonatorLevel();
        if (donatorLevel > 0)
        {
            float multiplier = 1.0 + (donatorLevel * 0.1); // 10% per donator level
            basePaycheck = Math.Round(basePaycheck * multiplier);
        }
        
        return basePaycheck;
    }
    
    //! Update market prices
    protected void UpdateMarketPrices()
    {
        if (!m_bServerReady || !m_MarketManager)
            return;
            
        m_MarketManager.UpdatePrices();
    }
    
    //! Called when player connects
    override void OnPlayerConnected(int playerId)
    {
        super.OnPlayerConnected(playerId);
        
        Print(string.Format("[EdenGameMode] Player %1 connected", playerId));
        
        // Initialize player data
        IEntity playerEntity = GetGame().GetPlayerManager().GetPlayerControlledEntity(playerId);
        if (playerEntity)
        {
            EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
            if (playerComp)
            {
                playerComp.InitializePlayer();
            }
        }
    }
    
    //! Called when player disconnects
    override void OnPlayerDisconnected(int playerId, KickCauseCode cause, int timeout)
    {
        super.OnPlayerDisconnected(playerId, cause, timeout);
        
        Print(string.Format("[EdenGameMode] Player %1 disconnected", playerId));
        
        // Save player data
        IEntity playerEntity = GetGame().GetPlayerManager().GetPlayerControlledEntity(playerId);
        if (playerEntity)
        {
            EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
            if (playerComp)
            {
                playerComp.SavePlayerData();
            }
        }
    }
    
    //! Called when game mode is being destroyed
    override void EOnFinalize(IEntity owner)
    {
        Print("[EdenGameMode] Shutting down Eden Game Mode...");
        
        // Perform final save
        if (m_DataManager)
        {
            m_DataManager.Cleanup();
        }
        
        // Cleanup managers
        // Cleanup economy systems
        if (m_BankingManager)
            m_BankingManager.Cleanup();
        if (m_MarketManager)
            m_MarketManager.Cleanup();
        if (m_ShopManager)
            m_ShopManager.Cleanup();
        if (m_VehicleManager)
            m_VehicleManager.Cleanup();
        if (m_VehicleModificationSystem)
            m_VehicleModificationSystem.Cleanup();
        if (m_GangManager)
            m_GangManager.Cleanup();
        if (m_GangTerritorySystem)
            m_GangTerritorySystem.Cleanup();
        if (m_HousingManager)
            m_HousingManager.Cleanup();
        if (m_PoliceManager)
            m_PoliceManager.Cleanup();
        if (m_MedicalManager)
            m_MedicalManager.Cleanup();
        if (m_CivilianManager)
            m_CivilianManager.Cleanup();
        if (m_EventManager)
            m_EventManager.Cleanup();
        if (m_AdminManager)
            m_AdminManager.Cleanup();
        if (m_AdminCommandSystem)
            m_AdminCommandSystem.Cleanup();
        if (m_AdminMonitoringSystem)
            m_AdminMonitoringSystem.Cleanup();
        if (m_AdminPunishmentSystem)
            m_AdminPunishmentSystem.Cleanup();
        if (m_UIManager)
            m_UIManager.Cleanup();

        // Cleanup event manager
        if (m_EventManager)
            m_EventManager = null;

        // Cleanup federal manager
        if (m_FederalManager)
            m_FederalManager = null;

        // Cleanup communication manager
        if (m_CommunicationManager)
            m_CommunicationManager = null;

        // Cleanup progression manager
        if (m_ProgressionManager)
            m_ProgressionManager = null;

        // Cleanup loading screen manager
        if (m_LoadingScreenManager)
            m_LoadingScreenManager = null;

        Print("[EdenGameMode] Eden Game Mode shutdown complete");
        
        super.EOnFinalize(owner);
    }

    //! Called when loading screen is complete
    void OnLoadingComplete()
    {
        Print("[EdenGameMode] Loading screen completed - game ready");

        // Perform any post-loading initialization
        // This could include spawning the player, initializing UI, etc.

        // Notify all managers that loading is complete
        if (m_UIManager)
            m_UIManager.OnLoadingComplete();
    }
    
    //! Get server ID
    int GetServerId() { return m_iServerId; }
    
    //! Check if server is ready
    bool IsServerReady() { return m_bServerReady; }
    
    //! Get manager instances
    EdenDataManager GetDataManager() { return m_DataManager; }
    EdenConfigManager GetConfigManager() { return m_ConfigManager; }

    // Economy managers
    EdenBankingManager GetBankingManager() { return m_BankingManager; }
    EdenMarketManager GetMarketManager() { return m_MarketManager; }
    EdenShopManager GetShopManager() { return m_ShopManager; }
    EdenVehicleManager GetVehicleManager() { return m_VehicleManager; }
    EdenVehicleModificationSystem GetVehicleModificationSystem() { return m_VehicleModificationSystem; }
    EdenGangManager GetGangManager() { return m_GangManager; }
    EdenGangTerritorySystem GetGangTerritorySystem() { return m_GangTerritorySystem; }
    EdenHousingManager GetHousingManager() { return m_HousingManager; }
    EdenHouseInventorySystem GetHouseInventorySystem() { return m_HouseInventorySystem; }
    EdenHouseInteractionSystem GetHouseInteractionSystem() { return m_HouseInteractionSystem; }
    EdenPoliceManager GetPoliceManager() { return m_PoliceManager; }
    EdenRestraintSystem GetRestraintSystem() { return m_RestraintSystem; }
    EdenMedicalManager GetMedicalManager() { return m_MedicalManager; }
    EdenMedicalEquipment GetMedicalEquipment() { return m_MedicalEquipment; }
    EdenCivilianManager GetCivilianManager() { return m_CivilianManager; }
    EdenEventManager GetEventManager() { return m_EventManager; }
    EdenFederalManager GetFederalManager() { return m_FederalManager; }
    EdenCommunicationManager GetCommunicationManager() { return m_CommunicationManager; }
    EdenProgressionManager GetProgressionManager() { return m_ProgressionManager; }
    EdenLoadingScreenManager GetLoadingScreenManager() { return m_LoadingScreenManager; }
    EdenAdminManager GetAdminManager() { return m_AdminManager; }
    EdenAdminCommandSystem GetAdminCommandSystem() { return m_AdminCommandSystem; }
    EdenAdminMonitoringSystem GetAdminMonitoringSystem() { return m_AdminMonitoringSystem; }
    EdenAdminPunishmentSystem GetAdminPunishmentSystem() { return m_AdminPunishmentSystem; }
    EdenAdminDialog GetAdminDialog() { return m_AdminDialog; }

    // UI System
    EdenUIManager GetUIManager() { return m_UIManager; }
}
