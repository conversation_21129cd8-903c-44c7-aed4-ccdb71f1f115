****** 최종 사용자 사용권 계약 ******


본 라이선스 계약("계약")은 귀하("라이선스")와 BattlEye Innovations e.K. ("라이선스 제공자"). 본 계약의 조건은 BattlEye 치트 방지 소프트웨어("BattlEye")의 모든 현재 및 미래 버전과 업데이트에 적용됩니다.


BattlEye를 설치, 활성화 또는 사용함으로써 정식 사용자는 본 계약의 모든 조건에 동의합니다. 라이선스 허가자는 라이선스 사용자에게 구체적으로 부여 및 이전되지 않은 모든 권리를 보유합니다.


라이센스 사용자는 다음 사항을 이해하고 인정하며 동의합니다.

- 라이선스 허가자는 라이선스 사용자에게 비상업적 목적으로만 BattlEye를 사용할 수 있는 비독점적이고 양도 불가능한 라이선스를 부여합니다. 따라서 라이선스 사용자는 BattlEye를 소유하지 않으며 라이선스 허가자는 BattlEye의 소유자로 남아 있습니다.

- 라이선스 허가자는 어떤 종류의 보증 없이 BattlEye를 "있는 그대로" 제공합니다. 라이선스 인가자는 BattlEye의 정확하고 오류 없는 기능을 보장하지 않으며 BattlEye의 사용으로 인해 발생하는 손상에 대해 책임을 지지 않습니다.

- 정식 사용자는 어떤 식으로든 BattlEye를 디컴파일, 디스어셈블, 리버스 엔지니어링, 수정 또는 재배포할 수 없습니다.

- BattlEye는 정식 사용자에 대한 통지 없이 자동으로 수시로 업데이트를 다운로드 및 설치합니다.

- BattlEye는 치트 프로그램 식별 알고리즘을 사용하여 라이선스 사용자의 전체 RAM(Random Access Memory), 게임 관련 및 시스템 관련 파일 및 폴더를 라이선스 사용자 시스템에 스캔하고 이러한 알고리즘의 결과를 연결된 다른 컴퓨터 및/또는 라이선스 허가자에게 보고할 수 있습니다. 치트 프로그램의 사용을 방지하고 탐지하기 위한 유일한 목적으로 그러한 정보를 저장합니다. BattlEye는 이 목적을 충족하기 위해 반드시 스캔 및/또는 보고해야 하는 데이터만 스캔 및/또는 보고합니다.

- BattlEye는 정식 사용자의 인터넷 프로토콜 주소, 게임 계정 이름 및 식별자, 게임 내 별명, 장치 식별자 및 하드웨어 일련 번호를 포함하되 이에 국한되지 않는 시스템 관련 및 하드웨어 관련 정보를 추가로 보고하고 저장할 수 있습니다.

- 라이선스 허가자는 라이선스 사용자의 개인정보를 소중히 여기며 항상 최선을 다해 보호합니다. BattlEye는 여기에 구체적으로 언급된 정보/데이터를 제외하고 개인 식별 정보 또는 개인 데이터를 보고하지 않습니다.

- 라이선스 허가자는 BattlEye가 수집한 모든 정보를 유럽 및/또는 미국에 위치한 서버에 저장합니다. 라이선스 허가자는 파트너 및/또는 계열사와 정보를 공유할 수 있습니다.

- 라이센스 사용자는 BattlEye의 침입 특성이 치트 프로그램을 방지하고 탐지하는 목적과 목표를 달성하는 데 필요하다는 것을 인정합니다.

- 라이선스 허가자는 라이선스 사용자에 대한 통지 없이 어떤 이유로든 언제든지 라이선스를 종료할 수 있습니다.


본 라이선스 계약은 라이선스 부여자와 라이선스 사용권자 간의 완전한 합의를 구성하며 이전의 모든 진술을 대체합니다.