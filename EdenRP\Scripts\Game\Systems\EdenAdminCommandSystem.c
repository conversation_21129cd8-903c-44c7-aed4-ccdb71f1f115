//! Eden Admin Command System - Handles chat-based admin commands
//! Converted from original admin chat command system

class EdenAdminCommandSystem
{
    protected ref map<string, ref EdenAdminCommand> m_Commands;
    protected EdenAdminManager m_AdminManager;
    protected string m_CommandPrefix;
    protected bool m_CommandSystemEnabled;
    
    void EdenAdminCommandSystem()
    {
        m_Commands = new map<string, ref EdenAdminCommand>();
        m_CommandPrefix = ";";
        m_CommandSystemEnabled = true;
        
        InitializeCommands();
    }
    
    //! Initialize command system
    void Initialize(EdenAdminManager adminManager)
    {
        Print("[EdenAdminCommandSystem] Initializing admin command system...");
        
        m_AdminManager = adminManager;
        RegisterAllCommands();
        
        Print("[EdenAdminCommandSystem] Admin command system initialized");
    }
    
    //! Initialize command definitions
    protected void InitializeCommands()
    {
        Print("[EdenAdminCommandSystem] Command definitions initialized");
    }
    
    //! Register all admin commands
    protected void RegisterAllCommands()
    {
        // God mode command
        RegisterCommand("god", new EdenAdminCommand("god", "Toggle god mode", 2, 
            "Toggles god mode on/off for yourself or target player"));
        
        // Invisibility command
        RegisterCommand("invis", new EdenAdminCommand("invis", "Toggle invisibility", 2, 
            "Toggles invisibility on/off for yourself"));
        
        // Spectator commands
        RegisterCommand("spec", new EdenAdminCommand("spec", "Enter spectator mode", 1, 
            "Enters basic spectator mode"));
        RegisterCommand("espec", new EdenAdminCommand("espec", "Enter enhanced spectator", 2, 
            "Enters enhanced spectator mode with advanced features"));
        
        // Teleportation commands
        RegisterCommand("tpmap", new EdenAdminCommand("tpmap", "Teleport to map position", 2, 
            "Opens map to select teleport destination"));
        RegisterCommand("tphere", new EdenAdminCommand("tphere", "Teleport player here", 2, 
            "Teleports selected player to your location"));
        RegisterCommand("tpto", new EdenAdminCommand("tpto", "Teleport to player", 2, 
            "Teleports you to selected player"));
        
        // Player management commands
        RegisterCommand("heal", new EdenAdminCommand("heal", "Heal player", 1, 
            "Heals yourself or target player"));
        RegisterCommand("revive", new EdenAdminCommand("revive", "Revive player", 1, 
            "Revives target player"));
        RegisterCommand("massrevive", new EdenAdminCommand("massrevive", "Mass revive all", 3, 
            "Revives all dead players on the server"));
        
        // Restraint commands
        RegisterCommand("restrain", new EdenAdminCommand("restrain", "Restrain player", 1, 
            "Restrains target player"));
        RegisterCommand("unrestrain", new EdenAdminCommand("unrestrain", "Unrestrain player", 1, 
            "Unrestrains target player"));
        
        // Vehicle commands
        RegisterCommand("getkeys", new EdenAdminCommand("getkeys", "Get vehicle keys", 1, 
            "Gets keys for target vehicle"));
        RegisterCommand("flip", new EdenAdminCommand("flip", "Flip vehicle", 1, 
            "Flips your current vehicle"));
        
        // Menu commands
        RegisterCommand("amenu", new EdenAdminCommand("amenu", "Open admin menu", 1, 
            "Opens the admin management menu"));
        RegisterCommand("emenu", new EdenAdminCommand("emenu", "Open event menu", 1, 
            "Opens the event management menu"));
        
        // Utility commands
        RegisterCommand("cmds", new EdenAdminCommand("cmds", "List commands", 1, 
            "Lists all available admin commands"));
        RegisterCommand("help", new EdenAdminCommand("help", "Show help", 0, 
            "Shows general help information"));
        
        // Debug commands
        RegisterCommand("debug", new EdenAdminCommand("debug", "Debug information", 4, 
            "Shows debug information and active scripts"));
        
        // Developer commands
        RegisterCommand("exec", new EdenAdminCommand("exec", "Execute payload", 3, 
            "Executes server payload (developer only)"));
        
        // Event commands
        RegisterCommand("ejoin", new EdenAdminCommand("ejoin", "Join event", 0, 
            "Joins the current active event"));
        RegisterCommand("eleave", new EdenAdminCommand("eleave", "Leave event", 0, 
            "Leaves the current active event"));
        
        // Communication commands
        RegisterCommand("r", new EdenAdminCommand("r", "Reply to message", 0, 
            "Replies to your last received message"));
        
        // Special commands
        RegisterCommand("streamer", new EdenAdminCommand("streamer", "Toggle streamer mode", 1, 
            "Toggles streamer mode on/off"));
        RegisterCommand("vote", new EdenAdminCommand("vote", "Vote in conquest", 0, 
            "Opens conquest voting dialog"));
    }
    
    //! Register a command
    void RegisterCommand(string commandName, EdenAdminCommand command)
    {
        if (commandName == "" || !command)
            return;
            
        m_Commands.Set(commandName, command);
        Print(string.Format("[EdenAdminCommandSystem] Registered command: %1", commandName));
    }
    
    //! Process chat message for admin commands
    bool ProcessChatMessage(string playerId, string message)
    {
        if (!m_CommandSystemEnabled || !m_AdminManager)
            return false;
            
        // Check if message starts with command prefix
        if (message.Length() == 0 || message.Substring(0, 1) != m_CommandPrefix)
            return false;
            
        // Parse command and arguments
        array<string> parts = {};
        message.Split(" ", parts);
        
        if (parts.Count() == 0)
            return false;
            
        string commandName = parts[0].Substring(1, parts[0].Length() - 1); // Remove prefix
        array<string> args = {};
        
        for (int i = 1; i < parts.Count(); i++)
        {
            args.Insert(parts[i]);
        }
        
        return ExecuteCommand(playerId, commandName, args);
    }
    
    //! Execute admin command
    bool ExecuteCommand(string playerId, string commandName, array<string> args)
    {
        if (!m_Commands.Contains(commandName))
        {
            SendMessageToPlayer(playerId, "Not a valid command. Type ;cmds for a list of valid commands.");
            return false;
        }
        
        EdenAdminCommand command = m_Commands.Get(commandName);
        if (!command)
            return false;
            
        // Check permissions
        int adminLevel = m_AdminManager.GetAdminLevel(playerId);
        int developerLevel = m_AdminManager.GetDeveloperLevel(playerId);
        
        if (adminLevel < command.GetRequiredLevel() && developerLevel < command.GetRequiredLevel())
        {
            SendMessageToPlayer(playerId, "Insufficient Permissions");
            return false;
        }
        
        // Execute command
        return ExecuteSpecificCommand(playerId, commandName, args);
    }
    
    //! Execute specific command implementation
    protected bool ExecuteSpecificCommand(string playerId, string commandName, array<string> args)
    {
        switch (commandName)
        {
            case "god":
                return HandleGodCommand(playerId, args);
            case "invis":
                return HandleInvisCommand(playerId, args);
            case "spec":
                return HandleSpecCommand(playerId, args);
            case "espec":
                return HandleESpecCommand(playerId, args);
            case "tpmap":
                return HandleTpMapCommand(playerId, args);
            case "tphere":
                return HandleTpHereCommand(playerId, args);
            case "tpto":
                return HandleTpToCommand(playerId, args);
            case "heal":
                return HandleHealCommand(playerId, args);
            case "revive":
                return HandleReviveCommand(playerId, args);
            case "massrevive":
                return HandleMassReviveCommand(playerId, args);
            case "restrain":
                return HandleRestrainCommand(playerId, args);
            case "unrestrain":
                return HandleUnrestrainCommand(playerId, args);
            case "getkeys":
                return HandleGetKeysCommand(playerId, args);
            case "flip":
                return HandleFlipCommand(playerId, args);
            case "amenu":
                return HandleAMenuCommand(playerId, args);
            case "emenu":
                return HandleEMenuCommand(playerId, args);
            case "cmds":
                return HandleCmdsCommand(playerId, args);
            case "help":
                return HandleHelpCommand(playerId, args);
            case "debug":
                return HandleDebugCommand(playerId, args);
            case "exec":
                return HandleExecCommand(playerId, args);
            case "ejoin":
                return HandleEJoinCommand(playerId, args);
            case "eleave":
                return HandleELeaveCommand(playerId, args);
            case "r":
                return HandleReplyCommand(playerId, args);
            case "streamer":
                return HandleStreamerCommand(playerId, args);
            case "vote":
                return HandleVoteCommand(playerId, args);
        }
        
        return false;
    }
    
    //! Command implementations
    protected bool HandleGodCommand(string playerId, array<string> args)
    {
        string targetId = (args.Count() > 0) ? args[0] : playerId;
        bool success = m_AdminManager.ToggleGodMode(playerId, targetId);
        
        if (success)
        {
            SendMessageToPlayer(playerId, "God mode toggled");
        }
        else
        {
            SendMessageToPlayer(playerId, "Failed to toggle god mode");
        }
        
        return success;
    }
    
    protected bool HandleInvisCommand(string playerId, array<string> args)
    {
        bool success = m_AdminManager.ToggleInvisibility(playerId);
        
        if (success)
        {
            SendMessageToPlayer(playerId, "Invisibility toggled");
        }
        else
        {
            SendMessageToPlayer(playerId, "Failed to toggle invisibility");
        }
        
        return success;
    }
    
    protected bool HandleSpecCommand(string playerId, array<string> args)
    {
        // Implementation would enter spectator mode
        SendMessageToPlayer(playerId, "Entering spectator mode...");
        return true;
    }
    
    protected bool HandleESpecCommand(string playerId, array<string> args)
    {
        // Implementation would enter enhanced spectator mode
        SendMessageToPlayer(playerId, "Entering enhanced spectator mode...");
        return true;
    }
    
    protected bool HandleTpMapCommand(string playerId, array<string> args)
    {
        // Implementation would open map for teleportation
        SendMessageToPlayer(playerId, "Opening teleport map...");
        return true;
    }
    
    protected bool HandleTpHereCommand(string playerId, array<string> args)
    {
        if (args.Count() == 0)
        {
            SendMessageToPlayer(playerId, "Usage: ;tphere <player>");
            return false;
        }
        
        string targetId = args[0];
        IEntity adminEntity = GetPlayerEntity(playerId);
        if (!adminEntity)
            return false;
            
        vector adminPos = adminEntity.GetOrigin();
        bool success = m_AdminManager.TeleportPlayer(playerId, targetId, adminPos);
        
        if (success)
        {
            SendMessageToPlayer(playerId, string.Format("Teleported %1 to your location", targetId));
        }
        else
        {
            SendMessageToPlayer(playerId, "Failed to teleport player");
        }
        
        return success;
    }
    
    protected bool HandleTpToCommand(string playerId, array<string> args)
    {
        if (args.Count() == 0)
        {
            SendMessageToPlayer(playerId, "Usage: ;tpto <player>");
            return false;
        }
        
        string targetId = args[0];
        bool success = m_AdminManager.TeleportPlayerToPlayer(playerId, playerId, targetId);
        
        if (success)
        {
            SendMessageToPlayer(playerId, string.Format("Teleported to %1", targetId));
        }
        else
        {
            SendMessageToPlayer(playerId, "Failed to teleport to player");
        }
        
        return success;
    }
    
    protected bool HandleHealCommand(string playerId, array<string> args)
    {
        string targetId = (args.Count() > 0) ? args[0] : playerId;
        bool success = m_AdminManager.HealPlayer(playerId, targetId);
        
        if (success)
        {
            SendMessageToPlayer(playerId, "Player healed");
        }
        else
        {
            SendMessageToPlayer(playerId, "Failed to heal player");
        }
        
        return success;
    }
    
    protected bool HandleReviveCommand(string playerId, array<string> args)
    {
        if (args.Count() == 0)
        {
            SendMessageToPlayer(playerId, "Usage: ;revive <player>");
            return false;
        }
        
        string targetId = args[0];
        bool success = m_AdminManager.RevivePlayer(playerId, targetId);
        
        if (success)
        {
            SendMessageToPlayer(playerId, string.Format("Revived %1", targetId));
        }
        else
        {
            SendMessageToPlayer(playerId, "Failed to revive player");
        }
        
        return success;
    }
    
    protected bool HandleMassReviveCommand(string playerId, array<string> args)
    {
        bool success = m_AdminManager.MassReviveAll(playerId);
        
        if (success)
        {
            SendMessageToPlayer(playerId, "Mass revive completed");
        }
        else
        {
            SendMessageToPlayer(playerId, "Failed to perform mass revive");
        }
        
        return success;
    }
    
    protected bool HandleRestrainCommand(string playerId, array<string> args)
    {
        if (args.Count() == 0)
        {
            SendMessageToPlayer(playerId, "Usage: ;restrain <player>");
            return false;
        }
        
        string targetId = args[0];
        bool success = m_AdminManager.RestrainPlayer(playerId, targetId);
        
        if (success)
        {
            SendMessageToPlayer(playerId, string.Format("Restrained %1", targetId));
        }
        else
        {
            SendMessageToPlayer(playerId, "Failed to restrain player");
        }
        
        return success;
    }
    
    protected bool HandleUnrestrainCommand(string playerId, array<string> args)
    {
        if (args.Count() == 0)
        {
            SendMessageToPlayer(playerId, "Usage: ;unrestrain <player>");
            return false;
        }
        
        string targetId = args[0];
        bool success = m_AdminManager.UnrestrainPlayer(playerId, targetId);
        
        if (success)
        {
            SendMessageToPlayer(playerId, string.Format("Unrestrained %1", targetId));
        }
        else
        {
            SendMessageToPlayer(playerId, "Failed to unrestrain player");
        }
        
        return success;
    }
    
    protected bool HandleGetKeysCommand(string playerId, array<string> args)
    {
        // Implementation would get keys for target vehicle
        SendMessageToPlayer(playerId, "You now have keys for the target vehicle");
        return true;
    }
    
    protected bool HandleFlipCommand(string playerId, array<string> args)
    {
        // Implementation would flip player's vehicle
        SendMessageToPlayer(playerId, "Vehicle flipped");
        return true;
    }
    
    protected bool HandleAMenuCommand(string playerId, array<string> args)
    {
        // Implementation would open admin menu
        SendMessageToPlayer(playerId, "Opening admin menu...");
        return true;
    }
    
    protected bool HandleEMenuCommand(string playerId, array<string> args)
    {
        // Implementation would open event menu
        SendMessageToPlayer(playerId, "Opening event menu...");
        return true;
    }
    
    protected bool HandleCmdsCommand(string playerId, array<string> args)
    {
        string commandList = "Available commands:\n";
        
        foreach (string cmdName, EdenAdminCommand cmd : m_Commands)
        {
            int adminLevel = m_AdminManager.GetAdminLevel(playerId);
            int developerLevel = m_AdminManager.GetDeveloperLevel(playerId);
            
            if (adminLevel >= cmd.GetRequiredLevel() || developerLevel >= cmd.GetRequiredLevel())
            {
                commandList += string.Format(";%1 - %2\n", cmdName, cmd.GetDescription());
            }
        }
        
        SendMessageToPlayer(playerId, commandList);
        return true;
    }
    
    protected bool HandleHelpCommand(string playerId, array<string> args)
    {
        string helpText = "General commands:\n";
        helpText += ";help - Display all commands you have access to\n";
        helpText += ";r - Reply to your last message\n";
        helpText += ";ejoin - Join the current active event\n";
        helpText += ";eleave - Leave the current active event you are in";
        
        SendMessageToPlayer(playerId, helpText);
        return true;
    }
    
    protected bool HandleDebugCommand(string playerId, array<string> args)
    {
        // Implementation would show debug information
        SendMessageToPlayer(playerId, "Debug information logged to console");
        return true;
    }
    
    protected bool HandleExecCommand(string playerId, array<string> args)
    {
        // Implementation would execute server payload (developer only)
        SendMessageToPlayer(playerId, "Payload executed");
        return true;
    }
    
    protected bool HandleEJoinCommand(string playerId, array<string> args)
    {
        // Implementation would join active event
        SendMessageToPlayer(playerId, "Joined active event");
        return true;
    }
    
    protected bool HandleELeaveCommand(string playerId, array<string> args)
    {
        // Implementation would leave active event
        SendMessageToPlayer(playerId, "Left active event");
        return true;
    }
    
    protected bool HandleReplyCommand(string playerId, array<string> args)
    {
        if (args.Count() == 0)
        {
            SendMessageToPlayer(playerId, "Usage: ;r <message>");
            return false;
        }
        
        string message = "";
        foreach (string arg : args)
        {
            message += arg + " ";
        }
        
        // Implementation would send reply message
        SendMessageToPlayer(playerId, "Reply sent");
        return true;
    }
    
    protected bool HandleStreamerCommand(string playerId, array<string> args)
    {
        // Implementation would toggle streamer mode
        SendMessageToPlayer(playerId, "Streamer mode toggled");
        return true;
    }
    
    protected bool HandleVoteCommand(string playerId, array<string> args)
    {
        // Implementation would open conquest voting
        SendMessageToPlayer(playerId, "Opening conquest vote...");
        return true;
    }
    
    //! Helper methods
    protected void SendMessageToPlayer(string playerId, string message)
    {
        // Implementation would send message to specific player
        Print(string.Format("[AdminCommand] To %1: %2", playerId, message));
    }
    
    protected IEntity GetPlayerEntity(string playerId)
    {
        // Implementation would get player entity by ID
        return null;
    }
    
    //! Get command information
    EdenAdminCommand GetCommand(string commandName)
    {
        if (!m_Commands.Contains(commandName))
            return null;
            
        return m_Commands.Get(commandName);
    }
    
    array<string> GetAvailableCommands(string playerId)
    {
        array<string> availableCommands = {};
        
        int adminLevel = m_AdminManager.GetAdminLevel(playerId);
        int developerLevel = m_AdminManager.GetDeveloperLevel(playerId);
        
        foreach (string cmdName, EdenAdminCommand cmd : m_Commands)
        {
            if (adminLevel >= cmd.GetRequiredLevel() || developerLevel >= cmd.GetRequiredLevel())
            {
                availableCommands.Insert(cmdName);
            }
        }
        
        return availableCommands;
    }
    
    //! Configuration
    void SetCommandPrefix(string prefix) { m_CommandPrefix = prefix; }
    string GetCommandPrefix() { return m_CommandPrefix; }
    
    void SetCommandSystemEnabled(bool enabled) { m_CommandSystemEnabled = enabled; }
    bool IsCommandSystemEnabled() { return m_CommandSystemEnabled; }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenAdminCommandSystem] Cleaning up admin command system...");
        
        m_Commands.Clear();
        m_AdminManager = null;
    }
}
