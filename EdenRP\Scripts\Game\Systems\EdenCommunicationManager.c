//! Communication Manager for Eden Reforger
//! Converted from original Eden Altis Life smartphone and messaging systems
//! Manages text messaging, phone calls, dispatch, emergency services, and player communication

[ComponentEditorProps(category: "Eden Systems", description: "Manages player communication and messaging")]
class EdenCommunicationManagerComponent : ScriptComponent
{
    [Attribute("10", UIWidgets.EditBox, "Maximum messages per conversation")]
    protected int m_iMaxMessagesPerConversation;
    
    [Attribute("100", UIWidgets.EditBox, "Maximum total messages per player")]
    protected int m_iMaxTotalMessages;
    
    [Attribute("30", UIWidgets.EditBox, "Message cleanup interval in seconds")]
    protected int m_iMessageCleanupInterval;
    
    [RplProp()]
    protected ref array<ref EdenMessage> m_aAllMessages;
    
    [RplProp()]
    protected ref array<ref EdenContact> m_aEmergencyContacts;
    
    [RplProp()]
    protected ref array<ref EdenDispatchCall> m_aActiveDispatchCalls;
    
    protected ref map<string, ref array<ref EdenMessage>> m_mPlayerMessages;
    protected ref map<string, ref array<ref EdenContact>> m_mPlayerContacts;
    protected ref map<string, string> m_mPlayerPhoneNumbers;
    protected int m_iNextPhoneNumber;
    
    //! Constructor
    void EdenCommunicationManagerComponent(IEntityComponentSource src, IEntity ent, IEntity parent)
    {
        m_aAllMessages = new array<ref EdenMessage>();
        m_aEmergencyContacts = new array<ref EdenContact>();
        m_aActiveDispatchCalls = new array<ref EdenDispatchCall>();
        m_mPlayerMessages = new map<string, ref array<ref EdenMessage>>();
        m_mPlayerContacts = new map<string, ref array<ref EdenContact>>();
        m_mPlayerPhoneNumbers = new map<string, string>();
        m_iNextPhoneNumber = 1000000; // Start phone numbers at 1,000,000
        
        InitializeEmergencyContacts();
        
        // Start message cleanup timer
        GetGame().GetCallqueue().CallLater(CleanupOldMessages, m_iMessageCleanupInterval * 1000, true);
    }
    
    //! Initialize emergency contacts
    protected void InitializeEmergencyContacts()
    {
        // Police emergency
        ref EdenContact policeContact = new EdenContact();
        policeContact.SetContactId("emergency_police");
        policeContact.SetName("Altis Police Department");
        policeContact.SetPhoneNumber("911");
        policeContact.SetContactType("emergency");
        policeContact.SetIsEmergency(true);
        m_aEmergencyContacts.Insert(policeContact);
        
        // EMS emergency
        ref EdenContact emsContact = new EdenContact();
        emsContact.SetContactId("emergency_ems");
        emsContact.SetName("Altis EMS");
        emsContact.SetPhoneNumber("912");
        emsContact.SetContactType("emergency");
        emsContact.SetIsEmergency(true);
        m_aEmergencyContacts.Insert(emsContact);
        
        // Taxi service
        ref EdenContact taxiContact = new EdenContact();
        taxiContact.SetContactId("service_taxi");
        taxiContact.SetName("Altis Taxi Service");
        taxiContact.SetPhoneNumber("913");
        taxiContact.SetContactType("service");
        taxiContact.SetIsEmergency(false);
        m_aEmergencyContacts.Insert(taxiContact);
        
        // Mechanic service
        ref EdenContact mechanicContact = new EdenContact();
        mechanicContact.SetContactId("service_mechanic");
        mechanicContact.SetName("Altis Mechanics");
        mechanicContact.SetPhoneNumber("914");
        mechanicContact.SetContactType("service");
        mechanicContact.SetIsEmergency(false);
        m_aEmergencyContacts.Insert(mechanicContact);
    }
    
    //! Send message between players
    bool SendMessage(string fromPlayerId, string toPlayerId, string messageContent, int messageType = 0)
    {
        if (fromPlayerId == "" || toPlayerId == "" || messageContent == "")
            return false;
            
        // Create message
        ref EdenMessage message = new EdenMessage();
        message.SetFromPlayerId(fromPlayerId);
        message.SetToPlayerId(toPlayerId);
        message.SetMessageContent(messageContent);
        message.SetMessageType(messageType);
        message.SetTimestamp(GetGame().GetWorld().GetWorldTime());
        message.SetIsRead(false);
        
        // Get player names
        string fromPlayerName = GetPlayerName(fromPlayerId);
        string toPlayerName = GetPlayerName(toPlayerId);
        message.SetFromPlayerName(fromPlayerName);
        message.SetToPlayerName(toPlayerName);
        
        // Add to global messages
        m_aAllMessages.Insert(message);
        
        // Add to sender's messages
        if (!m_mPlayerMessages.Contains(fromPlayerId))
            m_mPlayerMessages.Set(fromPlayerId, new array<ref EdenMessage>());
        m_mPlayerMessages.Get(fromPlayerId).Insert(message);
        
        // Add to recipient's messages
        if (!m_mPlayerMessages.Contains(toPlayerId))
            m_mPlayerMessages.Set(toPlayerId, new array<ref EdenMessage>());
        m_mPlayerMessages.Get(toPlayerId).Insert(message);
        
        // Handle different message types
        switch (messageType)
        {
            case 0: // Normal message
                {
                    SendClientMessage(toPlayerId, messageContent, fromPlayerName, messageType);
                    break;
                }
            case 1: // Message to cops
                {
                    BroadcastToFaction("police", messageContent, fromPlayerName, messageType);
                    break;
                }
            case 2: // Message to admins
                {
                    BroadcastToAdmins(messageContent, fromPlayerName, messageType);
                    break;
                }
            case 3: // EMS request
                {
                    BroadcastToFaction("ems", messageContent, fromPlayerName, messageType);
                    CreateDispatchCall(fromPlayerId, "ems", messageContent);
                    break;
                }
            case 9: // Panic button
                {
                    HandlePanicButton(fromPlayerId, messageContent);
                    break;
                }
        }
        
        // Save message to database
        SaveMessageToDatabase(message);
        
        Print(string.Format("EdenCommunicationManager: Message sent from %1 to %2 (Type: %3)", 
            fromPlayerName, toPlayerName, messageType));
        
        return true;
    }
    
    //! Send client message
    protected void SendClientMessage(string playerId, string message, string fromName, int messageType)
    {
        // TODO: Implement actual client message sending
        Print(string.Format("CLIENT MESSAGE to %1 from %2: %3", playerId, fromName, message));
    }
    
    //! Broadcast to faction
    protected void BroadcastToFaction(string faction, string message, string fromName, int messageType)
    {
        // TODO: Implement faction broadcasting
        Print(string.Format("FACTION BROADCAST to %1 from %2: %3", faction, fromName, message));
    }
    
    //! Broadcast to admins
    protected void BroadcastToAdmins(string message, string fromName, int messageType)
    {
        // TODO: Implement admin broadcasting
        Print(string.Format("ADMIN BROADCAST from %1: %2", fromName, message));
    }
    
    //! Handle panic button
    protected void HandlePanicButton(string playerId, string message)
    {
        // Create emergency dispatch call
        CreateDispatchCall(playerId, "police", "PANIC BUTTON ACTIVATED: " + message);
        
        // Broadcast to all police
        string playerName = GetPlayerName(playerId);
        BroadcastToFaction("police", "PANIC BUTTON: " + message, playerName, 9);
        
        // Create map marker for panic location
        // TODO: Implement panic marker creation
        
        Print(string.Format("EdenCommunicationManager: Panic button activated by %1", playerName));
    }
    
    //! Create dispatch call
    void CreateDispatchCall(string playerId, string serviceType, string description)
    {
        ref EdenDispatchCall dispatchCall = new EdenDispatchCall();
        dispatchCall.SetCallId(GenerateCallId());
        dispatchCall.SetPlayerId(playerId);
        dispatchCall.SetPlayerName(GetPlayerName(playerId));
        dispatchCall.SetServiceType(serviceType);
        dispatchCall.SetDescription(description);
        dispatchCall.SetTimestamp(GetGame().GetWorld().GetWorldTime());
        dispatchCall.SetIsActive(true);
        dispatchCall.SetIsAssigned(false);
        
        // TODO: Get player location
        dispatchCall.SetLocation("0 0 0");
        
        m_aActiveDispatchCalls.Insert(dispatchCall);
        
        Print(string.Format("EdenCommunicationManager: Dispatch call created - %1 for %2", 
            serviceType, dispatchCall.GetPlayerName()));
    }
    
    //! Assign dispatch call
    bool AssignDispatchCall(string callId, string responderId)
    {
        foreach (EdenDispatchCall call : m_aActiveDispatchCalls)
        {
            if (call && call.GetCallId() == callId && call.IsActive())
            {
                call.SetIsAssigned(true);
                call.SetResponderId(responderId);
                call.SetResponderName(GetPlayerName(responderId));
                call.SetAssignedTime(GetGame().GetWorld().GetWorldTime());
                
                Print(string.Format("EdenCommunicationManager: Dispatch call %1 assigned to %2", 
                    callId, call.GetResponderName()));
                return true;
            }
        }
        return false;
    }
    
    //! Complete dispatch call
    bool CompleteDispatchCall(string callId)
    {
        foreach (EdenDispatchCall call : m_aActiveDispatchCalls)
        {
            if (call && call.GetCallId() == callId && call.IsActive())
            {
                call.SetIsActive(false);
                call.SetCompletedTime(GetGame().GetWorld().GetWorldTime());
                
                Print(string.Format("EdenCommunicationManager: Dispatch call %1 completed", callId));
                return true;
            }
        }
        return false;
    }
    
    //! Get player messages
    array<ref EdenMessage> GetPlayerMessages(string playerId, string conversationWith = "")
    {
        if (!m_mPlayerMessages.Contains(playerId))
            return new array<ref EdenMessage>();
            
        ref array<ref EdenMessage> playerMessages = m_mPlayerMessages.Get(playerId);
        
        if (conversationWith == "")
            return playerMessages;
            
        // Filter messages for specific conversation
        ref array<ref EdenMessage> conversationMessages = new array<ref EdenMessage>();
        foreach (EdenMessage message : playerMessages)
        {
            if (message && (message.GetFromPlayerId() == conversationWith || message.GetToPlayerId() == conversationWith))
            {
                conversationMessages.Insert(message);
            }
        }
        
        return conversationMessages;
    }
    
    //! Add contact
    bool AddContact(string playerId, string contactId, string contactName, string phoneNumber = "")
    {
        if (!m_mPlayerContacts.Contains(playerId))
            m_mPlayerContacts.Set(playerId, new array<ref EdenContact>());
            
        ref array<ref EdenContact> playerContacts = m_mPlayerContacts.Get(playerId);
        
        // Check if contact already exists
        foreach (EdenContact contact : playerContacts)
        {
            if (contact && contact.GetContactId() == contactId)
                return false; // Contact already exists
        }
        
        // Create new contact
        ref EdenContact newContact = new EdenContact();
        newContact.SetContactId(contactId);
        newContact.SetName(contactName);
        newContact.SetPhoneNumber(phoneNumber != "" ? phoneNumber : GetPlayerPhoneNumber(contactId));
        newContact.SetContactType("player");
        newContact.SetIsEmergency(false);
        
        playerContacts.Insert(newContact);
        
        Print(string.Format("EdenCommunicationManager: Contact %1 added for player %2", contactName, playerId));
        return true;
    }
    
    //! Remove contact
    bool RemoveContact(string playerId, string contactId)
    {
        if (!m_mPlayerContacts.Contains(playerId))
            return false;
            
        ref array<ref EdenContact> playerContacts = m_mPlayerContacts.Get(playerId);
        
        for (int i = playerContacts.Count() - 1; i >= 0; i--)
        {
            EdenContact contact = playerContacts[i];
            if (contact && contact.GetContactId() == contactId)
            {
                playerContacts.RemoveOrdered(i);
                Print(string.Format("EdenCommunicationManager: Contact %1 removed for player %2", contactId, playerId));
                return true;
            }
        }
        
        return false;
    }
    
    //! Get player contacts
    array<ref EdenContact> GetPlayerContacts(string playerId)
    {
        if (!m_mPlayerContacts.Contains(playerId))
        {
            // Initialize with emergency contacts
            ref array<ref EdenContact> contacts = new array<ref EdenContact>();
            foreach (EdenContact emergencyContact : m_aEmergencyContacts)
            {
                contacts.Insert(emergencyContact);
            }
            m_mPlayerContacts.Set(playerId, contacts);
            return contacts;
        }
        
        return m_mPlayerContacts.Get(playerId);
    }
    
    //! Get or create player phone number
    string GetPlayerPhoneNumber(string playerId)
    {
        if (m_mPlayerPhoneNumbers.Contains(playerId))
            return m_mPlayerPhoneNumbers.Get(playerId);
            
        // Generate new phone number
        string phoneNumber = (m_iNextPhoneNumber++).ToString();
        m_mPlayerPhoneNumbers.Set(playerId, phoneNumber);
        
        return phoneNumber;
    }
    
    //! Generate call ID
    protected string GenerateCallId()
    {
        return string.Format("CALL_%1_%2", GetGame().GetWorld().GetWorldTime(), Math.RandomInt(1000, 9999));
    }
    
    //! Get player name
    protected string GetPlayerName(string playerId)
    {
        // TODO: Implement actual player name lookup
        return string.Format("Player_%1", playerId);
    }
    
    //! Save message to database
    protected void SaveMessageToDatabase(EdenMessage message)
    {
        // TODO: Implement database saving
        Print(string.Format("EdenCommunicationManager: Saving message to database (not yet implemented)"));
    }
    
    //! Cleanup old messages
    protected void CleanupOldMessages()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int maxAge = 24 * 60 * 60; // 24 hours
        
        // Clean up global messages
        for (int i = m_aAllMessages.Count() - 1; i >= 0; i--)
        {
            EdenMessage message = m_aAllMessages[i];
            if (message && (currentTime - message.GetTimestamp()) > maxAge)
            {
                m_aAllMessages.RemoveOrdered(i);
            }
        }
        
        // Clean up player messages
        foreach (string playerId, ref array<ref EdenMessage> messages : m_mPlayerMessages)
        {
            for (int i = messages.Count() - 1; i >= 0; i--)
            {
                EdenMessage message = messages[i];
                if (message && (currentTime - message.GetTimestamp()) > maxAge)
                {
                    messages.RemoveOrdered(i);
                }
            }
        }
        
        // Clean up completed dispatch calls
        for (int i = m_aActiveDispatchCalls.Count() - 1; i >= 0; i--)
        {
            EdenDispatchCall call = m_aActiveDispatchCalls[i];
            if (call && !call.IsActive() && (currentTime - call.GetCompletedTime()) > maxAge)
            {
                m_aActiveDispatchCalls.RemoveOrdered(i);
            }
        }
        
        Print("EdenCommunicationManager: Old messages and calls cleaned up");
    }
    
    //! Getters
    array<ref EdenMessage> GetAllMessages() { return m_aAllMessages; }
    array<ref EdenContact> GetEmergencyContacts() { return m_aEmergencyContacts; }
    array<ref EdenDispatchCall> GetActiveDispatchCalls() { return m_aActiveDispatchCalls; }
}

//! Communication Manager class for easy access
class EdenCommunicationManager
{
    protected static EdenCommunicationManager s_Instance;
    protected EdenCommunicationManagerComponent m_Component;
    
    //! Get singleton instance
    static EdenCommunicationManager GetInstance()
    {
        if (!s_Instance)
            s_Instance = new EdenCommunicationManager();
        return s_Instance;
    }
    
    //! Initialize with component
    void Initialize(EdenCommunicationManagerComponent component)
    {
        m_Component = component;
    }
    
    //! Get component
    EdenCommunicationManagerComponent GetComponent()
    {
        return m_Component;
    }
}
