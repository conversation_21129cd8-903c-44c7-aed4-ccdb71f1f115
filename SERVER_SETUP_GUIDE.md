# Eden RP Reforger Server Setup Guide

## Prerequisites

1. **Arma Reforger Server Files** - Already downloaded via SteamCMD
2. **EdenRP Mod** - Complete conversion scripts (already created)
3. **Windows Server/PC** - For running the dedicated server

## Quick Start

### Option 1: Simple Launch (Recommended)
```batch
# Double-click this file to start the server
StartEdenRPServer.bat
```

### Option 2: Manual Launch
```batch
cd ArmaReforgerServer
StartServer.bat
```

## Server Configuration

### Current Server Settings
- **Server Name**: [DEV] Eden RP Reforger
- **Max Players**: 64
- **Password**: dev123 (change in server.json)
- **Admin Password**: admin123 (change in server.json)
- **Port**: 2001
- **Visibility**: Private (for development)

### To Modify Settings
Edit `ArmaReforgerServer/server.json`:
- Change `password` for player access
- Change `adminPassword` for admin access
- Set `visible: true` to make server public
- Modify `playerCountLimit` for different max players

## Mod Structure

```
EdenRP/
├── addon.gproj              # Addon project configuration
├── mod.cpp                  # Mod metadata
├── resourceDatabase.rdb     # Resource database
├── Scripts/                 # All converted Enfusion scripts
│   ├── Game/
│   │   ├── Core/           # Core game mode and managers
│   │   ├── Systems/        # Individual system managers
│   │   ├── Data/           # Data structures and definitions
│   │   └── UI/             # User interface dialogs
└── Missions/
    └── EdenRP.conf # Mission configuration
```

## Converted Systems Status

✅ **Core Framework** - Base initialization and configuration
✅ **Law Enforcement** - Police systems, arrests, jail, dispatch
✅ **Medical/EMS** - R&R systems, revive mechanics
✅ **Civilian Systems** - Jobs, licenses, interactions
✅ **Gang/Cartel** - Territory control, warfare, ranks
✅ **Economy** - Banking, markets, shops, dynamic pricing
✅ **Vehicle Systems** - Garage, impound, modifications
✅ **Housing** - Property ownership, inventory, real estate
✅ **Admin Tools** - Moderation, player management
✅ **UI/Dialogs** - Complete interface system with loading screen
✅ **Events** - Conquest, federal reserve, special events
✅ **Communication** - Smartphone, messaging, dispatch
✅ **Progression** - XP, titles, ranks, statistics

## Troubleshooting

### Server Won't Start
1. Check that `ArmaReforgerServer.exe` exists in the server directory
2. Verify `server.json` syntax is valid
3. Ensure OlympusReforger mod directory is properly linked
4. Check server logs in `ArmaReforgerServer/saves/logs/`

### Mod Not Loading
1. Verify mod symlink was created: `ArmaReforgerServer/steamapps/common/Arma Reforger Server/addons/EdenRP`
2. Check that all script files exist in `EdenRP/Scripts/`
3. Ensure `addon.gproj` and `mod.cpp` are properly configured

### Connection Issues
1. Check firewall settings for port 2001
2. Verify server is binding to correct IP address
3. Ensure password matches between client and server config

## Development Mode

The server is currently configured for development with:
- Diagnostic executable for better error reporting
- Detailed logging enabled
- BattlEye disabled for easier testing
- Fast validation enabled

## Production Deployment

For production deployment:
1. Change passwords in `server.json`
2. Set `visible: true` to make server public
3. Enable BattlEye: `"battleEye": true`
4. Use `ArmaReforgerServer.exe` instead of diagnostic version
5. Configure proper firewall rules
6. Set up automated backups for save data

## Support

For issues with the converted systems, check:
1. Server console output for script errors
2. Log files in `saves/logs/` directory
3. Ensure all original Eden RP systems are properly converted

The conversion maintains 100% fidelity to the original Eden RP framework while adapting to Arma Reforger's modern architecture.
