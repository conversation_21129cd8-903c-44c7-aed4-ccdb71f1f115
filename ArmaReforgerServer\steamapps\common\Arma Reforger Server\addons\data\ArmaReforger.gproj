GameProject {
 ID "ArmaReforger"
 GUID "58D0FB3206B6F859"
 TITLE "Arma Reforger"
 Dependencies {
  "5614BBCCBB55ED1C"
 }
 EngineSettingsPath "ReforgerEngineSettings.conf"
 GameSettingsPath "ReforgerGameSettings.conf"
 InputSettingsPath "ReforgerUserInput.conf"
 BrushLibraryPath "Brushes"
 WelcomeScreenConfig "{458F9E622E278CE3}Configs/Workbench/welcomeScreen.conf"
 WBDocumentationConfig "{1D6445998412994C}Configs/Workbench/documentationLinks.conf"
 Configurations {
  GameProjectConfig PC {
   WorldFile "{077CF7C916637442}worlds/MainMenuWorld/MainMenuWorld.ent"
   SplashScreenClass "SplashScreen"
   DefaultSettings EngineUserSettings "{5702F087A781A9FC}" {
    DisplayUserSettings DisplayUserSettings "{F1B1E6EA2D4A7B39}" {
     PPQuality PPEffectsSettings "{54D6A197983B8089}" {
      HBAO 1
      SSDO 1
      SSR 1
      UnderWater 1
      PPAA 2
      Rain 1
     }
    }
    GraphicsQualitySettings GraphicsQualitySettings "{52E103D890868E2C}" {
     ObjectDrawDistanceScale Ultra
    }
    GrassMaterialSettings GrassMaterialSettings "{7441A756719A8542}" {
     Distance 116
     Lod 3
    }
    InputDeviceUserSettings InputDeviceUserSettings "{58971ACF26F20D87}" {
     MouseSpeed 0.001 0.001
     GamepadProfile InputProfileGamepad "{58971ACF24C7E1E9}" {
      LeftThumbHorizontal InputProfileKey "{58971ACF1939286A}" {
       Curve {
        0 0 0.5 0.2 0.9 0.5 1 1
       }
      }
      LeftThumbVertical InputProfileKey "{58971ACF717F004B}" {
       Curve {
        0 0 0.5 0.2 0.9 0.5 1 1
       }
      }
      RightThumbHorizontal InputProfileKey "{58971ACF773A07AA}" {
       Curve {
        0 0 0.5 0.2 0.9 0.5 1 1
       }
      }
      RightThumbVertical InputProfileKey "{58971ACF766CCA86}" {
       Curve {
        0 0 0.5 0.2 0.9 0.5 1 1
       }
      }
      LeftTrigger InputProfileKey "{58971ACF746B42F0}" {
       Curve {
        0 0 0.5 0.2 0.9 0.5 1 1
       }
      }
      RightTrigger InputProfileKey "{58971ACF6B4C0E9D}" {
       Curve {
        0 0 0.5 0.2 0.9 0.5 1 1
       }
      }
     }
     JoystickProfile InputProfileJoystick "{5EF8FE9679720836}" {
      Axis00 InputProfileKey "{5EF8FE9678731FE5}" {
       DeadZone 0.01
      }
      Axis01 InputProfileKey "{5EF8FE9673699A5C}" {
       DeadZone 0.01
      }
      Axis02 InputProfileKey "{5EF8FE96710888FF}" {
       DeadZone 0.01
      }
      Axis03 InputProfileKey "{5EF8FE966EA16C18}" {
       DeadZone 0.01
      }
      Axis04 InputProfileKey "{5EF8FE966CC8D96C}" {
       DeadZone 0.01
      }
      Axis05 InputProfileKey "{5EF8FE966A535E3D}" {
       DeadZone 0.01
      }
      Axis06 InputProfileKey "{5EF8FE9668498B66}" {
       DeadZone 0.01
      }
      Axis07 InputProfileKey "{5EF8FE96659AC8F6}" {
       DeadZone 0.01
      }
      Axis08 InputProfileKey "{5EF8FE9664527460}" {
       DeadZone 0.01
      }
      Axis09 InputProfileKey "{5EF8FE9662FFB50C}" {
       DeadZone 0.01
      }
     }
     GyroSettings GyroSettings "{62ACE215FC1EACFE}" {
     }
    }
    MaterialSystemUserSettings MaterialSystemUserSettings "{51DE9D5EBA2E0DF7}" {
     TextureFilter 2
    }
    PipelineUserSettings PipelineUserSettings "{B272A67D8DFAC7D2}" {
     ShadowQuality High
     TightCascades 0
     GBuffer AmbientDepthNear
    }
    PostprocessUserSettings PostprocessUserSettings "{B272A67DF850DADD}" {
     Postprocess 1
    }
    TerrainGenMaterialSettings TerrainGenMaterialSettings "{BBABE688B9061B69}" {
     Detail 2
    }
    VideoUserSettings VideoUserSettings "{B272A67DB18E04F9}" {
     Vsynch 1
     Fsaa NONE
     EnvironmentQuality HIGH
     Atoc "GRASS&TREES"
    }
   }
   AudioGlobalConfig AudioGlobalConfig "{55C4D22BA764B5E5}" {
    TerrainAttenuation {
     CurvePoint "1" {
      X 90
      Y -20
      Shape Exponential
      Modifier 0.6
     }
     CurvePoint "2" {
      X 180
     }
    }
    TerrainLPFRolloff {
     CurvePoint "1" {
      X 90
      Y 20
      Shape Exponential
      Modifier 1.5
     }
     CurvePoint "2" {
      X 180
     }
    }
   }
   ChimeraGlobalConfig ChimeraGlobalConfig "{6F12664687156A7B}" {
    DefaultPlayerController "{6E2BB64764E3BE9B}Prefabs/Characters/Core/DefaultPlayerController.et"
    "Maximum View Distance" 5000
    "Minimum View Distance" 500
    Ballistics BallisticsConfig "{527CEE05A02D96BD}" {
     CavitationConstant 800
    }
    InputButtonLayoutConfig "{2F14B8749FE911B4}Configs/WidgetLibrary/SCR_InputButton/SCR_InputButtonLayout.conf"
   }
   DistantShadowsQualityProfiles DistantShadowsQualityProfiles "{52DADA2E1DD82B9F}" {
    DistantShadowsQualityProfiles {
     DistantShadowsProfile Low {
      TextureSize "1024"
      MinPixelsPerFrame 16384
      MaxPixelsPerFrame 65536
      TerrainCascadesNum 1
      ObjectsMetersPerPixelNear 2
     }
     DistantShadowsProfile Medium {
      ObjectsMetersPerPixelFar 3
     }
     DistantShadowsProfile High {
      ObjectsCascadesNum 2
      ObjectsMetersPerPixelNear 1
      ObjectsMetersPerPixelFar 2
     }
     DistantShadowsProfile Ultra {
      ObjectsCascadesNum 2
      ObjectsMetersPerPixelNear 1.5
      ObjectsMetersPerPixelFar 4
      AOCascadesNum 2
      ObjHeightmapTextureSize "4096"
     }
    }
   }
   EnvironmentQualityProfile EnvironmentQualityProfile "{5698B4EE1E79AB65}" {
    EnvironmentQualityProfiles {
     EnvironmentProfile LOW {
      AtmosphereProfile AtmosphereProfile "{5698B4EEDC74C31B}" {
       CameraVolumeResolution 32 32 16
       SkyViewMaxSamples 16
      }
      VolCloudsProfile VolCloudsProfile "{5698B4EE1456B733}" {
       NumSamples 110
       DetailNoiseResolution 16
      }
     }
     EnvironmentProfile MEDIUM {
      AtmosphereProfile AtmosphereProfile "{5698B4EEDC2925E0}" {
       SkyViewMaxSamples 32
      }
      VolCloudsProfile VolCloudsProfile "{5698B4EE143440C8}" {
       NumSamples 150
      }
     }
     EnvironmentProfile HIGH {
      AtmosphereProfile AtmosphereProfile "{5698B4EEC5AC3DCF}" {
       HighQualityCameraVolume 1
      }
      VolCloudsProfile VolCloudsProfile "{5698B4EE159ABBAA}" {
       NumSamples 200
       DetailNoiseResolution 64
      }
     }
     EnvironmentProfile ULTRA {
      AtmosphereProfile AtmosphereProfile "{5698B4EF98568651}" {
       SkyViewResolution 192 108
       CameraVolumeResolution 64 64 64
       HighQualityCameraVolume 1
       RaymarchMinSamples 32
       RaymarchMaxSamples 64
      }
      VolCloudsProfile VolCloudsProfile "{5698B4EF8494AB67}" {
       NumSamples 256
       ShadowmapResolution 256
       ShadowmapDispatchesPerFrame 4
       DetailNoiseResolution 64
      }
     }
    }
   }
   FurQualityProfiles FurQualityProfiles "{5B0D40B3654D6453}" {
    FurQualityProfiles {
     FurQualityProfile Lowest {
      ShellCountLerpCoef 0
      ShellCountMax 4
     }
     FurQualityProfile Low {
      ShellCountLerpCoef 0.2
      ShellCountMax 6
     }
     FurQualityProfile Medium {
      ShellCountMax 10
     }
     FurQualityProfile High {
      ShellCountLerpCoef 0.8
      ShellCountMax 16
     }
     FurQualityProfile Ultra {
      ShellCountLerpCoef 1
      ShellCountMax 24
     }
    }
   }
   InputManagerSettings InputManagerSettings "{50A48858902D5F64}" {
    Default "{795184CF9AD764DB}Configs/System/chimeraInputCommon.conf"
    UiMappings "{0AD37213EFEB0B61}Configs/System/chimeraMapping.conf"
    HoldDuration 250
    ClickDuration 250
    DoubleClickDuration 250
    RepeatInitialInterval 400
    RepeatInterval 50
   }
   MenuManagerSettings MenuManagerSettings "{50C890C996B0A6FC}" {
    MenuConfigs {
     "{C747AFB6B750CE9A}Configs/System/chimeraMenus.conf"
    }
   }
   NavmeshManager NavmeshManager "{B462A58A882A1448}" {
    ProjectSettings {
     NavmeshProjectConfig Soldiers {
      PhysicsInteractionLayer 2097152
      DefaultWalkableFlags 62
      AreaTypeForTerrain 5
      FlagTypes {
       NavmeshAreaFlagConfig JumpOver {
       }
       NavmeshAreaFlagConfig Swim {
       }
       NavmeshAreaFlagConfig Climb {
       }
       NavmeshAreaFlagConfig Open {
       }
       NavmeshAreaFlagConfig Avert {
       }
      }
      AreaTypes {
       NavmeshAreaTypeConfig Fence {
        AreaFlags 4
        AreaCost 25
       }
       NavmeshAreaTypeConfig Avoid {
        AreaCost 50
       }
       NavmeshAreaTypeConfig Roof {
        AreaFlags 66
        AreaCost 20
       }
       NavmeshAreaTypeConfig Water {
        AreaFlags 10
        AreaCost 50
       }
       NavmeshAreaTypeConfig Asphalt {
       }
       NavmeshAreaTypeConfig OffRoad {
        AreaCost 3
       }
       NavmeshAreaTypeConfig DirtRoad {
        AreaCost 2.5
       }
       NavmeshAreaTypeConfig Ladder {
        AreaFlags 18
        AreaCost 20
       }
       NavmeshAreaTypeConfig Door {
        AreaFlags 34
        AreaCost 10
       }
      }
     }
     NavmeshProjectConfig BTRlike {
      PhysicsInteractionLayer 10485760
      ResponseIndexToAreaTypeConversion {
       ResponseIndexToAreaTypeConversionConfig "{62CED692F1A6E7FA}" {
        ResponseIndex "TinyDestructible"
        AreaType 6
       }
       ResponseIndexToAreaTypeConversionConfig "{62CED690D51E76CF}" {
        ResponseIndex "SmallDestructible"
        AreaType 6
       }
       ResponseIndexToAreaTypeConversionConfig "{62F276B19BCB8017}" {
        ResponseIndex "MediumDestructible"
        AreaType 6
       }
       ResponseIndexToAreaTypeConversionConfig "{62CED690D570E85F}" {
        ResponseIndex "LargeDestructible"
        AreaType 7
       }
       ResponseIndexToAreaTypeConversionConfig "{62CED690D5686C06}" {
        ResponseIndex "HugeDestructible"
        AreaType 7
       }
      }
      AreaTypeForTerrain 4
      FlagTypes {
       NavmeshAreaFlagConfig Destructible {
       }
       NavmeshAreaFlagConfig HardDestructible {
       }
       NavmeshAreaFlagConfig Swim {
       }
      }
      AreaTypes {
       NavmeshAreaTypeConfig Door {
        AreaCost 50
       }
       NavmeshAreaTypeConfig Water {
        AreaFlags 18
        AreaCost 100
       }
       NavmeshAreaTypeConfig Asphalt {
       }
       NavmeshAreaTypeConfig OffRoad {
        AreaCost 1.2
       }
       NavmeshAreaTypeConfig DirtRoad {
        AreaCost 1.1
       }
       NavmeshAreaTypeConfig Destructible {
        AreaFlags 6
        AreaCost 20
       }
       NavmeshAreaTypeConfig HardDestructible {
        AreaFlags 10
        AreaCost 80
       }
      }
     }
     NavmeshProjectConfig LowRes {
      PhysicsInteractionLayer 10485760
      AreaTypes {
       NavmeshAreaTypeConfig Water {
        AreaCost 50
       }
      }
     }
    }
   }
   PhysicsSettings PhysicsSettings "{50C8B06FB4D5FA52}" {
    Margin 0.025
    GridBP 0
    Interactions InteractionDef "{50C908F1A83672E2}" {
     Layers {
      "Default" "Static" "VehicleCast" "Cover" "Character" "Projectile" "Vehicle" "Terrain" "Dynamic" "Debris" "Ragdoll" "Vegetation" "CharNoCollide" "Camera" "FireGeometry" "ViewGeometry" "Ladder" "Interaction" "Foliage" "CharCollide" "CharacterAI" "Navmesh" "Water" "NavmeshVehicle" "VehicleSimple" "VehicleComplex" "Unused" "Mine" "Weapon" "CharSpecialCollisionNoCollide"
     }
     LayerPresets {
      LayerPreset None {
      }
      LayerPreset Main {
       Mask 1
      }
      LayerPreset Cover {
       Mask 8
      }
      LayerPreset Character {
       Mask 16
      }
      LayerPreset Projectile {
       Mask 32
      }
      LayerPreset Vehicle {
       Mask 50331648
      }
      LayerPreset VehicleCast {
       Mask 4
      }
      LayerPreset VehicleFire {
       Mask 50348032
      }
      LayerPreset BasicVehicleObstacles {
       Mask 2097152
      }
      LayerPreset VehicleFireView {
       Mask 50380800
      }
      LayerPreset Building {
       Mask 10485762
      }
      LayerPreset BuildingNoNavmesh {
       Mask 2
      }
      LayerPreset BuildingView {
       Mask 10518530
      }
      LayerPreset BuildingViewNoNavmesh {
       Mask 32770
      }
      LayerPreset BuildingFire {
       Mask 10502146
      }
      LayerPreset BuildingFireNoNavmesh {
       Mask 16386
      }
      LayerPreset BuildingFireView {
       Mask 10534914
      }
      LayerPreset BuildingFireViewNoNavmesh {
       Mask 49154
      }
      LayerPreset ItemFireView {
       Mask 20480
      }
      LayerPreset DoorFireView {
       Mask 10534914
      }
      LayerPreset Weapon {
       Mask 268435456
      }
      LayerPreset WeaponFire {
       Mask 268451840
      }
      LayerPreset Terrain {
       Mask 2097280
      }
      LayerPreset TreeFireView {
       Mask 10534914
      }
      LayerPreset CharNoCollide {
       Mask 4096
      }
      LayerPreset FireGeo {
       Mask 16384
      }
      LayerPreset RockFireView {
       Mask 2146306
      }
      LayerPreset Debris {
       Mask 512
      }
      LayerPreset Tree {
       Mask 10518530
      }
      LayerPreset Door {
       Mask 10616834
      }
      LayerPreset TreePart {
       Mask 512
      }
      LayerPreset Interaction {
       Mask 131072
      }
      LayerPreset Ladder {
       Mask 2162690
      }
      LayerPreset Bush {
       Mask 262144
      }
      LayerPreset Foliage {
       Mask 262144
      }
      LayerPreset Wheel {
       Mask 1064960
      }
      LayerPreset WheelViewMine {
       Mask 167804928
      }
      LayerPreset PropFireView {
       Mask 10535168
      }
      LayerPreset PropFireViewNoNavmesh {
       Mask 49408
      }
      LayerPreset PropView {
       Mask 10518784
      }
      LayerPreset PropViewNoNavmesh {
       Mask 33024
      }
      LayerPreset Prop {
       Mask 10486016
      }
      LayerPreset PropNoNavmesh {
       Mask 256
      }
      LayerPreset CharacterAI {
       Mask 1048576
      }
      LayerPreset Glass {
       Mask 2
      }
      LayerPreset GlassFire {
       Mask 16386
      }
      LayerPreset FireView {
       Mask 49152
      }
      LayerPreset ViewGeo {
       Mask 32768
      }
      LayerPreset VehicleComplex {
       Mask 33554432
      }
      LayerPreset VehicleComplexView {
       Mask 33587200
      }
      LayerPreset VehicleSimple {
       Mask 16777216
      }
      LayerPreset VehicleRotorDisc {
       Mask 50348032
      }
      LayerPreset CharacterFireGeoRagdoll {
       Mask 17408
      }
      LayerPreset InteractionFireGeo {
       Mask 147456
      }
      LayerPreset MineTrigger {
       Mask 134217728
      }
      LayerPreset MineTriggerFire {
       Mask 134234112
      }
      LayerPreset MineTriggerComplex {
       Mask 167772160
      }
      LayerPreset Liquids {
       Mask 4194304
      }
      LayerPreset CharSpecialCollisionNoCollide {
       Mask 547356672
      }
     }
     ResponseIndices {
      ResponseIndex Normal {
      }
      ResponseIndex TinyMomentum {
       Index 1
      }
      ResponseIndex SmallMomentum {
       Index 2
      }
      ResponseIndex MediumMomentum {
       Index 3
      }
      ResponseIndex LargeMomentum {
       Index 4
      }
      ResponseIndex HugeMomentum {
       Index 5
      }
      ResponseIndex TinyDestructible {
       Index 6
      }
      ResponseIndex SmallDestructible {
       Index 7
      }
      ResponseIndex MediumDestructible {
       Index 8
      }
      ResponseIndex LargeDestructible {
       Index 9
      }
      ResponseIndex HugeDestructible {
       Index 10
      }
      ResponseIndex NoCollision {
       Index 11
      }
     }
     Matrix {
      InteractionMatrixRow Default {
       Interacts 286272503
      }
      InteractionMatrixRow Static {
       Interacts 286267222
      }
      InteractionMatrixRow VehicleCast {
       Interacts 131
      }
      InteractionMatrixRow Cover {
       Interacts 0
      }
      InteractionMatrixRow Character {
       Interacts 34089424
      }
      InteractionMatrixRow Projectile {
       Interacts 4210816
      }
      InteractionMatrixRow Vehicle {
       Interacts 1059264
      }
      InteractionMatrixRow Terrain {
       Interacts 286275332
      }
      InteractionMatrixRow Dynamic {
       Interacts 33565952
      }
      InteractionMatrixRow Debris {
       Interacts 2560
      }
      InteractionMatrixRow Ragdoll {
       Interacts 16778496
      }
      InteractionMatrixRow Vegetation {
       Interacts 16777216
      }
      InteractionMatrixRow CharNoCollide {
       Interacts 2
      }
      InteractionMatrixRow Camera {
       Interacts 34635776
      }
      InteractionMatrixRow FireGeometry {
       Interacts 32
      }
      InteractionMatrixRow ViewGeometry {
       Interacts 0
      }
      InteractionMatrixRow Ladder {
       Interacts 65536
      }
      InteractionMatrixRow Interaction {
       Interacts 0
      }
      InteractionMatrixRow Foliage {
       Interacts 262144
      }
      InteractionMatrixRow CharCollide {
       Interacts 1056784
      }
      InteractionMatrixRow CharacterAI {
       Interacts 34087363
      }
      InteractionMatrixRow Navmesh {
       Interacts 2097152
      }
      InteractionMatrixRow Water {
       Interacts 32
      }
      InteractionMatrixRow NavmeshVehicle {
       Interacts 8388608
      }
      InteractionMatrixRow VehicleSimple {
       Interacts 285215875
      }
      InteractionMatrixRow VehicleComplex {
       Interacts 1057040
      }
      InteractionMatrixRow Unused {
       Interacts 0
      }
      InteractionMatrixRow Mine {
       Interacts 134217728
      }
      InteractionMatrixRow Weapon {
       Interacts 285212803
      }
      InteractionMatrixRow CharSpecialCollisionNoCollide {
       Interacts 536870912
      }
     }
     ResponseMatrix {
      InteractionResponseMatrixRow "{559CE142A0662FAD}" {
       Index1 "TinyMomentum"
       Index2 "TinyDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A27609F4A514D7}" {
       Index1 "SmallMomentum"
       Index2 "TinyDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A27609E48D00F6}" {
       Index1 "SmallMomentum"
       Index2 "SmallDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A27609DECB78ED}" {
       Index1 "MediumMomentum"
       Index2 "TinyDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A27609D7B1B5C2}" {
       Index1 "MediumMomentum"
       Index2 "SmallDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A27609D6A45125}" {
       Index1 "MediumMomentum"
       Index2 "MediumDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A4F2AADB5977A6}" {
       Index1 "LargeMomentum"
       Index2 "TinyDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A4F2AADEE9C661}" {
       Index1 "LargeMomentum"
       Index2 "SmallDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A4F2AAD70900A1}" {
       Index1 "LargeMomentum"
       Index2 "MediumDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A4F2AAD7715113}" {
       Index1 "LargeMomentum"
       Index2 "LargeDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A4F2AACC461B8B}" {
       Index1 "HugeMomentum"
       Index2 "TinyDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A4F2AACCADF437}" {
       Index1 "HugeMomentum"
       Index2 "SmallDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A4F2AACC87970F}" {
       Index1 "HugeMomentum"
       Index2 "MediumDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A4F2AACCCF3E61}" {
       Index1 "HugeMomentum"
       Index2 "LargeDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{58A4F2AAC321F9EA}" {
       Index1 "HugeMomentum"
       Index2 "HugeDestructible"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{59E6C48E878F8B7E}" {
       Index1 "NoCollision"
       Index2 "Normal"
       Response2 NONE
      }
      InteractionResponseMatrixRow "{59E6C48B36463777}" {
       Index1 "NoCollision"
       Index2 "TinyMomentum"
       Response2 NONE
      }
      InteractionResponseMatrixRow "{59E6C48B36E2C9E0}" {
       Index1 "NoCollision"
       Index2 "SmallMomentum"
       Response2 NONE
      }
      InteractionResponseMatrixRow "{59E6C48B37362A41}" {
       Index1 "NoCollision"
       Index2 "MediumMomentum"
       Response2 NONE
      }
      InteractionResponseMatrixRow "{59E6C48B3785420E}" {
       Index1 "NoCollision"
       Index2 "LargeMomentum"
       Response1 NONE
      }
      InteractionResponseMatrixRow "{59E6C48B37D6D1E0}" {
       Index1 "NoCollision"
       Index2 "HugeMomentum"
       Response1 NONE
      }
     }
     LayersFilter 2097151
     LayerProjectile 32
     DefaultParticlePreset ParticleCollisionPreset "{5D9EE44C1B950234}" {
      Name "Terrain & Static & VehSimple"
      LayerMask 16777346
     }
     AdditionalParticlePresets {
      ParticleCollisionPreset "{5D9EE44C68755DF5}" {
       Name "Terrain & FireGeo"
       LayerMask 16512
      }
      ParticleCollisionPreset "{5DC8DBF6E2ACBAF5}" {
       Name "Terrain & Static & Water & Foliage"
       Ocean 1
       LayerMask 4456578
      }
     }
    }
   }
   ResourceManagerSettings ResourceManagerSettings "{50C89C97A0E7987F}" {
    MemoryBudgetMeshes {
     204 224 256 304 384
    }
    MemoryBudgetTextures {
     768 1024 1280 1536 2048
    }
   }
   ScriptProjectManagerSettings ScriptProjectManagerSettings "{AC4BE58770485E4B}" {
    Modules {
     ScriptModulePathClass game {
      Paths {
       "{B92491157EA3E4AD}scripts/Game" "{E9D3C463BA7F39FA}scripts/GameCode"
      }
     }
     ScriptModulePathClass workbench {
      Paths {
       "{8B793EE0B45CA001}scripts/Workbench"
      }
     }
     ScriptModulePathClass workbenchGame {
      Paths {
       "{D153046BD777C56B}scripts/WorkbenchGame"
      }
     }
    }
   }
   SettingProfilesConfig SettingProfilesConfig "{A29326E9BB8CFEBF}" {
    Effects PPEffectsConfig "{54D6C46B5CC2144D}" {
     HBAO PPMaterials4 "{54D67D9F981D0A6F}" {
      Low "{2CA93A3D010C944A}Common/Postprocess/HBAO_Low.emat"
      Medium "{3074D788E570FBDC}Common/Postprocess/HBAO_Medium.emat"
      High "{C434EE0C2C05D4AA}Common/Postprocess/HBAO_High.emat"
      Ultra "{1B3A6E678E786254}Common/Postprocess/HBAO_Ultra.emat"
     }
     GodRays PPMaterials1 "{54D67D9F981D0B9A}" {
      On "{90C363AB5243A434}Common/Postprocess/Sun_GodRays.emat"
     }
     HDR PPMaterials1 "{54D67D9F981D0B42}" {
      On "{9DEECCABE8357209}Common/Postprocess/HDR.emat"
     }
     SSDO PPMaterials2 "{54D67D9F981D0B00}" {
      On "{3C5B205B6CBF1B8E}Common/Postprocess/SSDO.emat"
     }
     SSR PPMaterialSSR "{5856D8D2E19D1FC9}" {
      Low SSRProfile "{5856D8D280474C7A}" {
       WaterSSR SSRWaterProfile "{5856D8D287E97BC2}" {
        NumSamples 24
       }
      }
      Medium SSRProfile "{5856D8D28A92B159}" {
       WaterSSR SSRWaterProfile "{5856D8D283D10CC8}" {
        NumSamples 40
       }
       PostprocessMaterial "{D53830EA1BF69AE5}Common/Postprocess/SSR_Low.emat"
      }
      High SSRProfile "{5856D8D2F7ABDA79}" {
       WaterSSR SSRWaterProfile "{5856D8D28C47A2F2}" {
        NumSamples 56
       }
       PostprocessMaterial "{435152A47CC886E8}Common/Postprocess/SSR_Medium.emat"
      }
      Ultra SSRProfile "{5856D8D2E702C092}" {
       WaterSSR SSRWaterProfile "{5856D8D28DA2D8E6}" {
        NumSamples 72
       }
       PostprocessMaterial "{8A03B7ACCA77B696}Common/Postprocess/SSR.emat"
      }
     }
     UnderWater PPMaterials2 "{54D67D9F981D045A}" {
      On "{5366CEDE2A151631}Terrains/Common/Water/UnderWater/oceanUnderwater.emat"
     }
     PPAA PPMaterialsAA "{54D67D9F981D0419}" {
      "FXAA Low" "{18B15A50707B6FAE}Common/Postprocess/FXAALow.emat"
      "FXAA High" "{ED3FBDDC736206E0}Common/Postprocess/FXAAHigh.emat"
      "SMAA Low" "{A7D2C83592998D29}Common/Postprocess/SMAALow.emat"
      "SMAA High" "{CE543C0DB56915C8}Common/Postprocess/SMAAHigh.emat"
     }
     Rain PPMaterials4 "{54D67D9F981D0583}" {
      Low "{D3E32E11C5116D9E}Common/Postprocess/rainLow.emat"
      Medium "{FA4DE95A7276143D}Common/Postprocess/rain.emat"
      High "{FA4DE95A7276143D}Common/Postprocess/rain.emat"
      Ultra "{FA4DE95A7276143D}Common/Postprocess/rain.emat"
     }
    }
    OverallPresets {
     OverallSettingPreset LOW {
      GeometricDetail 1
      TextureDetail 3
      Detail 0
      Distance 60
      Lod 2
      ObjectDrawDistanceScale High
      PPQuality PPEffectsSettings "{5702C6C01BA04148}" {
       UnderWater 1
       PPAA 4
      }
     }
     OverallSettingPreset MEDIUM {
      TextureDetail 2
      Distance 80
      Lod 2
      ObjectDrawDistanceScale High
      Fsaa "2"
      DistantShadowsQuality HIGH
      EnvironmentQuality MEDIUM
      ShadowQuality High
      PPQuality PPEffectsSettings "{5702C6C0AFD08CB1}" {
       HBAO 1
       UnderWater 1
       Rain 1
      }
     }
     OverallSettingPreset HIGH {
      GeometricDetail 3
      TextureDetail 1
      Detail 2
      Fsaa "2"
      DistantShadowsQuality HIGH
      EnvironmentQuality MEDIUM
      ShadowQuality High
      PPQuality PPEffectsSettings "{5702C6C1491F20B1}" {
       HBAO 1
       SSDO 1
       SSR 2
       UnderWater 1
       Rain 2
      }
     }
     OverallSettingPreset ULTRA {
      GeometricDetail 4
      TextureDetail 0
      Detail 3
      Distance 120
      Fsaa "2"
      Atoc "GRASS&TREES"
      DistantShadowsQuality ULTRA
      EnvironmentQuality HIGH
      RenderFormat HIGH
      ShadowQuality High
      PPQuality PPEffectsSettings "{5702C6C1EAF048BA}" {
       HBAO 3
       SSDO 1
       SSR 3
       UnderWater 1
       Rain 3
      }
     }
     OverallSettingPreset DEFAULT_SERIES_X {
      GeometricDetail 1
      TextureDetail 1
      Detail 2
      Distance 70
      Lod 2
      ObjectDrawDistanceScale High
      Fsaa "2"
      Atoc "GRASS&TREES"
      DistantShadowsQuality HIGH
      ShadowQuality High
      PPQuality PPEffectsSettings "{5918EC9E7E1FDF0C}" {
       HBAO 1
       SSDO 1
       SSR 1
       UnderWater 1
      }
     }
     OverallSettingPreset DEFAULT_SERIES_S {
      GeometricDetail 1
      TextureDetail 2
      Detail 1
      Distance 50
      Lod 2
      ObjectDrawDistanceScale High
      Fsaa "2"
      DistantShadowsQuality LOW
      ShadowQuality High
      PPQuality PPEffectsSettings "{5918EC9E7FEBB6E1}" {
       HBAO 1
       UnderWater 1
      }
     }
     OverallSettingPreset SERIES_X_PERFORMANCE {
      GeometricDetail 1
      TextureDetail 1
      Detail 1
      Distance 70
      Lod 2
      ObjectDrawDistanceScale High
      Fsaa "2"
      Atoc "GRASS&TREES"
      DistantShadowsQuality HIGH
      ShadowQuality High
      PPQuality PPEffectsSettings "{5918EC9E7E1FDF0C}" {
       HBAO 1
       UnderWater 1
      }
     }
     OverallSettingPreset SERIES_S_PERFORMANCE {
      GeometricDetail 0
      TextureDetail 2
      Detail 0
      Distance 50
      Lod 2
      ObjectDrawDistanceScale High
      DistantShadowsQuality LOW
      PPQuality PPEffectsSettings "{5918EC9E7FEBB6E1}" {
       UnderWater 1
       PPAA 2
      }
     }
     OverallSettingPreset PS5_QUALITY {
      GeometricDetail 1
      TextureDetail 1
      Detail 2
      Distance 70
      Lod 2
      ObjectDrawDistanceScale High
      DistantShadowsQuality HIGH
      EnvironmentQuality MEDIUM
      ShadowQuality High
      PPQuality PPEffectsSettings "{621EBDC4636A6EFE}" {
       HBAO 1
       SSDO 1
       UnderWater 1
       PPAA 4
      }
     }
     OverallSettingPreset PS5_PERFORMANCE {
      GeometricDetail 1
      TextureDetail 1
      Detail 1
      Distance 70
      Lod 2
      ObjectDrawDistanceScale High
      DistantShadowsQuality HIGH
      ShadowQuality High
      PPQuality PPEffectsSettings "{621EBDC5A0541DEB}" {
       UnderWater 1
       PPAA 4
      }
     }
     OverallSettingPreset PS5_PRO_QUALITY {
      GeometricDetail 1
      TextureDetail 1
      Detail 2
      Distance 70
      Lod 2
      ObjectDrawDistanceScale High
      DistantShadowsQuality HIGH
      EnvironmentQuality MEDIUM
      ShadowQuality High
      PPQuality PPEffectsSettings "{625A81114CAAA49C}" {
       HBAO 1
       SSDO 1
       UnderWater 1
       PPAA 4
      }
     }
     OverallSettingPreset PS5_PRO_PERFORMANCE {
      GeometricDetail 1
      TextureDetail 1
      Detail 1
      Distance 70
      Lod 2
      ObjectDrawDistanceScale High
      DistantShadowsQuality HIGH
      ShadowQuality High
      PPQuality PPEffectsSettings "{625A811177748F25}" {
       UnderWater 1
       PPAA 4
      }
     }
    }
   }
   ShadowsQualityProfiles ShadowsQualityProfiles "{54C28EF5EC5AEF58}" {
    ShadowsQualityProfiles {
     ShadowsProfile Low {
      Cascades {
       ShadowCascadeProfile "{54C28EF51B8E2F21}" {
        TextureSize "512"
        Bias 0.03
        SlopeBias 0.03
       }
       ShadowCascadeProfile "{54C8969EC309A19F}" {
        TextureSize "512"
        Bias 0.1
        SlopeBias 0.1
       }
       ShadowCascadeProfile "{54C8969EC348D15C}" {
        TextureSize "512"
        Bias 0.4
        SlopeBias 0.3
       }
      }
      MaxShadowDistance 80
      MaxShadowCasterDistance 160
      ShadowMaskBlur 0
      ShadowMaskHalfRes 1
     }
     ShadowsProfile Medium {
      Type ShadowMedium
      Cascades {
       ShadowCascadeProfile "{54C89835B4D29713}" {
        Bias 0.01
        SlopeBias 0.02
       }
       ShadowCascadeProfile "{54C89835B591E4A1}" {
        TextureSize "512"
        Bias 0.02
        SlopeBias 0.04
       }
       ShadowCascadeProfile "{54C89835B5D1460D}" {
        TextureSize "512"
        Bias 0.1
        SlopeBias 0.15
       }
       ShadowCascadeProfile "{54C89835B6E08D71}" {
        TextureSize "512"
        Bias 0.5
        SlopeBias 0.3
       }
      }
      MaxShadowDistance 100
      MaxShadowCasterDistance 200
      ShadowMaskBlur 0
     }
     ShadowsProfile High {
      Type ShadowHigh
      Cascades {
       ShadowCascadeProfile "{54C8983522841939}" {
        TextureSize "2048"
        TextureFormat D32
        Bias 0.01
        SlopeBias 0.01
       }
       ShadowCascadeProfile "{54C8983522FD9585}" {
        TextureFormat D32
        Bias 0.02
        SlopeBias 0.04
       }
       ShadowCascadeProfile "{54C89835233A93BF}" {
        TextureSize "512"
        TextureFormat D32
        Bias 0.1
        SlopeBias 0.15
       }
       ShadowCascadeProfile "{54C89835237FEFD8}" {
        TextureSize "512"
        TextureFormat D32
        Bias 0.5
        SlopeBias 0.5
       }
      }
      MaxShadowDistance 150
      ShadowMaskBlur 0
     }
     ShadowsProfile Ultra {
      Type ShadowUltra
      Cascades {
       ShadowCascadeProfile "{54C898348591DACD}" {
        TextureSize "4096"
        TextureFormat D32
        Bias 0.01
        SlopeBias 0.01
       }
       ShadowCascadeProfile "{54C8983485D1C897}" {
        TextureSize "2048"
        TextureFormat D32
        Bias 0.01
        SlopeBias 0.03
       }
       ShadowCascadeProfile "{54C89834860D457A}" {
        TextureFormat D32
        Bias 0.15
        SlopeBias 0.2
       }
       ShadowCascadeProfile "{54C898348678108B}" {
        TextureSize "512"
        TextureFormat D32
        Bias 1.5
        SlopeBias 1.5
       }
      }
      MaxShadowDistance 200
     }
     ShadowsProfile EnvCaptureLow {
      Type ShadowEnvCaptureLow
      Cascades {
       ShadowCascadeProfile "{54C89834EDC98DE6}" {
        TextureSize "256"
        Bias 0.1
        SlopeBias 0.1
       }
       ShadowCascadeProfile "{54C89834E9AF8CB4}" {
        TextureSize "256"
        Bias 0.4
        SlopeBias 0.3
       }
      }
      MaxShadowDistance 50
      MaxShadowCasterDistance 80
      ShadowMaskBlur 0
      ShadowMaskHalfRes 0
      StabilizeCascades None
     }
     ShadowsProfile EnvCaptureMedium {
      Type ShadowEnvCaptureMedium
      Cascades {
       ShadowCascadeProfile "{54C89834EDC98DE6}" {
        TextureSize "256"
        Bias 0.03
        SlopeBias 0.03
       }
       ShadowCascadeProfile "{54C89834E935859B}" {
        TextureSize "256"
        Bias 0.1
        SlopeBias 0.1
       }
       ShadowCascadeProfile "{54C89834E9AF8CB4}" {
        TextureSize "256"
        Bias 0.4
        SlopeBias 0.3
       }
      }
      MaxShadowDistance 80
      MaxShadowCasterDistance 120
      ShadowMaskBlur 0
      ShadowMaskHalfRes 0
      StabilizeCascades None
     }
     ShadowsProfile EnvCaptureHigh {
      Type ShadowEnvCaptureHigh
      Cascades {
       ShadowCascadeProfile "{54C89834EDC98DE6}" {
        TextureSize "512"
        Bias 0.03
        SlopeBias 0.03
       }
       ShadowCascadeProfile "{54C89834E935859B}" {
        TextureSize "256"
        Bias 0.1
        SlopeBias 0.1
       }
       ShadowCascadeProfile "{54C89834E9AF8CB4}" {
        TextureSize "256"
        Bias 0.4
        SlopeBias 0.3
       }
      }
      MaxShadowDistance 120
      MaxShadowCasterDistance 180
      ShadowMaskBlur 0
      ShadowMaskHalfRes 0
      StabilizeCascades None
     }
     ShadowsProfile RainMask {
      Type RainMask
      Cascades {
       ShadowCascadeProfile "{54D297D50245C1E7}" {
        Bias 0.15
        SlopeBias 0
        ManualPCFDepthDiff 0.3
       }
       ShadowCascadeProfile "{54D4A45222AF9AE3}" {
        Bias 0.3
        SlopeBias 0
        ManualPCFDepthDiff 0.3
       }
      }
      MaxShadowDistance 150
      ShadowMaskBlur 1
      ShadowMaskHalfRes 1
      StabilizeCascades Sphere
      BlendCascades 1
      SplitDistributionCoef 1.8
      FilteringType ManualPCF
      BiasApplicationType Receiver
     }
     ShadowsProfile WetnessMask {
      Type WetnessMask
      Cascades {
       ShadowCascadeProfile "{54E08CD61ED2E740}" {
        TextureSize "1024"
        TextureFormat D16
        Bias 0.1
        SlopeBias 0.2
        ReceiverNormalBias 0.1
        ManualPCFDepthDiff 0.1
       }
      }
      MaxShadowDistance 200
      ShadowMaskBlur 1
      ShadowMaskHalfRes 1
      StabilizeCascades TopDown
      StabilizeMinFov 0
      BlendCascades 1
      SplitDistributionCoef 1.8
      FilteringType ManualPCF
     }
     ShadowsProfile CustomWetnessMask {
      Type CustomWetnessMask
      Cascades {
       ShadowCascadeProfile "{54E08CD61ED2E740}" {
        TextureSize "1024"
        TextureFormat D16
        Bias 0.04
        SlopeBias 0.005
        MaxSlope 0.3
        ReceiverNormalBias 0.15
        ManualPCFDepthDiff 0.05
       }
       ShadowCascadeProfile "{55F8EC3579DBF44C}" {
        Bias 0.01
        SlopeBias 0.01
       }
      }
      MaxShadowDistance 15
      MaxShadowCasterDistance 50
      ShadowMaskBlur 1
      ShadowMaskHalfRes 1
      StabilizeCascades TopDown
      StabilizeMinFov 0
      BlendCascades 1
      SplitDistributionCoef 1.8
      FilteringType StandardPCF
     }
     ShadowsProfile WetnessAddMask {
      Type WetnessAddMask
      Cascades {
       ShadowCascadeProfile "{54E08CD61ED2E740}" {
        TextureSize "1024"
        TextureFormat D32
        Bias 0
        SlopeBias 0
        ReceiverNormalBias 0
        ManualPCFDepthDiff 1
       }
      }
      MaxShadowDistance 250
      MaxShadowCasterDistance 200
      ShadowMaskBlur 1
      ShadowMaskHalfRes 1
      StabilizeCascades TopDown
      StabilizeMinFov 0
      BlendCascades 0
      SplitDistributionCoef 1.8
      FilteringType ManualPCF
     }
     ShadowsProfile ItemPreview {
      Type Custom
      Cascades {
       ShadowCascadeProfile "{60F491E8850C4495}" {
        TextureSize "2048"
        Bias 0.02
        SlopeBias 0.02
        MaxSlope 0.2
       }
      }
      MaxShadowDistance 10
      MaxShadowCasterDistance 50
      ShadowFadeStartCoef 0.95
      ShadowMaskBlur 0
      StabilizeCascades None
     }
    }
   }
   SystemModuleSettings SystemModuleSettings "{5F10A1570304557B}" {
    DefaultConfig "{86E953538A28A98D}Configs/Systems/ChimeraSystemsConfig.conf"
   }
   WidgetManagerSettings WidgetManagerSettings "{AC4BE58770485E02}" {
    WidgetStyles {
     "{22BD5FE3A787925E}UI/Styles/custom.styles" "{9C3BB6F5556D8A09}UI/Styles/editor.styles"
    }
    MouseCursors {
     WidgetMouseCursor "{8CFB278AA1B90D49}" {
      SourceTexture "{AC127C9C0970D8E5}UI/Textures/Cursor/arrow_raw_uiuc.edds"
      HotspotX 6
     }
     WidgetMouseCursor "{510284C4B62615F4}" {
      SourceTexture "{8C9181BCEF9D438B}UI/Textures/Cursor/arrow_select_uiuc.edds"
      HotspotX 6
      HotspotY 6
     }
     WidgetMouseCursor "{51BCFECC9A4B08AE}" {
      SourceTexture "{C2DD10AB50FBA94D}UI/Textures/Cursor/arrow_place_uiuc.edds"
      HotspotX 6
      HotspotY 0
     }
     WidgetMouseCursor "{5188EC212ED6226E}" {
      SourceTexture "{EC996245D1617F13}UI/Textures/Cursor/arrow_transform_uiuc.edds"
      HotspotX 16
      HotspotY 16
     }
     WidgetMouseCursor "{51CE56FBC008ADD7}" {
      SourceTexture "{0111FBCC99A03934}UI/Textures/Cursor/arrow_transform_disabled_uiuc.edds"
      HotspotX 16
      HotspotY 16
     }
     WidgetMouseCursor "{51F2D4A6AEB2CF67}" {
      SourceTexture "{FCE8F5C84FBD5E7F}UI/Textures/Cursor/arrow_wait_uiuc.edds"
      HotspotX 6
     }
     WidgetMouseCursor "{51FF0259741ADEA6}" {
      SourceTexture "{75A77088F5F43CBE}UI/Textures/Cursor/arrow_move_camera_uiuc.edds"
      HotspotX 16
      HotspotY 16
     }
     WidgetMouseCursor "{521663A856F36E53}" {
      SourceTexture "{73CF45BE35701452}UI/Textures/Cursor/arrow_transform_snap_uiuc.edds"
      HotspotX 16
      HotspotY 16
     }
     WidgetMouseCursor "{54C8AB94ADEEB454}" {
      SourceTexture "{B7E9983DFFD45A81}UI/Textures/Cursor/arrow_transform_snap_disabled_uiuc.edds"
      HotspotX 16
      HotspotY 16
     }
     WidgetMouseCursor "{52AAC0245262D7F9}" {
      SourceTexture "{C2E9FB81B2CC653E}UI/Textures/Cursor/arrow_rotate_uiuc.edds"
      HotspotX 16
      HotspotY 16
     }
     WidgetMouseCursor "{5498DAA2F299799C}" {
      SourceTexture "{0C2EEFF2468AE096}UI/Textures/Cursor/arrow_waypoint_uiuc.edds"
      HotspotX 16
      HotspotY 16
     }
     WidgetMouseCursor "{549E5C1EBC059D5B}" {
      SourceTexture "{FFC1BEEF85BA8B17}UI/Textures/Cursor/arrow_objective_uiuc.edds"
      HotspotX 16
      HotspotY 16
     }
     WidgetMouseCursor "{564CCC89CFFED214}" {
      SourceTexture "{E75FB4134580A496}UI/Textures/Cursor/cursors.imageset"
      Image "empty"
     }
     WidgetMouseCursor "{564CCC89CFFED214}" {
      SourceTexture "{C357269438A2AFAA}UI/Textures/Cursor/arrow_transform_geometry_uiuc.edds"
      HotspotX 16
      HotspotY 16
     }
     WidgetMouseCursor "{5D4A749C337669A9}" {
      SourceTexture "{F408BB687A00C106}UI/Textures/Cursor/arrow_place_geometry_uiuc.edds"
      HotspotX 6
     }
    }
    StringTables {
     StringTableDefinition "{518AE2E5A9361F76}" {
      StringTableSource "{C014582791ECBF24}Language/localization.st"
      Languages {
       LanguageDefinition "{516CA5DA9BCB536C}" {
        Code "fr_fr"
        StringTableRuntime "{814F491A30E8C3E6}Language/localization.fr_fr.conf"
       }
       LanguageDefinition "{516CAC1F2A647028}" {
        Code "it_it"
        StringTableRuntime "{0153C62E9DCA401F}Language/localization.it_it.conf"
       }
       LanguageDefinition "{516CAC1F2697C5EC}" {
        Code "de_de"
        StringTableRuntime "{A15B7BEF529A4EFA}Language/localization.de_de.conf"
       }
       LanguageDefinition "{516CAC1F1DFCCC41}" {
        Code "es_es"
        StringTableRuntime "{3AE7BC2DD66B20E4}Language/localization.es_es.conf"
       }
       LanguageDefinition "{5174991952800A5A}" {
        Code "cs_cz"
        StringTableRuntime "{742561DC41395FDA}Language/localization.cs_cz.conf"
       }
       LanguageDefinition "{5174991952C380AF}" {
        Code "pl_pl"
        StringTableRuntime "{ED87F5EA8E79EFF7}Language/localization.pl_pl.conf"
       }
       LanguageDefinition "{51749919510B618E}" {
        Code "ru_ru"
        StringTableRuntime "{6DB17B8E28FE403B}Language/localization.ru_ru.conf"
       }
       LanguageDefinition "{5174991950282E55}" {
        Code "ja_jp"
        FontOverride "{D3E75BCBECEB273E}UI/Fonts/NotoSansEnhanced/NotoSans-Enhanced-Regular-JP-KOR-SC-LAT.fnt"
        LanguageGroup EastAsian
        StringTableRuntime "{11487BBCA3BB70B4}Language/localization.ja_jp.conf"
       }
       LanguageDefinition "{517499195068397A}" {
        Code "ko_kr"
        FontOverride "{D3E75BCBECEB273E}UI/Fonts/NotoSansEnhanced/NotoSans-Enhanced-Regular-JP-KOR-SC-LAT.fnt"
        LanguageGroup EastAsian
        StringTableRuntime "{B1F653103096738B}Language/localization.ko_kr.conf"
       }
       LanguageDefinition "{5174991ABCBF3C54}" {
        Code "pt_br"
        StringTableRuntime "{38F06F26B587254D}Language/localization.pt_br.conf"
       }
       LanguageDefinition "{5174991ABC825E60}" {
        Code "zh_cn"
        FontOverride "{D3E75BCBECEB273E}UI/Fonts/NotoSansEnhanced/NotoSans-Enhanced-Regular-JP-KOR-SC-LAT.fnt"
        LanguageGroup EastAsian
        StringTableRuntime "{3D5940457D702E73}Language/localization.zh_cn.conf"
       }
       LanguageDefinition "{605EFD17BB8C9F0F}" {
        Code "uk_ua"
        StringTableRuntime "{E7E92E7844D0FE6E}Language/localization.uk_ua.conf"
       }
       LanguageDefinition "{5174991ACFFBD1F3}" {
        Code "en_us"
        StringTableRuntime "{FFABF1A438694BFB}Language/localization.en_us.conf"
       }
      }
     }
    }
   }
  }
  GameProjectConfig XBOX_ONE : PC {
   PlatformHardware XBOX_ONE
   DefaultSettings EngineUserSettings "{5702F087A7816DCA}" {
    DisplayUserSettings DisplayUserSettings "{F1B1E6EA2D4A7B39}" {
     PPQuality PPEffectsSettings "{54D6A197983B8089}" {
      HBAO 0
      SSDO 0
      SSR 0
      PPAA 1
      Rain 0
     }
    }
    GraphicsQualitySettings GraphicsQualitySettings "{528CB84499298D6C}" {
    }
    GrassMaterialSettings GrassMaterialSettings "{5B0BA6FFAB8F597C}" {
     Distance 85
     Lod 2
    }
    MaterialSystemUserSettings MaterialSystemUserSettings "{5B0BA6FF9D6730FE}" {
     TextureFilter 1
     MaxAniso 4
    }
    PipelineUserSettings PipelineUserSettings "{B272A67D8DFAC7D2}" {
     ShadowQuality Medium
    }
    PostprocessUserSettings PostprocessUserSettings "{B272A67DF850DADD}" {
     Postprocess 0
    }
    TerrainGenMaterialSettings TerrainGenMaterialSettings "{BBABE688B9061B69}" {
     Detail 1
    }
    VideoUserSettings VideoUserSettings "{B272A67DB18E04F9}" {
     WindowMode FULLSCREEN
     ScreenWidth 1280
     ScreenHeight 720
     ResolutionScale 0.667
     Ratio3d 1
     Fsaa NONE
     RenderFormat STANDARD
     DistantShadowsQuality LOW
     EnvironmentQuality LOW
     Atoc NONE
    }
   }
   ChimeraGlobalConfig ChimeraGlobalConfig "{6F12664687156A7B}" {
    "Maximum View Distance" 1600
    "Minimum View Distance" 100
   }
  }
  GameProjectConfig XBOX_SERIES : PC {
   PlatformHardware XBOX_SERIES
   DefaultSettings EngineUserSettings "{5702F087A781A9FC}" {
    DisplayUserSettings DisplayUserSettings "{F1B1E6EA2D4A7B39}" {
     PPQuality PPEffectsSettings "{54D6A197983B8089}" {
      HBAO 0
      PPAA 3
     }
    }
    GraphicsQualitySettings GraphicsQualitySettings "{52E103D890868E2C}" {
     ObjectDrawDistanceScale Medium
    }
    GrassMaterialSettings GrassMaterialSettings "{7441A756719A8542}" {
     Distance 80
     Lod 1
    }
    PipelineUserSettings PipelineUserSettings "{B272A67D8DFAC7D2}" {
     ShadowQuality Low
    }
    ResourceManagerUserSettings ResourceManagerUserSettings "{589EBA81512FC37F}" {
     TextureDetail 2
    }
    VideoUserSettings VideoUserSettings "{B272A67DB18E04F9}" {
     ResolutionScale 0.5
     Vsynch 0
     DistantShadowsQuality LOW
     EnvironmentQuality LOW
     Atoc NONE
     FsrEnabled 1
    }
   }
   ChimeraGlobalConfig ChimeraGlobalConfig "{6F12664687156A7B}" {
    "Maximum View Distance" 2000
    "Minimum View Distance" 2000
   }
  }
  GameProjectConfig PS4 : PC {
   PlatformHardware PS4
   DefaultSettings EngineUserSettings "{5702F087A781615B}" {
    VideoUserSettings VideoUserSettings "{B272A67DB18E04F9}" {
     DistantShadowsQuality LOW
     EnvironmentQuality LOW
    }
   }
   ChimeraGlobalConfig ChimeraGlobalConfig "{6F12664687156A7B}" {
    "Maximum View Distance" 1600
    "Minimum View Distance" 100
   }
  }
  GameProjectConfig PS5 : PC {
   PlatformHardware PS5
   DefaultSettings EngineUserSettings "{5702F087A781A9FC}" {
    VideoUserSettings VideoUserSettings "{B272A67DB18E04F9}" {
     FsrEnabled 1
    }
   }
   ChimeraGlobalConfig ChimeraGlobalConfig "{6F12664687156A7B}" {
    "Maximum View Distance" 2000
    "Minimum View Distance" 2000
   }
  }
  GameProjectConfig HEADLESS : PC {
   PlatformHardware PC
   DefaultSettings EngineUserSettings "{5702F087A7816718}" {
    VideoUserSettings VideoUserSettings "{B272A67DB18E04F9}" {
     EnvironmentQuality LOW
    }
   }
  }
 }
}