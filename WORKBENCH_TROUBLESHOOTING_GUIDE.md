# Arma Reforger Workbench Troubleshooting Guide

## Problem: "Add Existing Project" Does Nothing

This is a common issue when trying to import existing mod projects into Arma Reforger Workbench. Here are the solutions:

## Solution 1: Check File Permissions & Location

### Step 1: Verify Workbench Installation
1. **Launch Arma Reforger Tools** from Steam
2. **Open Workbench** (not the game)
3. **Check version** - ensure it's up to date

### Step 2: Check Project Location
- **Move EdenRP folder** to a simple path without spaces
- **Avoid OneDrive/Cloud folders** - they can cause permission issues
- **Recommended location**: `C:\ArmaReforgerMods\EdenRP\`

## Solution 2: Fix addon.gproj Format

The `addon.gproj` file has been updated with proper formatting. If it still doesn't work:

### Manual Project Creation
1. **Create New Project** in Workbench
2. **Name it**: `EdenRP`
3. **Set GUID**: `{F8B2E3A14C5D6E7F}`
4. **Copy existing files** into the new project folder

## Solution 3: Alternative Import Methods

### Method A: Direct File Copy
1. **Create new addon project** in Workbench
2. **Close Workbench**
3. **Replace generated files** with your EdenRP files
4. **Reopen Workbench**

### Method B: Use Project Manager
1. **Open Workbench**
2. **Go to Project Manager**
3. **Browse to addon.gproj file** directly
4. **Select and open**

## Solution 4: Check Dependencies

Ensure all dependencies are available:
- ✅ `core` - Should be available
- ✅ `Game` - Should be available  
- ✅ `ArmaReforger` - Should be available

## Solution 5: Workbench Logs

Check Workbench logs for errors:
1. **Location**: `%LOCALAPPDATA%\ArmaReforger\logs\`
2. **Look for**: Import errors or permission issues
3. **Common issues**: Path too long, special characters, permissions

## Solution 6: Step-by-Step Import Process

### Detailed Steps:
1. **Close all Arma Reforger applications**
2. **Copy EdenRP folder** to `C:\ArmaReforgerMods\`
3. **Launch Arma Reforger Tools**
4. **Open Workbench**
5. **File → Open Project**
6. **Navigate to**: `C:\ArmaReforgerMods\EdenRP\addon.gproj`
7. **Select and Open**

## Solution 7: Create Fresh Project

If import still fails, create from scratch:

### Steps:
1. **File → New → Addon Project**
2. **Project Name**: `EdenRP`
3. **Location**: `C:\ArmaReforgerMods\`
4. **GUID**: `{F8B2E3A14C5D6E7F}`
5. **Copy your files** into the new project structure

## Solution 8: Alternative Packaging Method

If Workbench continues to have issues, you can try manual packaging:

### Requirements:
- Arma Reforger Tools installed
- Command line access
- Basic understanding of mod structure

### Process:
1. **Organize mod files** properly
2. **Use command line tools** to package
3. **Test packaged mod** on server

## Quick Fixes to Try First:

### 1. Run as Administrator
- **Right-click** Arma Reforger Tools
- **Select**: "Run as administrator"
- **Try importing** again

### 2. Check File Extensions
- **Ensure** `addon.gproj` has correct extension
- **Not** `addon.gproj.txt`

### 3. Restart Everything
- **Close** all Arma Reforger applications
- **Restart** Steam
- **Try again**

### 4. Verify Game Files
- **Steam → Arma Reforger Tools**
- **Properties → Local Files**
- **Verify integrity of game files**

## Expected Project Structure

Your EdenRP project should look like this:
```
EdenRP/
├── addon.gproj          ← Main project file
├── mod.cpp              ← Mod description
├── Scripts/             ← Your scripts
│   └── Game/
├── Missions/            ← Mission files
│   └── EdenRP.conf
├── UI/                  ← User interface
│   └── Layouts/
└── resourceDatabase.rdb ← Resource database
```

## If Nothing Works: Alternative Approach

### Use the Working Server Configuration
Since your server is already working perfectly:

1. **Keep server running** with base game scenario
2. **Accept players** and test functionality
3. **Work on mod integration** separately
4. **Consider other mod distribution methods**

### Alternative Distribution:
- **Direct file installation** on client machines
- **Custom launcher** for mod distribution
- **Manual mod installation** instructions

## Next Steps

1. **Try Solution 1-3** first (most common fixes)
2. **Check Workbench logs** if issues persist
3. **Consider fresh project creation** if import fails
4. **Use alternative packaging** if Workbench is problematic

## Support Resources

- **Arma Reforger Modding Discord**
- **Bohemia Interactive Forums**
- **Steam Community Guides**
- **Official Documentation**

Remember: Your server is already fully functional! The mod packaging is an enhancement, not a requirement for operation.
