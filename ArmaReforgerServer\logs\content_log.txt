

[2025-07-27 16:03:37] Client version: 1751406682
[2025-07-27 16:03:37] Load failed: C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\steamapps\libraryfolders.vdf.
[2025-07-27 16:03:37] Load failed: C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\config\libraryfolders.vdf.
[2025-07-27 16:03:37] Steam library folders configuration will be reset.
[2025-07-27 16:03:37] No install folder configuration files found.
[2025-07-27 16:03:37] Loaded 0 apps from install folder "C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\steamapps\appmanifest_*.acf".
[2025-07-27 16:03:37] Triggered async write of Steam library folders configuration (1 libraries).
[2025-07-27 16:03:44] AppID 1874900 state changed : Update Required,
[2025-07-27 16:03:44] AppID 228980 state changed : Uninstalled, (Missing configuration)
[2025-07-27 16:03:44] AppID 228980 finished uninstall (Missing configuration)
[2025-07-27 16:03:44] AppID 1874900 scheduler update : Priority First, not played for 1 seconds, update disabled for 0 seconds
[2025-07-27 16:03:44] AppID 1874900 state changed : Update Required,Update Queued,
[2025-07-27 16:03:44] AppID 1874900 scheduler update : Priority First, not played for 1 seconds, update disabled for 0 seconds
[2025-07-27 16:03:44] AppID 228980 state changed : Uninstalled, (Missing configuration)
[2025-07-27 16:03:44] AppID 228980 finished uninstall (Missing configuration)
[2025-07-27 16:03:44] AppID 1874900 state changed : Update Required,Update Queued,Update Running,
[2025-07-27 16:03:44] AppID 1874900 App update changed : Running Update,
[2025-07-27 16:03:44] AppID 1874900 App update changed : Running Update,Reconfiguring,
[2025-07-27 16:03:46] Got 30 download sources and 0 caching proxies via ContentServerDirectoryService::BYieldingGetServersForSteamPipe (CellID 20 / Launcher 3)
[2025-07-27 16:03:46] Moving to source priority class '6'
[2025-07-27 16:03:46] Created download interface of type 'SteamCache' (7) to host cache2-yyz-rgrs.steamcontent.com (cache2-yyz-rgrs.steamcontent.com)
[2025-07-27 16:03:46] Created download interface of type 'SteamCache' (7) to host cache1-yyz-rgrs.steamcontent.com (cache1-yyz-rgrs.steamcontent.com)
[2025-07-27 16:03:46] HTTP/2 available
[2025-07-27 16:03:46] Created dual-stack http client
[2025-07-27 16:03:47] HTTPS (SteamCache,288) - cache2-yyz-rgrs.steamcontent.com ([2607:f798:d06:1::2]:443 / [2607:f798:d06:1::2]:443, host: cache2-yyz-rgrs.steamcontent.com): Negotiated HTTP/2
[2025-07-27 16:03:47] HTTP/2 (SteamCache,288) - cache2-yyz-rgrs.steamcontent.com ([2607:f798:d06:1::2]:443 / [2607:f798:d06:1::2]:443, host: cache2-yyz-rgrs.steamcontent.com): cache2-yyz-rgrs.steamcontent.com/depot/1874901/manifest/4769432600775589570/5/721946816044096570 - received 200 (OK) HTTP response
[2025-07-27 16:03:47] HTTPS (SteamCache,287) - cache1-yyz-rgrs.steamcontent.com ([2607:f798:d06::2]:443 / [2607:f798:d06::2]:443, host: cache1-yyz-rgrs.steamcontent.com): Negotiated HTTP/2
[2025-07-27 16:03:47] HTTP/2 (SteamCache,287) - cache1-yyz-rgrs.steamcontent.com ([2607:f798:d06::2]:443 / [2607:f798:d06::2]:443, host: cache1-yyz-rgrs.steamcontent.com): cache1-yyz-rgrs.steamcontent.com/depot/1874903/manifest/7627138200760813011/5/8059654359904450988 - received 200 (OK) HTTP response
[2025-07-27 16:03:47] AppID 1874900 App update changed : Running Update,
[2025-07-27 16:03:47] AppID 1874900 App update changed : Running Update,Verifying Installed,
[2025-07-27 16:03:47] AppID 1874900 App update changed : Running Update,
[2025-07-27 16:03:47] AppID 1874900 App update changed : Running Update,Preallocating,
[2025-07-27 16:03:47] AppID 1874900 preallocated 11 files (7060 MB) 
[2025-07-27 16:03:47] AppID 1874900 App update changed : Running Update,
[2025-07-27 16:03:47] AppID 1874900 update started : download 0/5447354672, store 0/0, reuse 0/0, delta 0/0, stage 0/7404021707 
[2025-07-27 16:03:47] AppID 1874900 state changed : Update Required,Update Queued,Update Running,Update Started,
[2025-07-27 16:03:47] AppID 1874900 App update changed : Running Update,Downloading,Staging,
[2025-07-27 16:03:47] Downloading 6880 chunks from depot 1874901
[2025-07-27 16:03:59] Increasing target number of download connections to 4 (rate was 0.000, now 177.154)
[2025-07-27 16:04:11] Increasing target number of download connections to 5 (rate was 177.154, now 219.942)
[2025-07-27 16:04:59] Current download rate: 237.031 Mbps
[2025-07-27 16:06:00] Current download rate: 175.991 Mbps
[2025-07-27 16:07:01] Current download rate: 59.831 Mbps
[2025-07-27 16:08:02] Current download rate: 125.157 Mbps
[2025-07-27 16:08:26] Downloading 70 chunks from depot 1874903
[2025-07-27 16:08:29] AppID 1874900 App update changed : Running Update,
[2025-07-27 16:08:29] AppID 1874900 App update changed : Running Update,Verifying Staged,
[2025-07-27 16:08:35] Verified 2049 MB in 'C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer', clean bytes tally is now 2049 MB
[2025-07-27 16:08:35] Triggered async write of Steam library folders configuration (1 libraries).
[2025-07-27 16:08:35] AppID 1874900 App update changed : Running Update,
[2025-07-27 16:08:35] AppID 1874900 App update changed : Running Update,Committing,
[2025-07-27 16:08:35] AppID 1874900 starting commit from "C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\steamapps\downloading\1874900" to "C:\Users\<USER>\OneDrive\Desktop\Arma Reforger Test\ArmaReforgerServer\steamapps\common\Arma Reforger Server" : 53 updated, 0 moved, 0 deleted files
[2025-07-27 16:08:35] AppID 1874900 state changed : Fully Installed,Update Queued,Update Running,
[2025-07-27 16:08:35] AppID 1874900 finished update, 2 mounted depots (BuildID 19101044) : 1874901 (4769432600775589570),1874903 (7627138200760813011),
[2025-07-27 16:08:35] AppID 1874900 App update changed : Running Update,
[2025-07-27 16:08:35] AppID 1874900 App update changed : None
[2025-07-27 16:08:35] AppID 1874900 state changed : Fully Installed,Update Queued,
[2025-07-27 16:08:35] AppID 228980 state changed : Update Required,Shared Only,
[2025-07-27 16:08:35] AppID 228980 state changed : Update Required,
[2025-07-27 16:08:35] Dependency added: parent 228980, child 1874900, depot 228989
[2025-07-27 16:08:35] AppID 1874900 state changed : Fully Installed,
[2025-07-27 16:08:35] AppID 1874900 scheduler finished : removed from schedule (result No Error, state 0xc) 
[2025-07-27 16:08:35] HTTP/2 (SteamCache,287) - cache1-yyz-rgrs.steamcontent.com ([2607:f798:d06::2]:443 / [2607:f798:d06::2]:443, host: cache1-yyz-rgrs.steamcontent.com): Closing connection
[2025-07-27 16:08:35] HTTP/2 (SteamCache,288) - cache2-yyz-rgrs.steamcontent.com ([2607:f798:d06:1::2]:443 / [2607:f798:d06:1::2]:443, host: cache2-yyz-rgrs.steamcontent.com): Closing connection
