//! Eden Housing Manager - Handles all housing systems including ownership, storage, upgrades, and real estate
//! Converted from original housing systems

class EdenHousingManager
{
    protected ref map<string, ref EdenHouseData> m_PlayerHouses;
    protected ref map<int, ref EdenHouseData> m_HouseDatabase; // House ID -> House Data
    protected ref map<string, ref array<int>> m_PlayerHouseKeys; // Player ID -> House IDs with keys
    protected ref array<ref EdenHouseData> m_HousesForSale;
    protected ref map<string, ref EdenHouseConfig> m_HouseConfigurations;
    
    // Housing system configuration
    protected bool m_HousingSystemEnabled;
    protected int m_MaxHousesPerPlayer;
    protected int m_HouseExpirationDays;
    protected float m_HouseTaxMultiplier;
    protected int m_VirtualStorageUpgradePrice;
    protected int m_PhysicalStorageUpgradePrice;
    protected int m_OilStorageUpgradePrice;
    protected int m_MaxHouseKeys;
    
    void EdenHousingManager()
    {
        m_PlayerHouses = new map<string, ref EdenHouseData>();
        m_HouseDatabase = new map<int, ref EdenHouseData>();
        m_PlayerHouseKeys = new map<string, ref array<int>>();
        m_HousesForSale = new array<ref EdenHouseData>();
        m_HouseConfigurations = new map<string, ref EdenHouseConfig>();
        
        m_HousingSystemEnabled = true;
        m_MaxHousesPerPlayer = 3;
        m_HouseExpirationDays = 45;
        m_HouseTaxMultiplier = 1.0;
        m_VirtualStorageUpgradePrice = 112500; // 15% of house price
        m_PhysicalStorageUpgradePrice = 200000;
        m_OilStorageUpgradePrice = 50000;
        m_MaxHouseKeys = 20;
        
        InitializeHousingSystem();
    }
    
    //! Initialize housing system
    void Initialize()
    {
        Print("[EdenHousingManager] Initializing housing system...");
        
        SetupHouseConfigurations();
        LoadHouseDatabase();
        InitializeHouseMarkers();
        
        // Set up periodic processing
        GetGame().GetCallqueue().CallLater(ProcessHouseExpiration, 3600000, true); // 1 hour
        GetGame().GetCallqueue().CallLater(ProcessHouseTaxes, 86400000, true); // 24 hours
        GetGame().GetCallqueue().CallLater(SaveAllHouses, 300000, true); // 5 minutes
        
        Print("[EdenHousingManager] Housing system initialized");
    }
    
    //! Initialize housing system configuration
    protected void InitializeHousingSystem()
    {
        Print("[EdenHousingManager] Housing system configuration initialized");
    }
    
    //! Setup house configurations
    protected void SetupHouseConfigurations()
    {
        // Small houses
        SetupHouseConfig("Land_i_House_Small_01_V1_F", 750000, 5, 100, 100);
        SetupHouseConfig("Land_i_House_Small_01_V2_F", 750000, 5, 100, 100);
        SetupHouseConfig("Land_i_House_Small_01_V3_F", 750000, 5, 100, 100);
        
        // Medium houses
        SetupHouseConfig("Land_i_House_Big_01_V1_F", 1000500, 7, 100, 100);
        SetupHouseConfig("Land_i_House_Big_01_V2_F", 1000500, 7, 100, 100);
        SetupHouseConfig("Land_i_House_Big_01_V3_F", 1000500, 7, 100, 100);
        
        // Large houses
        SetupHouseConfig("Land_i_House_Big_02_V1_F", 1250000, 10, 100, 100);
        SetupHouseConfig("Land_i_House_Big_02_V2_F", 1250000, 10, 100, 100);
        SetupHouseConfig("Land_i_House_Big_02_V3_F", 1250000, 10, 100, 100);
        
        // Luxury houses
        SetupHouseConfig("Land_Villa_01_F", 2200000, 15, 100, 100);
        SetupHouseConfig("Land_Villa_02_F", 2200000, 15, 100, 100);
        
        Print(string.Format("[EdenHousingManager] Set up %1 house configurations", m_HouseConfigurations.Count()));
    }
    
    //! Setup individual house configuration
    protected void SetupHouseConfig(string houseClass, int price, int maxCrates, int baseVirtualStorage, int basePhysicalStorage)
    {
        EdenHouseConfig config = new EdenHouseConfig();
        config.SetHouseClass(houseClass);
        config.SetPrice(price);
        config.SetMaxCrates(maxCrates);
        config.SetBaseVirtualStorage(baseVirtualStorage);
        config.SetBasePhysicalStorage(basePhysicalStorage);
        config.SetIsAvailable(true);
        
        m_HouseConfigurations.Set(houseClass, config);
    }
    
    //! Purchase house
    bool PurchaseHouse(string playerId, IEntity houseEntity, vector housePosition)
    {
        if (playerId == "" || !houseEntity)
            return false;
            
        if (!m_HousingSystemEnabled)
            return false;
            
        // Check if house is already owned
        if (IsHouseOwned(housePosition))
            return false;
            
        // Get house configuration
        string houseClass = houseEntity.GetPrefabData().GetPrefabName();
        if (!m_HouseConfigurations.Contains(houseClass))
            return false;
            
        EdenHouseConfig houseConfig = m_HouseConfigurations.Get(houseClass);
        
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player has enough money
        int housePrice = houseConfig.GetPrice();
        if (housePrice > playerComp.GetBankAccount())
            return false;
            
        // Check house limit
        if (GetPlayerHouseCount(playerId) >= m_MaxHousesPerPlayer)
            return false;
            
        // Process purchase
        playerComp.RemoveBankMoney(housePrice);
        
        // Create house data
        int houseId = GenerateHouseId();
        EdenHouseData houseData = new EdenHouseData();
        houseData.SetHouseId(houseId);
        houseData.SetOwnerId(playerId);
        houseData.SetOwnerName(playerComp.GetPlayerName());
        houseData.SetHouseClass(houseClass);
        houseData.SetPosition(housePosition);
        houseData.SetIsOwned(true);
        houseData.SetPurchaseTime(GetGame().GetWorld().GetWorldTime());
        houseData.SetExpirationTime(GetGame().GetWorld().GetWorldTime() + (m_HouseExpirationDays * 86400));
        houseData.SetVirtualInventory("[[],0]");
        houseData.SetPhysicalInventory("[[],0]");
        houseData.SetVirtualStorageCapacity(houseConfig.GetBaseVirtualStorage());
        houseData.SetPhysicalStorageCapacity(houseConfig.GetBasePhysicalStorage());
        houseData.SetPlayerKeys("[]");
        houseData.SetIsLocked(true);
        houseData.SetHasOilStorage(false);
        houseData.SetIsInAuctionHouse(false);
        
        // Store house data
        m_HouseDatabase.Set(houseId, houseData);
        
        // Add to player houses
        if (!m_PlayerHouses.Contains(playerId))
        {
            m_PlayerHouses.Set(playerId, new array<ref EdenHouseData>());
        }
        array<ref EdenHouseData> playerHouses = m_PlayerHouses.Get(playerId);
        playerHouses.Insert(houseData);
        
        // Set house entity variables
        SetupHouseEntity(houseEntity, houseData);
        
        // Create house marker
        CreateHouseMarker(houseData);
        
        // Save to database
        SaveHouseToDatabase(houseData);
        
        Print(string.Format("[EdenHousingManager] %1 purchased house %2 for $%3", playerId, houseId, housePrice));
        return true;
    }
    
    //! Sell house
    bool SellHouse(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        // Get house data
        if (!m_HouseDatabase.Contains(houseId))
            return false;
            
        EdenHouseData houseData = m_HouseDatabase.Get(houseId);
        
        // Check ownership
        if (houseData.GetOwnerId() != playerId)
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Calculate sell price (50% of purchase price + 25% of upgrades)
        EdenHouseConfig houseConfig = m_HouseConfigurations.Get(houseData.GetHouseClass());
        int basePrice = houseConfig.GetPrice() / 2;
        int upgradeValue = CalculateUpgradeValue(houseData) * 0.25;
        int sellPrice = basePrice + upgradeValue;
        
        // Process sale
        playerComp.AddBankMoney(sellPrice);
        
        // Remove house ownership
        houseData.SetIsOwned(false);
        houseData.SetOwnerId("");
        houseData.SetOwnerName("");
        houseData.SetPlayerKeys("[]");
        houseData.SetVirtualInventory("[[],0]");
        houseData.SetPhysicalInventory("[[],0]");
        houseData.SetIsLocked(false);
        
        // Remove from player houses
        if (m_PlayerHouses.Contains(playerId))
        {
            array<ref EdenHouseData> playerHouses = m_PlayerHouses.Get(playerId);
            int houseIndex = -1;
            for (int i = 0; i < playerHouses.Count(); i++)
            {
                if (playerHouses[i].GetHouseId() == houseId)
                {
                    houseIndex = i;
                    break;
                }
            }
            if (houseIndex != -1)
            {
                playerHouses.Remove(houseIndex);
            }
        }
        
        // Remove house marker
        RemoveHouseMarker(houseData);
        
        // Update database
        UpdateHouseInDatabase(houseData);
        
        Print(string.Format("[EdenHousingManager] %1 sold house %2 for $%3", playerId, houseId, sellPrice));
        return true;
    }
    
    //! Upgrade house storage
    bool UpgradeHouseStorage(string playerId, int houseId, string upgradeType)
    {
        if (playerId == "" || houseId <= 0 || upgradeType == "")
            return false;
            
        // Get house data
        if (!m_HouseDatabase.Contains(houseId))
            return false;
            
        EdenHouseData houseData = m_HouseDatabase.Get(houseId);
        
        // Check ownership
        if (houseData.GetOwnerId() != playerId)
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Get house configuration
        EdenHouseConfig houseConfig = m_HouseConfigurations.Get(houseData.GetHouseClass());
        
        int upgradePrice = 0;
        bool canUpgrade = false;
        
        switch (upgradeType)
        {
            case "storage":
            {
                // Virtual storage upgrade (+700 capacity)
                int maxVirtualStorage = houseConfig.GetMaxCrates() * 700;
                if (houseData.GetVirtualStorageCapacity() < maxVirtualStorage)
                {
                    upgradePrice = Math.Floor(houseConfig.GetPrice() * 0.15); // 15% of house price
                    canUpgrade = true;
                }
                break;
            }
            case "physicalstorage":
            {
                // Physical storage upgrade (+200 capacity)
                int maxPhysicalStorage = (houseConfig.GetMaxCrates() * 200) + 100;
                if (houseData.GetPhysicalStorageCapacity() < maxPhysicalStorage)
                {
                    upgradePrice = m_PhysicalStorageUpgradePrice;
                    canUpgrade = true;
                }
                break;
            }
            case "oil":
            {
                // Oil storage upgrade
                if (!houseData.HasOilStorage())
                {
                    upgradePrice = m_OilStorageUpgradePrice;
                    canUpgrade = true;
                }
                break;
            }
        }
        
        if (!canUpgrade)
            return false;
            
        // Check if player has enough money
        if (upgradePrice > playerComp.GetBankAccount())
            return false;
            
        // Process payment
        playerComp.RemoveBankMoney(upgradePrice);
        
        // Apply upgrade
        switch (upgradeType)
        {
            case "storage":
            {
                houseData.SetVirtualStorageCapacity(houseData.GetVirtualStorageCapacity() + 700);
                break;
            }
            case "physicalstorage":
            {
                houseData.SetPhysicalStorageCapacity(houseData.GetPhysicalStorageCapacity() + 200);
                break;
            }
            case "oil":
            {
                houseData.SetHasOilStorage(true);
                break;
            }
        }
        
        // Update database
        UpdateHouseInDatabase(houseData);
        
        Print(string.Format("[EdenHousingManager] %1 upgraded house %2 (%3) for $%4", playerId, houseId, upgradeType, upgradePrice));
        return true;
    }
    
    //! Give house keys
    bool GiveHouseKeys(string playerId, int houseId, string targetPlayerId)
    {
        if (playerId == "" || houseId <= 0 || targetPlayerId == "")
            return false;
            
        // Get house data
        if (!m_HouseDatabase.Contains(houseId))
            return false;
            
        EdenHouseData houseData = m_HouseDatabase.Get(houseId);
        
        // Check ownership
        if (houseData.GetOwnerId() != playerId)
            return false;
            
        // Get current key holders
        array<string> keyHolders = ParsePlayerKeys(houseData.GetPlayerKeys());
        
        // Check key limit
        if (keyHolders.Count() >= m_MaxHouseKeys)
            return false;
            
        // Check if target already has keys
        if (keyHolders.Find(targetPlayerId) != -1)
            return false;
            
        // Add key holder
        keyHolders.Insert(targetPlayerId);
        houseData.SetPlayerKeys(SerializePlayerKeys(keyHolders));
        
        // Add to target player's key list
        if (!m_PlayerHouseKeys.Contains(targetPlayerId))
        {
            m_PlayerHouseKeys.Set(targetPlayerId, new array<int>());
        }
        array<int> playerKeys = m_PlayerHouseKeys.Get(targetPlayerId);
        playerKeys.Insert(houseId);
        
        // Update database
        UpdateHouseInDatabase(houseData);
        
        // Create house marker for key holder
        CreateHouseKeyMarker(houseData, targetPlayerId);
        
        Print(string.Format("[EdenHousingManager] %1 gave house keys for %2 to %3", playerId, houseId, targetPlayerId));
        return true;
    }
    
    //! Remove house keys
    bool RemoveHouseKeys(string playerId, int houseId, string targetPlayerId)
    {
        if (playerId == "" || houseId <= 0 || targetPlayerId == "")
            return false;
            
        // Get house data
        if (!m_HouseDatabase.Contains(houseId))
            return false;
            
        EdenHouseData houseData = m_HouseDatabase.Get(houseId);
        
        // Check ownership
        if (houseData.GetOwnerId() != playerId)
            return false;
            
        // Get current key holders
        array<string> keyHolders = ParsePlayerKeys(houseData.GetPlayerKeys());
        
        // Remove key holder
        int keyIndex = keyHolders.Find(targetPlayerId);
        if (keyIndex != -1)
        {
            keyHolders.Remove(keyIndex);
            houseData.SetPlayerKeys(SerializePlayerKeys(keyHolders));
        }
        
        // Remove from target player's key list
        if (m_PlayerHouseKeys.Contains(targetPlayerId))
        {
            array<int> playerKeys = m_PlayerHouseKeys.Get(targetPlayerId);
            int houseKeyIndex = playerKeys.Find(houseId);
            if (houseKeyIndex != -1)
            {
                playerKeys.Remove(houseKeyIndex);
            }
        }
        
        // Update database
        UpdateHouseInDatabase(houseData);
        
        // Remove house key marker
        RemoveHouseKeyMarker(houseData, targetPlayerId);
        
        Print(string.Format("[EdenHousingManager] %1 removed house keys for %2 from %3", playerId, houseId, targetPlayerId));
        return true;
    }
    
    //! Change house locks (remove all keys)
    bool ChangeHouseLocks(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        // Get house data
        if (!m_HouseDatabase.Contains(houseId))
            return false;
            
        EdenHouseData houseData = m_HouseDatabase.Get(houseId);
        
        // Check ownership
        if (houseData.GetOwnerId() != playerId)
            return false;
            
        // Get current key holders
        array<string> keyHolders = ParsePlayerKeys(houseData.GetPlayerKeys());
        
        // Remove all key holders from their key lists
        foreach (string keyHolderId : keyHolders)
        {
            if (m_PlayerHouseKeys.Contains(keyHolderId))
            {
                array<int> playerKeys = m_PlayerHouseKeys.Get(keyHolderId);
                int houseKeyIndex = playerKeys.Find(houseId);
                if (houseKeyIndex != -1)
                {
                    playerKeys.Remove(houseKeyIndex);
                }
            }
            
            // Remove house key markers
            RemoveHouseKeyMarker(houseData, keyHolderId);
        }
        
        // Clear all keys
        houseData.SetPlayerKeys("[]");
        
        // Update database
        UpdateHouseInDatabase(houseData);
        
        Print(string.Format("[EdenHousingManager] %1 changed locks for house %2", playerId, houseId));
        return true;
    }
    
    //! Pay house tax
    bool PayHouseTax(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        // Get house data
        if (!m_HouseDatabase.Contains(houseId))
            return false;
            
        EdenHouseData houseData = m_HouseDatabase.Get(houseId);
        
        // Check ownership
        if (houseData.GetOwnerId() != playerId)
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Calculate tax amount
        int daysLeft = CalculateDaysUntilExpiration(houseData);
        int taxCost = CalculateHouseTax(houseData) * (m_HouseExpirationDays - daysLeft);
        
        // Check if player has enough money
        if (taxCost > playerComp.GetBankAccount())
            return false;
            
        // Process payment
        playerComp.RemoveBankMoney(taxCost);
        
        // Extend expiration
        houseData.SetExpirationTime(GetGame().GetWorld().GetWorldTime() + (m_HouseExpirationDays * 86400));
        
        // Update database
        UpdateHouseInDatabase(houseData);
        
        Print(string.Format("[EdenHousingManager] %1 paid house tax $%2 for house %3", playerId, taxCost, houseId));
        return true;
    }
    
    //! List house for sale
    bool ListHouseForSale(string playerId, int houseId, int salePrice)
    {
        if (playerId == "" || houseId <= 0 || salePrice <= 0)
            return false;
            
        // Get house data
        if (!m_HouseDatabase.Contains(houseId))
            return false;
            
        EdenHouseData houseData = m_HouseDatabase.Get(houseId);
        
        // Check ownership
        if (houseData.GetOwnerId() != playerId)
            return false;
            
        // Set for sale
        houseData.SetIsInAuctionHouse(true);
        houseData.SetSalePrice(salePrice);
        houseData.SetSaleTime(GetGame().GetWorld().GetWorldTime());
        
        // Add to houses for sale
        m_HousesForSale.Insert(houseData);
        
        // Update database
        UpdateHouseInDatabase(houseData);
        
        Print(string.Format("[EdenHousingManager] %1 listed house %2 for sale at $%3", playerId, houseId, salePrice));
        return true;
    }
    
    //! Remove house from sale
    bool RemoveHouseFromSale(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        // Get house data
        if (!m_HouseDatabase.Contains(houseId))
            return false;
            
        EdenHouseData houseData = m_HouseDatabase.Get(houseId);
        
        // Check ownership
        if (houseData.GetOwnerId() != playerId)
            return false;
            
        // Remove from sale
        houseData.SetIsInAuctionHouse(false);
        houseData.SetSalePrice(0);
        houseData.SetSaleTime(0);
        
        // Remove from houses for sale
        int saleIndex = m_HousesForSale.Find(houseData);
        if (saleIndex != -1)
        {
            m_HousesForSale.Remove(saleIndex);
        }
        
        // Update database
        UpdateHouseInDatabase(houseData);
        
        Print(string.Format("[EdenHousingManager] %1 removed house %2 from sale", playerId, houseId));
        return true;
    }
    
    //! Get player houses
    array<ref EdenHouseData> GetPlayerHouses(string playerId)
    {
        if (playerId == "" || !m_PlayerHouses.Contains(playerId))
            return {};
            
        return m_PlayerHouses.Get(playerId);
    }
    
    //! Get player house keys
    array<int> GetPlayerHouseKeys(string playerId)
    {
        if (playerId == "" || !m_PlayerHouseKeys.Contains(playerId))
            return {};
            
        return m_PlayerHouseKeys.Get(playerId);
    }
    
    //! Get houses for sale
    array<ref EdenHouseData> GetHousesForSale()
    {
        return m_HousesForSale;
    }
    
    //! Check if player can access house
    bool CanAccessHouse(string playerId, int houseId)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        // Get house data
        if (!m_HouseDatabase.Contains(houseId))
            return false;
            
        EdenHouseData houseData = m_HouseDatabase.Get(houseId);
        
        // Check ownership
        if (houseData.GetOwnerId() == playerId)
            return true;
            
        // Check keys
        array<string> keyHolders = ParsePlayerKeys(houseData.GetPlayerKeys());
        return keyHolders.Find(playerId) != -1;
    }
    
    //! Helper methods
    protected int GetPlayerHouseCount(string playerId)
    {
        if (!m_PlayerHouses.Contains(playerId))
            return 0;
            
        return m_PlayerHouses.Get(playerId).Count();
    }
    
    protected bool IsHouseOwned(vector position)
    {
        foreach (int houseId, EdenHouseData houseData : m_HouseDatabase)
        {
            if (houseData.IsOwned() && vector.Distance(houseData.GetPosition(), position) < 5.0)
                return true;
        }
        return false;
    }
    
    protected int GenerateHouseId()
    {
        return Math.RandomInt(100000, 999999);
    }
    
    protected int CalculateUpgradeValue(EdenHouseData houseData)
    {
        int upgradeValue = 0;
        
        // Virtual storage upgrades
        int virtualUpgrades = (houseData.GetVirtualStorageCapacity() - 100) / 700;
        upgradeValue += virtualUpgrades * m_VirtualStorageUpgradePrice;
        
        // Physical storage upgrades
        int physicalUpgrades = (houseData.GetPhysicalStorageCapacity() - 100) / 200;
        upgradeValue += physicalUpgrades * m_PhysicalStorageUpgradePrice;
        
        // Oil storage upgrade
        if (houseData.HasOilStorage())
        {
            upgradeValue += m_OilStorageUpgradePrice;
        }
        
        return upgradeValue;
    }
    
    protected int CalculateHouseTax(EdenHouseData houseData)
    {
        // Get house configuration
        EdenHouseConfig houseConfig = m_HouseConfigurations.Get(houseData.GetHouseClass());
        int basePrice = houseConfig.GetPrice();
        
        // Calculate tax based on house value and location
        // Simplified tax calculation - in original it's based on distance from high-value areas
        float taxRate = 0.1; // 10% base tax rate
        int dailyTax = Math.Floor((basePrice * taxRate) / 30); // Daily tax
        
        return dailyTax;
    }
    
    protected int CalculateDaysUntilExpiration(EdenHouseData houseData)
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int timeUntilExpiration = houseData.GetExpirationTime() - currentTime;
        return Math.Max(0, timeUntilExpiration / 86400); // Convert to days
    }
    
    protected array<string> ParsePlayerKeys(string playerKeysJson)
    {
        // Implementation would parse JSON array of player IDs
        // For now, return empty array
        return {};
    }
    
    protected string SerializePlayerKeys(array<string> playerKeys)
    {
        // Implementation would serialize array to JSON
        // For now, return empty JSON array
        return "[]";
    }
    
    protected void ProcessHouseExpiration()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        int expiredCount = 0;
        
        foreach (int houseId, EdenHouseData houseData : m_HouseDatabase)
        {
            if (houseData.IsOwned() && houseData.GetExpirationTime() < currentTime)
            {
                // House expired - remove ownership
                houseData.SetIsOwned(false);
                houseData.SetOwnerId("");
                houseData.SetOwnerName("");
                houseData.SetPlayerKeys("[]");
                houseData.SetVirtualInventory("[[],0]");
                houseData.SetPhysicalInventory("[[],0]");
                houseData.SetIsLocked(false);
                
                // Remove from player houses
                RemoveFromPlayerHouses(houseData);
                
                // Remove markers
                RemoveHouseMarker(houseData);
                
                // Update database
                UpdateHouseInDatabase(houseData);
                
                expiredCount++;
            }
        }
        
        if (expiredCount > 0)
        {
            Print(string.Format("[EdenHousingManager] Expired %1 houses", expiredCount));
        }
    }
    
    protected void ProcessHouseTaxes()
    {
        // Implementation would process automatic tax collection
        Print("[EdenHousingManager] Processing house taxes...");
    }
    
    protected void SaveAllHouses()
    {
        // Implementation would save all house data to database
        Print("[EdenHousingManager] Saving all houses...");
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadHouseDatabase() { }
    protected void InitializeHouseMarkers() { }
    protected void SetupHouseEntity(IEntity houseEntity, EdenHouseData houseData) { }
    protected void CreateHouseMarker(EdenHouseData houseData) { }
    protected void RemoveHouseMarker(EdenHouseData houseData) { }
    protected void CreateHouseKeyMarker(EdenHouseData houseData, string playerId) { }
    protected void RemoveHouseKeyMarker(EdenHouseData houseData, string playerId) { }
    protected void RemoveFromPlayerHouses(EdenHouseData houseData) { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    protected void SaveHouseToDatabase(EdenHouseData houseData) { }
    protected void UpdateHouseInDatabase(EdenHouseData houseData) { }
    
    //! Configuration methods
    void SetHousingSystemEnabled(bool enabled) { m_HousingSystemEnabled = enabled; }
    bool IsHousingSystemEnabled() { return m_HousingSystemEnabled; }
    
    void SetMaxHousesPerPlayer(int maxHouses) { m_MaxHousesPerPlayer = maxHouses; }
    int GetMaxHousesPerPlayer() { return m_MaxHousesPerPlayer; }
    
    void SetHouseExpirationDays(int days) { m_HouseExpirationDays = days; }
    int GetHouseExpirationDays() { return m_HouseExpirationDays; }
    
    void SetHouseTaxMultiplier(float multiplier) { m_HouseTaxMultiplier = multiplier; }
    float GetHouseTaxMultiplier() { return m_HouseTaxMultiplier; }
    
    void SetUpgradePrices(int virtualPrice, int physicalPrice, int oilPrice)
    {
        m_VirtualStorageUpgradePrice = virtualPrice;
        m_PhysicalStorageUpgradePrice = physicalPrice;
        m_OilStorageUpgradePrice = oilPrice;
    }
    
    void SetMaxHouseKeys(int maxKeys) { m_MaxHouseKeys = maxKeys; }
    int GetMaxHouseKeys() { return m_MaxHouseKeys; }
    
    //! Get house configurations
    array<ref EdenHouseConfig> GetHouseConfigurations()
    {
        array<ref EdenHouseConfig> configs = {};
        foreach (string houseClass, EdenHouseConfig config : m_HouseConfigurations)
        {
            configs.Insert(config);
        }
        return configs;
    }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenHousingManager] Cleaning up housing system...");
        
        m_PlayerHouses.Clear();
        m_HouseDatabase.Clear();
        m_PlayerHouseKeys.Clear();
        m_HousesForSale.Clear();
        m_HouseConfigurations.Clear();
    }
}
