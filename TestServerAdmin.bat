@echo off

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo This script requires administrator privileges to create symlinks.
    echo Requesting elevation...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

title Eden RP Server Test (Admin)
color 0A

echo ========================================
echo      EDEN RP SERVER TEST (ADMIN)
echo ========================================
echo.
echo This will test the server startup with
echo detailed error reporting and admin privileges.
echo.

cd ArmaReforgerServer\steamapps\common\Arma Reforger Server

echo Current directory: %CD%
echo.

echo Checking for server executable...
if not exist "ArmaReforgerServerDiag.exe" (
    echo ERROR: ArmaReforgerServerDiag.exe not found!
    echo Please ensure Arma Reforger Server is properly installed.
    pause
    exit /b 1
)
echo ✓ Server executable found

echo.
echo Checking configuration file...
if not exist "..\..\..\server.json" (
    echo ERROR: server.json not found!
    pause
    exit /b 1
)
echo ✓ Configuration file found

echo.
echo Checking mod directory...
if exist "addons\EdenRP" (
    echo ✓ EdenRP mod symlink already exists
) else (
    echo Creating EdenRP mod symlink...
    mklink /D "addons\EdenRP" "..\..\..\EdenRP"
    if errorlevel 1 (
        echo ERROR: Failed to create mod symlink!
        echo Trying alternative method...
        
        :: Try copying instead of symlinking
        echo Copying mod files instead...
        xcopy "..\..\..\EdenRP" "addons\EdenRP" /E /I /Y
        if errorlevel 1 (
            echo ERROR: Failed to copy mod files!
            pause
            exit /b 1
        )
        echo ✓ Mod files copied successfully
    ) else (
        echo ✓ Mod symlink created successfully
    )
)

echo.
echo Starting server with verbose logging...
echo Command: ArmaReforgerServerDiag.exe -config "..\..\..\server.json" -profile "..\..\..\saves" -addonsDir "addons" -logLevel 3 -logStats
echo.
echo ========================================
echo           SERVER OUTPUT
echo ========================================

ArmaReforgerServerDiag.exe -config "..\..\..\server.json" -profile "..\..\..\saves" -addonsDir "addons" -logLevel 3 -logStats

echo.
echo ========================================
echo         SERVER STOPPED
echo ========================================
echo Exit code: %ERRORLEVEL%
echo.
echo Check the output above for error details.
echo Log files are saved in: saves\logs\
echo.
echo Press any key to continue...
pause
