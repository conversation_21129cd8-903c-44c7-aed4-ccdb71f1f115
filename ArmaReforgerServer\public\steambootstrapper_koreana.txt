"Language" "korean" 
"Tokens" 
{ 
"SteamBootstrapper_UpdatingSteam"				"%appname% 업데이트 중..."
"SteamBootstrapper_ElevationPrompt"			"실행 전에 먼저 %appname% 자기 자신을 업데이트해야 합니다."
"SteamBootstrapper_ReadOnlyInstallOSX"		"%appname% 설치 폴더에 쓰기 작업을 할 수 없습니다. 혹시 디스크 이미지에서 실행 중인 것은 아닌지 확인해보세요."
"SteamBootstrapper_RunFromDiskImageOSX"		"실행하기 전에 %appname%을 응용프로그램 폴더 안으로 드래그 하세요."
"SteamBootstrapper_ReadOnlyInstall"			"%appname% 설치 폴더에 쓰기 작업을 할 수 없습니다."
"SteamBootstrapper_InstallDirNotASCII"			"%appname%은 알파벳 외의 문자가 포함된 폴더에서는 실행할 수 없습니다. %appname%을 기본 폴더에 재설치하세요."
"SteamBootstrapper_InstallDirIncompatible"		"이 버전의 Windows에서는 비 ASCII 문자가 포함된 폴더 경로에서 %appname%을(를) 실행할 수 없습니다. %appname%을(를) 기본 폴더에 재설치하거나 Windows 7 또는 그 이후 버전의 OS를 설치하세요."
"SteamBootstrapper_InsufficientDiskSpace"		"%appname% 업데이트는 디스크 여유 공간 %nMegaBytes%MB가 있어야 가능합니다."
"SteamBootstrapper_FailedManifestLoad"		"패키지 manifest를 불러오지 못했습니다."
"SteamBootstrapper_FailedManifestVerify"		"패키지 manifest를 확인하지 못했습니다."
"SteamBootstrapper_FailedManifestOSType"		"시스템 OS 버전이 패키지 매니페스트의 운영 체제 유형과 호환되지 않습니다."
"SteamBootstrapper_FailedManifestSave"		"패키지 manifest를 저장하지 못했습니다."
"SteamBootstrapper_FailedPackageRead"			"패키지(%pkgName%)를 읽지 못했습니다.."
"SteamBootstrapper_CorruptPackageFile"		"손상된 패키지 파일(%pkgName%)입니다."
"SteamBootstrapper_FailedPackageUnzip"		"패키지(%pkgName%)의 압축을 풀지 못했습니다.."
"SteamBootstrapper_UpdateApplyFail"			"업데이트를 적용하지 못했습니다. 되돌리는 중..."
"SteamBootstrapper_UpdateCleanup"				"삭제 중..."
"SteamBootstrapper_UpdateCleanupFail"			"업데이트 후 삭제하지 못했습니다. 계속하는 중..."
"SteamBootstrapper_UpdateFinalizeFail"		"설치 manifest를 쓰지 못했습니다."
"SteamBootstrapper_UpdateComplete"			"업데이트 완료! %appname% 실행 중..."
"SteamBootstrapper_UpdateChecking"			"사용 가능한 업데이트 확인 중..."
"SteamBootstrapper_UpdatePrepare"				"업데이트 준비 중..."
"SteamBootstrapper_UpdateDownload"			"업데이트 다운로드 중..."
"SteamBootstrapper_UpdateDownloading"			"업데이트 다운로드 중...(%bytes%/%size%KB)"
"SteamBootstrapper_UpdateExtractingPackage"	"패키지 압축 푸는 중..."
"SteamBootstrapper_UpdateInstalling"			"업데이트 설치 중..."
"SteamBootstrapper_InstallVerify"				"설치 확인 중..."
"SteamBootstrapper_DownloadComplete"			"다운로드 완료."
"SteamBootstrapper_PercentComplete"			"%percent%%% 완료"
"SteamBootstrapper_NoNetwork"					"%appname% 업데이트 과정은 인터넷을 통해 진행됩니다. 네트워크 연결을 확인하고 다시 시도하세요."
"SteamBootstrapper_FailedMkdir"				"디렉터리 %dirname% 생성 실패, %errno% 오류"
"SteamBootstrapper_NotWriteable"				"%dirname% 디렉터리에 쓰기 작업을 할 수 없습니다."
"SteamBootstrapper_FailedDownloadHTTPError"	"다운로드 실패: http 오류(%errno%)"
"SteamBootstrapper_AlreadyRunningError"		"이 컴퓨터에서 %appname%이(가) 이미 다른 사용자 계정으로 실행 중입니다."
"SteamBootstrapper_MoveContentDir_DestExists" "%appname%의 게임 콘텐츠 저장 위치가 '~/Documents/Steam Content'에서 '~/Library/Application Support/Steam/SteamApps'로 변경되었습니다. 새로운 위치에 이미 파일이 존재하기 때문에 원래 위치의 게임 파일을 이동하지 못했습니다. 원래 위치의 파일을 직접 옮기거나 오래된 파일을 지우고 게임을 다시 다운로드할 수 있습니다. 계속 실행할까요?"
"SteamBootstrapper_MoveContentDir_CopyError"  "%appname%의 게임 콘텐츠 저장 위치가 '~/Documents/Steam Content'에서 '~/Library/Application Support/Steam/SteamApps'로 변경되었습니다. 원래 위치의 게임 파일은 이동하지 못했습니다. 원래 위치의 파일을 직접 옮기거나 오래된 파일을 지우고 게임을 다시 다운로드할 수 있습니다. 계속 실행할까요?"
"SteamBootstrapper_OK"				"확인"
"SteamBootstrapper_Cancel"			"취소"
"SteamBootstrapper_WindowTitle"			"%appname%"
"SteamBootstrapper_Error_Title"			"%appname% - 치명적인 오류"
"SteamBootstrapper_Warning_Title"			"%appname% - 경고"
"SteamBootstrapper_OperatingSystemNoLongerSupported" "%appname% 앱은 현재 사용 중인 운영 체제 버전에서 더 이상 지원되지 않습니다."
"SteamCmd_OperatingSystemNoLongerSupported" "%appname% 게임은 더 이상 현재 사용 중인 운영 체제 버전에서 지원되지 않습니다.\n-overrideminos 명령을 사용하여 이를 무시할 수 있습니다.\n%appname% 게임이 제대로 실행되지 않거나 향후 해당 명령이 제거될 수 있습니다.\n계속 사용하려면 지원되는 운영 체제 버전으로 업데이트하세요."
"SteamBootstrapper_OperatingSystemNoLongerSupportedOnBeta" "%appname% 게임은 더 이상 현재 사용 중인 운영 체제 버전에서 지원되지 않습니다.\n\n선택한 %appname% 베타를 종료하려면 '확인'을 클릭하세요."
"SteamBootstrapper_Starting"                  "시작 중..."
}
