name = "Eden RP Reforger";
author = "Eden RP Community";
version = "1.0.0";
description = "Complete conversion of Eden RP framework from Arma 3 to Arma Reforger using native Enfusion scripting";

// Mod dependencies
dependencies[] = {};

// Required game version
requiredVersion = 1.0;

// Mod type
modType = "Gameplay";

// Server mod
serverMod = 1;

// Client mod  
clientMod = 1;

// Mission compatibility
missionWhitelist[] = {};
missionBlacklist[] = {};

// Workshop information
workshopId = "";
publishedFileId = "";

// Mod logo
logo = "logo.paa";

// Mod overview
overview = "Complete Eden RP roleplay framework converted to Arma Reforger with all original systems preserved and enhanced.";

// Change log
changelog = "v1.0.0 - Initial release with complete Eden RP framework conversion";

// Tags
tags[] = {"Roleplay", "Life", "Civilian", "Police", "Medical", "Economy"};
