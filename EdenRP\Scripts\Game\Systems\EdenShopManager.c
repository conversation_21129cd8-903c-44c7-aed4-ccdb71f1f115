//! Eden Shop Manager - Handles all shop systems including general stores, weapon shops, and vehicle dealers
//! Converted from original shop systems

class EdenShopManager
{
    protected ref map<string, ref EdenShopData> m_Shops;
    protected ref map<string, ref EdenShopItem> m_ShopItems;
    protected ref map<string, ref array<string>> m_ShopInventories;
    protected ref map<string, int> m_ItemPrices;
    protected ref array<ref EdenShopLocation> m_ShopLocations;
    
    // Shop configuration
    protected float m_MedicDiscount;
    protected bool m_ShopsEnabled;
    protected int m_MaxPurchaseAmount;
    
    void EdenShopManager()
    {
        m_Shops = new map<string, ref EdenShopData>();
        m_ShopItems = new map<string, ref EdenShopItem>();
        m_ShopInventories = new map<string, ref array<string>>();
        m_ItemPrices = new map<string, int>();
        m_ShopLocations = new array<ref EdenShopLocation>();
        
        m_MedicDiscount = 0.75; // 25% discount for medics
        m_ShopsEnabled = true;
        m_MaxPurchaseAmount = 999999;
        
        InitializeShopSystem();
    }
    
    //! Initialize shop system
    void Initialize()
    {
        Print("[EdenShopManager] Initializing shop system...");
        
        SetupShops();
        SetupShopItems();
        LoadShopData();
        
        Print("[EdenShopManager] Shop system initialized");
    }
    
    //! Initialize shop system configuration
    protected void InitializeShopSystem()
    {
        SetupItemPrices();
        Print("[EdenShopManager] Shop system configuration initialized");
    }
    
    //! Setup item prices
    protected void SetupItemPrices()
    {
        // General items
        m_ItemPrices.Set("barrier", 1000);
        m_ItemPrices.Set("beer", 2000);
        m_ItemPrices.Set("blastingcharge", 15000);
        m_ItemPrices.Set("hackingterminal", 7000);
        m_ItemPrices.Set("boltcutter", 2500);
        m_ItemPrices.Set("fireaxe", 100);
        m_ItemPrices.Set("burger", 500);
        m_ItemPrices.Set("coffee", 1500);
        m_ItemPrices.Set("cupcake", 2500);
        m_ItemPrices.Set("defusekit", 2500);
        m_ItemPrices.Set("donuts", 120);
        m_ItemPrices.Set("fuelF", 850);
        m_ItemPrices.Set("gpstracker", 15000);
        m_ItemPrices.Set("egpstracker", 45000);
        m_ItemPrices.Set("gpsjammer", 33000);
        m_ItemPrices.Set("lockpick", 150);
        m_ItemPrices.Set("pepsi", 500);
        m_ItemPrices.Set("pickaxe", 1200);
        m_ItemPrices.Set("redgull", 1500);
        m_ItemPrices.Set("lollypop", 1500);
        m_ItemPrices.Set("scalpel", 35000);
        m_ItemPrices.Set("speedbomb", 700000);
        m_ItemPrices.Set("spikeStrip", 2500);
        m_ItemPrices.Set("takeoverterminal", 10000);
        m_ItemPrices.Set("storagebig", 150000);
        m_ItemPrices.Set("storagesmall", 75000);
        m_ItemPrices.Set("tbacon", 75);
        m_ItemPrices.Set("turtlesoup", 2500);
        m_ItemPrices.Set("water", 10);
        m_ItemPrices.Set("ziptie", 500);
        m_ItemPrices.Set("heliTowHook", 20000);
        m_ItemPrices.Set("fireworks", 2000);
        m_ItemPrices.Set("potato", 70);
        m_ItemPrices.Set("cream", 50);
        m_ItemPrices.Set("bloodbag", 7500);
        m_ItemPrices.Set("epiPen", 15000);
        m_ItemPrices.Set("dopeShot", 100000);
        m_ItemPrices.Set("blindfold", 1000);
        
        Print(string.Format("[EdenShopManager] Set up %1 item prices", m_ItemPrices.Count()));
    }
    
    //! Setup shops
    protected void SetupShops()
    {
        // General Store
        SetupShop("general", "General Store", "general_store");
        
        // Weapon Shops
        SetupShop("gun_store", "Gun Store", "weapon_shop");
        SetupShop("rebel_shop", "Rebel Outpost", "weapon_shop");
        
        // Vehicle Dealers
        SetupShop("car_dealer", "Car Dealer", "vehicle_shop");
        SetupShop("boat_dealer", "Boat Dealer", "vehicle_shop");
        SetupShop("air_dealer", "Aircraft Dealer", "vehicle_shop");
        
        // Clothing Stores
        SetupShop("clothing", "Clothing Store", "clothing_shop");
        
        // Specialty Shops
        SetupShop("market", "Market", "market");
        SetupShop("dopamine", "Dopamine Shop", "specialty");
        
        Print(string.Format("[EdenShopManager] Set up %1 shops", m_Shops.Count()));
    }
    
    //! Setup individual shop
    protected void SetupShop(string shopId, string shopName, string shopType)
    {
        EdenShopData shop = new EdenShopData();
        shop.SetShopId(shopId);
        shop.SetShopName(shopName);
        shop.SetShopType(shopType);
        shop.SetIsActive(true);
        
        m_Shops.Set(shopId, shop);
        
        // Initialize shop inventory
        array<string> inventory = new array<string>();
        m_ShopInventories.Set(shopId, inventory);
        
        // Setup shop-specific items
        SetupShopInventory(shopId, shopType);
    }
    
    //! Setup shop inventory based on type
    protected void SetupShopInventory(string shopId, string shopType)
    {
        array<string> inventory = m_ShopInventories.Get(shopId);
        
        switch (shopType)
        {
            case "general_store":
            {
                inventory.Insert("water");
                inventory.Insert("burger");
                inventory.Insert("coffee");
                inventory.Insert("donuts");
                inventory.Insert("tbacon");
                inventory.Insert("pepsi");
                inventory.Insert("redgull");
                inventory.Insert("pickaxe");
                inventory.Insert("lockpick");
                inventory.Insert("boltcutter");
                inventory.Insert("fireaxe");
                break;
            }
            
            case "weapon_shop":
            {
                inventory.Insert("defusekit");
                inventory.Insert("spikeStrip");
                inventory.Insert("ziptie");
                inventory.Insert("blindfold");
                break;
            }
            
            case "market":
            {
                // Market items are handled by the market system
                break;
            }
            
            case "specialty":
            {
                inventory.Insert("scalpel");
                inventory.Insert("bloodbag");
                inventory.Insert("epiPen");
                inventory.Insert("dopeShot");
                break;
            }
        }
    }
    
    //! Setup shop items
    protected void SetupShopItems()
    {
        foreach (string itemName, int price : m_ItemPrices)
        {
            EdenShopItem item = new EdenShopItem();
            item.SetItemName(itemName);
            item.SetBasePrice(price);
            item.SetCurrentPrice(price);
            item.SetIsAvailable(true);
            item.SetStockQuantity(-1); // Unlimited stock
            
            m_ShopItems.Set(itemName, item);
        }
        
        Print(string.Format("[EdenShopManager] Set up %1 shop items", m_ShopItems.Count()));
    }
    
    //! Purchase item from shop
    bool PurchaseItem(string playerId, string shopId, string itemName, int quantity)
    {
        if (playerId == "" || shopId == "" || itemName == "" || quantity <= 0)
            return false;
            
        if (!m_ShopsEnabled)
            return false;
            
        // Check if shop exists and is active
        if (!m_Shops.Contains(shopId))
            return false;
            
        EdenShopData shop = m_Shops.Get(shopId);
        if (!shop.IsActive())
            return false;
            
        // Check if item is available in shop
        if (!m_ShopInventories.Contains(shopId))
            return false;
            
        array<string> inventory = m_ShopInventories.Get(shopId);
        if (inventory.Find(itemName) == -1)
            return false;
            
        // Check if item exists
        if (!m_ShopItems.Contains(itemName))
            return false;
            
        EdenShopItem item = m_ShopItems.Get(itemName);
        if (!item.IsAvailable())
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Calculate price with discounts
        int basePrice = item.GetCurrentPrice();
        float discount = CalculateDiscount(playerId, shopId);
        int finalPrice = Math.Floor(basePrice * (1.0 - discount));
        int totalCost = finalPrice * quantity;
        
        // Check if player has enough money
        if (totalCost > playerComp.GetBankAccount())
            return false;
            
        // Check inventory space
        if (!CanAddToInventory(playerEntity, itemName, quantity))
            return false;
            
        // Process purchase
        playerComp.RemoveBankMoney(totalCost);
        AddToInventory(playerEntity, itemName, quantity);
        
        // Update shop statistics
        shop.AddTotalSales(totalCost);
        shop.AddItemsSold(quantity);
        
        // Log transaction
        if (totalCost >= 1000)
        {
            LogItemPurchase(playerId, playerComp.GetPlayerName(), itemName, quantity, totalCost);
        }
        
        // Handle special shop logic
        HandleSpecialShopLogic(shopId, itemName, quantity, totalCost, playerId);
        
        Print(string.Format("[EdenShopManager] %1 purchased %2x %3 for $%4 from %5", playerId, quantity, itemName, totalCost, shopId));
        return true;
    }
    
    //! Sell item to shop
    bool SellItem(string playerId, string shopId, string itemName, int quantity)
    {
        if (playerId == "" || shopId == "" || itemName == "" || quantity <= 0)
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player has the item
        if (!HasInventoryItem(playerEntity, itemName, quantity))
            return false;
            
        // Calculate sell price (usually lower than buy price)
        int sellPrice = CalculateSellPrice(itemName, quantity);
        
        // Process sale
        RemoveFromInventory(playerEntity, itemName, quantity);
        playerComp.AddBankMoney(sellPrice);
        
        // Update market prices if applicable
        UpdateMarketPrice(itemName, quantity);
        
        // Handle territory taxes
        float territoryTax = CalculateTerritoryTax(itemName, playerEntity.GetOrigin());
        if (territoryTax > 0)
        {
            int taxAmount = Math.Floor(sellPrice * territoryTax);
            playerComp.RemoveBankMoney(taxAmount);
            PayTerritoryTax(playerEntity.GetOrigin(), taxAmount);
        }
        
        Print(string.Format("[EdenShopManager] %1 sold %2x %3 for $%4 to %5", playerId, quantity, itemName, sellPrice, shopId));
        return true;
    }
    
    //! Calculate discount for player
    protected float CalculateDiscount(string playerId, string shopId)
    {
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return 0.0;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return 0.0;
            
        // Medic discount
        if (playerComp.GetFaction() == "medic")
        {
            return 1.0 - m_MedicDiscount; // 25% discount
        }
        
        // Other discounts could be added here (VIP, gang bonuses, etc.)
        
        return 0.0;
    }
    
    //! Calculate sell price
    protected int CalculateSellPrice(string itemName, int quantity)
    {
        // Check if it's a market item
        EdenMarketManager marketManager = GetMarketManager();
        if (marketManager)
        {
            int marketPrice = marketManager.GetMarketPrice(itemName);
            if (marketPrice > 0)
                return marketPrice * quantity;
        }
        
        // Use base shop price
        if (m_ItemPrices.Contains(itemName))
        {
            int basePrice = m_ItemPrices.Get(itemName);
            return Math.Floor(basePrice * 0.8) * quantity; // 80% of buy price
        }
        
        return 0;
    }
    
    //! Handle special shop logic
    protected void HandleSpecialShopLogic(string shopId, string itemName, int quantity, int totalCost, string playerId)
    {
        if (shopId == "dopamine")
        {
            // Handle dopamine shop special logic
            // This would involve finding the crate owner and paying them
            HandleDopamineShopPurchase(itemName, quantity, totalCost, playerId);
        }
    }
    
    //! Handle dopamine shop purchase
    protected void HandleDopamineShopPurchase(string itemName, int quantity, int totalCost, string playerId)
    {
        // Find nearest dopamine crate
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return;
            
        // Implementation would find the crate and pay the owner
        // This is a complex system from the original that involves finding
        // the nearest Land_Cargo10_yellow_F crate and paying its owner
    }
    
    //! Get shop data
    EdenShopData GetShopData(string shopId)
    {
        if (m_Shops.Contains(shopId))
            return m_Shops.Get(shopId);
        return null;
    }
    
    //! Get shop inventory
    array<string> GetShopInventory(string shopId)
    {
        if (m_ShopInventories.Contains(shopId))
            return m_ShopInventories.Get(shopId);
        return new array<string>();
    }
    
    //! Get item price
    int GetItemPrice(string itemName, string shopId = "")
    {
        if (m_ShopItems.Contains(itemName))
        {
            EdenShopItem item = m_ShopItems.Get(itemName);
            return item.GetCurrentPrice();
        }
        
        if (m_ItemPrices.Contains(itemName))
            return m_ItemPrices.Get(itemName);
            
        return -1;
    }
    
    //! Check if shop is active
    bool IsShopActive(string shopId)
    {
        if (m_Shops.Contains(shopId))
            return m_Shops.Get(shopId).IsActive();
        return false;
    }
    
    //! Enable/disable shop
    void SetShopActive(string shopId, bool active)
    {
        if (m_Shops.Contains(shopId))
        {
            EdenShopData shop = m_Shops.Get(shopId);
            shop.SetIsActive(active);
            
            Print(string.Format("[EdenShopManager] Shop %1 %2", shopId, active ? "enabled" : "disabled"));
        }
    }
    
    //! Get all shops
    array<ref EdenShopData> GetAllShops()
    {
        array<ref EdenShopData> shops = {};
        foreach (string shopId, EdenShopData shop : m_Shops)
        {
            shops.Insert(shop);
        }
        return shops;
    }
    
    //! Helper methods
    protected void LogItemPurchase(string playerId, string playerName, string itemName, int quantity, int totalCost)
    {
        // Implementation would log to database or file
        Print(string.Format("[EdenShopManager] Item Purchase: Player %1 (%2), Item %3, Quantity %4, Cost $%5", playerName, playerId, itemName, quantity, totalCost));
    }
    
    protected void UpdateMarketPrice(string itemName, int quantity)
    {
        EdenMarketManager marketManager = GetMarketManager();
        if (marketManager)
        {
            marketManager.ProcessItemSale(itemName, quantity);
        }
    }
    
    protected float CalculateTerritoryTax(string itemName, vector position)
    {
        // Implementation would check gang territory system
        return 0.0;
    }
    
    protected void PayTerritoryTax(vector position, int taxAmount)
    {
        // Implementation would pay tax to controlling gang
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadShopData() { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    protected EdenMarketManager GetMarketManager() { return null; }
    protected bool CanAddToInventory(IEntity playerEntity, string itemName, int quantity) { return true; }
    protected void AddToInventory(IEntity playerEntity, string itemName, int quantity) { }
    protected bool HasInventoryItem(IEntity playerEntity, string itemName, int quantity) { return true; }
    protected void RemoveFromInventory(IEntity playerEntity, string itemName, int quantity) { }
    
    //! Public configuration methods
    void SetMedicDiscount(float discount) { m_MedicDiscount = discount; }
    float GetMedicDiscount() { return m_MedicDiscount; }
    
    void SetShopsEnabled(bool enabled) { m_ShopsEnabled = enabled; }
    bool AreShopsEnabled() { return m_ShopsEnabled; }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenShopManager] Cleaning up shop system...");
        
        m_Shops.Clear();
        m_ShopItems.Clear();
        m_ShopInventories.Clear();
        m_ItemPrices.Clear();
        m_ShopLocations.Clear();
    }
}
