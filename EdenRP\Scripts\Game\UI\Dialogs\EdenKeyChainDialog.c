//! KeyChain dialog for Eden Reforger
//! Converted from original yMenuKeyChain.hpp
//! Manages player's vehicle keys and house keys

class EdenKeyChainDialog : EdenBaseDialog
{
    protected Widget m_VehicleKeysList;
    protected Widget m_HouseKeysList;
    protected Widget m_KeyTypeComboBox;
    protected Widget m_GiveKeyButton;
    protected Widget m_RemoveKeyButton;
    protected Widget m_RefreshButton;
    protected Widget m_SelectedKeyLabel;
    
    protected ref array<ref EdenVehicleKeyData> m_VehicleKeys;
    protected ref array<string> m_HouseKeys;
    protected int m_SelectedVehicleKeyIndex;
    protected int m_SelectedHouseKeyIndex;
    protected bool m_ShowingVehicleKeys;
    
    //! Constructor
    void EdenKeyChainDialog()
    {
        m_VehicleKeys = new array<ref EdenVehicleKeyData>();
        m_HouseKeys = new array<string>();
        m_SelectedVehicleKeyIndex = -1;
        m_SelectedHouseKeyIndex = -1;
        m_ShowingVehicleKeys = true;
    }
    
    //! Initialize the dialog
    override void OnCreate()
    {
        super.OnCreate();
        
        // Get widget references
        m_VehicleKeysList = m_Root.FindAnyWidget("VehicleKeysList");
        m_HouseKeysList = m_Root.FindAnyWidget("HouseKeysList");
        m_KeyTypeComboBox = m_Root.FindAnyWidget("KeyTypeComboBox");
        m_GiveKeyButton = m_Root.FindAnyWidget("GiveKeyButton");
        m_RemoveKeyButton = m_Root.FindAnyWidget("RemoveKeyButton");
        m_RefreshButton = m_Root.FindAnyWidget("RefreshButton");
        m_SelectedKeyLabel = m_Root.FindAnyWidget("SelectedKeyLabel");
        
        // Initialize display
        LoadPlayerKeys();
        RefreshKeyDisplay();
        UpdateSelectedKeyInfo();
    }
    
    //! Get dialog title
    override string GetDialogTitle()
    {
        return "Key Chain";
    }
    
    //! Get active tab ID
    override int GetActiveTabId()
    {
        return 4; // Keys tab
    }
    
    //! Load player's keys from data manager
    void LoadPlayerKeys()
    {
        EdenGameMode gameMode = EdenGameMode.Cast(GetGame().GetGameMode());
        if (!gameMode)
            return;
            
        EdenDataManager dataManager = gameMode.GetDataManager();
        if (!dataManager)
            return;
            
        IEntity player = GetGame().GetPlayerController().GetControlledEntity();
        if (!player)
            return;
            
        string playerId = player.GetPrefabData().GetPrefabName(); // Simplified ID
        
        // Load vehicle keys
        m_VehicleKeys.Clear();
        // TODO: Implement vehicle key loading from data manager
        // For now, create sample data
        CreateSampleVehicleKeys();
        
        // Load house keys
        m_HouseKeys.Clear();
        // TODO: Implement house key loading from data manager
        // For now, create sample data
        CreateSampleHouseKeys();
    }
    
    //! Create sample vehicle keys for testing
    void CreateSampleVehicleKeys()
    {
        EdenVehicleKeyData key1 = new EdenVehicleKeyData();
        key1.SetKeyId("vkey_001");
        key1.SetVehicleId("vehicle_001");
        key1.SetKeyType("Owner");
        key1.SetIsPermanent(true);
        m_VehicleKeys.Insert(key1);
        
        EdenVehicleKeyData key2 = new EdenVehicleKeyData();
        key2.SetKeyId("vkey_002");
        key2.SetVehicleId("vehicle_002");
        key2.SetKeyType("Temporary");
        key2.SetIsPermanent(false);
        key2.SetExpirationTime(GetGame().GetWorld().GetWorldTime() + 3600); // 1 hour
        m_VehicleKeys.Insert(key2);
    }
    
    //! Create sample house keys for testing
    void CreateSampleHouseKeys()
    {
        m_HouseKeys.Insert("House #1 - Kavala");
        m_HouseKeys.Insert("House #2 - Athira");
        m_HouseKeys.Insert("Apartment #3 - Pyrgos");
    }
    
    //! Refresh the key display
    void RefreshKeyDisplay()
    {
        if (m_ShowingVehicleKeys)
        {
            RefreshVehicleKeysList();
            if (m_VehicleKeysList) m_VehicleKeysList.SetVisible(true);
            if (m_HouseKeysList) m_HouseKeysList.SetVisible(false);
        }
        else
        {
            RefreshHouseKeysList();
            if (m_VehicleKeysList) m_VehicleKeysList.SetVisible(false);
            if (m_HouseKeysList) m_HouseKeysList.SetVisible(true);
        }
    }
    
    //! Refresh vehicle keys list
    void RefreshVehicleKeysList()
    {
        if (!m_VehicleKeysList)
            return;
            
        // Clear existing items
        Widget child = m_VehicleKeysList.GetChildren();
        while (child)
        {
            Widget next = child.GetSibling();
            child.RemoveFromHierarchy();
            child = next;
        }
        
        // Add vehicle keys
        for (int i = 0; i < m_VehicleKeys.Count(); i++)
        {
            EdenVehicleKeyData keyData = m_VehicleKeys[i];
            if (!keyData)
                continue;
                
            string keyText = FormatVehicleKeyDisplay(keyData, i);
            
            // Create list item widget
            Widget keyItem = GetGame().GetWorkspace().CreateWidgets("{2EFEA2AF1F38E7F0}UI/layouts/Menus/ContentBrowser/ContentBrowserEntry.layout", m_VehicleKeysList);
            if (keyItem)
            {
                TextWidget keyLabel = TextWidget.Cast(keyItem.FindAnyWidget("Name"));
                if (keyLabel)
                    keyLabel.SetText(keyText);
            }
        }
    }
    
    //! Refresh house keys list
    void RefreshHouseKeysList()
    {
        if (!m_HouseKeysList)
            return;
            
        // Clear existing items
        Widget child = m_HouseKeysList.GetChildren();
        while (child)
        {
            Widget next = child.GetSibling();
            child.RemoveFromHierarchy();
            child = next;
        }
        
        // Add house keys
        for (int i = 0; i < m_HouseKeys.Count(); i++)
        {
            string keyText = string.Format("[%1] %2", i + 1, m_HouseKeys[i]);
            
            // Create list item widget
            Widget keyItem = GetGame().GetWorkspace().CreateWidgets("{2EFEA2AF1F38E7F0}UI/layouts/Menus/ContentBrowser/ContentBrowserEntry.layout", m_HouseKeysList);
            if (keyItem)
            {
                TextWidget keyLabel = TextWidget.Cast(keyItem.FindAnyWidget("Name"));
                if (keyLabel)
                    keyLabel.SetText(keyText);
            }
        }
    }
    
    //! Format vehicle key display text
    string FormatVehicleKeyDisplay(EdenVehicleKeyData keyData, int index)
    {
        string keyType = keyData.GetKeyType();
        string vehicleId = keyData.GetVehicleId();
        string status = keyData.IsPermanent() ? "Permanent" : "Temporary";
        
        if (!keyData.IsPermanent())
        {
            int currentTime = GetGame().GetWorld().GetWorldTime();
            int timeLeft = keyData.GetExpirationTime() - currentTime;
            if (timeLeft > 0)
            {
                int minutesLeft = timeLeft / 60;
                status = string.Format("Expires in %1m", minutesLeft);
            }
            else
            {
                status = "EXPIRED";
            }
        }
        
        return string.Format("[%1] %2 - %3 (%4)", index + 1, vehicleId, keyType, status);
    }
    
    //! Update selected key information
    void UpdateSelectedKeyInfo()
    {
        if (!m_SelectedKeyLabel)
            return;
            
        string selectedInfo = "No key selected";
        
        if (m_ShowingVehicleKeys && m_SelectedVehicleKeyIndex >= 0 && m_SelectedVehicleKeyIndex < m_VehicleKeys.Count())
        {
            EdenVehicleKeyData keyData = m_VehicleKeys[m_SelectedVehicleKeyIndex];
            selectedInfo = string.Format("Selected: %1 (%2)", keyData.GetVehicleId(), keyData.GetKeyType());
        }
        else if (!m_ShowingVehicleKeys && m_SelectedHouseKeyIndex >= 0 && m_SelectedHouseKeyIndex < m_HouseKeys.Count())
        {
            selectedInfo = string.Format("Selected: %1", m_HouseKeys[m_SelectedHouseKeyIndex]);
        }
        
        m_SelectedKeyLabel.SetText(selectedInfo);
    }
    
    //! Handle widget click events
    override bool OnClick(Widget w, int x, int y, int button)
    {
        if (super.OnClick(w, x, y, button))
            return true;
            
        if (w == m_KeyTypeComboBox)
        {
            OnKeyTypeChanged();
            return true;
        }
        else if (w == m_GiveKeyButton)
        {
            OnGiveKeyButtonClick();
            return true;
        }
        else if (w == m_RemoveKeyButton)
        {
            OnRemoveKeyButtonClick();
            return true;
        }
        else if (w == m_RefreshButton)
        {
            OnRefreshButtonClick();
            return true;
        }
        
        return false;
    }
    
    //! Handle key type change
    void OnKeyTypeChanged()
    {
        // Toggle between vehicle keys and house keys
        m_ShowingVehicleKeys = !m_ShowingVehicleKeys;
        m_SelectedVehicleKeyIndex = -1;
        m_SelectedHouseKeyIndex = -1;
        RefreshKeyDisplay();
        UpdateSelectedKeyInfo();
    }
    
    //! Handle give key button click
    void OnGiveKeyButtonClick()
    {
        // TODO: Implement key giving functionality
        Print("EdenKeyChainDialog: Give key functionality not yet implemented");
    }
    
    //! Handle remove key button click
    void OnRemoveKeyButtonClick()
    {
        // TODO: Implement key removal functionality
        Print("EdenKeyChainDialog: Remove key functionality not yet implemented");
    }
    
    //! Handle refresh button click
    void OnRefreshButtonClick()
    {
        LoadPlayerKeys();
        RefreshKeyDisplay();
        UpdateSelectedKeyInfo();
    }
    
    //! Update dialog content
    override void OnUpdate()
    {
        super.OnUpdate();
        
        // Update key expiration status periodically
        if (m_ShowingVehicleKeys)
        {
            bool needsRefresh = false;
            int currentTime = GetGame().GetWorld().GetWorldTime();
            
            for (int i = 0; i < m_VehicleKeys.Count(); i++)
            {
                EdenVehicleKeyData keyData = m_VehicleKeys[i];
                if (keyData && !keyData.IsPermanent() && keyData.IsExpired(currentTime))
                {
                    needsRefresh = true;
                    break;
                }
            }
            
            if (needsRefresh)
            {
                RefreshVehicleKeysList();
                UpdateSelectedKeyInfo();
            }
        }
    }
}
