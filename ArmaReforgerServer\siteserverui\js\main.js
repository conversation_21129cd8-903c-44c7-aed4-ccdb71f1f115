(self.webpackChunksiteserverui=self.webpackChunksiteserverui||[]).push([[8792],{978:e=>{e.exports={LoadingWrapper:"_2wAKy-0ZkO_vhbiQCP9MgE",Static:"_1QfwpLmLTSuiIRLDzdY_7l",none:"_1F0lzP-Krz_y5P9ewZEvBD",bottomCircle:"_2qjZm0dB7I6lcRkZhNTqvD",noString:"_1Sy0pXVZOdWbgouFyD2zJj",Throbber:"lYUEjD8Qh3GM_ZrZeLwpI",throbber_small:"_2zbyh5jEDlV5HVD3iUve-k",throbber_medium:"_2CphUsHNDgIWItNIzeIldY",throbber_large:"_1x18vR7Qop8v8_G8qSR6CL",throbber_center_wrapper:"_3IYPzqtvB2ZE7x710d5x2C",ThrobberText:"_21nVi2jNuF_prxLY1mAaKK",blur:"_1ctjA2yjeB21eCDj_r1mVz",ThrobberRoundLoop:"_1O-cWE6nuIVO3x6-Onw0IU",roundOuterOutline:"_1Vv--mA6PueyEKXXMTwljt",roundOuter:"_2K0LzwjOXDopMHoqY_v_CO",roundFill:"_3sQFkavAEPhliH6DiFmHRH",ThrobberFillLoop:"ofdi9VLSRZ5W98WsXVHiq",topCircle:"_1LZffuoDq-N25wNHckxsf",circlePulse:"_1XN6ZJ1l8nVoUxp1WnOBqw",ThrobberTopCircleLoop:"_1jALk36CiS_T9Pg7vBjpIQ",ThrobberBottomCircleLoop:"_1KvOgdKiNE9XuSYUau3hKW",roundThrobber15:"_2LGHpu_-ihfOhQwdfR3Kiy",roundThrobber14:"_2FGCneH2AemMrIrZnZskw_",roundThrobber13:"_1X-5j16jaQntVrZfadibyc",roundThrobber12:"_1pj1Q3Ef4FdHDdl9QEtpxp",roundThrobber11:"xFmZaMe5nZpoTc5PQBKn3",roundThrobber10:"_3MGYowzMQ6TwbH3fu9SVjt",roundThrobber09:"ktVJ42jiVhWEVV9O6z2FK",roundThrobber08:"_3s_7my869lIOj-1vnqJ39y",roundThrobber07:"_11MVp53Me_frOuaJfsQ4FE",roundThrobber06:"_2cl-maglen-RF0YQjShgqG",roundThrobber05:"_3-JE0FpIzECzeqtd5cyjGI",roundThrobber04:"_1xNIKvHdzLhBldONo6yIZs",roundThrobber03:"_1S17yicfcrPc11m83ydGp3",roundThrobber02:"_1-oUJqmCLOoGqVJz5dgj6J",roundThrobber01:"_2tXgejjqNKy6cSX1Lv7PrG",ThrobberRoundLoopThickness:"_1fs4RGcMBRzbQQirDBiFoM",throbber_xlarge:"_1gYeZMYKBDqNpVuw58_LCl",throbber_xxlarge:"_3j-p4JOqdpaF4obGtIeAB",ThrobberDelayAppear:"I3aEq3lbK7Pm8ujENvtri",Visible:"_2SwSJd-DlX2dRiDGxZWHI5",NewThrobber:"_1m8iQOSVziKDHiInrfskv_"}},8042:(e,t,r)=>{var a={"./shared_arabic.json":[8476,8476],"./shared_brazilian.json":[9574,9574],"./shared_bulgarian.json":[3789,3789],"./shared_czech.json":[815,815],"./shared_danish.json":[4289,4289],"./shared_dutch.json":[4978,4978],"./shared_english.json":[3800,3800],"./shared_finnish.json":[3907,3907],"./shared_french.json":[5040,5040],"./shared_german.json":[4750,4750],"./shared_greek.json":[9668,9668],"./shared_hungarian.json":[5233,5233],"./shared_indonesian.json":[200,200],"./shared_italian.json":[1864,1864],"./shared_japanese.json":[7263,7263],"./shared_koreana.json":[295,295],"./shared_latam.json":[559,559],"./shared_norwegian.json":[6512,6512],"./shared_polish.json":[2889,2889],"./shared_portuguese.json":[2269,2269],"./shared_romanian.json":[4419,4419],"./shared_russian.json":[5777,5777],"./shared_sc_schinese.json":[3723,3723],"./shared_schinese.json":[5436,5436],"./shared_spanish.json":[6736,6736],"./shared_swedish.json":[4625,4625],"./shared_tchinese.json":[5191,5191],"./shared_thai.json":[4230,4230],"./shared_turkish.json":[4792,4792],"./shared_ukrainian.json":[7246,7246],"./shared_vietnamese.json":[9863,9863]};function n(e){if(!r.o(a,e))return Promise.resolve().then((()=>{var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=a[e],n=t[0];return r.e(t[1]).then((()=>r.t(n,19)))}n.keys=()=>Object.keys(a),n.id=8042,e.exports=n},4749:(e,t,r)=>{"use strict";var a=r(626),n=r(4844);function s(e,t,...r){try{console.assert?0==r.length?console.assert(!!e,t):console.assert(!!e,t,...r):e||console.warn(t,...r)}catch(e){}}var i=r(4629),o=r(3288);function l(...e){return e.reduce(((e,t)=>t?"string"==typeof t?e?`${e} ${t}`:t:"object"==typeof t?e?`${e} ${c(t)}`:c(t):e:e),"")}function c(e){return Object.keys(e).reduce(((t,r)=>e[r]?t?`${t} ${r}`:r:t),"")}var u=r(978),m=r.n(u);const d=r.p+"images/steam_spinner.png?v=8669e97b288da32670e77181618c3dfb";function _(){return!!window.document}let g;function p(){if(!_())return g||(g=h()),g;let e=function(e){if(!_()||!window.document.cookie)return null;const t=document.cookie.match("(^|; )"+e+"=([^;]*)");return t&&t[2]?decodeURIComponent(t[2]):null}("sessionid");return e||(e=h()),e}function h(){const e=function(){let e="";for(let a=0;a<24;a++)e+=(t=0,r=35,t=Math.ceil(t),r=Math.floor(r),Math.floor(Math.random()*(r-t+1))+t).toString(36);var t,r;return e}();return function(e,t,r,a){if(!_())return;a||(a="/");let n="";if(void 0!==r&&r){const e=new Date;e.setTime(e.getTime()+864e5*r),n="; expires="+e.toUTCString()}document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+n+";path="+a}("sessionid",e,0),e}let b=new Set,S=!1;const E={EUNIVERSE:0,WEB_UNIVERSE:"",LANGUAGE:"english",SUPPORTED_LANGUAGES:[],COUNTRY:"",AVATAR_BASE_URL:"",MEDIA_CDN_COMMUNITY_URL:"",MEDIA_CDN_URL:"",CLAN_CDN_ASSET_URL:"",VIDEO_CDN_URL:"",COMMUNITY_CDN_URL:"",COMMUNITY_CDN_ASSET_URL:"",BASE_URL_SHARED_CDN:"",STORE_CDN_URL:"",PUBLIC_SHARED_URL:"",COMMUNITY_BASE_URL:"",CHAT_BASE_URL:"",STORE_BASE_URL:"",STORE_CHECKOUT_BASE_URL:"",LOGIN_BASE_URL:"",SUPPORT_BASE_URL:"",STORE_ICON_BASE_URL:"",STORE_ITEM_BASE_URL:"",IMG_URL:"",STEAMTV_BASE_URL:"",HELP_BASE_URL:"",PARTNER_BASE_URL:"",STATS_BASE_URL:"",INTERNAL_STATS_BASE_URL:"",BASE_URL_STORE_CDN_ASSETS:"",IN_CLIENT:!1,USE_POPUPS:!1,IN_MOBILE:!1,IN_MOBILE_WEBVIEW:!1,IN_TENFOOT:!1,PLATFORM:"",SNR:"",LAUNCHER_TYPE:0,EREALM:0,IN_CHROMEOS:!1,TESLA:!1,LOCAL_HOSTNAME:"",WEBAPI_BASE_URL:"",TOKEN_URL:"",BUILD_TIMESTAMP:0,PAGE_TIMESTAMP:0,FROM_WEB:!1,WEBSITE_ID:"Unknown",get SESSIONID(){return p()},FRIENDSUI_BETA:!1,STEAM_TV:!1,DEV_MODE:!1,IN_STEAMUI:!1,IN_GAMEPADUI:!1,IN_STEAMUI_SHARED_CONTEXT:!1,DECK_DISPLAY_MODE:!1,ON_DECK:!1,ON_STEAMOS:!1,IN_GAMESCOPE:!1,IN_LOGIN:!1,IN_LOGIN_REFRESH:!1,USE_LONGEST_LOC_STRING:!1,SILENT_STARTUP:!1,CLIENT_SESSION:0};const y={logged_in:!1,steamid:"",accountid:0,account_name:"",token:void 0,token_use_id:void 0,webapi_token:"",authwgtoken:"",is_support:!1,is_limited:!1,is_partner_member:!1,short_url:"",country_code:"",excluded_content_descriptors:[3,4,1]};a.createContext({});function C(e,t=""){switch(e){case 0:return"english";case 1:return"german";case 2:return"french";case 3:return"italian";case 4:return"koreana";case 5:return"spanish";case 6:return"schinese";case 7:return"tchinese";case 8:return"russian";case 9:return"thai";case 10:return"japanese";case 11:return"portuguese";case 12:return"polish";case 13:return"danish";case 14:return"dutch";case 15:return"finnish";case 16:return"norwegian";case 17:return"swedish";case 18:return"hungarian";case 19:return"czech";case 20:return"romanian";case 21:return"turkish";case 25:return"arabic";case 22:return"brazilian";case 23:return"bulgarian";case 24:return"greek";case 26:return"ukrainian";case 27:return"latam";case 28:return"vietnamese";case 29:return"sc_schinese";case 30:return"indonesian";default:return t}}function f(e,t=0){switch(e){case"english":return 0;case"german":return 1;case"french":return 2;case"italian":return 3;case"korean":case"koreana":return 4;case"spanish":return 5;case"schinese":return 6;case"tchinese":return 7;case"russian":return 8;case"thai":return 9;case"japanese":return 10;case"portuguese":return 11;case"polish":return 12;case"danish":return 13;case"dutch":return 14;case"finnish":return 15;case"norwegian":return 16;case"swedish":return 17;case"hungarian":return 18;case"czech":return 19;case"romanian":return 20;case"turkish":return 21;case"arabic":return 25;case"brazilian":return 22;case"bulgarian":return 23;case"greek":return 24;case"ukrainian":return 26;case"latam":return 27;case"vietnamese":return 28;case"sc_schinese":return 29;case"indonesian":return 30;default:return t}}var w,B,k,M,v,T,N,P,I,R,L,F,A,z,x,O,W;!function(e){e[e.k_EConnectivityTestResult_Unknown=0]="k_EConnectivityTestResult_Unknown",e[e.k_EConnectivityTestResult_Connected=1]="k_EConnectivityTestResult_Connected",e[e.k_EConnectivityTestResult_CaptivePortal=2]="k_EConnectivityTestResult_CaptivePortal",e[e.k_EConnectivityTestResult_TimedOut=3]="k_EConnectivityTestResult_TimedOut",e[e.k_EConnectivityTestResult_Failed=4]="k_EConnectivityTestResult_Failed",e[e.k_EConnectivityTestResult_WifiDisabled=5]="k_EConnectivityTestResult_WifiDisabled",e[e.k_EConnectivityTestResult_NoLAN=6]="k_EConnectivityTestResult_NoLAN"}(w||(w={})),function(e){e[e.k_ENetFakeLocalSystemState_Normal=0]="k_ENetFakeLocalSystemState_Normal",e[e.k_ENetFakeLocalSystemState_NoLAN=1]="k_ENetFakeLocalSystemState_NoLAN",e[e.k_ENetFakeLocalSystemState_CaptivePortal_Redirected=2]="k_ENetFakeLocalSystemState_CaptivePortal_Redirected",e[e.k_ENetFakeLocalSystemState_CaptivePortal_InPlace=3]="k_ENetFakeLocalSystemState_CaptivePortal_InPlace",e[e.k_ENetFakeLocalSystemState_NoInternet=4]="k_ENetFakeLocalSystemState_NoInternet",e[e.k_ENetFakeLocalSystemState_NoSteam=5]="k_ENetFakeLocalSystemState_NoSteam"}(B||(B={})),function(e){e[e.k_ESuspendResumeProgressState_Invalid=0]="k_ESuspendResumeProgressState_Invalid",e[e.k_ESuspendResumeProgressState_Complete=1]="k_ESuspendResumeProgressState_Complete",e[e.k_ESuspendResumeProgressState_CloudSync=2]="k_ESuspendResumeProgressState_CloudSync",e[e.k_ESuspendResumeProgressState_LoggingIn=3]="k_ESuspendResumeProgressState_LoggingIn",e[e.k_ESuspendResumeProgressState_WaitingForApp=4]="k_ESuspendResumeProgressState_WaitingForApp",e[e.k_ESuspendResumeProgressState_Working=5]="k_ESuspendResumeProgressState_Working"}(k||(k={})),function(e){e[e.k_EFloatingGamepadTextInputModeModeSingleLine=0]="k_EFloatingGamepadTextInputModeModeSingleLine",e[e.k_EFloatingGamepadTextInputModeModeMultipleLines=1]="k_EFloatingGamepadTextInputModeModeMultipleLines",e[e.k_EFloatingGamepadTextInputModeModeEmail=2]="k_EFloatingGamepadTextInputModeModeEmail",e[e.k_EFloatingGamepadTextInputModeModeNumeric=3]="k_EFloatingGamepadTextInputModeModeNumeric"}(M||(M={})),function(e){e[e.k_EAppUpdateContentType_Content=0]="k_EAppUpdateContentType_Content",e[e.k_EAppUpdateContentType_Workshop=1]="k_EAppUpdateContentType_Workshop",e[e.k_EAppUpdateContentType_Shader=2]="k_EAppUpdateContentType_Shader",e[e.k_EAppUpdateContentType_Max=3]="k_EAppUpdateContentType_Max"}(v||(v={})),function(e){e[e.k_EAppUpdateProgress_VerifyingInstalledFiles=0]="k_EAppUpdateProgress_VerifyingInstalledFiles",e[e.k_EAppUpdateProgress_Preallocating=1]="k_EAppUpdateProgress_Preallocating",e[e.k_EAppUpdateProgress_Download=2]="k_EAppUpdateProgress_Download",e[e.k_EAppUpdateProgress_Staging=3]="k_EAppUpdateProgress_Staging",e[e.k_EAppUpdateProgress_VerifyingStagedFiles=4]="k_EAppUpdateProgress_VerifyingStagedFiles",e[e.k_EAppUpdateProgress_Copying=5]="k_EAppUpdateProgress_Copying",e[e.k_EAppUpdateProgress_Committing=6]="k_EAppUpdateProgress_Committing",e[e.k_EAppUpdateProgress_Max=7]="k_EAppUpdateProgress_Max"}(T||(T={})),function(e){e[e.k_EOverlayToStoreFlag_None=0]="k_EOverlayToStoreFlag_None",e[e.k_EOverlayToStoreFlag_AddToCart=1]="k_EOverlayToStoreFlag_AddToCart",e[e.k_EOverlayToStoreFlag_AddToCartAndShow=2]="k_EOverlayToStoreFlag_AddToCartAndShow"}(N||(N={})),function(e){e[e.k_EActivateGameOverlayToWebPageMode_Default=0]="k_EActivateGameOverlayToWebPageMode_Default",e[e.k_EActivateGameOverlayToWebPageMode_Modal=1]="k_EActivateGameOverlayToWebPageMode_Modal"}(P||(P={})),function(e){e[e.k_EGamingDeviceType_Unknown=0]="k_EGamingDeviceType_Unknown",e[e.k_EGamingDeviceType_StandardPC=1]="k_EGamingDeviceType_StandardPC",e[e.k_EGamingDeviceType_Console=256]="k_EGamingDeviceType_Console",e[e.k_EGamingDeviceType_PS3=272]="k_EGamingDeviceType_PS3",e[e.k_EGamingDeviceType_Steambox=288]="k_EGamingDeviceType_Steambox",e[e.k_EGamingDeviceType_Tesla=320]="k_EGamingDeviceType_Tesla",e[e.k_EGamingDeviceType_Handheld=512]="k_EGamingDeviceType_Handheld",e[e.k_EGamingDeviceType_Phone=528]="k_EGamingDeviceType_Phone",e[e.k_EGamingDeviceType_SteamDeck=544]="k_EGamingDeviceType_SteamDeck"}(I||(I={})),function(e){e[e.k_ELoginUIStyleOld=0]="k_ELoginUIStyleOld",e[e.k_ELoginUIStyleNewWithoutQRCode=1]="k_ELoginUIStyleNewWithoutQRCode",e[e.k_ELoginUIStyleNew=2]="k_ELoginUIStyleNew"}(R||(R={})),function(e){e[e.k_ECommunityProfileItemProperty_ImageSmall=0]="k_ECommunityProfileItemProperty_ImageSmall",e[e.k_ECommunityProfileItemProperty_ImageLarge=1]="k_ECommunityProfileItemProperty_ImageLarge",e[e.k_ECommunityProfileItemProperty_InternalName=2]="k_ECommunityProfileItemProperty_InternalName",e[e.k_ECommunityProfileItemProperty_Title=3]="k_ECommunityProfileItemProperty_Title",e[e.k_ECommunityProfileItemProperty_Description=4]="k_ECommunityProfileItemProperty_Description",e[e.k_ECommunityProfileItemProperty_AppID=5]="k_ECommunityProfileItemProperty_AppID",e[e.k_ECommunityProfileItemProperty_TypeID=6]="k_ECommunityProfileItemProperty_TypeID",e[e.k_ECommunityProfileItemProperty_Class=7]="k_ECommunityProfileItemProperty_Class",e[e.k_ECommunityProfileItemProperty_MovieWebM=8]="k_ECommunityProfileItemProperty_MovieWebM",e[e.k_ECommunityProfileItemProperty_MovieMP4=9]="k_ECommunityProfileItemProperty_MovieMP4",e[e.k_ECommunityProfileItemProperty_MovieWebMSmall=10]="k_ECommunityProfileItemProperty_MovieWebMSmall",e[e.k_ECommunityProfileItemProperty_MovieMP4Small=11]="k_ECommunityProfileItemProperty_MovieMP4Small"}(L||(L={})),function(e){e[e.k_ERaiseGameWindowResult_NotRunning=1]="k_ERaiseGameWindowResult_NotRunning",e[e.k_ERaiseGameWindowResult_Success=2]="k_ERaiseGameWindowResult_Success",e[e.k_ERaiseGameWindowResult_Failure=3]="k_ERaiseGameWindowResult_Failure"}(F||(F={})),function(e){e[e.k_EPositionInvalid=-1]="k_EPositionInvalid",e[e.k_EPositionTopLeft=0]="k_EPositionTopLeft",e[e.k_EPositionTopRight=1]="k_EPositionTopRight",e[e.k_EPositionBottomLeft=2]="k_EPositionBottomLeft",e[e.k_EPositionBottomRight=3]="k_EPositionBottomRight"}(A||(A={})),function(e){e[e.k_EAppReleaseState_Unknown=0]="k_EAppReleaseState_Unknown",e[e.k_EAppReleaseState_Unavailable=1]="k_EAppReleaseState_Unavailable",e[e.k_EAppReleaseState_Prerelease=2]="k_EAppReleaseState_Prerelease",e[e.k_EAppReleaseState_PreloadOnly=3]="k_EAppReleaseState_PreloadOnly",e[e.k_EAppReleaseState_Released=4]="k_EAppReleaseState_Released",e[e.k_EAppReleaseState_Disabled=5]="k_EAppReleaseState_Disabled"}(z||(z={})),function(e){e[e.k_EGameIDTypeApp=0]="k_EGameIDTypeApp",e[e.k_EGameIDTypeGameMod=1]="k_EGameIDTypeGameMod",e[e.k_EGameIDTypeShortcut=2]="k_EGameIDTypeShortcut",e[e.k_EGameIDTypeP2P=3]="k_EGameIDTypeP2P"}(x||(x={})),function(e){e[e.k_EInstallMgrStateNone=0]="k_EInstallMgrStateNone",e[e.k_EInstallMgrStateSetup=1]="k_EInstallMgrStateSetup",e[e.k_EInstallMgrStateWaitLicense=2]="k_EInstallMgrStateWaitLicense",e[e.k_EInstallMgrStateFreeLicense=3]="k_EInstallMgrStateFreeLicense",e[e.k_EInstallMgrStateShowCDKey=4]="k_EInstallMgrStateShowCDKey",e[e.k_EInstallMgrStateWaitAppInfo=5]="k_EInstallMgrStateWaitAppInfo",e[e.k_EInstallMgrStateShowPassword=6]="k_EInstallMgrStateShowPassword",e[e.k_EInstallMgrStateShowConfig=7]="k_EInstallMgrStateShowConfig",e[e.k_EInstallMgrStateShowEULAs=8]="k_EInstallMgrStateShowEULAs",e[e.k_EInstallMgrStateCreateApps=9]="k_EInstallMgrStateCreateApps",e[e.k_EInstallMgrStateReadFromMedia=10]="k_EInstallMgrStateReadFromMedia",e[e.k_EInstallMgrStateShowChangeMedia=11]="k_EInstallMgrStateShowChangeMedia",e[e.k_EInstallMgrStateWaitLegacyCDKeys=12]="k_EInstallMgrStateWaitLegacyCDKeys",e[e.k_EInstallMgrStateShowSignup=13]="k_EInstallMgrStateShowSignup",e[e.k_EInstallMgrStateComplete=14]="k_EInstallMgrStateComplete",e[e.k_EInstallMgrStateFailed=15]="k_EInstallMgrStateFailed",e[e.k_EInstallMgrStateCanceled=16]="k_EInstallMgrStateCanceled"}(O||(O={})),function(e){e[e.k_EWindowBringToFrontInvalid=0]="k_EWindowBringToFrontInvalid",e[e.k_EWindowBringToFrontAndForceOS=1]="k_EWindowBringToFrontAndForceOS",e[e.k_EWindowBringToFrontWithoutForcingOS=2]="k_EWindowBringToFrontWithoutForcingOS"}(W||(W={}));window.Config,window.UserConfig,window.Config;window.Config&&Object.assign(E,window.Config),window.UserConfig&&Object.assign(y,window.UserConfig);const G=a.memo((function(e){const{className:t,size:r,string:n,position:s,static:i,msDelayAppear:o}=e;let c=[m().LoadingWrapper,"SteamLogoThrobber",U(r)];const[u,_]=a.useState(!o),g=!(E.IN_CLIENT&&function(e){switch(e){default:break;case 4:case 1:case 8:return!0}return!1}(E.LAUNCHER_TYPE));return(0,a.useEffect)((()=>{if(u)return;const e=setTimeout((()=>_(!0)),o);return()=>clearTimeout(e)}),[o,u]),void 0===n&&c.push(m().noString),t&&c.push(t),i&&c.push(m().Static),a.createElement("div",{className:l("center"==s&&m().throbber_center_wrapper,!!o&&m().ThrobberDelayAppear,u&&m().Visible)},u&&g&&a.createElement("div",{className:c.join(" ")},a.createElement("div",{className:m().NewThrobber},a.createElement("img",{src:d}))),Boolean(n)&&a.createElement("div",{className:m().ThrobberText},n))}));a.memo((function(e){const{className:t,size:r,string:n,position:s,static:i,msDelayAppear:o}=e;let c=[m().LoadingWrapper,"SteamLogoThrobber",U(r)];const[u,d]=a.useState(!o);return(0,a.useEffect)((()=>{if(u)return;const e=setTimeout((()=>d(!0)),o);return()=>clearTimeout(e)}),[o,u]),void 0===n&&c.push(m().noString),t&&c.push(t),i&&c.push(m().Static),a.createElement("div",{className:l("center"==s&&m().throbber_center_wrapper,!!o&&m().ThrobberDelayAppear,u&&m().Visible)},u&&a.createElement("div",{className:c.join(" ")},a.createElement("div",{className:m().Throbber},a.createElement(j,{className:m().base}),a.createElement(j,{className:m().blur}))),Boolean(n)&&a.createElement("div",{className:m().ThrobberText},n))}));function U(e){switch(e){case"small":return m().throbber_small;case"medium":return m().throbber_medium;case"xlarge":return m().throbber_xlarge;case"xxlarge":return m().throbber_xxlarge;default:return m().throbber_large}}function j(e){let t="SVGIcon_Button SVGIcon_Throbber ";return e.className&&(t+=e.className),a.createElement("svg",{version:"1.1",id:"base",xmlns:"http://www.w3.org/2000/svg",className:t,x:"0px",y:"0px",width:"256px",height:"256px",viewBox:"0 0 256 256"},a.createElement("g",{className:m().partCircle},a.createElement("path",{className:m().roundOuter,fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895"}),a.createElement("path",{className:m().roundOuter,fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M201.432,101.166"}),a.createElement("path",{className:m().roundOuter,fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754"})),a.createElement("g",{className:m().mainOutline},a.createElement("path",{className:m().roundFill,fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundOuterOutline,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber01,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber02,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber03,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber04,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber05,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber06,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber07,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber08,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber09,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber10,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber11,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber12,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber13,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber14,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"}),a.createElement("path",{className:m().roundThrobber15,strokeLinecap:"butt",fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",d:"M127.82,23.895 c-54.686,0-99.487,42.167-103.745,95.754l55.797,23.069c4.729-3.231,10.44-5.122,16.584-5.122c0.551,0,1.098,0.014,1.642,0.047 l24.815-35.968c0-0.17-0.004-0.338-0.004-0.509c0-21.647,17.61-39.261,39.26-39.261s39.263,17.613,39.263,39.261 c0,21.65-17.611,39.264-39.263,39.264c-0.299,0-0.593-0.007-0.887-0.014l-35.392,25.251c0.018,0.462,0.035,0.931,0.035,1.396 c0,16.252-13.22,29.472-29.469,29.472c-14.265,0-26.19-10.185-28.892-23.666L27.66,156.37 c12.355,43.698,52.503,75.733,100.16,75.733c57.495,0,104.104-46.61,104.104-104.105S185.314,23.895,127.82,23.895z"})),a.createElement("g",{className:m().bottomCircle},a.createElement("path",{fill:"#ffffff",d:"M89.226,181.579L76.5,176.321c2.256,4.696,6.159,8.628,11.339,10.786 c11.197,4.668,24.11-0.647,28.779-11.854c2.259-5.425,2.274-11.405,0.033-16.841c-2.237-5.436-6.46-9.675-11.886-11.938 c-5.384-2.24-11.151-2.156-16.22-0.244l13.146,5.436c8.261,3.443,12.166,12.93,8.725,21.189 C106.976,181.115,97.486,185.022,89.226,181.579"})),a.createElement("g",{className:m().topCircle},a.createElement("circle",{fill:"none",stroke:"#ffffff",strokeWidth:"6",strokeMiterlimit:"10",cx:"161.731",cy:"101.274",r:"23.019"})))}var D=r(3175);function H(e,t,r){return{get(){let e=r.value.bind(this);return Object.prototype.hasOwnProperty.call(this,t)||Object.defineProperty(this,t,{value:e}),e}}}var V=r(613),q=r.n(V);const $=V.BinaryReader.prototype,K=V.BinaryWriter.prototype;function Q(e){const t={},{fields:r}=e;for(const e in r){const a=r[e];t[a.n]=a}return t}function X(e,t){const{proto:r,fields:a}=e,n=new r;if(null==t)return n;for(const e in a){const{n:r,c:s,r:i,d:o,q:l}=a[e];if(!Object.prototype.hasOwnProperty.call(t,e))continue;const c=t[e];s?i?V.Message.setRepeatedWrapperField(n,r,Array.isArray(c)?c.map((e=>s.fromObject(e))):[]):V.Message.setWrapperField(n,r,s.fromObject(c)):V.Message.setField(n,r,c)}return n}function Y(e,t,r){const{proto:a,fields:n}=e,s={};for(const e in n){const{n:a,c:i,r:o,d:l,q:c}=n[e];if(i)if(o)s[e]=V.Message.toObjectList(V.Message.getRepeatedWrapperField(r,i,a),i.toObject,t);else{const n=V.Message.getWrapperField(r,i,a,c?1:0);n&&(s[e]=i.toObject(t,n))}else{const t=V.Message.getFieldWithDefault(r,a,void 0!==l?l:null);(null!==t||c)&&(s[e]=t)}}return t&&(s.$jspbMessageInstance=r),s}function Z(e,t,r){for(;r.nextField()&&!r.isEndGroup();){const a=e[r.getFieldNumber()];if(a){const{n:e,c:n,r:s,d:i,q:o,br:l}=a;if(n){const a=new n;r.readMessage(a,n.deserializeBinaryFromReader),s?V.Message.addToRepeatedWrapperField(t,e,a,n):V.Message.setWrapperField(t,e,a)}else if(l){const a=l.call(r);s?V.Message.addToRepeatedField(t,e,a):V.Message.setField(t,e,a)}else console.assert(!!l,`Reader func not set for field number ${e} in class ${n}`),r.skipField()}else r.skipField()}return t}function J(e,t,r){const{fields:a}=e;for(const e in a){const{n,c:s,r:i,d:o,q:l,bw:c}=a[e];if(s)if(i){const e=V.Message.getRepeatedWrapperField(t,s,n);(e&&e.length||l)&&r.writeRepeatedMessage(n,e,s.serializeBinaryToWriter)}else{const e=V.Message.getWrapperField(t,s,n,l?1:0);e&&r.writeMessage(n,e,s.serializeBinaryToWriter)}else if(c){const e=V.Message.getField(t,n);void 0!==e&&c.call(r,n,e)}else console.assert(!!c,`Writer func not set for field number ${n} in class ${s}`)}}function ee(e){const t=e.proto;for(const r in e.fields){const a=e.fields[r],{n,c:s,r:i,d:o,q:l}=a;Object.prototype.hasOwnProperty.call(a,"d")?t.prototype[r]=te(V.Message.getFieldWithDefault,n,o):t.prototype[r]=s?i?te(V.Message.getRepeatedWrapperField,s,n):re(s,n):te(V.Message.getField,n),t.prototype[`set_${r}`]=ae(s?i?V.Message.setRepeatedWrapperField:V.Message.setWrapperField:V.Message.setField,n),i&&(t.prototype[`add_${r}`]=ne(n,s))}}function te(e,...t){return function(){return e(this,...t)}}function re(e,t){return function(r=!0){return V.Message.getWrapperField(this,e,t,r?1:0)}}function ae(e,t){return function(r){return e(this,t,r)}}function ne(e,t){return t?function(r,a){return V.Message.addToRepeatedWrapperField(this,e,r,t,a)}:function(t,r){V.Message.addToRepeatedField(this,e,t,r)}}V.Message;V.Message;class se extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),se.prototype.dst_gcid_queue||ee(se.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return se.sm_m||(se.sm_m={proto:se,fields:{dst_gcid_queue:{n:1,br:$.readUint64String,bw:K.writeUint64String},dst_gc_dir_index:{n:2,br:$.readUint32,bw:K.writeUint32}}}),se.sm_m}static MBF(){return se.sm_mbf||(se.sm_mbf=Q(se.M())),se.sm_mbf}toObject(e=!1){return se.toObject(e,this)}static toObject(e,t){return Y(se.M(),e,t)}static fromObject(e){return X(se.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new se;return se.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(se.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return se.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(se.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return se.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"CMsgGCRoutingProtoBufHeader"}}class ie extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),ie.prototype.steamid||ee(ie.M()),V.Message.initialize(this,e,0,-1,[27,41],null)}static sm_m;static sm_mbf;static M(){return ie.sm_m||(ie.sm_m={proto:ie,fields:{steamid:{n:1,br:$.readFixed64String,bw:K.writeFixed64String},client_sessionid:{n:2,br:$.readInt32,bw:K.writeInt32},routing_appid:{n:3,br:$.readUint32,bw:K.writeUint32},jobid_source:{n:10,d:"18446744073709551615",br:$.readFixed64String,bw:K.writeFixed64String},jobid_target:{n:11,d:"18446744073709551615",br:$.readFixed64String,bw:K.writeFixed64String},target_job_name:{n:12,br:$.readString,bw:K.writeString},seq_num:{n:24,br:$.readInt32,bw:K.writeInt32},eresult:{n:13,d:2,br:$.readInt32,bw:K.writeInt32},error_message:{n:14,br:$.readString,bw:K.writeString},ip:{n:15,br:$.readUint32,bw:K.writeUint32},ip_v6:{n:29,br:$.readBytes,bw:K.writeBytes},auth_account_flags:{n:16,br:$.readUint32,bw:K.writeUint32},token_source:{n:22,br:$.readUint32,bw:K.writeUint32},admin_spoofing_user:{n:23,br:$.readBool,bw:K.writeBool},transport_error:{n:17,d:1,br:$.readInt32,bw:K.writeInt32},messageid:{n:18,d:"18446744073709551615",br:$.readUint64String,bw:K.writeUint64String},publisher_group_id:{n:19,br:$.readUint32,bw:K.writeUint32},sysid:{n:20,br:$.readUint32,bw:K.writeUint32},webapi_key_id:{n:25,br:$.readUint32,bw:K.writeUint32},is_from_external_source:{n:26,br:$.readBool,bw:K.writeBool},forward_to_sysid:{n:27,r:!0,q:!0,br:$.readUint32,pbr:$.readPackedUint32,bw:K.writeRepeatedUint32},cm_sysid:{n:28,br:$.readUint32,bw:K.writeUint32},launcher_type:{n:31,d:0,br:$.readUint32,bw:K.writeUint32},realm:{n:32,d:0,br:$.readUint32,bw:K.writeUint32},timeout_ms:{n:33,d:-1,br:$.readInt32,bw:K.writeInt32},debug_source:{n:34,br:$.readString,bw:K.writeString},debug_source_string_index:{n:35,br:$.readUint32,bw:K.writeUint32},token_id:{n:36,br:$.readUint64String,bw:K.writeUint64String},routing_gc:{n:37,c:se},session_disposition:{n:38,d:0,br:$.readEnum,bw:K.writeEnum},wg_token:{n:39,br:$.readString,bw:K.writeString},webui_auth_key:{n:40,br:$.readString,bw:K.writeString},exclude_client_sessionids:{n:41,r:!0,q:!0,br:$.readInt32,pbr:$.readPackedInt32,bw:K.writeRepeatedInt32},admin_request_spoofing_steamid:{n:43,br:$.readFixed64String,bw:K.writeFixed64String},is_valveds:{n:44,br:$.readBool,bw:K.writeBool},trace_tag:{n:45,br:$.readFixed64String,bw:K.writeFixed64String}}}),ie.sm_m}static MBF(){return ie.sm_mbf||(ie.sm_mbf=Q(ie.M())),ie.sm_mbf}toObject(e=!1){return ie.toObject(e,this)}static toObject(e,t){return Y(ie.M(),e,t)}static fromObject(e){return X(ie.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new ie;return ie.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(ie.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return ie.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(ie.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return ie.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"CMsgProtoBufHeader"}}V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;V.Message;class oe{m_nOffset;m_nLength;m_viewPacket;m_rgubPacket;m_iGet;m_iPut;constructor(e,t=0,r){this.m_nOffset=t||0,e instanceof Uint8Array||e instanceof DataView?(this.m_nLength=r||e.byteLength-this.m_nOffset,this.m_nOffset+=e.byteOffset,this.m_viewPacket=new DataView(e.buffer,this.m_nOffset,this.m_nLength)):(this.m_nLength=r||e.byteLength-this.m_nOffset,this.m_viewPacket=new DataView(e,this.m_nOffset,this.m_nLength)),this.m_rgubPacket=new Uint8Array(this.m_viewPacket.buffer,this.m_viewPacket.byteOffset,this.m_viewPacket.byteLength),this.m_iGet=0,this.m_iPut=0}TellGet(){return this.m_iGet+this.m_viewPacket.byteOffset}GetPacket(){return this.m_viewPacket.buffer}GetUint8(){return this.m_viewPacket.getUint8(this.m_iGet++)}GetUint32(e=!0){const t=this.m_viewPacket.getUint32(this.m_iGet,e);return this.m_iGet+=4,t}SeekGetHead(e=0){this.m_iGet=e||0}SeekGetCurrent(e){this.m_iGet+=e}TellPut(){return this.m_iPut+this.m_viewPacket.byteOffset}TellMaxPut(){return this.m_viewPacket.byteLength}PutUint8(e){this.m_viewPacket.setUint8(this.m_iPut++,e)}PutUint32(e,t=!0){this.m_viewPacket.setUint32(this.m_iPut,e,t),this.m_iPut+=4}PutBytes(e){this.m_rgubPacket.set(e,this.m_iPut),this.m_iPut+=e.length}SeekPut(e){this.m_iPut+=e}GetCountBytesRemaining(){return this.m_viewPacket.byteLength-this.m_iGet}}var le;!function(e){e[e.k_EAccountFlagNormalUser=0]="k_EAccountFlagNormalUser",e[e.k_EAccountFlagPersonaNameSet=1]="k_EAccountFlagPersonaNameSet",e[e.k_EAccountFlagUnbannable=2]="k_EAccountFlagUnbannable",e[e.k_EAccountFlagPasswordSet=4]="k_EAccountFlagPasswordSet",e[e.k_EAccountFlagSupport=8]="k_EAccountFlagSupport",e[e.k_EAccountFlagAdmin=16]="k_EAccountFlagAdmin",e[e.k_EAccountFlagSupervisor=32]="k_EAccountFlagSupervisor",e[e.k_EAccountFlagAppEditor=64]="k_EAccountFlagAppEditor",e[e.k_EAccountFlagHWIDSet=128]="k_EAccountFlagHWIDSet",e[e.k_EAccountFlagVacBeta=512]="k_EAccountFlagVacBeta",e[e.k_EAccountFlagDebug=1024]="k_EAccountFlagDebug",e[e.k_EAccountFlagDisabled=2048]="k_EAccountFlagDisabled",e[e.k_EAccountFlagLimitedUser=4096]="k_EAccountFlagLimitedUser",e[e.k_EAccountFlagLimitedUserForce=8192]="k_EAccountFlagLimitedUserForce",e[e.k_EAccountFlagEmailValidated=16384]="k_EAccountFlagEmailValidated",e[e.k_EAccountFlagValveEmail=32768]="k_EAccountFlagValveEmail",e[e.k_EAccountFlagForcePasswordChange=131072]="k_EAccountFlagForcePasswordChange",e[e.k_EAccountFlagLogonExtraSecurity=524288]="k_EAccountFlagLogonExtraSecurity",e[e.k_EAccountFlagLogonExtraSecurityDisabled=1048576]="k_EAccountFlagLogonExtraSecurityDisabled",e[e.k_EAccountFlagSteam2MigrationComplete=2097152]="k_EAccountFlagSteam2MigrationComplete",e[e.k_EAccountFlagNeedLogs=4194304]="k_EAccountFlagNeedLogs",e[e.k_EAccountFlagLockdown=8388608]="k_EAccountFlagLockdown",e[e.k_EAccountFlagMasterAppEditor=********]="k_EAccountFlagMasterAppEditor",e[e.k_EAccountFlagBannedFromWebAPI=********]="k_EAccountFlagBannedFromWebAPI",e[e.k_EAccountFlagPartnerMember=********]="k_EAccountFlagPartnerMember",e[e.k_EAccountFlagGlobalModerator=*********]="k_EAccountFlagGlobalModerator",e[e.k_EAccountFlagParentalSettings=*********]="k_EAccountFlagParentalSettings",e[e.k_EAccountFlagThirdPartySupport=*********]="k_EAccountFlagThirdPartySupport",e[e.k_EAccountFlagNeedsSSANextSteamLogon=**********]="k_EAccountFlagNeedsSSANextSteamLogon"}(le||(le={}));le.k_EAccountFlagAdmin,le.k_EAccountFlagSupervisor,le.k_EAccountFlagSupport,le.k_EAccountFlagAdmin,le.k_EAccountFlagSupervisor;r(8506);class ce{static sm_ErrorReportingStore;static InstallErrorReportingStore(e){this.sm_ErrorReportingStore=e}static InitHeaderFromPacket(e){return new ce(void 0,e)}m_eMsg;m_bValid;m_netPacket;m_cubHeader;m_header;m_body;constructor(e,t,r,a,n,s){if(a)this.m_eMsg=a.m_eMsg,this.m_bValid=a.m_bValid,this.m_bValid&&(this.m_netPacket=a.m_netPacket,this.m_cubHeader=a.m_cubHeader,this.m_header=a.m_header,this.InitForType(r));else{if(this.m_header=new ie(null),this.m_bValid=!0,t)if(this.m_netPacket=t,this.m_netPacket.SeekGetHead(),this.m_eMsg=this.m_netPacket.GetUint32(),**********&this.m_eMsg){this.m_eMsg=**********&this.m_eMsg,this.m_cubHeader=this.m_netPacket.GetUint32();try{ie.deserializeBinaryFromReader(this.m_header,new V.BinaryReader(this.m_netPacket.GetPacket(),this.m_netPacket.TellGet(),this.m_cubHeader)),this.m_netPacket.SeekGetCurrent(this.m_cubHeader),r&&this.InitForType(r)}catch(e){console.error("Exception deserializing protobuf",e),this.m_bValid=!1}}else this.m_bValid=!1;else e&&(this.m_eMsg=e),s&&r?this.m_body=r.fromObject(s):r&&(this.m_body=new r);n&&this.m_header.set_jobid_target(n.Hdr().jobid_target())}}InitForType(e){this.m_body=new e,this.m_netPacket&&(this.m_netPacket.SeekGetHead(8+this.m_cubHeader),this.ReadBodyFromBuffer(e,this.m_netPacket))}ReadBodyFromBuffer(e,t){try{e.deserializeBinaryFromReader(this.m_body,new V.BinaryReader(t.GetPacket(),t.TellGet(),t.GetCountBytesRemaining()))}catch(e){this.m_bValid=!1;const t=ce.sm_ErrorReportingStore,r=`Exception parsing protobuf message body of type ${this.m_eMsg}.  Definitions may be out of sync with server version.`;t&&t.ReportError(new Error(r),{bIncludeMessageInIdentifier:!0}),console.warn(r),console.log(e.stack||e)}}BIsValid(){return this.m_bValid}Body(){return this.m_body}SetBodyJSON(e){e.toObject=()=>e,this.m_body=e}Hdr(){return this.m_header}GetEMsg(){return this.m_eMsg}SetEMsg(e){this.m_eMsg=e}GetEResult(){return this.Hdr().eresult()}BSuccess(){return 1==this.Hdr().eresult()}GetErrorMessage(){return this.Hdr().error_message()?this.Hdr().error_message():`eresult ${this.Hdr().eresult()}`}Serialize(){const e=this.m_header.serializeBinary(),t=this.m_body.serializeBinary(),r=**********|this.m_eMsg,a=new Uint8Array(8+e.length+t.length),n=new oe(a);return n.PutUint32(r),n.PutUint32(e.length),n.PutBytes(e),n.PutBytes(t),a}SerializeBody(){const e=this.m_body.serializeBinary(),t=new Uint8Array(e.length);return new oe(t).PutBytes(e),t}DEBUG_ToObject(){return{}}DEBUG_LogToConsole(){0}}class ue extends ce{constructor(e,t=0,r,a,n){super(t,r,e,a,void 0,n)}static InitFromPacket(e,t){return new ue(e,0,t)}static InitFromMsg(e,t){return new ue(e,void 0,void 0,t)}static Init(e,t){return new ue(e,t)}static InitFromObject(e,t){return new ue(e,void 0,void 0,void 0,t)}Body(){return super.Body()}SetBodyFields(e){for(const t in e)Array.isArray(e[t])?this.Body()[`add_${t}`]&&e[t].forEach((e=>{this.Body()[`add_${t}`](e)})):this.Body()[`set_${t}`]&&this.Body()[`set_${t}`](e[t])}}class me extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),me.prototype.username||ee(me.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return me.sm_m||(me.sm_m={proto:me,fields:{username:{n:1,br:$.readString,bw:K.writeString},password:{n:2,br:$.readString,bw:K.writeString},steamguardcode:{n:3,br:$.readString,bw:K.writeString},remember_password:{n:4,br:$.readBool,bw:K.writeBool}}}),me.sm_m}static MBF(){return me.sm_mbf||(me.sm_mbf=Q(me.M())),me.sm_mbf}toObject(e=!1){return me.toObject(e,this)}static toObject(e,t){return Y(me.M(),e,t)}static fromObject(e){return X(me.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new me;return me.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(me.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return me.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(me.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return me.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_Login_Request"}}class de extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),de.prototype.logon_state||ee(de.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return de.sm_m||(de.sm_m={proto:de,fields:{logon_state:{n:1,br:$.readInt32,bw:K.writeInt32},logon_eresult:{n:2,br:$.readInt32,bw:K.writeInt32}}}),de.sm_m}static MBF(){return de.sm_mbf||(de.sm_mbf=Q(de.M())),de.sm_mbf}toObject(e=!1){return de.toObject(e,this)}static toObject(e,t){return Y(de.M(),e,t)}static fromObject(e){return X(de.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new de;return de.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(de.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return de.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(de.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return de.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_Login_Response"}}class _e extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return _e.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new _e}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new _e;return _e.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return _e.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return _e.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_LoginStatus_Request"}}class ge extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),ge.prototype.username||ee(ge.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return ge.sm_m||(ge.sm_m={proto:ge,fields:{username:{n:1,br:$.readString,bw:K.writeString},cached_credentials:{n:2,br:$.readBool,bw:K.writeBool},logon_state:{n:3,br:$.readInt32,bw:K.writeInt32},logon_eresult:{n:4,br:$.readInt32,bw:K.writeInt32}}}),ge.sm_m}static MBF(){return ge.sm_mbf||(ge.sm_mbf=Q(ge.M())),ge.sm_mbf}toObject(e=!1){return ge.toObject(e,this)}static toObject(e,t){return Y(ge.M(),e,t)}static fromObject(e){return X(ge.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new ge;return ge.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(ge.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return ge.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(ge.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return ge.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_LoginStatus_Response"}}class pe extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return pe.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new pe}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new pe;return pe.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return pe.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return pe.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_CancelLogin_Request"}}class he extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),he.prototype.logon_state||ee(he.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return he.sm_m||(he.sm_m={proto:he,fields:{logon_state:{n:1,br:$.readInt32,bw:K.writeInt32},logon_eresult:{n:2,br:$.readInt32,bw:K.writeInt32}}}),he.sm_m}static MBF(){return he.sm_mbf||(he.sm_mbf=Q(he.M())),he.sm_mbf}toObject(e=!1){return he.toObject(e,this)}static toObject(e,t){return Y(he.M(),e,t)}static fromObject(e){return X(he.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new he;return he.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(he.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return he.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(he.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return he.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_CancelLogin_Response"}}class be extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return be.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new be}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new be;return be.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return be.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return be.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_Logout_Request"}}class Se extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),Se.prototype.logon_state||ee(Se.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return Se.sm_m||(Se.sm_m={proto:Se,fields:{logon_state:{n:1,br:$.readInt32,bw:K.writeInt32},logout_eresult:{n:2,br:$.readInt32,bw:K.writeInt32}}}),Se.sm_m}static MBF(){return Se.sm_mbf||(Se.sm_mbf=Q(Se.M())),Se.sm_mbf}toObject(e=!1){return Se.toObject(e,this)}static toObject(e,t){return Y(Se.M(),e,t)}static fromObject(e){return X(Se.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Se;return Se.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(Se.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return Se.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(Se.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return Se.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_Logout_Response"}}class Ee extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),Ee.prototype.restart||ee(Ee.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return Ee.sm_m||(Ee.sm_m={proto:Ee,fields:{restart:{n:1,br:$.readBool,bw:K.writeBool}}}),Ee.sm_m}static MBF(){return Ee.sm_mbf||(Ee.sm_mbf=Q(Ee.M())),Ee.sm_mbf}toObject(e=!1){return Ee.toObject(e,this)}static toObject(e,t){return Y(Ee.M(),e,t)}static fromObject(e){return X(Ee.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Ee;return Ee.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(Ee.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return Ee.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(Ee.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return Ee.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_Quit_Request"}}class ye extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return ye.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new ye}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new ye;return ye.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return ye.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return ye.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_Quit_Response"}}class Ce extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return Ce.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new Ce}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Ce;return Ce.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return Ce.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return Ce.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_Status_Request"}}class fe extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),fe.prototype.logon_state||ee(fe.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return fe.sm_m||(fe.sm_m={proto:fe,fields:{logon_state:{n:1,br:$.readInt32,bw:K.writeInt32},logon_eresult:{n:2,br:$.readInt32,bw:K.writeInt32},connected:{n:3,br:$.readBool,bw:K.writeBool},cache_enabled:{n:4,br:$.readBool,bw:K.writeBool},acct_status:{n:5,br:$.readInt32,bw:K.writeInt32}}}),fe.sm_m}static MBF(){return fe.sm_mbf||(fe.sm_mbf=Q(fe.M())),fe.sm_mbf}toObject(e=!1){return fe.toObject(e,this)}static toObject(e,t){return Y(fe.M(),e,t)}static fromObject(e){return X(fe.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new fe;return fe.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(fe.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return fe.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(fe.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return fe.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_Status_Response"}}class we extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return we.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new we}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new we;return we.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return we.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return we.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_GetLanguage_Request"}}class Be extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),Be.prototype.language||ee(Be.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return Be.sm_m||(Be.sm_m={proto:Be,fields:{language:{n:1,br:$.readString,bw:K.writeString}}}),Be.sm_m}static MBF(){return Be.sm_mbf||(Be.sm_mbf=Q(Be.M())),Be.sm_mbf}toObject(e=!1){return Be.toObject(e,this)}static toObject(e,t){return Y(Be.M(),e,t)}static fromObject(e){return X(Be.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Be;return Be.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(Be.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return Be.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(Be.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return Be.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_GetLanguage_Response"}}class ke extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),ke.prototype.language||ee(ke.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return ke.sm_m||(ke.sm_m={proto:ke,fields:{language:{n:1,br:$.readString,bw:K.writeString}}}),ke.sm_m}static MBF(){return ke.sm_mbf||(ke.sm_mbf=Q(ke.M())),ke.sm_mbf}toObject(e=!1){return ke.toObject(e,this)}static toObject(e,t){return Y(ke.M(),e,t)}static fromObject(e){return X(ke.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new ke;return ke.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(ke.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return ke.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(ke.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return ke.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_SetLanguage_Request"}}class Me extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return Me.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new Me}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Me;return Me.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return Me.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return Me.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_SetLanguage_Response"}}class ve extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return ve.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new ve}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new ve;return ve.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return ve.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return ve.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_ClientStatus_Request"}}class Te extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),Te.prototype.clients||ee(Te.M()),V.Message.initialize(this,e,0,-1,[4,5],null)}static sm_m;static sm_mbf;static M(){return Te.sm_m||(Te.sm_m={proto:Te,fields:{clients:{n:4,c:Ne,r:!0,q:!0},payments:{n:5,c:Pe,r:!0,q:!0}}}),Te.sm_m}static MBF(){return Te.sm_mbf||(Te.sm_mbf=Q(Te.M())),Te.sm_mbf}toObject(e=!1){return Te.toObject(e,this)}static toObject(e,t){return Y(Te.M(),e,t)}static fromObject(e){return X(Te.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Te;return Te.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(Te.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return Te.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(Te.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return Te.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_ClientStatus_Response"}}class Ne extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),Ne.prototype.ip||ee(Ne.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return Ne.sm_m||(Ne.sm_m={proto:Ne,fields:{ip:{n:1,br:$.readUint32,bw:K.writeUint32},hostname:{n:2,br:$.readString,bw:K.writeString},connected:{n:3,br:$.readBool,bw:K.writeBool},instance_id:{n:4,br:$.readUint64String,bw:K.writeUint64String}}}),Ne.sm_m}static MBF(){return Ne.sm_mbf||(Ne.sm_mbf=Q(Ne.M())),Ne.sm_mbf}toObject(e=!1){return Ne.toObject(e,this)}static toObject(e,t){return Y(Ne.M(),e,t)}static fromObject(e){return X(Ne.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Ne;return Ne.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(Ne.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return Ne.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(Ne.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return Ne.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_ClientStatus_Response_ClientInfo"}}class Pe extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),Pe.prototype.transid||ee(Pe.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return Pe.sm_m||(Pe.sm_m={proto:Pe,fields:{transid:{n:1,br:$.readUint64String,bw:K.writeUint64String},steamid:{n:2,br:$.readUint64String,bw:K.writeUint64String},amount:{n:3,br:$.readString,bw:K.writeString},time_created:{n:4,br:$.readInt32,bw:K.writeInt32},purchase_status:{n:5,br:$.readInt32,bw:K.writeInt32},hostname:{n:6,br:$.readString,bw:K.writeString},persona_name:{n:7,br:$.readString,bw:K.writeString},profile_url:{n:8,br:$.readString,bw:K.writeString},avatar_url:{n:9,br:$.readString,bw:K.writeString}}}),Pe.sm_m}static MBF(){return Pe.sm_mbf||(Pe.sm_mbf=Q(Pe.M())),Pe.sm_mbf}toObject(e=!1){return Pe.toObject(e,this)}static toObject(e,t){return Y(Pe.M(),e,t)}static fromObject(e){return X(Pe.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Pe;return Pe.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(Pe.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return Pe.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(Pe.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return Pe.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_ClientStatus_Response_Payment"}}class Ie extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return Ie.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new Ie}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Ie;return Ie.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return Ie.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return Ie.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_ContentCacheStatus_Request"}}class Re extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),Re.prototype.enabled||ee(Re.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return Re.sm_m||(Re.sm_m={proto:Re,fields:{enabled:{n:1,br:$.readBool,bw:K.writeBool},port:{n:2,br:$.readUint32,bw:K.writeUint32},cache_location:{n:3,br:$.readString,bw:K.writeString},max_size_gb:{n:4,br:$.readUint32,bw:K.writeUint32},p2p_enabled:{n:5,br:$.readBool,bw:K.writeBool},explicit_ip_address:{n:9,br:$.readString,bw:K.writeString},external_process:{n:10,br:$.readBool,bw:K.writeBool},current_size_gb:{n:6,br:$.readUint32,bw:K.writeUint32},current_bw:{n:7,br:$.readUint64String,bw:K.writeUint64String},total_bytes_served:{n:8,br:$.readUint64String,bw:K.writeUint64String}}}),Re.sm_m}static MBF(){return Re.sm_mbf||(Re.sm_mbf=Q(Re.M())),Re.sm_mbf}toObject(e=!1){return Re.toObject(e,this)}static toObject(e,t){return Y(Re.M(),e,t)}static fromObject(e){return X(Re.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Re;return Re.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(Re.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return Re.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(Re.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return Re.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_ContentCacheStatus_Response"}}class Le extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),Le.prototype.enabled||ee(Le.M()),V.Message.initialize(this,e,0,-1,void 0,null)}static sm_m;static sm_mbf;static M(){return Le.sm_m||(Le.sm_m={proto:Le,fields:{enabled:{n:1,br:$.readBool,bw:K.writeBool},port:{n:2,br:$.readUint32,bw:K.writeUint32},cache_location:{n:3,br:$.readString,bw:K.writeString},max_size_gb:{n:4,br:$.readUint32,bw:K.writeUint32},p2p_enabled:{n:5,br:$.readBool,bw:K.writeBool},external_process:{n:6,br:$.readBool,bw:K.writeBool},explicit_ip_address:{n:7,br:$.readString,bw:K.writeString}}}),Le.sm_m}static MBF(){return Le.sm_mbf||(Le.sm_mbf=Q(Le.M())),Le.sm_mbf}toObject(e=!1){return Le.toObject(e,this)}static toObject(e,t){return Y(Le.M(),e,t)}static fromObject(e){return X(Le.M(),e)}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Le;return Le.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return Z(Le.MBF(),e,t)}serializeBinary(){var e=new(q().BinaryWriter);return Le.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){J(Le.M(),e,t)}serializeBase64String(){var e=new(q().BinaryWriter);return Le.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_ContentCacheConfig_Request"}}class Fe extends V.Message{static ImplementsStaticInterface(){}constructor(e=null){super(),V.Message.initialize(this,e,0,-1,void 0,null)}toObject(e=!1){return Fe.toObject(e,this)}static toObject(e,t){return e?{$jspbMessageInstance:t}:{}}static fromObject(e){return new Fe}static deserializeBinary(e){let t=new(q().BinaryReader)(e),r=new Fe;return Fe.deserializeBinaryFromReader(r,t)}static deserializeBinaryFromReader(e,t){return e}serializeBinary(){var e=new(q().BinaryWriter);return Fe.serializeBinaryToWriter(this,e),e.getResultBuffer()}static serializeBinaryToWriter(e,t){}serializeBase64String(){var e=new(q().BinaryWriter);return Fe.serializeBinaryToWriter(this,e),e.getResultBase64String()}getClassName(){return"SiteServerUI_ContentCacheConfig_Response"}}var Ae=r(1944),ze=r(1735),xe=r.n(ze);class Oe{m_eView=0;m_bShutDown=!1;m_bConnected=!1;m_strLanguage="None";m_bCacheState=!1;m_eAcctStatus=0;m_vecModals=[];m_bClientsConnected=!1;m_bSteamCmd=!1;m_unModalNextKey=0;constructor(){(0,D.Gn)(this)}get view(){return this.m_eView}SetView(e){this.m_eView=e}get shutdown(){return this.m_bShutDown}SetShutdown(){this.m_bShutDown=!0}get connected(){return this.m_bConnected}SetConnection(e){this.m_bConnected=e}get language(){return this.m_strLanguage}SetLanguage(e){this.m_strLanguage=e}get cacheEnabled(){return this.m_bCacheState}SetCacheState(e){this.m_bCacheState=e}get acctStatus(){return this.m_eAcctStatus}SetAcctStatus(e){this.m_eAcctStatus=e}get clientsHaveConnected(){return this.m_bClientsConnected}SetClientsConnected(e){this.m_bClientsConnected=e}get steamcmdConnected(){return this.m_bSteamCmd}SetSteamcmdConnected(e){this.m_bSteamCmd=e}get activeModal(){return 0!=this.m_vecModals.length?this.m_vecModals[this.m_vecModals.length-1]:null}ShowModal(e){let t={handle:null},r=(0,D.XI)((()=>{let e=this.m_vecModals.indexOf(t.handle);e>=0&&this.m_vecModals.splice(e,1)})),n=this.m_unModalNextKey++,s=a.cloneElement(e,{closeModal:r,key:n});this.m_vecModals.push(s),t.handle=this.m_vecModals[this.m_vecModals.length-1]}}(0,i.Cg)([D.sH],Oe.prototype,"m_eView",void 0),(0,i.Cg)([D.sH],Oe.prototype,"m_bShutDown",void 0),(0,i.Cg)([D.sH],Oe.prototype,"m_bConnected",void 0),(0,i.Cg)([D.sH],Oe.prototype,"m_strLanguage",void 0),(0,i.Cg)([D.sH],Oe.prototype,"m_bCacheState",void 0),(0,i.Cg)([D.sH],Oe.prototype,"m_eAcctStatus",void 0),(0,i.Cg)([D.sH.shallow],Oe.prototype,"m_vecModals",void 0),(0,i.Cg)([D.sH],Oe.prototype,"m_bClientsConnected",void 0),(0,i.Cg)([D.sH],Oe.prototype,"m_bSteamCmd",void 0),(0,i.Cg)([D.EW],Oe.prototype,"view",null),(0,i.Cg)([D.XI.bound],Oe.prototype,"SetView",null),(0,i.Cg)([D.EW],Oe.prototype,"shutdown",null),(0,i.Cg)([H],Oe.prototype,"SetShutdown",null),(0,i.Cg)([D.EW],Oe.prototype,"connected",null),(0,i.Cg)([D.XI.bound],Oe.prototype,"SetConnection",null),(0,i.Cg)([D.EW],Oe.prototype,"language",null),(0,i.Cg)([D.XI.bound],Oe.prototype,"SetLanguage",null),(0,i.Cg)([D.EW],Oe.prototype,"cacheEnabled",null),(0,i.Cg)([D.XI.bound],Oe.prototype,"SetCacheState",null),(0,i.Cg)([D.EW],Oe.prototype,"acctStatus",null),(0,i.Cg)([D.XI.bound],Oe.prototype,"SetAcctStatus",null),(0,i.Cg)([D.EW],Oe.prototype,"clientsHaveConnected",null),(0,i.Cg)([D.XI.bound],Oe.prototype,"SetClientsConnected",null),(0,i.Cg)([D.EW],Oe.prototype,"steamcmdConnected",null),(0,i.Cg)([D.XI.bound],Oe.prototype,"SetSteamcmdConnected",null),(0,i.Cg)([D.EW],Oe.prototype,"activeModal",null),(0,i.Cg)([D.XI],Oe.prototype,"ShowModal",null);const We=new Oe;window.uiStore=We;class Ge{m_ServiceTransport;m_strBaseURL;constructor(){this.m_strBaseURL="http://localhost:"+String(27100)+"/"}Login(e){return this.SendMsgAndAwaitResponse(e,de,1,"login")}GetLoginStatus(e){return this.SendMsgAndAwaitResponse(e,ge,0,"loginstatus")}CancelLogin(e){return this.SendMsgAndAwaitResponse(e,he,1,"cancellogin")}Logout(e){return this.SendMsgAndAwaitResponse(e,Se,1,"logout")}Quit(e){return this.SendMsgAndAwaitResponse(e,ye,1,"quit")}GetStatus(e){return this.SendMsgAndAwaitResponse(e,fe,0,"status")}GetLanguage(e){return this.SendMsgAndAwaitResponse(e,Be,0,"language")}SetLanguage(e){return this.SendMsgAndAwaitResponse(e,Me,1,"language")}GetClientStatus(e){return this.SendMsgAndAwaitResponse(e,Te,0,"clientstatus")}GetContentCacheStatus(e){return this.SendMsgAndAwaitResponse(e,Re,0,"cachestatus")}UpdateCacheConfig(e){return this.SendMsgAndAwaitResponse(e,Fe,1,"cacheconfig")}async SendMsgAndAwaitResponse(e,t,r,a){let n=null;try{let s=await this.Send(e,r,a);if(200!=s.status||!s.data)throw new Error("Request Error");let i=new oe(s.data);n=ue.Init(t),n.Hdr().set_eresult(2),s.headers&&(s.headers["x-eresult"]&&n.Hdr().set_eresult(s.headers["x-eresult"]),s.headers["x-error_message"]&&n.Hdr().set_error_message(s.headers["x-error_message"]));let o=new V.BinaryReader(i.GetPacket(),i.TellGet(),i.GetCountBytesRemaining());t.deserializeBinaryFromReader(n.Body(),o),We.SetSteamcmdConnected(!0)}catch(e){n=this.CreateFailedMsgProtobuf(t,3,null)}return n}Send(e,t,r){let a=this.CreateWebAPIURL(r),n=e.SerializeBody(),s=Ae.iI(n),i={responseType:"arraybuffer",params:{}};if(1===t){const e=new FormData;return e.append("input_protobuf_encoded",s),xe().post(a,e,i)}return i.params={...i.params,input_protobuf_encoded:s},xe().get(a,i)}CreateWebAPIURL(e){return this.m_strBaseURL+e}CreateFailedMsgProtobuf(e,t,r){let a=ue.Init(e);return a.Hdr().set_eresult(2),a.Hdr().set_transport_error(t),r&&a.Hdr().set_error_message(r),a}}(0,i.Cg)([H],Ge.prototype,"Login",null),(0,i.Cg)([H],Ge.prototype,"GetLoginStatus",null),(0,i.Cg)([H],Ge.prototype,"CancelLogin",null),(0,i.Cg)([H],Ge.prototype,"Logout",null),(0,i.Cg)([H],Ge.prototype,"Quit",null),(0,i.Cg)([H],Ge.prototype,"GetStatus",null),(0,i.Cg)([H],Ge.prototype,"GetLanguage",null),(0,i.Cg)([H],Ge.prototype,"SetLanguage",null),(0,i.Cg)([H],Ge.prototype,"GetClientStatus",null),(0,i.Cg)([H],Ge.prototype,"GetContentCacheStatus",null),(0,i.Cg)([H],Ge.prototype,"UpdateCacheConfig",null),(0,i.Cg)([H],Ge.prototype,"SendMsgAndAwaitResponse",null),(0,i.Cg)([H],Ge.prototype,"Send",null);const Ue=new Ge;window.loginStore=Ue;class je{m_strAccountName="";m_bUserHasCachedCredentials=!1;m_eLoginState=-1;m_eLoginResult=1;m_strPassword="";m_strSteamGuardCode="";m_bRememberPassword=!1;constructor(){(0,D.Gn)(this)}get accountName(){return this.m_strAccountName}get hasCachedCredentials(){return this.m_bUserHasCachedCredentials}get loginState(){return this.m_eLoginState}get loginResult(){return this.m_eLoginResult}SetAccountAndPassword(e,t,r){this.m_strAccountName=e,this.m_strPassword=t,this.m_strSteamGuardCode="",this.m_bRememberPassword=r}SetSteamGuardCode(e){this.m_strSteamGuardCode=e}OnLoginStateChange(e,t,r){this.m_strAccountName=e,this.m_eLoginState=t,this.m_eLoginResult=r,5==r&&this.m_bUserHasCachedCredentials&&(this.m_bUserHasCachedCredentials=!1)}SetLoginStatus(e,t){this.OnLoginStateChange(this.m_strAccountName,e,t)}SetSteamCmdNotConnected(){this.OnLoginStateChange(De.accountName,6,2)}async StartLogin(){const e=ue.Init(me);e.Body().set_username(this.m_strAccountName),e.Body().set_password(this.m_strPassword),e.Body().set_steamguardcode(this.m_strSteamGuardCode),e.Body().set_remember_password(this.m_bRememberPassword);const t=await Ue.Login(e);this.OnLoginStateChange(this.m_strAccountName,t.Body().logon_state(),t.Body().logon_eresult())}async UpdateLoginStatus(){const e=ue.Init(_e),t=await Ue.GetLoginStatus(e);1==t.Hdr().eresult()?(this.m_bUserHasCachedCredentials=t.Body().cached_credentials(),this.OnLoginStateChange(t.Body().username(),t.Body().logon_state(),t.Body().logon_eresult())):this.OnLoginStateChange(this.m_strAccountName,6,2)}async CancelLogin(){const e=ue.Init(pe),t=await Ue.CancelLogin(e);this.OnLoginStateChange(this.m_strAccountName,t.Body().logon_state(),t.Body().logon_eresult())}async StartLogout(){this.SetAccountAndPassword("","",!1),this.m_bUserHasCachedCredentials=!1,this.m_bRememberPassword=!1;const e=ue.Init(be),t=await Ue.Logout(e);this.OnLoginStateChange("",t.Body().logon_state(),t.Body().logout_eresult())}}(0,i.Cg)([D.sH],je.prototype,"m_strAccountName",void 0),(0,i.Cg)([D.sH],je.prototype,"m_bUserHasCachedCredentials",void 0),(0,i.Cg)([D.sH],je.prototype,"m_eLoginState",void 0),(0,i.Cg)([D.sH],je.prototype,"m_eLoginResult",void 0),(0,i.Cg)([D.EW],je.prototype,"accountName",null),(0,i.Cg)([D.EW],je.prototype,"hasCachedCredentials",null),(0,i.Cg)([D.EW],je.prototype,"loginState",null),(0,i.Cg)([D.EW],je.prototype,"loginResult",null),(0,i.Cg)([D.XI.bound],je.prototype,"SetAccountAndPassword",null),(0,i.Cg)([H],je.prototype,"SetSteamGuardCode",null),(0,i.Cg)([D.XI.bound],je.prototype,"OnLoginStateChange",null),(0,i.Cg)([H],je.prototype,"SetLoginStatus",null),(0,i.Cg)([H],je.prototype,"SetSteamCmdNotConnected",null),(0,i.Cg)([H],je.prototype,"StartLogin",null),(0,i.Cg)([H],je.prototype,"UpdateLoginStatus",null),(0,i.Cg)([H],je.prototype,"CancelLogin",null),(0,i.Cg)([H],je.prototype,"StartLogout",null);const De=new je;var He;window.loginStore=De,function(e){e[e.k_ESteamRealmUnknown=0]="k_ESteamRealmUnknown",e[e.k_ESteamRealmGlobal=1]="k_ESteamRealmGlobal",e[e.k_ESteamRealmChina=2]="k_ESteamRealmChina"}(He||(He={}));function Ve(e,t){return function(e,t){const r=e.findIndex(t);return r>=0&&(e.splice(r,1),!0)}(e,(e=>t==e))}class qe{m_vecCallbacks=[];Register(e){this.m_vecCallbacks.push(e);return{Unregister:()=>{Ve(this.m_vecCallbacks,e)}}}Dispatch(...e){for(const t of Array.from(this.m_vecCallbacks))t(...e)}ClearAllCallbacks(){this.m_vecCallbacks=[]}CountRegistered(){return this.m_vecCallbacks.length}static PromiseFromAny(e){return new Promise((t=>{let r=[];const a=()=>{r.forEach((e=>e.Unregister())),t()};for(const t of e)r.push(t.Register(a))}))}}(0,i.Cg)([H],class{m_schTimer;m_fnCallback;Schedule(e,t){this.IsScheduled()&&this.Cancel(),this.m_fnCallback=t,this.m_schTimer=window.setTimeout(this.ScheduledInternal,e)}IsScheduled(){return void 0!==this.m_schTimer}Cancel(){this.m_schTimer&&(clearTimeout(this.m_schTimer),this.m_schTimer=void 0)}ScheduledInternal(){this.m_schTimer=void 0;const e=this.m_fnCallback;this.m_fnCallback=void 0,e?.()}}.prototype,"ScheduledInternal",null);(0,i.Cg)([H],class{m_vecCallbacks=[];Push(e){this.m_vecCallbacks.push(e)}PushArrayRemove(e,t){this.m_vecCallbacks.push((()=>Ve(e,t)))}Unregister(){for(const e of this.m_vecCallbacks)e();this.m_vecCallbacks=[]}GetUnregisterFunc(){return this.Unregister}}.prototype,"Unregister",null);var $e;!function(e){e[e.None=0]="None",e[e.Ago=1]="Ago",e[e.Remaining=2]="Remaining"}($e||($e={}));new Map;new Map;new Map,new Map,new Map,new Map,new Map,new Map;class Ke{m_mapTokens=new Map;m_mapFallbackTokens=new Map;m_cbkTokensChanged=new qe;m_rgLocalesToUse;static sm_ErrorReportingStore;static InstallErrorReportingStore(e){this.sm_ErrorReportingStore=e}static GetLanguageFallback(e){return"sc_schinese"===e?"schinese":"english"}static GetELanguageFallback(e){return 29===e?6:0}static IsELanguageValidInRealm(e,t){return t===(29===e?He.k_ESteamRealmChina:He.k_ESteamRealmGlobal)}static GetLanguageListForRealms(e){const t=new Array;for(let r=0;r<31;r++)for(const a of e)if(this.IsELanguageValidInRealm(r,a)){t.push(r);break}return t}InitFromObjects(e,t,r,a,n){n||this.m_mapTokens.clear();const s={...r||{},...e},i={...a||{},...t||{}};this.AddTokens(s,i),this.m_cbkTokensChanged.Dispatch()}InitDirect(e,t){this.m_mapTokens.clear(),this.m_mapFallbackTokens.clear(),this.AddTokens(e,t),this.m_cbkTokensChanged.Dispatch()}AddTokens(e,t){Object.keys(e).forEach((t=>{this.m_mapTokens.set(t,e[t])})),t&&Object.keys(t).forEach((e=>{this.m_mapTokens.has(e)||this.m_mapTokens.set(e,t[e]),this.m_mapFallbackTokens.set(e,t[e])}))}GetTokensChangedCallbackList(){return this.m_cbkTokensChanged}GetPreferredLocales(){return this.m_rgLocalesToUse?this.m_rgLocalesToUse:navigator&&navigator.languages?navigator.languages:["en-US"]}GetELanguageFallbackOrder(e=null){let t=new Array;if(t.push(f(E.LANGUAGE)),(E.SUPPORTED_LANGUAGES||[]).forEach((e=>{e.value!=E.LANGUAGE&&t.push(f(e.value))})),e){Ke.GetLanguageListForRealms(e).forEach((e=>{-1==t.indexOf(e)&&t.push(e)}))}return t}SetPreferredLocales(e){this.m_rgLocalesToUse=e}BLooksLikeToken(e){return!!e&&e.length>0&&"#"==e.charAt(0)}LocalizeIfToken(e,t){return this.BLooksLikeToken(e)?this.LocalizeString(e,t):e}LocalizeString(e,t){const r=0==this.m_mapTokens.size;if(s(!r,`Attempting to localize token '${e}' with no tokens in our map.`),!this.BLooksLikeToken(e))return;let a=this.m_mapTokens.get(e.substring(1));if(void 0!==a)return a;t||!Ke.sm_ErrorReportingStore||r||Ke.sm_ErrorReportingStore.ReportError(new Error(`Unable to find localization token '${e}' for language '${E.LANGUAGE}', ${this.m_mapTokens.size} tokens in map`),{bIncludeMessageInIdentifier:!0})}LocalizeStringFromFallback(e){if(!e||0==e.length||"#"!=e.charAt(0))return;let t=this.m_mapFallbackTokens.get(e.substring(1));return void 0!==t?t:void 0}static GetTokenWithFallback(e){if(!e)return"";const t=f(E.LANGUAGE),r=e.find((e=>e.language==t));if(r)return r.localized_string;const a=Ke.GetELanguageFallback(t),n=e.find((e=>e.language==a));return n?.localized_string??""}static BHasTokenLanguage(e,t){return Boolean(t.find((t=>t.language==e)))}}function Qe(e,...t){let r=Ze.LocalizeString(e);return void 0===r?e:Ye(r,...t)}function Xe(e,...t){let r=Ze.LocalizeString(e);if(void 0===r)return e;let n,s=[],i=/(.*?)%(\d+)\$s/g,o=0;for(;n=i.exec(r);){o+=n[0].length,s.push(n[1]);let e=parseInt(n[2]);e>=1&&e<=t.length&&s.push(t[e-1])}return s.push(r.substr(o)),a.createElement(a.Fragment,null,...s)}function Ye(e,...t){return 0==t.length?e:e=e.replace(/%(?:(\d+)\$)?s/g,(function(e,r){if(r<=t.length&&r>=1){let e=t[r-1];return String(null==e?"":e)}return e}))}const Ze=new Ke;window.LocalizationManager=Ze;var Je=r(2630),et=r(3741);let tt=class extends a.Component{render(){return We.activeModal}};function rt(e){let t=Qe("#Button_OK");e.strOkButtonText&&(t=e.strOkButtonText);let r=Qe("#Button_Cancel");e.strCancelButtonText&&(r=e.strCancelButtonText);const n=a.useRef(null);return a.createElement(Je.A,null,a.createElement(et.A,{nodeRef:n,key:0,classNames:"modal-anim",timeout:500,appear:!0},a.createElement("div",{ref:n,className:"modal_background",onClick:t=>{t.target===t.currentTarget&&(e.onCancel&&e.onCancel(),e.closeModal&&e.closeModal())}},a.createElement("div",{className:"PopupGeneric PopupVisible"+(null!=e.className?" "+e.className:"")},a.createElement("div",{className:"PopupGenericBackground"}),a.createElement("div",{className:"PopupGenericContainer"},e.strTitle&&a.createElement("div",{className:"modal_title"},a.createElement("div",{className:"PopupGenericTitle"},e.strTitle)),a.createElement("div",{className:"model_content"},e.strMessage&&a.createElement("div",{className:"PopupGenericBodyText"},function(e,...t){return Qe(e,...t).split("<br>").map(((e,t)=>a.createElement("span",{key:t},e,a.createElement("br",null))))}(e.strMessage)),a.createElement("div",{className:"PopupGenericChildren"},e.children),a.createElement("div",{className:"GenericButtonGroup"},(e.bShowOK||null==e.bShowOK)&&a.createElement("button",{className:"GenericButton",onClick:()=>{(e=>{let t=!0;e.onOk&&(t=e.onOk()),t&&e.closeModal&&e.closeModal()})(e)}},t),e.optionalButtons,(e.bShowCancel||null==e.bShowCancel)&&a.createElement("button",{className:"GenericButton",onClick:()=>{(e=>{let t=!0;e.onCancel&&(t=e.onCancel()),t&&e.closeModal&&e.closeModal()})(e)}},r))))))))}tt=(0,i.Cg)([o.PA],tt);class at extends a.Component{constructor(e){super(e)}render(){return a.createElement(rt,{closeModal:this.props.closeModal,strTitle:this.props.strTitle,className:"GenericDialog",bShowCancel:this.props.bShowCancel,onOk:this.props.onOk},a.createElement("div",{className:"GenericDialogContainer"},a.createElement("div",{className:"PopupGenericBodyText"},this.props.strBodyText)))}}async function nt(e){try{const[t,a]=await Promise.all([r(8042)(`./shared_${e}.json`),r(2857)(`./siteserverui_${e}.json`)]),n={...a.default};if("english"!==e){const[e,a]=await Promise.all([r.e(3800).then(r.t.bind(r,3800,19)),r.e(8876).then(r.t.bind(r,8876,19))]),s={...a.default};Ze.InitFromObjects(n,s,t.default,e.default)}else Ze.InitFromObjects(n,null,t.default,null)}catch(t){console.log("InitLocalization Error: Failed to load loc files for",e);const[a,n]=await Promise.all([r.e(3800).then(r.t.bind(r,3800,19)),r.e(8876).then(r.t.bind(r,8876,19))]),s={...n.default};Ze.InitFromObjects(s,null,a.default,null)}}function st(e=!1){({NODE_ENV:"production",STEAM_BUILD:"buildbot"}).ELECTRON_BUILD&&We.SetShutdown();const t=ue.Init(Ee);return t.Body().set_restart(e),Ue.Quit(t)}function it(e=null){let t=a.createElement(at,{strTitle:Qe("#Status_Error"),strBodyText:e,bShowCancel:!1});We.ShowModal(t)}class ot extends a.Component{constructor(e){super(e)}render(){return a.createElement("div",{className:"tooltip tooltip-icon"},a.createElement("div",{className:"tooltip-container"},a.createElement("span",{className:"tooltiptext"},this.props.strText)))}}let lt=class extends a.Component{constructor(e){super(e)}render(){return a.createElement("div",{className:"MainWindowTitleBar TitleBar"},a.createElement("div",{className:"MainNavContainer"},a.createElement("div",{className:"SteamLogoMainNav"}),a.createElement("div",null,a.createElement("div",{className:"TitleBarTitle"},Qe("#Title")),a.createElement(ut,null))))}};lt=(0,i.Cg)([o.PA],lt);let ct=class extends a.Component{constructor(e){super(e)}render(){return a.createElement("div",{className:"MainWindowTitleBar TitleBar"},a.createElement("div",{className:"MainNavContainer"},a.createElement("div",{className:"SteamLogoMainNav"}),a.createElement("div",null,a.createElement("div",{className:"TitleBarTitle"},Qe("#Title")))))}};ct=(0,i.Cg)([o.PA],ct);class ut extends a.Component{async Language(){We.SetView(2)}async Cache(){We.SetView(4)}OnChangeAccount(){let e=a.createElement(at,{strTitle:Qe("#Logout_Logout"),strBodyText:Qe("#Logout_Prompt_ChangeUser"),onOk:()=>this.Logout()},null);We.ShowModal(e)}Logout(){return De.StartLogout(),!0}render(){return a.createElement("div",{className:"MainWindowTitleBarButton"},Qe("#Menu_Header_Settings"),a.createElement("div",{className:"MainNavPopout"},a.createElement("div",{className:"TopNavSettingsPopoutContents TopNavPopoutContents"},a.createElement("a",{className:"SteamFeatureNavItem",href:"#",onClick:this.Language},Qe("#Menu_Language")),a.createElement("a",{className:"SteamFeatureNavItem",href:"#",onClick:this.Cache},Qe("#Menu_Cache")),a.createElement("a",{className:"SteamFeatureNavItem",href:"#",onClick:this.OnChangeAccount},Qe("#Menu_ChangeAccount")))))}}(0,i.Cg)([H],ut.prototype,"Language",null),(0,i.Cg)([H],ut.prototype,"Cache",null),(0,i.Cg)([H],ut.prototype,"OnChangeAccount",null);let mt=class extends a.Component{constructor(e){super(e)}render(){let e=We.connected?Qe("#Status_Connected"):Qe("#Status_NotConnected"),t=We.cacheEnabled?Qe("#Status_Enabled"):Qe("#Status_Disabled");return a.createElement("div",{className:"Footer"},a.createElement(_t,null),a.createElement("div",{className:"FooterConnectionStatus"},a.createElement("div",{className:"FooterStatusName"},Qe("#Status_SteamService")+": "),a.createElement("div",{className:"FooterStatusValue"},e)),a.createElement("div",{className:"FooterCacheStatus"},a.createElement("div",{className:"FooterStatusName"},Qe("#Status_ContentCache")+": "),a.createElement("div",{className:"FooterStatusValue"},t)))}};mt=(0,i.Cg)([o.PA],mt);let dt=class extends a.Component{constructor(e){super(e)}render(){We.connected?Qe("#Status_Connected"):Qe("#Status_NotConnected");return a.createElement("div",{className:"Footer"})}};dt=(0,i.Cg)([o.PA],dt);let _t=class extends a.Component{m_eAcctStatusLast=0;m_bAlertDismissed=!1;constructor(e){super(e),this.state={bToggle:!1}}Dismiss(){this.m_bAlertDismissed=!0,this.setState({bToggle:!this.state.bToggle})}Show(){this.m_bAlertDismissed=!1,this.setState({bToggle:!this.state.bToggle})}render(){let e=We.acctStatus,t=!1;0!=e?e!=this.m_eAcctStatusLast?(this.m_bAlertDismissed=!1,t=!0):this.m_bAlertDismissed||(t=!0):t=!this.m_bAlertDismissed;let r=a.createElement("a",{className:"link",href:"https://partner.steamgames.com"},"partner.steamgames.com"),n=a.createElement("a",{className:"link",href:"https://store.steampowered.com/pccafe/"},"store.steampowered.com"),s="",i=!0;switch(We.acctStatus){case 0:s=Xe("#ForAdditionalInfo",r),i=!1;break;case 1:s=Xe("#AcctStatus_NoLicenses",n);break;case 2:s=Xe("#AcctStatus_NotAssociated",r);break;case 3:s=Qe("#AcctStatus_LoggedInElsewhere")}this.m_eAcctStatusLast=e;const o=i?"AcctAlertIcon":"AcctInfoIcon",l=t?{display:"none"}:{};return a.createElement("div",null,a.createElement("div",{className:"AcctStatusAlert "+(t?"slideup":"slidedown")},a.createElement("div",{className:o}),a.createElement("div",{className:"AcctStatusAlertMsg"},s),a.createElement("div",{className:"TitleButtonBar"},a.createElement("div",{className:"title-area-icon closeButton",onClick:this.Dismiss},a.createElement("div",{className:"closeButtonContainer"})))),a.createElement("div",{className:"ExpandAlertIcon",style:l,onClick:this.Show}))}};function gt(e){switch(e){case 5:return Qe("#Login_PasswordMismatch");case 88:return Qe("#Login_TwoFactorMismatch")}return Qe("#Steam_EResult_"+e)+" ("+String(e)+")"}(0,i.Cg)([H],_t.prototype,"Dismiss",null),(0,i.Cg)([H],_t.prototype,"Show",null),_t=(0,i.Cg)([o.PA],_t);class pt extends a.PureComponent{constructor(e){super(e)}render(){return a.createElement("div",{className:"MainWindow"},a.createElement(ct,null),a.createElement("div",{className:"LoginWindowContentContainer"},a.createElement("div",{className:"LoginWindowContent"},a.createElement("div",{className:"LoginWindowContentGradient"},a.createElement("div",{className:"LoginBackground"},a.createElement("div",{className:"LoginContainer"},this.props.children))))),a.createElement(dt,null))}}let ht=class extends a.Component{m_bShowErrors=!0;constructor(e){super(e),this.state={strAccountName:"",strPassword:"",strUser:"",bRememberPassword:!1}}componentWillMount(){this.setState({strAccountName:De.accountName})}OnGetLoginUsers(e){this.setState({strUser:e})}LoginWithCachedCredentials(e){De.SetSteamGuardCode(""),De.SetAccountAndPassword(this.state.strAccountName,"",!0),De.StartLogin(),this.m_bShowErrors=!0}OnAccountNameChange(e){this.setState({strAccountName:e.target.value})}OnPasswordChange(e){this.setState({strPassword:e.target.value})}OnRememberChange(e){this.setState({bRememberPassword:e.target.checked})}LoginWithCredentials(e){e.preventDefault(),De.SetSteamGuardCode(""),this.state.strAccountName&&this.state.strPassword&&(De.SetAccountAndPassword(this.state.strAccountName,this.state.strPassword,this.state.bRememberPassword),De.StartLogin()),this.m_bShowErrors=!0}async ShowLoginFailure(){await it(Qe("#Login_LastError",gt(De.loginResult))),this.m_bShowErrors=!1}render(){if(this.state.strAccountName&&De.hasCachedCredentials)return this.LoginWithCachedCredentials(this.state.strAccountName),a.createElement(pt,null);1!=De.loginResult&&21!=De.loginResult&&this.m_bShowErrors&&this.ShowLoginFailure();let e=this.state.bRememberPassword?"checked":"";return a.createElement(pt,null,a.createElement("div",{className:"PopupGenericTitle"},Qe("#Login_EnterCredentials")),a.createElement("form",{className:"MainLoginForm"},a.createElement("div",{className:"MainLoginFormGroup"},a.createElement("label",{className:"PopupGenericBodyText"},Qe("#Login_AccountName")),a.createElement(ot,{strText:Qe("#Login_AccountHelp")}),a.createElement("input",{type:"text",autoFocus:!0,value:this.state.strAccountName,onChange:this.OnAccountNameChange})),a.createElement("div",{className:"MainLoginFormGroup"},a.createElement("label",{className:"PopupGenericBodyText"},Qe("#Login_Password")),a.createElement("input",{type:"password",value:this.state.strPassword,onChange:this.OnPasswordChange})),a.createElement("div",{className:"MainLoginFormRemember"},a.createElement("input",{type:"checkbox",value:e,onChange:this.OnRememberChange}),a.createElement("label",{className:"PopupGenericBodyText"},Qe("#Login_RememberPassword"))),a.createElement("div",{className:"GenericButtonGroup"},a.createElement("button",{className:"GenericButton default",type:"submit",onClick:this.LoginWithCredentials},Qe("#Login_Login")),a.createElement("button",{className:"GenericButton",type:"button",onClick:this.props.onCancel},Qe("#Button_Cancel")))))}};(0,i.Cg)([H],ht.prototype,"OnGetLoginUsers",null),(0,i.Cg)([H],ht.prototype,"LoginWithCachedCredentials",null),(0,i.Cg)([H],ht.prototype,"OnAccountNameChange",null),(0,i.Cg)([H],ht.prototype,"OnPasswordChange",null),(0,i.Cg)([H],ht.prototype,"OnRememberChange",null),(0,i.Cg)([H],ht.prototype,"LoginWithCredentials",null),ht=(0,i.Cg)([o.PA],ht);class bt extends a.PureComponent{m_bShowErrors=!0;constructor(e){super(e),this.state={strAuthCode:""}}OnAuthCodeChange(e){this.setState({strAuthCode:e.target.value})}LoginWithSteamGuard(e){e.preventDefault(),De.SetSteamGuardCode(this.state.strAuthCode),De.StartLogin(),this.m_bShowErrors=!0}async ShowFailure(){await it(Qe("#Login_LastError",gt(De.loginResult))),this.m_bShowErrors=!1}render(){1!=De.loginResult&&63!=De.loginResult&&85!=De.loginResult&&this.m_bShowErrors&&this.ShowFailure();let e=85==De.loginResult?Qe("#Login_EnterTwoFactor",De.accountName):Qe("#Login_EnterSteamGuard",De.accountName),t=85==De.loginResult?Qe("#Login_TwoFactorTitle",De.accountName):Qe("#Login_SteamGuardTitle",De.accountName),r=85==De.loginResult?Qe("#Login_TwoFactorDesc",De.accountName):Qe("#Login_SteamGuardDesc",De.accountName);return a.createElement(pt,null,a.createElement("div",{className:"PopupGenericTitle"},t),a.createElement("br",null),a.createElement("label",{className:"PopupGenericBodyText"},r),a.createElement("form",{className:"SteamGuardForm"},a.createElement("div",{className:"MainLoginFormGroup"},a.createElement("label",{className:"PopupGenericBodyText"},e),a.createElement("input",{type:"text",maxLength:5,autoFocus:!0,value:this.state.strAuthCode,onChange:this.OnAuthCodeChange})),a.createElement("div",{className:"GenericButtonGroup"},a.createElement("button",{className:"GenericButton default",type:"submit",onClick:this.LoginWithSteamGuard},Qe("#Login_Login")),a.createElement("button",{className:"GenericButton",onClick:this.props.onCancel},Qe("#Button_Cancel")))))}}(0,i.Cg)([H],bt.prototype,"OnAuthCodeChange",null),(0,i.Cg)([H],bt.prototype,"LoginWithSteamGuard",null);let St=class extends a.Component{m_TimerID;m_bRequestInFlight;constructor(e){super(e),this.m_TimerID=0,this.m_bRequestInFlight=!1}CancelLogin(){}componentDidMount(){this.m_TimerID=window.setInterval((()=>this.tick()),1e3)}componentWillUnmount(){clearInterval(this.m_TimerID),this.m_TimerID=0}tick(){this.m_bRequestInFlight||We.shutdown||(this.m_bRequestInFlight=!0,De.UpdateLoginStatus(),this.m_bRequestInFlight=!1)}render(){let e="",t=!1,r=Qe("#Button_Cancel"),n=null;switch(this.props.loginState){case 1:e=Qe("#Login_ConnectingUser")+" "+De.accountName,t=!0;break;case 5:e=Qe("#Status_NoConnection"),t=!0,r=Qe("#Menu_Exit");break;case 3:e=Qe("#Login_LoggingOut");break;case 6:e=Qe("#Status_NoSteamCmd"),n=!We.steamcmdConnected&&a.createElement("div",{className:"HelpText"},a.createElement("br",null),Qe("#Status_NoSteamCmd_Help",String(27100)))}return a.createElement(pt,null,a.createElement("div",{className:"LoginStatusContainer"},a.createElement("div",{style:{width:"100%",height:"100%"}},a.createElement(G,{size:"medium",position:"center"})),a.createElement("div",{className:"LoginStatusMessage"},a.createElement("div",{className:"LoginStatus PopupGenericBodyText"},e)),n,a.createElement("div",{className:"LoginStatusButtonContainer"},t&&a.createElement("button",{className:"GenericButton",onClick:this.props.onCancel},r))))}};(0,i.Cg)([H],St.prototype,"CancelLogin",null),St=(0,i.Cg)([o.PA],St);let Et=class extends a.Component{constructor(e){super(e)}CancelLogin(){2==De.loginState||1==De.loginState||7==De.loginState?De.CancelLogin():st()}componentDidMount(){De.UpdateLoginStatus()}render(){let e=De.loginState;switch(e){case-1:return a.createElement(pt,null);case 0:return a.createElement(ht,{onCancel:this.CancelLogin});case 2:case 7:return a.createElement(bt,{loginState:e,onCancel:this.CancelLogin});default:return a.createElement(St,{loginState:e,onCancel:this.CancelLogin})}}};(0,i.Cg)([H],Et.prototype,"CancelLogin",null),Et=(0,i.Cg)([o.PA],Et);let yt=class extends a.Component{constructor(e){super(e)}render(){return a.createElement("div",{className:"MainWindow"},a.createElement(ct,null),a.createElement("div",{className:"LanguageWindowContentContainer"},a.createElement("div",{className:"LanguageWindowContent"},a.createElement("div",{className:"LanguageWindowContentGradient"},a.createElement("div",{className:"LanguageBackground"},a.createElement(Ct,null))))),a.createElement(dt,null))}};yt=(0,i.Cg)([o.PA],yt);class Ct extends a.Component{constructor(e){super(e),this.state={strLanguage:We.language}}async Save(){let e=this.state.strLanguage;"None"===e&&(e="english");const t=ue.Init(ke);t.Body().set_language(e),await Ue.SetLanguage(t),await nt(e),We.SetLanguage(e),We.SetView(3)}Cancel(){We.SetView(3)}onSelect(e){this.setState({strLanguage:e.target.value})}render(){let e=[];for(let t=0;t<31;t++){let r=C(t),a=Qe("#Language_"+r);e.push({key:t,value:r,name:a})}return a.createElement("div",{className:"LanguageContainer"},a.createElement("div",{className:"PopupGenericTitle"},Qe("#Language_Configure")),a.createElement("br",null),a.createElement("form",{className:"LanguageForm"},a.createElement("div",{className:"LanguageFormGroup"},a.createElement("select",{value:this.state.strLanguage,onChange:this.onSelect},e.map(((e,t)=>a.createElement("option",{key:t,value:e.value},e.name))))),a.createElement("div",{className:"GenericButtonGroup"},a.createElement("button",{className:"GenericButton",type:"submit",onClick:this.Save},Qe("#Button_Save")),a.createElement("button",{className:"GenericButton",type:"button",onClick:this.Cancel},Qe("#Button_Cancel")))))}}(0,i.Cg)([H],Ct.prototype,"Save",null),(0,i.Cg)([H],Ct.prototype,"Cancel",null),(0,i.Cg)([H],Ct.prototype,"onSelect",null);class ft extends a.Component{state={};reactErrorHandler=void 0;constructor(e){super(e),this.state.lastErrorKey=e.errorKey}static sm_ErrorReportingStore;static InstallErrorReportingStore(e){this.sm_ErrorReportingStore=e}componentDidCatch(e,t){const r=ft.sm_ErrorReportingStore;r?r.ReportError(e).then((e=>e&&this.setState({identifierHash:e.identifierHash}))):console.warn("No ErrorReportingStore - use ErrorReportingStore().Init() to configure error reporting to server"),this.setState({error:{error:e,info:t},lastErrorKey:this.props.errorKey})}Reset(){this.setState({error:void 0})}render(){const{children:e,fallback:t,errorKey:r}=this.props,{error:n,identifierHash:s,lastErrorKey:i}=this.state;return n&&r==i?void 0!==t?"function"==typeof t?t(n.error):t:ft.sm_ErrorReportingStore&&ft.sm_ErrorReportingStore.reporting_enabled?a.createElement(Bt,{error:n,identifierHash:s,store:ft.sm_ErrorReportingStore,onRefresh:this.Reset}):a.createElement(wt,{error:n,onDismiss:this.Reset}):e||null}}(0,i.Cg)([H],ft.prototype,"Reset",null);const wt=({error:e,onDismiss:t})=>{let r=e.error?e.error.stack:"Stack missing",n=e.info?e.info.componentStack:"",s=e.error&&e.error.message||"unknown error";return a.createElement(kt,null,a.createElement(Mt,null,'Error: "',s,'"'),"   ",a.createElement("span",{style:{textDecoration:"underline",cursor:"pointer"},onClick:t},"(x) Dismiss"),a.createElement("br",null),a.createElement(vt,null,r),a.createElement(vt,null,"The error occurred while rendering:",n))},Bt=e=>{const{error:t,onRefresh:r,identifierHash:n,store:s}=e,i=t.error&&t.error.message||"unknown error",o=`${s.product}_${s.version}_${n}`;return a.createElement(kt,null,a.createElement(Mt,null,"Something went wrong while displaying this content. ",a.createElement("span",{style:{textDecoration:"underline",cursor:"pointer"},onClick:r},"Refresh")),a.createElement(vt,null,"Error Reference: ",o),a.createElement(vt,null,i))},kt=({children:e})=>a.createElement("div",{style:{overflow:"auto",marginLeft:"15px",color:"white",fontSize:"16px",userSelect:"auto",backgroundColor:"black"},className:"ErrorBoundary"},e),Mt=({children:e})=>a.createElement("h1",{style:{fontSize:"20px",display:"inline-block",marginTop:"15px",userSelect:"auto"}},e),vt=({children:e})=>a.createElement("pre",{style:{marginTop:"15px",opacity:.7,userSelect:"auto"}},e);class Tt extends a.Component{m_TimerID;m_bRequestInFlight;constructor(e){super(e),this.state={rgClients:[],rgPayments:[]}}async componentDidMount(){this.refreshStatus(),this.m_TimerID=window.setInterval((()=>this.refreshStatus()),5e3)}componentWillUnmount(){clearInterval(this.m_TimerID),this.m_TimerID=0}renderIP(e){return(e>>>24)+"."+(e>>16&255)+"."+(e>>8&255)+"."+(255&e)}async refreshStatus(){this.m_bRequestInFlight=!0;const e=ue.Init(ve),t=await Ue.GetClientStatus(e);if(1!=t.Hdr().eresult())return;const{clients:r=[],payments:a=[]}=t.Body().toObject(),n=r.map((e=>({ip:this.renderIP(e.ip),hostname:e.hostname,connected:e.connected,instanceid:e.instance_id}))),s=a.map((e=>({transid:e.transid,hostname:e.hostname,amount:e.amount,status:e.purchase_status,name:e.persona_name,profile:e.profile_url,avatar:e.avatar_url})));this.setState({rgClients:n,rgPayments:s}),this.m_bRequestInFlight=!1}render(){const e=[Qe("#ClientStatus_IPAddress"),Qe("#ClientStatus_Hostname"),Qe("#ClientStatus_Status")];return a.createElement("div",{className:"ClientStatusContainerGradient"},a.createElement(Pt,{data:this.state.rgPayments}),a.createElement(Nt,{columns:e,data:this.state.rgClients}))}}class Nt extends a.Component{constructor(e){super(e)}render(){let e=this.props.columns,t=this.props.data;if(0==t.length){if(We.clientsHaveConnected)return a.createElement("div",null,a.createElement("div",{className:"ClientStatusHeader"},a.createElement("label",{className:"PopupGenericTitle"},Qe("#ClientStatus_Title"))),a.createElement("label",{className:"PopupGenericBodyText"},Qe("#ClientStatus_NoClients")));let e=a.createElement("a",{className:"link blue",href:"https://support.steampowered.com/kb_article.php?ref=8571-GLVN-8711"},"support.steampowered.com");return a.createElement("div",null,a.createElement("div",{className:"ClientStatusHeader"},a.createElement("label",{className:"PopupGenericTitle"},Qe("#ClientStatus_Title"))),a.createElement("label",{className:"PopupGenericBodyText"},Qe("#ClientStatus_NoClients")),a.createElement("br",null),a.createElement("label",{className:"HelpText"},Qe("#ClientStatus_Troubleshoot")+":"),a.createElement("ul",null,a.createElement("li",null,a.createElement("label",{className:"HelpText"},Qe("#ClientStatus_Troubleshoot1"))),a.createElement("li",null,a.createElement("label",{className:"HelpText"},Xe("#ClientStatus_Troubleshoot2",e)))))}We.SetClientsConnected(!0);let r=a.createElement("tr",null,e.map(((e,t)=>a.createElement("th",{className:"col"+t,key:t},e)))),n=t.map((function(t){let r=t.ip+String(t.instanceid);return a.createElement("tr",{key:r},e.map(((e,r)=>{let n="";return n=0==r?t.ip:1==r?t.hostname:t.connected?Qe("#Status_Connected"):Qe("#Status_NotConnected"),a.createElement("td",{className:"col"+r,key:r},n)})))}));return a.createElement("div",{className:"ClientStatusContainer"},a.createElement("div",{className:"ClientStatusHeader"},a.createElement("label",{className:"PopupGenericBodyTitle"},Qe("#ClientStatus_Title"))),a.createElement("div",{className:"ClientStatusTableContainer"},a.createElement("table",{className:"ClientStatusTable table table-bordered table-hover"},a.createElement("thead",null,r),a.createElement("tbody",null,n))))}}class Pt extends a.Component{constructor(e){super(e)}Approve(e){window.location.href="https://partner.steamgames.com/walletfunding/approval/"}render(){let e=[Qe("#ClientStatus_Action"),Qe("#ClientStatus_Hostname"),Qe("#ClientStatus_Account"),Qe("#ClientStatus_Amount")],t=this.props.data,r=a.createElement("tr",null,e.map(((e,t)=>a.createElement("th",{className:"col"+t,key:t},e)))),n=t.map((t=>a.createElement("tr",{key:t.transid},e.map(((e,r)=>{switch(r){case 0:return a.createElement("td",{className:"col"+r,key:r},a.createElement("button",{className:"btn_blue_white_innerfade",onClick:this.Approve},Qe("#ClientStatus_Approve")));case 1:return a.createElement("td",{className:"col"+r,key:r},t.hostname);case 2:return a.createElement("td",{className:"col"+r,key:r},a.createElement("div",{className:"profile"},a.createElement("a",{href:t.profile},a.createElement("img",{className:"avatar",src:t.avatar})),a.createElement("a",{className:"link",href:t.profile},t.name)));case 3:return a.createElement("td",{className:"col"+r,key:r},t.amount);default:return null}})))));return 0==t.length?null:a.createElement("div",{className:"CafeFundingContainer"},a.createElement("div",{className:"CafeFundingHeader"},a.createElement("label",{className:"PopupGenericTitle"},Qe("#ClientStatus_FundingRequests"))),a.createElement("div",{className:"ClientStatusDesc"},a.createElement("label",{className:"HelpText"},Qe("#ClientStatus_FundingInstr"))),a.createElement("div",{className:"CafeFundingTableContainer"},a.createElement("table",{className:"CafeFundingTable table table-bordered table-hover"},a.createElement("thead",null,r),a.createElement("tbody",null,n))))}}(0,i.Cg)([H],Pt.prototype,"Approve",null);let It=class extends a.Component{constructor(e){super(e)}render(){return a.createElement("div",{className:"MainWindow"},a.createElement(ct,null),a.createElement("div",{className:"CacheWindowContentContainer"},a.createElement("div",{className:"CacheWindowContent"},a.createElement("div",{className:"CacheWindowContentGradient"},a.createElement("div",{className:"CacheBackground"},a.createElement(Lt,null))))),a.createElement(dt,null))}};function Rt(e){return null==e?"":e.toLocaleString()}It=(0,i.Cg)([o.PA],It);class Lt extends a.Component{m_bEditDialogVisible=!1;m_TimerID;m_bRequestInFlight=!1;constructor(e){super(e),this.state={bEnabled:!1,unPort:0,strCacheLocation:"",unMaxSize:0,bP2PEnabled:!1,unCurrentSize:0,unCurrentBW:0,unTotalBytesServed:0,strExplicitIP:"",bExternalProcess:!1}}async Edit(){if(this.m_bEditDialogVisible)return;let e=a.createElement(Ft,{bEnabled:this.state.bEnabled,unPort:this.state.unPort,strCacheLocation:this.state.strCacheLocation,unMaxSize:this.state.unMaxSize,bP2PEnabled:this.state.bP2PEnabled,bExternalProcess:this.state.bExternalProcess,strExplicitIP:this.state.strExplicitIP,onOk:(e,t,r,a,n,s,i)=>this.OnUpdate(e,t,r,a,n,s,i),CloseModal:()=>this.CloseModal()},null);this.m_bEditDialogVisible=!0,We.ShowModal(e)}Cancel(){We.SetView(3)}CloseModal(){this.m_bEditDialogVisible=!1}async OnUpdate(e,t,r,a,n,s,i){const o=ue.Init(Le);o.Body().set_enabled(e),o.Body().set_port(t),o.Body().set_cache_location(r),o.Body().set_max_size_gb(a),o.Body().set_p2p_enabled(n),o.Body().set_explicit_ip_address(i),o.Body().set_external_process(s);1!=(await Ue.UpdateCacheConfig(o)).Hdr().eresult()?await it(Qe("#Cache_UpdateFailed")):(st(!0),We.SetView(3))}async componentWillMount(){const e=ue.Init(Ie),t=await Ue.GetContentCacheStatus(e);this.setState({bEnabled:t.Body().enabled(),unPort:t.Body().port(),strCacheLocation:t.Body().cache_location(),unMaxSize:t.Body().max_size_gb(),bP2PEnabled:t.Body().p2p_enabled(),bExternalProcess:t.Body().external_process(),strExplicitIP:t.Body().explicit_ip_address()}),t.Body().enabled()&&this.setState({unCurrentSize:t.Body().current_size_gb(),unCurrentBW:parseFloat(t.Body().current_bw()),unTotalBytesServed:parseFloat(t.Body().total_bytes_served())}),this.m_TimerID=window.setInterval((()=>this.tick()),1e3)}componentWillUnmount(){clearInterval(this.m_TimerID),this.m_TimerID=0}async tick(){if(this.m_bRequestInFlight||We.shutdown)return;this.m_bRequestInFlight=!0;const e=ue.Init(Ie),t=await Ue.GetContentCacheStatus(e);t.Body().enabled()&&this.setState({unCurrentSize:t.Body().current_size_gb(),unCurrentBW:parseFloat(t.Body().current_bw()),unTotalBytesServed:parseFloat(t.Body().total_bytes_served())}),this.m_bRequestInFlight=!1}render(){let e=this.state.bEnabled?Qe("#Status_Enabled"):Qe("#Status_Disabled"),t=this.state.bP2PEnabled?Qe("#Status_Enabled"):Qe("#Status_Disabled"),r=this.state.bExternalProcess?Qe("#Status_Enabled"):Qe("#Status_Disabled");return a.createElement("div",{className:"CacheContainer"},a.createElement("div",{className:"PopupGenericTitle"},Qe("#Cache_Configure")),a.createElement("br",null),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_Location")+":"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},e)),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_Location")+":"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},this.state.strCacheLocation),a.createElement(ot,{strText:Qe("#Cache_LocationTip")})),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_Port")+":"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},this.state.unPort),a.createElement(ot,{strText:Qe("#Cache_PortTip")})),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_MaxSize")+" (GB):"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},Rt(this.state.unMaxSize)),a.createElement(ot,{strText:Qe("#Cache_MaxSizeTip")})),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_P2P")+":"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},t),a.createElement(ot,{strText:Qe("#Cache_P2PTip")})),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_Explicit_IP")+":"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},this.state.strExplicitIP),a.createElement(ot,{strText:Qe("#Cache_ExplicitIPTip")})),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_External_Process")+":"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},r),a.createElement(ot,{strText:Qe("#Cache_ExternalProcessTip")})),a.createElement("br",null),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_CurrentSize")+" (GB):"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},Rt(this.state.unCurrentSize))),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_Bandwidth")+":"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},(null==(n=this.state.unCurrentBW)?"":(n/1e6).toFixed(1).toLocaleString())+" Mbps")),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_BytesServed")+":"),a.createElement("label",{className:"PopupGenericBodyText TextPairValue"},function(e){return null==e?"":e<1e3?e.toLocaleString():e<1e6?(e/1e3).toFixed(1).toLocaleString()+" KB":e<1e9?(e/1e6).toFixed(2).toLocaleString()+" MB":(e/1e9).toFixed(3).toLocaleString()+" GB"}(this.state.unTotalBytesServed))),a.createElement("div",{className:"GenericButtonGroup"},a.createElement("button",{className:"GenericButton",type:"submit",onClick:this.Edit},Qe("#Button_Edit")),a.createElement("button",{className:"GenericButton",type:"button",onClick:this.Cancel},Qe("#Button_Cancel"))));var n}}(0,i.Cg)([H],Lt.prototype,"Edit",null),(0,i.Cg)([H],Lt.prototype,"Cancel",null),(0,i.Cg)([H],Lt.prototype,"CloseModal",null);class Ft extends a.Component{constructor(e){super(e),this.state={bEnabled:this.props.bEnabled,unPort:this.props.unPort,strCacheLocation:this.props.strCacheLocation,unMaxSize:this.props.unMaxSize,bP2PEnabled:this.props.bP2PEnabled,bExternalProcess:this.props.bExternalProcess,strExplicitIP:this.props.strExplicitIP}}Save(){return this.props.onOk(this.state.bEnabled,this.state.unPort,this.state.strCacheLocation,this.state.unMaxSize,this.state.bP2PEnabled,this.state.bExternalProcess,this.state.strExplicitIP),this.props.CloseModal(),!0}Cancel(){return this.props.CloseModal(),!0}OnEnableChange(e){this.setState({bEnabled:e.target.checked}),e.target.checked&&0==this.state.unPort&&this.setState({unPort:80})}OnP2PChange(e){this.setState({bP2PEnabled:e.target.checked})}OnPortChange(e){this.setState({unPort:Number(e.target.value)})}OnMaxSizeChange(e){this.setState({unMaxSize:Number(e.target.value)})}OnLocationChange(e){this.setState({strCacheLocation:e.target.value})}OnExternalProcessChange(e){this.setState({bExternalProcess:e.target.checked})}OnExplicitIPChange(e){this.setState({strExplicitIP:e.target.value})}render(){return a.createElement(rt,{closeModal:this.props.closeModal,onOk:this.Save,onCancel:this.Cancel,strTitle:Qe("#Cache_Configure"),className:"CacheEditDialog",strOkButtonText:Qe("#Button_Restart")},a.createElement("div",{className:"CacheEditContainer"},a.createElement("div",{className:"CacheEditGroup CacheStateEnable"},a.createElement("input",{type:"checkbox",checked:this.state.bEnabled,onChange:this.OnEnableChange}),a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_EnableCache"))),a.createElement("div",{className:"CacheEditGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_Location")+":"),a.createElement("input",{className:"CacheEditLocation TextPairValue",type:"text",value:this.state.strCacheLocation,onChange:this.OnLocationChange})),a.createElement("div",{className:"CacheEditGroup"},a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_MaxSize")+" (GB):"),a.createElement("input",{className:"CacheEditMaxSize TextPairValue",type:"number",value:this.state.unMaxSize,onChange:this.OnMaxSizeChange})),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_Port")+":"),a.createElement("input",{className:"CacheEditPort TextPairValue",type:"number",value:this.state.unPort,onChange:this.OnPortChange})),a.createElement("div",{className:"CacheFormGroup"},a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_Explicit_IP")+":"),a.createElement("input",{className:"CacheEditPort TextPairValue",type:"text",value:this.state.strExplicitIP,onChange:this.OnExplicitIPChange}))),a.createElement("div",{className:"CacheEditGroup CachePNPEnable"},a.createElement("input",{type:"checkbox",checked:this.state.bP2PEnabled,onChange:this.OnP2PChange}),a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_EnableP2P"))),a.createElement("div",{className:"CacheEditGroup CacheExternalProcess"},a.createElement("input",{type:"checkbox",checked:this.state.bExternalProcess,onChange:this.OnExternalProcessChange}),a.createElement("label",{className:"PopupGenericBodyText TextPairName"},Qe("#Cache_External_Process"))),a.createElement("div",{className:"CacheEditGroup"},a.createElement("label",{className:"PopupGenericBodyText"},Qe("#Cache_RestartRequired")))))}}(0,i.Cg)([H],Ft.prototype,"Save",null),(0,i.Cg)([H],Ft.prototype,"Cancel",null),(0,i.Cg)([H],Ft.prototype,"OnEnableChange",null),(0,i.Cg)([H],Ft.prototype,"OnP2PChange",null),(0,i.Cg)([H],Ft.prototype,"OnPortChange",null),(0,i.Cg)([H],Ft.prototype,"OnMaxSizeChange",null),(0,i.Cg)([H],Ft.prototype,"OnLocationChange",null),(0,i.Cg)([H],Ft.prototype,"OnExternalProcessChange",null),(0,i.Cg)([H],Ft.prototype,"OnExplicitIPChange",null);let At=class extends a.Component{m_TimerID;m_bRequestInFlight;constructor(e){super(e),this.state={bLocalizationComplete:!1}}async componentDidMount(){const e=ue.Init(we);let t;const r=ue.Init(Ie);await Promise.all([Ue.GetLanguage(e),Ue.GetContentCacheStatus(r)]).then((e=>{t=e[0];const r=e[1];We.SetCacheState(r.Body().enabled())}));let a="None"===t.Body().language(),n=C(f(t.Body().language()));"None"!==t.Body().language()&&We.SetLanguage(n),await nt(n),this.setState({bLocalizationComplete:!0}),We.SetView(a?2:1),this.m_TimerID=window.setInterval((()=>this.tick()),1e3)}componentWillUnmount(){clearInterval(this.m_TimerID),this.m_TimerID=0}async tick(){if(this.m_bRequestInFlight||We.shutdown)return;this.m_bRequestInFlight=!0;const e=ue.Init(Ce),t=await Ue.GetStatus(e);1!=t.Hdr().eresult()?(De.SetSteamCmdNotConnected(),We.SetView(1)):(De.SetLoginStatus(t.Body().logon_state(),t.Body().logon_eresult()),We.SetConnection(t.Body().connected()),We.SetCacheState(t.Body().cache_enabled()),We.SetAcctStatus(t.Body().acct_status())),4==De.loginState&&1==We.view?We.SetView(3):4!=De.loginState&&3==We.view&&We.SetView(1),this.m_bRequestInFlight=!1}render(){let e=We.view;return 3==e&&4!=De.loginState&&(e=1),a.createElement("div",{id:"SiteServerMain",className:"SiteServerMain"},a.createElement(ft,null,1==e&&a.createElement(Et,null),2==e&&a.createElement(yt,null),3==e&&a.createElement(zt,null),4==e&&a.createElement(It,null),a.createElement(tt,null)))}};At=(0,i.Cg)([o.PA],At);class zt extends a.PureComponent{constructor(e){super(e),this.state={bSidebarCollapsed:!1}}render(){return a.createElement("div",{className:"MainWindow"},a.createElement(lt,null),a.createElement("div",{className:"MainWindowContentContainer"},a.createElement(xt,null)),a.createElement(mt,null))}}let xt=class extends a.Component{constructor(e){super(e)}render(){return a.createElement("div",{className:"MainWindowContent"},a.createElement("div",{className:"MainWindowContentGradient"},a.createElement("div",{className:"MainWindowClientStatusContainer"},a.createElement(Tt,null))))}};xt=(0,i.Cg)([o.PA],xt);const Ot=At;(0,D.jK)({enforceActions:"never"}),window.AssertMsg=s,n.H(document.getElementById("root")).render(a.createElement(Ot,null))},2857:(e,t,r)=>{var a={"./siteserverui_arabic.json":[488,488],"./siteserverui_brazilian.json":[5802,5802],"./siteserverui_bulgarian.json":[465,465],"./siteserverui_czech.json":[5291,5291],"./siteserverui_danish.json":[8765,8765],"./siteserverui_dutch.json":[3518,3518],"./siteserverui_english.json":[8876,8876],"./siteserverui_finnish.json":[4111,4111],"./siteserverui_french.json":[196,196],"./siteserverui_german.json":[9274,9274],"./siteserverui_greek.json":[5088,5088],"./siteserverui_hungarian.json":[4725,4725],"./siteserverui_indonesian.json":[8564,8564],"./siteserverui_italian.json":[3380,3380],"./siteserverui_japanese.json":[8499,8499],"./siteserverui_koreana.json":[1891,1891],"./siteserverui_latam.json":[4955,4955],"./siteserverui_norwegian.json":[4468,4468],"./siteserverui_polish.json":[9845,9845],"./siteserverui_portuguese.json":[9745,9745],"./siteserverui_romanian.json":[1151,1151],"./siteserverui_russian.json":[2165,2165],"./siteserverui_schinese.json":[6752,6752],"./siteserverui_spanish.json":[4732,4732],"./siteserverui_swedish.json":[7885,7885],"./siteserverui_tchinese.json":[1939,1939],"./siteserverui_thai.json":[9850,9850],"./siteserverui_turkish.json":[6556,6556],"./siteserverui_ukrainian.json":[1498,1498],"./siteserverui_vietnamese.json":[1595,1595]};function n(e){if(!r.o(a,e))return Promise.resolve().then((()=>{var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=a[e],n=t[0];return r.e(t[1]).then((()=>r.t(n,19)))}n.keys=()=>Object.keys(a),n.id=2857,e.exports=n}},e=>{e.O(0,[8997],(()=>{return t=4749,e(e.s=t);var t}));e.O()}]);