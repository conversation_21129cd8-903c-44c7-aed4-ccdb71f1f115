//! Eden UI Manager
//! Main UI system manager for Eden Reforger
//! Converts the original Y-menu tabbed interface system to Reforger UI framework

class EdenUIManager
{
    protected ref map<string, Widget> m_OpenDialogs;
    protected ref array<string> m_DialogStack;
    protected Widget m_CurrentMainDialog;
    protected string m_CurrentDialogName;
    protected bool m_IsUIOpen;
    
    // UI Layout Configuration
    protected static const float UI_PANEL_WIDTH = 0.48;
    protected static const float UI_PANEL_HEIGHT = 1.075;
    protected static const float UI_TAB_WIDTH = 0.08;
    protected static const float UI_TAB_HEIGHT = 0.08;
    protected static const int MAX_TABS = 12;
    
    // Tab definitions matching original Y-menu system
    protected ref array<ref EdenUITab> m_UITabs;
    
    void EdenUIManager()
    {
        m_OpenDialogs = new map<string, Widget>();
        m_DialogStack = new array<string>();
        m_CurrentMainDialog = null;
        m_CurrentDialogName = "";
        m_IsUIOpen = false;
        m_UITabs = new array<ref EdenUITab>();
        
        InitializeUITabs();
    }
    
    void Initialize()
    {
        Print("[EdenUIManager] UI Manager initialized");
    }
    
    //! Initialize UI tab definitions
    protected void InitializeUITabs()
    {
        // Tab 1 - Main Menu
        ref EdenUITab mainTab = new EdenUITab();
        mainTab.SetTabId(1);
        mainTab.SetTabName("Main");
        mainTab.SetDialogClass("EdenMainMenuDialog");
        mainTab.SetTooltip("Main Menu");
        mainTab.SetIconPath("images/icons/main.paa");
        m_UITabs.Insert(mainTab);
        
        // Tab 2 - Inventory
        ref EdenUITab inventoryTab = new EdenUITab();
        inventoryTab.SetTabId(2);
        inventoryTab.SetTabName("Inventory");
        inventoryTab.SetDialogClass("EdenInventoryDialog");
        inventoryTab.SetTooltip("Player Inventory");
        inventoryTab.SetIconPath("images/icons/inventory.paa");
        m_UITabs.Insert(inventoryTab);
        
        // Tab 3 - Stats
        ref EdenUITab statsTab = new EdenUITab();
        statsTab.SetTabId(3);
        statsTab.SetTabName("Stats");
        statsTab.SetDialogClass("EdenStatsDialog");
        statsTab.SetTooltip("Player Stats");
        statsTab.SetIconPath("images/icons/stats.paa");
        m_UITabs.Insert(statsTab);
        
        // Tab 4 - Key Chain
        ref EdenUITab keyTab = new EdenUITab();
        keyTab.SetTabId(4);
        keyTab.SetTabName("Keys");
        keyTab.SetDialogClass("EdenKeyChainDialog");
        keyTab.SetTooltip("Key Chain");
        keyTab.SetIconPath("images/icons/keys.paa");
        m_UITabs.Insert(keyTab);
        
        // Tab 5 - Phone
        ref EdenUITab phoneTab = new EdenUITab();
        phoneTab.SetTabId(5);
        phoneTab.SetTabName("Phone");
        phoneTab.SetDialogClass("EdenPhoneDialog");
        phoneTab.SetTooltip("Phone");
        phoneTab.SetIconPath("images/icons/phone.paa");
        m_UITabs.Insert(phoneTab);
        
        // Tab 6 - Settings
        ref EdenUITab settingsTab = new EdenUITab();
        settingsTab.SetTabId(6);
        settingsTab.SetTabName("Settings");
        settingsTab.SetDialogClass("EdenSettingsDialog");
        settingsTab.SetTooltip("Settings");
        settingsTab.SetIconPath("images/icons/settings.paa");
        m_UITabs.Insert(settingsTab);
        
        // Tab 7 - Groups
        ref EdenUITab groupsTab = new EdenUITab();
        groupsTab.SetTabId(7);
        groupsTab.SetTabName("Groups");
        groupsTab.SetDialogClass("EdenGroupsDialog");
        groupsTab.SetTooltip("Groups");
        groupsTab.SetIconPath("images/icons/groups.paa");
        m_UITabs.Insert(groupsTab);
        
        // Tab 8 - Gangs
        ref EdenUITab gangsTab = new EdenUITab();
        gangsTab.SetTabId(8);
        gangsTab.SetTabName("Gangs");
        gangsTab.SetDialogClass("EdenGangsDialog");
        gangsTab.SetTooltip("Gangs");
        gangsTab.SetIconPath("images/icons/gangs.paa");
        m_UITabs.Insert(gangsTab);
        
        // Tab 9 - Market
        ref EdenUITab marketTab = new EdenUITab();
        marketTab.SetTabId(9);
        marketTab.SetTabName("Market");
        marketTab.SetDialogClass("EdenMarketDialog");
        marketTab.SetTooltip("Market Values");
        marketTab.SetIconPath("images/icons/market.paa");
        m_UITabs.Insert(marketTab);
        
        // Tab 10 - Wanted
        ref EdenUITab wantedTab = new EdenUITab();
        wantedTab.SetTabId(10);
        wantedTab.SetTabName("Wanted");
        wantedTab.SetDialogClass("EdenWantedDialog");
        wantedTab.SetTooltip("Wanted Players");
        wantedTab.SetIconPath("images/icons/wanted.paa");
        m_UITabs.Insert(wantedTab);
        
        // Tab 11 - Titles
        ref EdenUITab titlesTab = new EdenUITab();
        titlesTab.SetTabId(11);
        titlesTab.SetTabName("Titles");
        titlesTab.SetDialogClass("EdenTitlesDialog");
        titlesTab.SetTooltip("Titles Menu");
        titlesTab.SetIconPath("images/icons/titles.paa");
        m_UITabs.Insert(titlesTab);
        
        // Tab 12 - Admin (conditional)
        ref EdenUITab adminTab = new EdenUITab();
        adminTab.SetTabId(12);
        adminTab.SetTabName("Admin");
        adminTab.SetDialogClass("EdenAdminMenuDialog");
        adminTab.SetTooltip("Admin Menu");
        adminTab.SetIconPath("images/icons/admin.paa");
        adminTab.SetRequiresAdmin(true);
        m_UITabs.Insert(adminTab);
        
        Print("[EdenUIManager] UI tabs initialized - Count: " + m_UITabs.Count());
    }
    
    //! Open main UI menu
    bool OpenMainUI(string playerId, int defaultTab = 1)
    {
        if (m_IsUIOpen)
        {
            CloseAllDialogs();
        }
        
        // Open the specified tab
        return OpenDialog(GetTabDialogClass(defaultTab), playerId);
    }
    
    //! Open specific dialog
    bool OpenDialog(string dialogClass, string playerId, string parameter = "")
    {
        if (m_OpenDialogs.Contains(dialogClass))
        {
            Print("[EdenUIManager] Dialog already open: " + dialogClass);
            return false;
        }
        
        // Create dialog widget
        Widget dialogWidget = CreateDialogWidget(dialogClass, playerId, parameter);
        if (!dialogWidget)
        {
            Print("[EdenUIManager] Failed to create dialog widget: " + dialogClass);
            return false;
        }
        
        // Store dialog reference
        m_OpenDialogs.Set(dialogClass, dialogWidget);
        m_DialogStack.Insert(dialogClass);
        
        // Set as current main dialog if it's a main UI dialog
        if (IsMainUIDialog(dialogClass))
        {
            m_CurrentMainDialog = dialogWidget;
            m_CurrentDialogName = dialogClass;
            m_IsUIOpen = true;
        }
        
        Print("[EdenUIManager] Dialog opened: " + dialogClass);
        return true;
    }
    
    //! Close specific dialog
    bool CloseDialog(string dialogClass)
    {
        if (!m_OpenDialogs.Contains(dialogClass))
        {
            Print("[EdenUIManager] Dialog not open: " + dialogClass);
            return false;
        }
        
        Widget dialogWidget = m_OpenDialogs.Get(dialogClass);
        if (dialogWidget)
        {
            dialogWidget.RemoveFromHierarchy();
        }
        
        m_OpenDialogs.Remove(dialogClass);
        
        // Remove from stack
        int stackIndex = m_DialogStack.Find(dialogClass);
        if (stackIndex != -1)
            m_DialogStack.RemoveOrdered(stackIndex);
        
        // Update current main dialog
        if (m_CurrentDialogName == dialogClass)
        {
            m_CurrentMainDialog = null;
            m_CurrentDialogName = "";
            m_IsUIOpen = false;
        }
        
        Print("[EdenUIManager] Dialog closed: " + dialogClass);
        return true;
    }
    
    //! Close all dialogs
    void CloseAllDialogs()
    {
        array<string> dialogsToClose = new array<string>();
        
        // Collect all open dialogs
        for (int i = 0; i < m_OpenDialogs.Count(); i++)
        {
            dialogsToClose.Insert(m_OpenDialogs.GetKey(i));
        }
        
        // Close all dialogs
        for (int i = 0; i < dialogsToClose.Count(); i++)
        {
            CloseDialog(dialogsToClose[i]);
        }
        
        m_IsUIOpen = false;
        Print("[EdenUIManager] All dialogs closed");
    }

    //! Show loading screen
    void ShowLoadingScreen(EdenLoadingDialog loadingDialog)
    {
        if (!loadingDialog)
            return;

        // Close any existing dialogs
        CloseAllDialogs();

        // Create loading screen widget from layout
        Widget loadingWidget = GetGame().GetWorkspace().CreateWidgets("{EdenReforger/UI/Layouts/LoadingScreen.layout}");
        if (!loadingWidget)
        {
            Print("[EdenUIManager] Failed to create loading screen widget from layout");

            // Fallback to simple widget creation
            loadingWidget = GetGame().GetWorkspace().CreateWidget(FrameWidgetTypeID, WidgetFlags.VISIBLE, Color.BLACK, 0);
            if (!loadingWidget)
            {
                Print("[EdenUIManager] Failed to create fallback loading screen widget");
                return;
            }

            // Set fullscreen
            loadingWidget.SetFlags(WidgetFlags.VISIBLE);
            loadingWidget.SetSize(1.0, 1.0);
            loadingWidget.SetPos(0.0, 0.0);
        }

        // Store the dialog
        m_OpenDialogs.Set("EdenLoadingDialog", loadingWidget);

        // Initialize the dialog with the widget
        loadingDialog.SetRootWidget(loadingWidget);
        loadingDialog.OnMenuOpen();

        Print("[EdenUIManager] Loading screen displayed");
    }

    //! Switch to different tab
    bool SwitchTab(int tabId, string playerId)
    {
        string dialogClass = GetTabDialogClass(tabId);
        if (dialogClass == "")
        {
            Print("[EdenUIManager] Invalid tab ID: " + tabId);
            return false;
        }
        
        // Close current main dialog if open
        if (m_CurrentMainDialog)
        {
            CloseDialog(m_CurrentDialogName);
        }
        
        // Open new tab dialog
        return OpenDialog(dialogClass, playerId);
    }
    
    //! Get dialog class for tab ID
    protected string GetTabDialogClass(int tabId)
    {
        for (int i = 0; i < m_UITabs.Count(); i++)
        {
            ref EdenUITab tab = m_UITabs[i];
            if (tab.GetTabId() == tabId)
                return tab.GetDialogClass();
        }
        
        return "";
    }
    
    //! Check if dialog is a main UI dialog
    protected bool IsMainUIDialog(string dialogClass)
    {
        for (int i = 0; i < m_UITabs.Count(); i++)
        {
            ref EdenUITab tab = m_UITabs[i];
            if (tab.GetDialogClass() == dialogClass)
                return true;
        }
        
        return false;
    }
    
    //! Create dialog widget (implementation would depend on specific dialog system)
    protected Widget CreateDialogWidget(string dialogClass, string playerId, string parameter)
    {
        // This would create the actual widget based on the dialog class
        // For now, return a placeholder
        Print("[EdenUIManager] Creating dialog widget: " + dialogClass + " for player: " + playerId);
        return null; // Would return actual widget in full implementation
    }
    
    //! Get available tabs for player
    array<ref EdenUITab> GetAvailableTabs(string playerId)
    {
        array<ref EdenUITab> availableTabs = new array<ref EdenUITab>();
        
        for (int i = 0; i < m_UITabs.Count(); i++)
        {
            ref EdenUITab tab = m_UITabs[i];
            
            // Check if tab requires admin privileges
            if (tab.RequiresAdmin())
            {
                // Check if player is admin (would integrate with admin system)
                if (!IsPlayerAdmin(playerId))
                    continue;
            }
            
            availableTabs.Insert(tab);
        }
        
        return availableTabs;
    }
    
    //! Check if player is admin (placeholder)
    protected bool IsPlayerAdmin(string playerId)
    {
        // This would integrate with the admin system
        return false;
    }
    
    //! Get current dialog info
    bool IsUIOpen() { return m_IsUIOpen; }
    string GetCurrentDialogName() { return m_CurrentDialogName; }
    Widget GetCurrentMainDialog() { return m_CurrentMainDialog; }
    array<string> GetOpenDialogs() { return m_DialogStack; }

    //! Called when loading is complete
    void OnLoadingComplete()
    {
        Print("[EdenUIManager] Loading complete - UI system ready");

        // Perform any post-loading UI initialization
        // This could include showing welcome messages, tutorials, etc.
    }

    //! Cleanup
    void Cleanup()
    {
        CloseAllDialogs();
        m_UITabs.Clear();
        
        Print("[EdenUIManager] UI Manager cleaned up");
    }
}

//! UI Tab data class
class EdenUITab
{
    protected int m_TabId;
    protected string m_TabName;
    protected string m_DialogClass;
    protected string m_Tooltip;
    protected string m_IconPath;
    protected bool m_RequiresAdmin;
    
    void EdenUITab()
    {
        m_TabId = 0;
        m_TabName = "";
        m_DialogClass = "";
        m_Tooltip = "";
        m_IconPath = "";
        m_RequiresAdmin = false;
    }
    
    // Getters and setters
    void SetTabId(int id) { m_TabId = id; }
    int GetTabId() { return m_TabId; }
    
    void SetTabName(string name) { m_TabName = name; }
    string GetTabName() { return m_TabName; }
    
    void SetDialogClass(string dialogClass) { m_DialogClass = dialogClass; }
    string GetDialogClass() { return m_DialogClass; }
    
    void SetTooltip(string tooltip) { m_Tooltip = tooltip; }
    string GetTooltip() { return m_Tooltip; }
    
    void SetIconPath(string iconPath) { m_IconPath = iconPath; }
    string GetIconPath() { return m_IconPath; }
    
    void SetRequiresAdmin(bool requiresAdmin) { m_RequiresAdmin = requiresAdmin; }
    bool RequiresAdmin() { return m_RequiresAdmin; }
}
