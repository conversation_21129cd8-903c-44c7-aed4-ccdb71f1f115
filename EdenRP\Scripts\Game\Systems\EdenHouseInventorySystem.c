//! Eden House Inventory System - Handles house storage, virtual/physical inventory management
//! Converted from original house inventory systems

class EdenHouseInventorySystem
{
    protected ref map<int, ref EdenHouseInventory> m_HouseInventories; // House ID -> Inventory
    protected ref map<string, int> m_PlayersInHouseInventory; // Player ID -> House ID
    
    // Inventory system configuration
    protected bool m_InventorySystemEnabled;
    protected int m_MaxItemStackSize;
    protected float m_WeightMultiplier;
    
    void EdenHouseInventorySystem()
    {
        m_HouseInventories = new map<int, ref EdenHouseInventory>();
        m_PlayersInHouseInventory = new map<string, int>();
        
        m_InventorySystemEnabled = true;
        m_MaxItemStackSize = 999;
        m_WeightMultiplier = 1.0;
        
        InitializeInventorySystem();
    }
    
    //! Initialize inventory system
    void Initialize()
    {
        Print("[EdenHouseInventorySystem] Initializing house inventory system...");
        
        LoadHouseInventories();
        
        // Set up periodic processing
        GetGame().GetCallqueue().CallLater(SaveAllInventories, 300000, true); // 5 minutes
        GetGame().GetCallqueue().CallLater(CleanupExpiredSessions, 600000, true); // 10 minutes
        
        Print("[EdenHouseInventorySystem] House inventory system initialized");
    }
    
    //! Initialize inventory system configuration
    protected void InitializeInventorySystem()
    {
        Print("[EdenHouseInventorySystem] Inventory system configuration initialized");
    }
    
    //! Open house inventory
    bool OpenHouseInventory(string playerId, int houseId, bool isVirtual)
    {
        if (playerId == "" || houseId <= 0)
            return false;
            
        if (!m_InventorySystemEnabled)
            return false;
            
        // Check if player can access house
        EdenHousingManager housingManager = EdenGameMode.GetInstance().GetHousingManager();
        if (!housingManager.CanAccessHouse(playerId, houseId))
            return false;
            
        // Get or create house inventory
        if (!m_HouseInventories.Contains(houseId))
        {
            CreateHouseInventory(houseId);
        }
        
        EdenHouseInventory houseInventory = m_HouseInventories.Get(houseId);
        
        // Set player as accessing this house inventory
        m_PlayersInHouseInventory.Set(playerId, houseId);
        
        // Set inventory type
        houseInventory.SetCurrentInventoryType(isVirtual);
        
        // Get player entity
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        // Open inventory interface
        OpenInventoryInterface(playerEntity, houseInventory, isVirtual);
        
        Print(string.Format("[EdenHouseInventorySystem] %1 opened %2 inventory for house %3", 
            playerId, isVirtual ? "virtual" : "physical", houseId));
        return true;
    }
    
    //! Close house inventory
    bool CloseHouseInventory(string playerId)
    {
        if (playerId == "" || !m_PlayersInHouseInventory.Contains(playerId))
            return false;
            
        int houseId = m_PlayersInHouseInventory.Get(playerId);
        
        // Remove player from inventory access
        m_PlayersInHouseInventory.Remove(playerId);
        
        // Save inventory changes
        if (m_HouseInventories.Contains(houseId))
        {
            SaveHouseInventory(houseId);
        }
        
        Print(string.Format("[EdenHouseInventorySystem] %1 closed inventory for house %2", playerId, houseId));
        return true;
    }
    
    //! Store item in house
    bool StoreItemInHouse(string playerId, int houseId, string itemClass, int quantity, bool isVirtual)
    {
        if (playerId == "" || houseId <= 0 || itemClass == "" || quantity <= 0)
            return false;
            
        // Check if player is accessing this house inventory
        if (!m_PlayersInHouseInventory.Contains(playerId) || m_PlayersInHouseInventory.Get(playerId) != houseId)
            return false;
            
        // Get house inventory
        if (!m_HouseInventories.Contains(houseId))
            return false;
            
        EdenHouseInventory houseInventory = m_HouseInventories.Get(houseId);
        
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player has the item
        if (!playerComp.HasItem(itemClass, quantity))
            return false;
            
        // Get item weight
        float itemWeight = GetItemWeight(itemClass) * quantity;
        
        // Check storage capacity
        if (isVirtual)
        {
            if (!CanStoreInVirtualInventory(houseInventory, itemWeight))
                return false;
        }
        else
        {
            if (!CanStoreInPhysicalInventory(houseInventory, itemWeight))
                return false;
        }
        
        // Remove item from player
        if (!playerComp.RemoveItem(itemClass, quantity))
            return false;
            
        // Add item to house inventory
        if (isVirtual)
        {
            houseInventory.AddVirtualItem(itemClass, quantity, itemWeight);
        }
        else
        {
            houseInventory.AddPhysicalItem(itemClass, quantity, itemWeight);
        }
        
        // Save inventory changes
        SaveHouseInventory(houseId);
        
        Print(string.Format("[EdenHouseInventorySystem] %1 stored %2x %3 in %4 inventory of house %5", 
            playerId, quantity, itemClass, isVirtual ? "virtual" : "physical", houseId));
        return true;
    }
    
    //! Take item from house
    bool TakeItemFromHouse(string playerId, int houseId, string itemClass, int quantity, bool isVirtual)
    {
        if (playerId == "" || houseId <= 0 || itemClass == "" || quantity <= 0)
            return false;
            
        // Check if player is accessing this house inventory
        if (!m_PlayersInHouseInventory.Contains(playerId) || m_PlayersInHouseInventory.Get(playerId) != houseId)
            return false;
            
        // Get house inventory
        if (!m_HouseInventories.Contains(houseId))
            return false;
            
        EdenHouseInventory houseInventory = m_HouseInventories.Get(houseId);
        
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if house has the item
        if (isVirtual)
        {
            if (!houseInventory.HasVirtualItem(itemClass, quantity))
                return false;
        }
        else
        {
            if (!houseInventory.HasPhysicalItem(itemClass, quantity))
                return false;
        }
        
        // Check if player can carry the item
        float itemWeight = GetItemWeight(itemClass) * quantity;
        if (!playerComp.CanCarryWeight(itemWeight))
            return false;
            
        // Remove item from house inventory
        if (isVirtual)
        {
            if (!houseInventory.RemoveVirtualItem(itemClass, quantity, itemWeight))
                return false;
        }
        else
        {
            if (!houseInventory.RemovePhysicalItem(itemClass, quantity, itemWeight))
                return false;
        }
        
        // Add item to player
        if (!playerComp.AddItem(itemClass, quantity))
        {
            // Rollback house inventory change
            if (isVirtual)
            {
                houseInventory.AddVirtualItem(itemClass, quantity, itemWeight);
            }
            else
            {
                houseInventory.AddPhysicalItem(itemClass, quantity, itemWeight);
            }
            return false;
        }
        
        // Save inventory changes
        SaveHouseInventory(houseId);
        
        Print(string.Format("[EdenHouseInventorySystem] %1 took %2x %3 from %4 inventory of house %5", 
            playerId, quantity, itemClass, isVirtual ? "virtual" : "physical", houseId));
        return true;
    }
    
    //! Move item between virtual and physical inventory
    bool MoveItemBetweenInventories(string playerId, int houseId, string itemClass, int quantity, bool fromVirtual)
    {
        if (playerId == "" || houseId <= 0 || itemClass == "" || quantity <= 0)
            return false;
            
        // Check if player is accessing this house inventory
        if (!m_PlayersInHouseInventory.Contains(playerId) || m_PlayersInHouseInventory.Get(playerId) != houseId)
            return false;
            
        // Get house inventory
        if (!m_HouseInventories.Contains(houseId))
            return false;
            
        EdenHouseInventory houseInventory = m_HouseInventories.Get(houseId);
        
        // Get item weight
        float itemWeight = GetItemWeight(itemClass) * quantity;
        
        // Check source and destination capacity
        if (fromVirtual)
        {
            if (!houseInventory.HasVirtualItem(itemClass, quantity))
                return false;
            if (!CanStoreInPhysicalInventory(houseInventory, itemWeight))
                return false;
        }
        else
        {
            if (!houseInventory.HasPhysicalItem(itemClass, quantity))
                return false;
            if (!CanStoreInVirtualInventory(houseInventory, itemWeight))
                return false;
        }
        
        // Move item
        if (fromVirtual)
        {
            if (!houseInventory.RemoveVirtualItem(itemClass, quantity, itemWeight))
                return false;
            houseInventory.AddPhysicalItem(itemClass, quantity, itemWeight);
        }
        else
        {
            if (!houseInventory.RemovePhysicalItem(itemClass, quantity, itemWeight))
                return false;
            houseInventory.AddVirtualItem(itemClass, quantity, itemWeight);
        }
        
        // Save inventory changes
        SaveHouseInventory(houseId);
        
        Print(string.Format("[EdenHouseInventorySystem] %1 moved %2x %3 from %4 to %5 inventory in house %6", 
            playerId, quantity, itemClass, fromVirtual ? "virtual" : "physical", 
            fromVirtual ? "physical" : "virtual", houseId));
        return true;
    }
    
    //! Store money in house
    bool StoreMoneyInHouse(string playerId, int houseId, int amount, bool isVirtual)
    {
        if (playerId == "" || houseId <= 0 || amount <= 0)
            return false;
            
        // Check if player is accessing this house inventory
        if (!m_PlayersInHouseInventory.Contains(playerId) || m_PlayersInHouseInventory.Get(playerId) != houseId)
            return false;
            
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Check if player has enough cash
        if (amount > playerComp.GetCash())
            return false;
            
        // Get house inventory
        if (!m_HouseInventories.Contains(houseId))
            return false;
            
        EdenHouseInventory houseInventory = m_HouseInventories.Get(houseId);
        
        // Check storage capacity for money (money has weight)
        float moneyWeight = amount * 0.001; // 1000 cash = 1 weight unit
        
        if (isVirtual)
        {
            if (!CanStoreInVirtualInventory(houseInventory, moneyWeight))
                return false;
        }
        else
        {
            if (!CanStoreInPhysicalInventory(houseInventory, moneyWeight))
                return false;
        }
        
        // Remove money from player
        playerComp.RemoveCash(amount);
        
        // Add money to house inventory
        if (isVirtual)
        {
            houseInventory.AddVirtualItem("money", amount, moneyWeight);
        }
        else
        {
            houseInventory.AddPhysicalItem("money", amount, moneyWeight);
        }
        
        // Save inventory changes
        SaveHouseInventory(houseId);
        
        Print(string.Format("[EdenHouseInventorySystem] %1 stored $%2 in %3 inventory of house %4", 
            playerId, amount, isVirtual ? "virtual" : "physical", houseId));
        return true;
    }
    
    //! Take money from house
    bool TakeMoneyFromHouse(string playerId, int houseId, int amount, bool isVirtual)
    {
        if (playerId == "" || houseId <= 0 || amount <= 0)
            return false;
            
        // Check if player is accessing this house inventory
        if (!m_PlayersInHouseInventory.Contains(playerId) || m_PlayersInHouseInventory.Get(playerId) != houseId)
            return false;
            
        // Get house inventory
        if (!m_HouseInventories.Contains(houseId))
            return false;
            
        EdenHouseInventory houseInventory = m_HouseInventories.Get(houseId);
        
        // Check if house has enough money
        if (isVirtual)
        {
            if (!houseInventory.HasVirtualItem("money", amount))
                return false;
        }
        else
        {
            if (!houseInventory.HasPhysicalItem("money", amount))
                return false;
        }
        
        // Get player entity and component
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Calculate money weight
        float moneyWeight = amount * 0.001;
        
        // Remove money from house inventory
        if (isVirtual)
        {
            if (!houseInventory.RemoveVirtualItem("money", amount, moneyWeight))
                return false;
        }
        else
        {
            if (!houseInventory.RemovePhysicalItem("money", amount, moneyWeight))
                return false;
        }
        
        // Add money to player
        playerComp.AddCash(amount);
        
        // Save inventory changes
        SaveHouseInventory(houseId);
        
        Print(string.Format("[EdenHouseInventorySystem] %1 took $%2 from %3 inventory of house %4", 
            playerId, amount, isVirtual ? "virtual" : "physical", houseId));
        return true;
    }
    
    //! Get house inventory
    EdenHouseInventory GetHouseInventory(int houseId)
    {
        if (houseId <= 0 || !m_HouseInventories.Contains(houseId))
            return null;
            
        return m_HouseInventories.Get(houseId);
    }
    
    //! Check if player is accessing house inventory
    bool IsPlayerAccessingHouseInventory(string playerId)
    {
        return m_PlayersInHouseInventory.Contains(playerId);
    }
    
    //! Get house ID player is accessing
    int GetPlayerAccessingHouseId(string playerId)
    {
        if (!m_PlayersInHouseInventory.Contains(playerId))
            return -1;
            
        return m_PlayersInHouseInventory.Get(playerId);
    }
    
    //! Helper methods
    protected void CreateHouseInventory(int houseId)
    {
        EdenHouseInventory houseInventory = new EdenHouseInventory();
        houseInventory.SetHouseId(houseId);
        houseInventory.Initialize();
        
        m_HouseInventories.Set(houseId, houseInventory);
        
        Print(string.Format("[EdenHouseInventorySystem] Created inventory for house %1", houseId));
    }
    
    protected bool CanStoreInVirtualInventory(EdenHouseInventory houseInventory, float itemWeight)
    {
        return (houseInventory.GetVirtualWeight() + itemWeight) <= houseInventory.GetVirtualCapacity();
    }
    
    protected bool CanStoreInPhysicalInventory(EdenHouseInventory houseInventory, float itemWeight)
    {
        return (houseInventory.GetPhysicalWeight() + itemWeight) <= houseInventory.GetPhysicalCapacity();
    }
    
    protected float GetItemWeight(string itemClass)
    {
        // Implementation would get item weight from configuration
        // For now, return default weight
        if (itemClass == "money")
            return 0.001;
            
        return 1.0 * m_WeightMultiplier;
    }
    
    protected void SaveHouseInventory(int houseId)
    {
        if (!m_HouseInventories.Contains(houseId))
            return;
            
        EdenHouseInventory houseInventory = m_HouseInventories.Get(houseId);
        
        // Implementation would save inventory to database
        Print(string.Format("[EdenHouseInventorySystem] Saved inventory for house %1", houseId));
    }
    
    protected void SaveAllInventories()
    {
        int savedCount = 0;
        foreach (int houseId, EdenHouseInventory houseInventory : m_HouseInventories)
        {
            SaveHouseInventory(houseId);
            savedCount++;
        }
        
        if (savedCount > 0)
        {
            Print(string.Format("[EdenHouseInventorySystem] Saved %1 house inventories", savedCount));
        }
    }
    
    protected void CleanupExpiredSessions()
    {
        // Implementation would cleanup expired inventory sessions
        Print("[EdenHouseInventorySystem] Cleaning up expired inventory sessions...");
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadHouseInventories() { }
    protected void OpenInventoryInterface(IEntity playerEntity, EdenHouseInventory houseInventory, bool isVirtual) { }
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    
    //! Configuration methods
    void SetInventorySystemEnabled(bool enabled) { m_InventorySystemEnabled = enabled; }
    bool IsInventorySystemEnabled() { return m_InventorySystemEnabled; }
    
    void SetMaxItemStackSize(int maxStack) { m_MaxItemStackSize = maxStack; }
    int GetMaxItemStackSize() { return m_MaxItemStackSize; }
    
    void SetWeightMultiplier(float multiplier) { m_WeightMultiplier = multiplier; }
    float GetWeightMultiplier() { return m_WeightMultiplier; }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenHouseInventorySystem] Cleaning up house inventory system...");
        
        // Save all inventories before cleanup
        SaveAllInventories();
        
        m_HouseInventories.Clear();
        m_PlayersInHouseInventory.Clear();
    }
}
