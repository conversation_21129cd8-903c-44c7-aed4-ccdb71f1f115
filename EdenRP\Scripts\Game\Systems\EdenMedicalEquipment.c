//! Eden Medical Equipment System - Handles medical gear and loadouts
//! Converted from original medical equipment and loadout systems

class EdenMedicalEquipment
{
    protected ref map<string, ref EdenMedicalLoadout> m_MedicLoadouts;
    protected ref array<string> m_MedicalItems;
    protected ref array<string> m_MedicalVehicles;
    protected ref map<string, bool> m_VehicleLights; // vehicle -> lights state
    
    void EdenMedicalEquipment()
    {
        m_MedicLoadouts = new map<string, ref EdenMedicalLoadout>();
        m_MedicalItems = new array<string>();
        m_MedicalVehicles = new array<string>();
        m_VehicleLights = new map<string, bool>();
        
        InitializeMedicalItems();
        InitializeMedicalVehicles();
    }
    
    //! Initialize medical equipment system
    void Initialize()
    {
        Print("[EdenMedicalEquipment] Initializing medical equipment system...");
        
        SetupMedicLoadouts();
        
        Print("[EdenMedicalEquipment] Medical equipment system initialized");
    }
    
    //! Initialize medical items list
    protected void InitializeMedicalItems()
    {
        // Medical supplies
        m_MedicalItems.Insert("defibrillator");
        m_MedicalItems.Insert("medikit");
        m_MedicalItems.Insert("firstaidkit");
        m_MedicalItems.Insert("bloodbag");
        m_MedicalItems.Insert("epinephrine");
        m_MedicalItems.Insert("morphine");
        m_MedicalItems.Insert("bandage");
        m_MedicalItems.Insert("splint");
        m_MedicalItems.Insert("painkillers");
        m_MedicalItems.Insert("antibiotics");
        m_MedicalItems.Insert("saline");
        m_MedicalItems.Insert("oxygen_tank");
        m_MedicalItems.Insert("stretcher");
        m_MedicalItems.Insert("medical_bag");
        m_MedicalItems.Insert("surgical_kit");
        m_MedicalItems.Insert("lollypop"); // Special item for roleplay
        
        // Medical tools
        m_MedicalItems.Insert("stethoscope");
        m_MedicalItems.Insert("thermometer");
        m_MedicalItems.Insert("blood_pressure_cuff");
        m_MedicalItems.Insert("flashlight_medical");
        m_MedicalItems.Insert("scissors_medical");
        m_MedicalItems.Insert("tweezers");
        
        Print(string.Format("[EdenMedicalEquipment] Initialized %1 medical items", m_MedicalItems.Count()));
    }
    
    //! Initialize medical vehicles list
    protected void InitializeMedicalVehicles()
    {
        // Ambulances
        m_MedicalVehicles.Insert("ambulance_01");
        m_MedicalVehicles.Insert("ambulance_02");
        m_MedicalVehicles.Insert("ambulance_heli");
        
        // Medical support vehicles
        m_MedicalVehicles.Insert("medical_suv");
        m_MedicalVehicles.Insert("medical_truck");
        m_MedicalVehicles.Insert("medical_boat");
        
        // Air medical
        m_MedicalVehicles.Insert("medical_helicopter");
        m_MedicalVehicles.Insert("air_ambulance");
        
        Print(string.Format("[EdenMedicalEquipment] Initialized %1 medical vehicles", m_MedicalVehicles.Count()));
    }
    
    //! Set up medic loadouts by rank
    protected void SetupMedicLoadouts()
    {
        // EMT Basic loadout
        EdenMedicalLoadout emtBasic = new EdenMedicalLoadout();
        emtBasic.SetRankName("EMT Basic");
        emtBasic.SetRankLevel(1);
        emtBasic.AddItem("firstaidkit", 3);
        emtBasic.AddItem("bandage", 10);
        emtBasic.AddItem("painkillers", 5);
        emtBasic.AddItem("lollypop", 20);
        emtBasic.AddItem("flashlight_medical", 1);
        emtBasic.SetUniform("medic_uniform_basic");
        emtBasic.SetVest("medic_vest_basic");
        emtBasic.SetHelmet("medic_cap");
        m_MedicLoadouts.Set("emt_basic", emtBasic);
        
        // EMT Intermediate loadout
        EdenMedicalLoadout emtIntermediate = new EdenMedicalLoadout();
        emtIntermediate.SetRankName("EMT Intermediate");
        emtIntermediate.SetRankLevel(2);
        emtIntermediate.AddItem("medikit", 2);
        emtIntermediate.AddItem("firstaidkit", 5);
        emtIntermediate.AddItem("bandage", 15);
        emtIntermediate.AddItem("splint", 3);
        emtIntermediate.AddItem("painkillers", 8);
        emtIntermediate.AddItem("saline", 3);
        emtIntermediate.AddItem("lollypop", 25);
        emtIntermediate.AddItem("stethoscope", 1);
        emtIntermediate.AddItem("thermometer", 1);
        emtIntermediate.SetUniform("medic_uniform_intermediate");
        emtIntermediate.SetVest("medic_vest_intermediate");
        emtIntermediate.SetHelmet("medic_cap");
        m_MedicLoadouts.Set("emt_intermediate", emtIntermediate);
        
        // Paramedic loadout
        EdenMedicalLoadout paramedic = new EdenMedicalLoadout();
        paramedic.SetRankName("Paramedic");
        paramedic.SetRankLevel(3);
        paramedic.AddItem("defibrillator", 1);
        paramedic.AddItem("medikit", 3);
        paramedic.AddItem("firstaidkit", 8);
        paramedic.AddItem("bloodbag", 5);
        paramedic.AddItem("epinephrine", 10);
        paramedic.AddItem("morphine", 8);
        paramedic.AddItem("bandage", 20);
        paramedic.AddItem("splint", 5);
        paramedic.AddItem("antibiotics", 5);
        paramedic.AddItem("saline", 8);
        paramedic.AddItem("oxygen_tank", 2);
        paramedic.AddItem("lollypop", 30);
        paramedic.AddItem("medical_bag", 1);
        paramedic.AddItem("stethoscope", 1);
        paramedic.AddItem("blood_pressure_cuff", 1);
        paramedic.SetUniform("medic_uniform_paramedic");
        paramedic.SetVest("medic_vest_paramedic");
        paramedic.SetHelmet("medic_helmet");
        m_MedicLoadouts.Set("paramedic", paramedic);
        
        // Senior Paramedic loadout
        EdenMedicalLoadout seniorParamedic = new EdenMedicalLoadout();
        seniorParamedic.SetRankName("Senior Paramedic");
        seniorParamedic.SetRankLevel(4);
        seniorParamedic.AddItem("defibrillator", 2);
        seniorParamedic.AddItem("medikit", 5);
        seniorParamedic.AddItem("firstaidkit", 10);
        seniorParamedic.AddItem("bloodbag", 8);
        seniorParamedic.AddItem("epinephrine", 15);
        seniorParamedic.AddItem("morphine", 12);
        seniorParamedic.AddItem("bandage", 25);
        seniorParamedic.AddItem("splint", 8);
        seniorParamedic.AddItem("antibiotics", 8);
        seniorParamedic.AddItem("saline", 12);
        seniorParamedic.AddItem("oxygen_tank", 3);
        seniorParamedic.AddItem("stretcher", 1);
        seniorParamedic.AddItem("lollypop", 40);
        seniorParamedic.AddItem("medical_bag", 1);
        seniorParamedic.AddItem("surgical_kit", 1);
        seniorParamedic.SetUniform("medic_uniform_senior");
        seniorParamedic.SetVest("medic_vest_senior");
        seniorParamedic.SetHelmet("medic_helmet_senior");
        m_MedicLoadouts.Set("senior_paramedic", seniorParamedic);
        
        // Chief of Medicine loadout
        EdenMedicalLoadout chief = new EdenMedicalLoadout();
        chief.SetRankName("Chief of Medicine");
        chief.SetRankLevel(5);
        chief.AddItem("defibrillator", 3);
        chief.AddItem("medikit", 8);
        chief.AddItem("firstaidkit", 15);
        chief.AddItem("bloodbag", 12);
        chief.AddItem("epinephrine", 20);
        chief.AddItem("morphine", 15);
        chief.AddItem("bandage", 30);
        chief.AddItem("splint", 10);
        chief.AddItem("antibiotics", 12);
        chief.AddItem("saline", 15);
        chief.AddItem("oxygen_tank", 5);
        chief.AddItem("stretcher", 2);
        chief.AddItem("lollypop", 50);
        chief.AddItem("medical_bag", 2);
        chief.AddItem("surgical_kit", 2);
        chief.SetUniform("medic_uniform_chief");
        chief.SetVest("medic_vest_chief");
        chief.SetHelmet("medic_beret");
        m_MedicLoadouts.Set("chief", chief);
        
        Print(string.Format("[EdenMedicalEquipment] Set up %1 medic loadouts", m_MedicLoadouts.Count()));
    }
    
    //! Give medic loadout to player
    bool GiveMedicLoadout(string playerId, int medicRank)
    {
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        // Determine loadout based on rank
        string loadoutKey = GetLoadoutKeyByRank(medicRank);
        if (!m_MedicLoadouts.Contains(loadoutKey))
            return false;
            
        EdenMedicalLoadout loadout = m_MedicLoadouts.Get(loadoutKey);
        
        // Clear existing inventory
        ClearPlayerInventory(playerEntity);
        
        // Give uniform and gear
        GivePlayerUniform(playerEntity, loadout.GetUniform());
        GivePlayerVest(playerEntity, loadout.GetVest());
        GivePlayerHelmet(playerEntity, loadout.GetHelmet());
        
        // Give medical items
        map<string, int> items = loadout.GetItems();
        foreach (string itemName, int quantity : items)
        {
            GivePlayerItem(playerEntity, itemName, quantity);
        }
        
        Print(string.Format("[EdenMedicalEquipment] Gave %1 loadout to %2", loadout.GetRankName(), playerId));
        return true;
    }
    
    //! Check if player has required medical gear
    bool CheckMedicalGear(string playerId, string requiredItem)
    {
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        // Check if player has the required medical item
        return PlayerHasItem(playerEntity, requiredItem);
    }
    
    //! Check if vehicle is medical vehicle
    bool IsMedicalVehicle(IEntity vehicle)
    {
        if (!vehicle)
            return false;
            
        string vehicleType = vehicle.GetPrefabData().GetPrefabName();
        return m_MedicalVehicles.Find(vehicleType) != -1;
    }
    
    //! Toggle medical vehicle lights
    bool ToggleMedicalLights(IEntity vehicle, string playerId)
    {
        if (!IsMedicalVehicle(vehicle))
            return false;
            
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        // Check if player is medic
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp || playerComp.GetCurrentFaction() != "medic")
            return false;
            
        string vehicleId = GetVehicleId(vehicle);
        bool currentState = false;
        
        if (m_VehicleLights.Contains(vehicleId))
        {
            currentState = m_VehicleLights.Get(vehicleId);
        }
        
        // Toggle lights
        bool newState = !currentState;
        m_VehicleLights.Set(vehicleId, newState);
        
        // Apply visual effects
        SetVehicleLights(vehicle, newState);
        
        Print(string.Format("[EdenMedicalEquipment] Medical lights %1 for vehicle %2", 
            newState ? "enabled" : "disabled", vehicleId));
        return true;
    }
    
    //! Give medical item to player
    bool GiveMedicalItem(string playerId, string itemName, int quantity = 1)
    {
        if (m_MedicalItems.Find(itemName) == -1)
            return false;
            
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        return GivePlayerItem(playerEntity, itemName, quantity);
    }
    
    //! Use medical item
    bool UseMedicalItem(string playerId, string itemName, string targetId = "")
    {
        if (m_MedicalItems.Find(itemName) == -1)
            return false;
            
        IEntity playerEntity = GetPlayerEntity(playerId);
        if (!playerEntity)
            return false;
            
        // Check if player has the item
        if (!PlayerHasItem(playerEntity, itemName))
            return false;
            
        // Use the item
        bool success = false;
        
        switch (itemName)
        {
            case "defibrillator":
                success = UseDefibrillator(playerId, targetId);
                break;
            case "epinephrine":
                success = UseEpinephrine(playerId, targetId);
                break;
            case "morphine":
                success = UseMorphine(playerId, targetId);
                break;
            case "lollypop":
                success = GiveLollipop(playerId, targetId);
                break;
            default:
                success = UseGenericMedicalItem(playerId, itemName, targetId);
                break;
        }
        
        if (success)
        {
            // Remove item from inventory
            RemovePlayerItem(playerEntity, itemName, 1);
        }
        
        return success;
    }
    
    //! Get loadout key by medic rank
    protected string GetLoadoutKeyByRank(int rank)
    {
        switch (rank)
        {
            case 1: return "emt_basic";
            case 2: return "emt_intermediate";
            case 3: return "paramedic";
            case 4: return "senior_paramedic";
            case 5: return "chief";
            default: return "emt_basic";
        }
    }
    
    //! Use defibrillator
    protected bool UseDefibrillator(string medicId, string targetId)
    {
        if (targetId == "")
            return false;
            
        // Implementation for defibrillator use
        Print(string.Format("[EdenMedicalEquipment] %1 used defibrillator on %2", medicId, targetId));
        return true;
    }
    
    //! Use epinephrine
    protected bool UseEpinephrine(string medicId, string targetId)
    {
        if (targetId == "")
            return false;
            
        // Implementation for epinephrine use
        Print(string.Format("[EdenMedicalEquipment] %1 used epinephrine on %2", medicId, targetId));
        return true;
    }
    
    //! Use morphine
    protected bool UseMorphine(string medicId, string targetId)
    {
        if (targetId == "")
            return false;
            
        // Implementation for morphine use
        Print(string.Format("[EdenMedicalEquipment] %1 used morphine on %2", medicId, targetId));
        return true;
    }
    
    //! Give lollipop
    protected bool GiveLollipop(string medicId, string targetId)
    {
        if (targetId == "")
            return false;
            
        // Implementation for lollipop giving (roleplay item)
        Print(string.Format("[EdenMedicalEquipment] %1 gave lollipop to %2", medicId, targetId));
        return true;
    }
    
    //! Use generic medical item
    protected bool UseGenericMedicalItem(string medicId, string itemName, string targetId)
    {
        // Implementation for generic medical item use
        Print(string.Format("[EdenMedicalEquipment] %1 used %2", medicId, itemName));
        return true;
    }
    
    //! Helper methods
    protected IEntity GetPlayerEntity(string playerId)
    {
        // Implementation to get player entity by ID
        return null; // Placeholder
    }
    
    protected string GetVehicleId(IEntity vehicle)
    {
        // Implementation to get unique vehicle ID
        return ""; // Placeholder
    }
    
    protected void ClearPlayerInventory(IEntity playerEntity)
    {
        // Implementation to clear player inventory
    }
    
    protected void GivePlayerUniform(IEntity playerEntity, string uniform)
    {
        // Implementation to give player uniform
    }
    
    protected void GivePlayerVest(IEntity playerEntity, string vest)
    {
        // Implementation to give player vest
    }
    
    protected void GivePlayerHelmet(IEntity playerEntity, string helmet)
    {
        // Implementation to give player helmet
    }
    
    protected bool GivePlayerItem(IEntity playerEntity, string itemName, int quantity)
    {
        // Implementation to give player item
        return true; // Placeholder
    }
    
    protected bool PlayerHasItem(IEntity playerEntity, string itemName)
    {
        // Implementation to check if player has item
        return true; // Placeholder
    }
    
    protected bool RemovePlayerItem(IEntity playerEntity, string itemName, int quantity)
    {
        // Implementation to remove item from player
        return true; // Placeholder
    }
    
    protected void SetVehicleLights(IEntity vehicle, bool enabled)
    {
        // Implementation to set vehicle lights
    }
    
    //! Public query methods
    array<string> GetMedicalItems() { return m_MedicalItems; }
    array<string> GetMedicalVehicles() { return m_MedicalVehicles; }
    
    EdenMedicalLoadout GetLoadout(string loadoutKey)
    {
        if (m_MedicLoadouts.Contains(loadoutKey))
            return m_MedicLoadouts.Get(loadoutKey);
        return null;
    }
    
    bool IsVehicleLightsEnabled(IEntity vehicle)
    {
        string vehicleId = GetVehicleId(vehicle);
        if (m_VehicleLights.Contains(vehicleId))
            return m_VehicleLights.Get(vehicleId);
        return false;
    }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenMedicalEquipment] Cleaning up medical equipment system...");
        
        // Turn off all vehicle lights
        foreach (string vehicleId, bool lightsState : m_VehicleLights)
        {
            m_VehicleLights.Set(vehicleId, false);
        }
        
        m_MedicLoadouts.Clear();
        m_VehicleLights.Clear();
    }
}
