//! Eden Player Component - Handles individual player data and functionality
//! Attached to each player entity to manage their Eden-specific data

[ComponentEditorProps(category: "Eden", description: "Eden player component")]
class EdenPlayerComponentClass : ScriptComponentClass
{
}

class EdenPlayerComponent : ScriptComponent
{
    protected ref EdenPlayerData m_PlayerData;
    protected string m_PlayerId;
    protected string m_CurrentFaction;
    protected bool m_IsInitialized = false;
    protected bool m_IsDataLoaded = false;
    
    // Cached references
    protected IEntity m_PlayerEntity;
    protected SCR_CharacterControllerComponent m_CharacterController;
    protected SCR_InventoryStorageManagerComponent m_InventoryManager;
    
    // Network synchronization
    [RplProp()]
    protected int m_iCash;
    
    [RplProp()]
    protected int m_iBankAccount;
    
    [RplProp()]
    protected int m_iCopLevel;
    
    [RplProp()]
    protected int m_iMedicLevel;
    
    [RplProp()]
    protected int m_iAdminLevel;
    
    [RplProp()]
    protected string m_sCurrentTitle;
    
    [RplProp()]
    protected string m_sCurrentFaction;
    
    //! Initialize component
    override void OnPostInit(IEntity owner)
    {
        super.OnPostInit(owner);
        
        m_PlayerEntity = owner;
        
        // Get required components
        m_CharacterController = SCR_CharacterControllerComponent.Cast(owner.FindComponent(SCR_CharacterControllerComponent));
        m_InventoryManager = SCR_InventoryStorageManagerComponent.Cast(owner.FindComponent(SCR_InventoryStorageManagerComponent));
        
        // Initialize on server only
        if (Replication.IsServer())
        {
            GetGame().GetCallqueue().CallLater(InitializePlayer, 1000, false);
        }
    }
    
    //! Initialize player data
    void InitializePlayer()
    {
        if (m_IsInitialized)
            return;
            
        // Get player ID
        int playerId = GetGame().GetPlayerManager().GetPlayerIdFromControlledEntity(m_PlayerEntity);
        if (playerId == 0)
        {
            GetGame().GetCallqueue().CallLater(InitializePlayer, 1000, false);
            return;
        }
        
        m_PlayerId = GetGame().GetPlayerManager().GetPlayerIdentityId(playerId);
        if (m_PlayerId == "")
        {
            GetGame().GetCallqueue().CallLater(InitializePlayer, 1000, false);
            return;
        }
        
        Print(string.Format("[EdenPlayerComponent] Initializing player %1", m_PlayerId));
        
        // Load player data
        LoadPlayerData();
        
        // Set default faction
        m_CurrentFaction = "civ";
        m_sCurrentFaction = m_CurrentFaction;
        
        // Sync initial data to client
        SyncDataToClient();
        
        m_IsInitialized = true;
        
        Print(string.Format("[EdenPlayerComponent] Player %1 initialized successfully", m_PlayerId));
    }
    
    //! Load player data from storage
    protected void LoadPlayerData()
    {
        EdenDataManager dataManager = EdenDataManager.GetInstance();
        if (!dataManager)
        {
            Print("[EdenPlayerComponent] ERROR: Data manager not available");
            return;
        }
        
        m_PlayerData = dataManager.GetPlayerData(m_PlayerId);
        if (!m_PlayerData)
        {
            Print(string.Format("[EdenPlayerComponent] ERROR: Could not load data for player %1", m_PlayerId));
            return;
        }
        
        // Update player name
        int playerId = GetGame().GetPlayerManager().GetPlayerIdFromControlledEntity(m_PlayerEntity);
        string playerName = GetGame().GetPlayerManager().GetPlayerName(playerId);
        m_PlayerData.SetPlayerName(playerName);
        
        // Cache frequently accessed data
        m_iCash = m_PlayerData.GetCash();
        m_iBankAccount = m_PlayerData.GetBankAccount();
        m_iCopLevel = m_PlayerData.GetCopLevel();
        m_iMedicLevel = m_PlayerData.GetMedicLevel();
        m_iAdminLevel = m_PlayerData.GetAdminLevel();
        m_sCurrentTitle = m_PlayerData.GetCurrentTitle();
        
        // Set last side as current faction
        m_CurrentFaction = m_PlayerData.GetLastSide();
        m_sCurrentFaction = m_CurrentFaction;
        
        m_IsDataLoaded = true;
        
        Print(string.Format("[EdenPlayerComponent] Loaded data for player %1 - Cash: $%2, Bank: $%3", 
            m_PlayerId, m_iCash, m_iBankAccount));
    }
    
    //! Save player data to storage
    void SavePlayerData()
    {
        if (!m_IsDataLoaded || !m_PlayerData)
            return;
            
        // Update data from cached values
        m_PlayerData.SetCash(m_iCash);
        m_PlayerData.SetBankAccount(m_iBankAccount);
        m_PlayerData.SetLastSide(m_CurrentFaction);
        m_PlayerData.UpdateLastActive();
        
        // Save position
        if (m_PlayerEntity)
        {
            vector pos = m_PlayerEntity.GetOrigin();
            vector dir = m_PlayerEntity.GetAngles();
            m_PlayerData.SetPosition(pos, dir);
        }
        
        // Save to storage
        EdenDataManager dataManager = EdenDataManager.GetInstance();
        if (dataManager)
        {
            dataManager.SavePlayerData(m_PlayerId);
        }
        
        Print(string.Format("[EdenPlayerComponent] Saved data for player %1", m_PlayerId));
    }
    
    //! Sync data to client
    protected void SyncDataToClient()
    {
        if (!Replication.IsServer())
            return;
            
        Replication.BumpMe();
    }
    
    //! Get player ID
    string GetPlayerId() { return m_PlayerId; }
    
    //! Get player data
    EdenPlayerData GetPlayerData() { return m_PlayerData; }
    
    //! Get current faction
    string GetCurrentFaction() { return m_CurrentFaction; }
    
    //! Set current faction
    void SetCurrentFaction(string faction)
    {
        if (m_CurrentFaction == faction)
            return;
            
        m_CurrentFaction = faction;
        m_sCurrentFaction = faction;
        
        if (m_PlayerData)
            m_PlayerData.SetLastSide(faction);
            
        SyncDataToClient();
        
        Print(string.Format("[EdenPlayerComponent] Player %1 switched to faction: %2", m_PlayerId, faction));
    }
    
    //! Money management
    int GetCash() { return m_iCash; }
    int GetBankAccount() { return m_iBankAccount; }
    
    void SetCash(int amount)
    {
        m_iCash = amount;
        if (m_PlayerData)
            m_PlayerData.SetCash(amount);
        SyncDataToClient();
    }
    
    void AddCash(int amount)
    {
        m_iCash += amount;
        if (m_PlayerData)
            m_PlayerData.SetCash(m_iCash);
        SyncDataToClient();
    }
    
    bool RemoveCash(int amount)
    {
        if (m_iCash >= amount)
        {
            m_iCash -= amount;
            if (m_PlayerData)
                m_PlayerData.SetCash(m_iCash);
            SyncDataToClient();
            return true;
        }
        return false;
    }
    
    void SetBankAccount(int amount)
    {
        m_iBankAccount = amount;
        if (m_PlayerData)
            m_PlayerData.SetBankAccount(amount);
        SyncDataToClient();
    }
    
    void AddBankMoney(int amount)
    {
        m_iBankAccount += amount;
        if (m_PlayerData)
            m_PlayerData.SetBankAccount(m_iBankAccount);
        SyncDataToClient();
    }
    
    bool RemoveBankMoney(int amount)
    {
        if (m_iBankAccount >= amount)
        {
            m_iBankAccount -= amount;
            if (m_PlayerData)
                m_PlayerData.SetBankAccount(m_iBankAccount);
            SyncDataToClient();
            return true;
        }
        return false;
    }
    
    //! Level management
    int GetCopLevel() { return m_iCopLevel; }
    int GetMedicLevel() { return m_iMedicLevel; }
    int GetAdminLevel() { return m_iAdminLevel; }
    int GetDonatorLevel() 
    { 
        if (m_PlayerData) 
            return m_PlayerData.GetDonatorLevel(); 
        return 0; 
    }
    
    void SetCopLevel(int level)
    {
        m_iCopLevel = level;
        if (m_PlayerData)
            m_PlayerData.SetCopLevel(level);
        SyncDataToClient();
    }
    
    void SetMedicLevel(int level)
    {
        m_iMedicLevel = level;
        if (m_PlayerData)
            m_PlayerData.SetMedicLevel(level);
        SyncDataToClient();
    }
    
    void SetAdminLevel(int level)
    {
        m_iAdminLevel = level;
        if (m_PlayerData)
            m_PlayerData.SetAdminLevel(level);
        SyncDataToClient();
    }
    
    //! License management
    bool HasCopLicense(string license)
    {
        if (m_PlayerData)
            return m_PlayerData.HasCopLicense(license);
        return false;
    }
    
    bool HasCivLicense(string license)
    {
        if (m_PlayerData)
            return m_PlayerData.HasCivLicense(license);
        return false;
    }
    
    bool HasMedLicense(string license)
    {
        if (m_PlayerData)
            return m_PlayerData.HasMedLicense(license);
        return false;
    }
    
    void AddCopLicense(string license)
    {
        if (m_PlayerData)
            m_PlayerData.AddCopLicense(license);
    }
    
    void AddCivLicense(string license)
    {
        if (m_PlayerData)
            m_PlayerData.AddCivLicense(license);
    }
    
    void AddMedLicense(string license)
    {
        if (m_PlayerData)
            m_PlayerData.AddMedLicense(license);
    }
    
    //! Statistics
    int GetStat(int index)
    {
        if (m_PlayerData)
            return m_PlayerData.GetStat(index);
        return 0;
    }
    
    void AddStat(int index, int value)
    {
        if (m_PlayerData)
            m_PlayerData.AddStat(index, value);
    }
    
    //! Title management
    string GetCurrentTitle() { return m_sCurrentTitle; }
    
    void SetCurrentTitle(string title)
    {
        m_sCurrentTitle = title;
        if (m_PlayerData)
            m_PlayerData.SetCurrentTitle(title);
        SyncDataToClient();
    }
    
    //! Check if player is initialized
    bool IsInitialized() { return m_IsInitialized && m_IsDataLoaded; }
    
    //! Cleanup
    override void OnDelete(IEntity owner)
    {
        if (Replication.IsServer())
        {
            SavePlayerData();
        }
        
        super.OnDelete(owner);
    }
}
