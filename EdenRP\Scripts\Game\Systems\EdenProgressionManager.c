//! Progression Manager for Eden Reforger
//! Converted from original Eden Altis Life progression systems
//! Manages XP system, titles, ranks, statistics, and player advancement

[ComponentEditorProps(category: "Eden Systems", description: "Manages player progression and statistics")]
class EdenProgressionManagerComponent : ScriptComponent
{
    [Attribute("100", UIWidgets.EditBox, "Base XP required for level 1")]
    protected int m_iBaseXPRequirement;
    
    [Attribute("1.5", UIWidgets.EditBox, "XP multiplier per level")]
    protected float m_fXPMultiplier;
    
    [Attribute("60", UIWidgets.EditBox, "Statistics update interval in seconds")]
    protected int m_iStatsUpdateInterval;
    
    [RplProp()]
    protected ref map<string, ref EdenPlayerStats> m_mPlayerStats;
    
    [RplProp()]
    protected ref map<string, ref array<string>> m_mPlayerTitles;
    
    [RplProp()]
    protected ref array<ref EdenTitleDefinition> m_aTitleDefinitions;
    
    protected ref map<string, int> m_mPlayerLastUpdate;
    protected ref array<ref EdenRankDefinition> m_aRankDefinitions;
    
    //! Constructor
    void EdenProgressionManagerComponent(IEntityComponentSource src, IEntity ent, IEntity parent)
    {
        m_mPlayerStats = new map<string, ref EdenPlayerStats>();
        m_mPlayerTitles = new map<string, ref array<string>>();
        m_aTitleDefinitions = new array<ref EdenTitleDefinition>();
        m_mPlayerLastUpdate = new map<string, int>();
        m_aRankDefinitions = new array<ref EdenRankDefinition>();
        
        InitializeTitleDefinitions();
        InitializeRankDefinitions();
        
        // Start statistics update timer
        GetGame().GetCallqueue().CallLater(UpdatePlayerStatistics, m_iStatsUpdateInterval * 1000, true);
    }
    
    //! Initialize title definitions
    protected void InitializeTitleDefinitions()
    {
        // Civilian titles
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Novice", "civilian", "kills", 1, "First blood"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Thug", "civilian", "kills", 5, "5 kills"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Gangster", "civilian", "kills", 15, "15 kills"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Hitman", "civilian", "kills", 50, "50 kills"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Assassin", "civilian", "kills", 100, "100 kills"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Death Dealer", "civilian", "kills", 250, "250 kills"));
        
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Locksmith", "civilian", "lockpicks_success", 25, "25 successful lockpicks"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Master Thief", "civilian", "lockpicks_success", 100, "100 successful lockpicks"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Robber", "civilian", "players_robbed", 10, "10 players robbed"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Bandit", "civilian", "players_robbed", 50, "50 players robbed"));
        
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Drug Runner", "civilian", "drugs_sold", 100, "100 drugs sold"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Cartel Boss", "civilian", "drugs_sold", 1000, "1000 drugs sold"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Kingpin", "civilian", "drugs_sold", 5000, "5000 drugs sold"));
        
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Miner", "civilian", "resources_gathered", 500, "500 resources gathered"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Industrialist", "civilian", "resources_gathered", 2500, "2500 resources gathered"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Tycoon", "civilian", "money_earned", 10000000, "$10M earned"));
        
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("World Traveller", "civilian", "distance_foot", 1000000, "1000km on foot"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Road Warrior", "civilian", "distance_vehicle", 5000000, "5000km in vehicles"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Blooded", "civilian", "deaths", 25, "25 deaths"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Survivor", "civilian", "playtime", 72, "72 hours played"));
        
        // Police titles
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Rookie Cop", "police", "arrests_made", 1, "First arrest"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Officer", "police", "arrests_made", 25, "25 arrests"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Detective", "police", "arrests_made", 100, "100 arrests"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Lieutenant", "police", "arrests_made", 250, "250 arrests"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Captain", "police", "arrests_made", 500, "500 arrests"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Chief", "police", "arrests_made", 1000, "1000 arrests"));
        
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Ticket Master", "police", "tickets_issued", 100, "100 tickets issued"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Revenue Generator", "police", "tickets_issued", 500, "500 tickets issued"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Bomb Squad", "police", "bombs_defused", 10, "10 bombs defused"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("EOD Expert", "police", "bombs_defused", 50, "50 bombs defused"));
        
        // Medical titles
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("First Responder", "medical", "revives", 1, "First revive"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Paramedic", "medical", "revives", 25, "25 revives"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("EMT", "medical", "revives", 100, "100 revives"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Doctor", "medical", "revives", 250, "250 revives"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Surgeon", "medical", "revives", 500, "500 revives"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Life Saver", "medical", "revives", 1000, "1000 revives"));
        
        // Special titles
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Donator", "special", "donator_level", 1, "Server supporter"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("VIP", "special", "donator_level", 2, "VIP member"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Legend", "special", "donator_level", 3, "Legendary supporter"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Staff", "special", "admin_level", 1, "Server staff"));
        m_aTitleDefinitions.Insert(new EdenTitleDefinition("Administrator", "special", "admin_level", 3, "Server administrator"));
    }
    
    //! Initialize rank definitions
    protected void InitializeRankDefinitions()
    {
        // Civilian ranks
        m_aRankDefinitions.Insert(new EdenRankDefinition("civilian", 1, "Citizen", 0));
        m_aRankDefinitions.Insert(new EdenRankDefinition("civilian", 2, "Resident", 500));
        m_aRankDefinitions.Insert(new EdenRankDefinition("civilian", 3, "Local", 1500));
        m_aRankDefinitions.Insert(new EdenRankDefinition("civilian", 4, "Veteran", 3500));
        m_aRankDefinitions.Insert(new EdenRankDefinition("civilian", 5, "Expert", 7500));
        
        // Police ranks
        m_aRankDefinitions.Insert(new EdenRankDefinition("police", 1, "Cadet", 0));
        m_aRankDefinitions.Insert(new EdenRankDefinition("police", 2, "Officer", 1000));
        m_aRankDefinitions.Insert(new EdenRankDefinition("police", 3, "Senior Officer", 2500));
        m_aRankDefinitions.Insert(new EdenRankDefinition("police", 4, "Corporal", 5000));
        m_aRankDefinitions.Insert(new EdenRankDefinition("police", 5, "Sergeant", 10000));
        m_aRankDefinitions.Insert(new EdenRankDefinition("police", 6, "Lieutenant", 20000));
        m_aRankDefinitions.Insert(new EdenRankDefinition("police", 7, "Captain", 35000));
        m_aRankDefinitions.Insert(new EdenRankDefinition("police", 8, "Deputy Chief", 50000));
        m_aRankDefinitions.Insert(new EdenRankDefinition("police", 9, "Chief", 75000));
        
        // Medical ranks
        m_aRankDefinitions.Insert(new EdenRankDefinition("medical", 1, "Trainee", 0));
        m_aRankDefinitions.Insert(new EdenRankDefinition("medical", 2, "EMT", 750));
        m_aRankDefinitions.Insert(new EdenRankDefinition("medical", 3, "Paramedic", 2000));
        m_aRankDefinitions.Insert(new EdenRankDefinition("medical", 4, "Senior Paramedic", 4500));
        m_aRankDefinitions.Insert(new EdenRankDefinition("medical", 5, "Doctor", 8500));
        m_aRankDefinitions.Insert(new EdenRankDefinition("medical", 6, "Senior Doctor", 15000));
        m_aRankDefinitions.Insert(new EdenRankDefinition("medical", 7, "Chief Medical Officer", 25000));
    }
    
    //! Get or create player statistics
    EdenPlayerStats GetPlayerStats(string playerId)
    {
        if (!m_mPlayerStats.Contains(playerId))
        {
            ref EdenPlayerStats newStats = new EdenPlayerStats();
            newStats.SetPlayerId(playerId);
            m_mPlayerStats.Set(playerId, newStats);
        }
        
        return m_mPlayerStats.Get(playerId);
    }
    
    //! Add experience to player
    bool AddExperience(string playerId, string experienceType, int amount)
    {
        EdenPlayerStats playerStats = GetPlayerStats(playerId);
        if (!playerStats)
            return false;
            
        bool leveledUp = false;
        int oldLevel = 0;
        int newLevel = 0;
        
        switch (experienceType.ToLower())
        {
            case "civilian":
                {
                    oldLevel = CalculateLevel(playerStats.GetCivExperience());
                    playerStats.AddCivExperience(amount);
                    newLevel = CalculateLevel(playerStats.GetCivExperience());
                    break;
                }
            case "police":
                {
                    oldLevel = CalculateLevel(playerStats.GetCopExperience());
                    playerStats.AddCopExperience(amount);
                    newLevel = CalculateLevel(playerStats.GetCopExperience());
                    break;
                }
            case "medical":
                {
                    oldLevel = CalculateLevel(playerStats.GetMedicExperience());
                    playerStats.AddMedicExperience(amount);
                    newLevel = CalculateLevel(playerStats.GetMedicExperience());
                    break;
                }
            default:
                return false;
        }
        
        leveledUp = (newLevel > oldLevel);
        
        if (leveledUp)
        {
            HandleLevelUp(playerId, experienceType, oldLevel, newLevel);
        }
        
        // Check for new titles
        CheckForNewTitles(playerId);
        
        Print(string.Format("EdenProgressionManager: Added %1 %2 XP to %3 (Level: %4)", 
            amount, experienceType, playerId, newLevel));
        
        return true;
    }
    
    //! Update statistic
    void UpdateStatistic(string playerId, string statName, int value, bool increment = true)
    {
        EdenPlayerStats playerStats = GetPlayerStats(playerId);
        if (!playerStats)
            return;
            
        if (increment)
            playerStats.IncrementStatistic(statName, value);
        else
            playerStats.SetStatistic(statName, value);
            
        // Check for new titles after stat update
        CheckForNewTitles(playerId);
        
        Print(string.Format("EdenProgressionManager: Updated %1 stat %2 by %3", playerId, statName, value));
    }
    
    //! Calculate level from experience
    int CalculateLevel(int experience)
    {
        if (experience < m_iBaseXPRequirement)
            return 1;
            
        int level = 1;
        int totalXPNeeded = 0;
        
        while (totalXPNeeded <= experience)
        {
            totalXPNeeded += Math.Round(m_iBaseXPRequirement * Math.Pow(m_fXPMultiplier, level - 1));
            if (totalXPNeeded <= experience)
                level++;
        }
        
        return level;
    }
    
    //! Calculate experience needed for next level
    int CalculateXPForNextLevel(int currentLevel)
    {
        return Math.Round(m_iBaseXPRequirement * Math.Pow(m_fXPMultiplier, currentLevel));
    }
    
    //! Handle level up
    protected void HandleLevelUp(string playerId, string experienceType, int oldLevel, int newLevel)
    {
        // Notify player of level up
        NotifyPlayerLevelUp(playerId, experienceType, oldLevel, newLevel);
        
        // Give level up rewards
        GiveLevelUpRewards(playerId, experienceType, newLevel);
        
        Print(string.Format("EdenProgressionManager: Player %1 leveled up in %2 (%3 -> %4)", 
            playerId, experienceType, oldLevel, newLevel));
    }
    
    //! Check for new titles
    protected void CheckForNewTitles(string playerId)
    {
        EdenPlayerStats playerStats = GetPlayerStats(playerId);
        if (!playerStats)
            return;
            
        if (!m_mPlayerTitles.Contains(playerId))
            m_mPlayerTitles.Set(playerId, new array<string>());
            
        ref array<string> playerTitles = m_mPlayerTitles.Get(playerId);
        
        foreach (EdenTitleDefinition titleDef : m_aTitleDefinitions)
        {
            if (titleDef && playerTitles.Find(titleDef.GetTitleName()) == -1)
            {
                if (CheckTitleRequirement(playerStats, titleDef))
                {
                    playerTitles.Insert(titleDef.GetTitleName());
                    NotifyPlayerNewTitle(playerId, titleDef);
                    
                    Print(string.Format("EdenProgressionManager: Player %1 unlocked title: %2", 
                        playerId, titleDef.GetTitleName()));
                }
            }
        }
    }
    
    //! Check title requirement
    protected bool CheckTitleRequirement(EdenPlayerStats playerStats, EdenTitleDefinition titleDef)
    {
        string statName = titleDef.GetRequirementStat();
        int requiredValue = titleDef.GetRequiredValue();
        int currentValue = 0;
        
        switch (statName)
        {
            case "kills": currentValue = playerStats.GetKills(); break;
            case "deaths": currentValue = playerStats.GetDeaths(); break;
            case "arrests_made": currentValue = playerStats.GetArrestsMade(); break;
            case "revives": currentValue = playerStats.GetRevives(); break;
            case "lockpicks_success": currentValue = playerStats.GetLockpicksSuccess(); break;
            case "players_robbed": currentValue = playerStats.GetPlayersRobbed(); break;
            case "drugs_sold": currentValue = playerStats.GetDrugsSold(); break;
            case "resources_gathered": currentValue = playerStats.GetResourcesGathered(); break;
            case "money_earned": currentValue = playerStats.GetTotalMoneyEarned(); break;
            case "distance_foot": currentValue = playerStats.GetDistanceFoot(); break;
            case "distance_vehicle": currentValue = playerStats.GetDistanceVehicle(); break;
            case "playtime": currentValue = playerStats.GetPlaytime(); break;
            case "tickets_issued": currentValue = playerStats.GetTicketsIssued(); break;
            case "bombs_defused": currentValue = playerStats.GetBombsDefused(); break;
            case "donator_level": currentValue = GetPlayerDonatorLevel(playerStats.GetPlayerId()); break;
            case "admin_level": currentValue = GetPlayerAdminLevel(playerStats.GetPlayerId()); break;
            default: return false;
        }
        
        return currentValue >= requiredValue;
    }
    
    //! Update player statistics periodically
    protected void UpdatePlayerStatistics()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        // Update playtime for all active players
        foreach (string playerId, ref EdenPlayerStats playerStats : m_mPlayerStats)
        {
            if (playerStats && IsPlayerOnline(playerId))
            {
                int lastUpdate = 0;
                if (m_mPlayerLastUpdate.Contains(playerId))
                    lastUpdate = m_mPlayerLastUpdate.Get(playerId);
                    
                if (lastUpdate > 0)
                {
                    int timeDiff = currentTime - lastUpdate;
                    playerStats.AddPlaytime(timeDiff / 60); // Convert to minutes
                }
                
                m_mPlayerLastUpdate.Set(playerId, currentTime);
            }
        }
    }
    
    //! Get player donator level
    protected int GetPlayerDonatorLevel(string playerId)
    {
        // TODO: Implement actual donator level lookup
        return 0;
    }
    
    //! Get player admin level
    protected int GetPlayerAdminLevel(string playerId)
    {
        // TODO: Implement actual admin level lookup
        return 0;
    }
    
    //! Check if player is online
    protected bool IsPlayerOnline(string playerId)
    {
        // TODO: Implement actual online check
        return true;
    }
    
    //! Notify player of level up
    protected void NotifyPlayerLevelUp(string playerId, string experienceType, int oldLevel, int newLevel)
    {
        // TODO: Implement level up notification
        Print(string.Format("LEVEL UP: %1 reached %2 level %3!", playerId, experienceType, newLevel));
    }
    
    //! Give level up rewards
    protected void GiveLevelUpRewards(string playerId, string experienceType, int newLevel)
    {
        // TODO: Implement level up rewards
        Print(string.Format("Giving level up rewards to %1 for reaching %2 level %3", playerId, experienceType, newLevel));
    }
    
    //! Notify player of new title
    protected void NotifyPlayerNewTitle(string playerId, EdenTitleDefinition titleDef)
    {
        // TODO: Implement title notification
        Print(string.Format("NEW TITLE: %1 unlocked '%2'!", playerId, titleDef.GetTitleName()));
    }
    
    //! Get player titles
    array<string> GetPlayerTitles(string playerId)
    {
        if (m_mPlayerTitles.Contains(playerId))
            return m_mPlayerTitles.Get(playerId);
        return new array<string>();
    }
    
    //! Get rank name for level
    string GetRankName(string rankType, int level)
    {
        foreach (EdenRankDefinition rankDef : m_aRankDefinitions)
        {
            if (rankDef && rankDef.GetRankType() == rankType && rankDef.GetLevel() == level)
                return rankDef.GetRankName();
        }
        return "Unknown";
    }
    
    //! Getters
    map<string, ref EdenPlayerStats> GetAllPlayerStats() { return m_mPlayerStats; }
    array<ref EdenTitleDefinition> GetTitleDefinitions() { return m_aTitleDefinitions; }
    array<ref EdenRankDefinition> GetRankDefinitions() { return m_aRankDefinitions; }
}

//! Progression Manager class for easy access
class EdenProgressionManager
{
    protected static EdenProgressionManager s_Instance;
    protected EdenProgressionManagerComponent m_Component;
    
    //! Get singleton instance
    static EdenProgressionManager GetInstance()
    {
        if (!s_Instance)
            s_Instance = new EdenProgressionManager();
        return s_Instance;
    }
    
    //! Initialize with component
    void Initialize(EdenProgressionManagerComponent component)
    {
        m_Component = component;
    }
    
    //! Get component
    EdenProgressionManagerComponent GetComponent()
    {
        return m_Component;
    }
}
