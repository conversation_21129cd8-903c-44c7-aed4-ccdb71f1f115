//! Eden Gang Territory System - Handles territory capture mechanics and cartel control
//! Converted from original territory capture systems

class EdenGangTerritorySystem
{
    protected ref map<string, ref EdenTerritoryData> m_Territories;
    protected ref map<string, ref EdenTerritoryCapture> m_ActiveCaptures;
    protected ref map<string, IEntity> m_TerritoryFlags;
    protected ref map<string, vector> m_TerritorySpawns;
    
    // Territory configuration
    protected float m_CaptureRadius;
    protected float m_CaptureRate;
    protected float m_UncaptureRate;
    protected int m_MinPlayersForCapture;
    protected int m_CaptureTimeout;
    
    void EdenGangTerritorySystem()
    {
        m_Territories = new map<string, ref EdenTerritoryData>();
        m_ActiveCaptures = new map<string, ref EdenTerritoryCapture>();
        m_TerritoryFlags = new map<string, IEntity>();
        m_TerritorySpawns = new map<string, vector>();
        
        m_CaptureRadius = 150.0;
        m_CaptureRate = 0.001;
        m_UncaptureRate = -0.001;
        m_MinPlayersForCapture = 1;
        m_CaptureTimeout = 600; // 10 minutes
        
        InitializeTerritories();
    }
    
    //! Initialize territory system
    void Initialize()
    {
        Print("[EdenGangTerritorySystem] Initializing territory system...");
        
        SetupTerritoryFlags();
        SetupTerritorySpawns();
        LoadTerritoryData();
        
        // Start territory update loop
        GetGame().GetCallqueue().CallLater(UpdateTerritories, 1000, true); // Every second
        
        Print("[EdenGangTerritorySystem] Territory system initialized");
    }
    
    //! Initialize territory definitions
    protected void InitializeTerritories()
    {
        // Meth and Weed Cartel
        EdenTerritoryData methTerritory = new EdenTerritoryData();
        methTerritory.SetTerritoryName("Meth");
        methTerritory.SetPosition("0 0 0"); // Placeholder position
        methTerritory.SetRadius(m_CaptureRadius);
        m_Territories.Set("Meth", methTerritory);
        
        // Moonshine and Heroin Cartel
        EdenTerritoryData moonshineTerritory = new EdenTerritoryData();
        moonshineTerritory.SetTerritoryName("Moonshine");
        moonshineTerritory.SetPosition("1000 0 1000"); // Placeholder position
        moonshineTerritory.SetRadius(m_CaptureRadius);
        m_Territories.Set("Moonshine", moonshineTerritory);
        
        // Mushroom and Cocaine Cartel
        EdenTerritoryData mushroomTerritory = new EdenTerritoryData();
        mushroomTerritory.SetTerritoryName("Mushroom");
        mushroomTerritory.SetPosition("2000 0 2000"); // Placeholder position
        mushroomTerritory.SetRadius(m_CaptureRadius);
        m_Territories.Set("Mushroom", mushroomTerritory);
        
        // Arms Dealer Cartel
        EdenTerritoryData armsTerritory = new EdenTerritoryData();
        armsTerritory.SetTerritoryName("Arms");
        armsTerritory.SetPosition("3000 0 3000"); // Placeholder position
        armsTerritory.SetRadius(m_CaptureRadius);
        m_Territories.Set("Arms", armsTerritory);
        
        Print(string.Format("[EdenGangTerritorySystem] Initialized %1 territories", m_Territories.Count()));
    }
    
    //! Setup territory flags
    protected void SetupTerritoryFlags()
    {
        foreach (string territoryName, EdenTerritoryData territory : m_Territories)
        {
            // Create flag entity at territory position
            vector position = territory.GetPosition();
            IEntity flagEntity = CreateTerritoryFlag(position, territoryName);
            
            if (flagEntity)
            {
                m_TerritoryFlags.Set(territoryName, flagEntity);
                
                // Set flag variables
                flagEntity.SetVariableString("territory_name", territoryName);
                flagEntity.SetVariableInt("cartel_num", GetTerritoryId(territoryName));
                
                Print(string.Format("[EdenGangTerritorySystem] Created flag for territory %1", territoryName));
            }
        }
    }
    
    //! Setup territory vehicle spawns
    protected void SetupTerritorySpawns()
    {
        // Meth cartel spawns
        m_TerritorySpawns.Set("Meth_spawn1", "50 0 50");
        m_TerritorySpawns.Set("Meth_spawn2", "100 0 100");
        
        // Moonshine cartel spawns
        m_TerritorySpawns.Set("Moonshine_spawn1", "1050 0 1050");
        m_TerritorySpawns.Set("Moonshine_spawn2", "1100 0 1100");
        
        // Mushroom cartel spawns
        m_TerritorySpawns.Set("Mushroom_spawn1", "2050 0 2050");
        m_TerritorySpawns.Set("Mushroom_spawn2", "2100 0 2100");
        
        // Arms cartel spawns
        m_TerritorySpawns.Set("Arms_spawn1", "3050 0 3050");
        m_TerritorySpawns.Set("Arms_spawn2", "3100 0 3100");
    }
    
    //! Start territory capture
    bool StartTerritoryCapture(IEntity playerEntity, string territoryName)
    {
        if (!playerEntity || territoryName == "")
            return false;
            
        if (!m_Territories.Contains(territoryName))
            return false;
            
        // Check if player is in a gang
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        string playerId = playerComp.GetPlayerId();
        if (!IsPlayerInGang(playerId))
            return false;
            
        // Check if territory is already being captured
        if (m_ActiveCaptures.Contains(territoryName))
            return false;
            
        // Check player position
        EdenTerritoryData territory = m_Territories.Get(territoryName);
        vector playerPos = playerEntity.GetOrigin();
        vector territoryPos = territory.GetPosition();
        float distance = vector.Distance(playerPos, territoryPos);
        
        if (distance > territory.GetRadius())
            return false;
            
        // Check for illegal weapons or restrictions
        if (HasIllegalWeapons(playerEntity))
            return false;
            
        // Start capture
        int gangId = GetPlayerGangId(playerId);
        EdenTerritoryCapture captureData = new EdenTerritoryCapture();
        captureData.SetTerritoryName(territoryName);
        captureData.SetCapturingGangId(gangId);
        captureData.SetStartTime(GetGame().GetWorld().GetWorldTime());
        captureData.SetPlayerId(playerId);
        
        m_ActiveCaptures.Set(territoryName, captureData);
        
        // Start capture UI for player
        ShowCaptureUI(playerEntity, territoryName);
        
        Print(string.Format("[EdenGangTerritorySystem] Gang %1 started capturing territory %2", gangId, territoryName));
        return true;
    }
    
    //! Update territory capture progress
    protected void UpdateTerritories()
    {
        array<string> capturesToRemove = {};
        
        foreach (string territoryName, EdenTerritoryCapture captureData : m_ActiveCaptures)
        {
            if (!UpdateTerritoryCapture(territoryName, captureData))
            {
                capturesToRemove.Insert(territoryName);
            }
        }
        
        // Remove completed or failed captures
        foreach (string territoryName : capturesToRemove)
        {
            m_ActiveCaptures.Remove(territoryName);
            HideCaptureUI(territoryName);
        }
    }
    
    //! Update individual territory capture
    protected bool UpdateTerritoryCapture(string territoryName, EdenTerritoryCapture captureData)
    {
        EdenTerritoryData territory = m_Territories.Get(territoryName);
        if (!territory)
            return false;
            
        // Check timeout
        int currentTime = GetGame().GetWorld().GetWorldTime();
        if (currentTime - captureData.GetStartTime() > m_CaptureTimeout)
        {
            Print(string.Format("[EdenGangTerritorySystem] Territory capture timeout for %1", territoryName));
            return false;
        }
        
        // Get players in territory
        array<IEntity> playersInTerritory = GetPlayersInTerritory(territoryName);
        array<IEntity> gangMembers = FilterGangMembers(playersInTerritory, captureData.GetCapturingGangId());
        
        if (gangMembers.Count() == 0)
        {
            // No gang members in territory, stop capture
            return false;
        }
        
        // Calculate capture rate based on number of players
        int playerCount = gangMembers.Count();
        if (playerCount > 2) playerCount = 2; // Max 2 players for bonus
        
        float captureRate = m_CaptureRate + (playerCount * 0.000375);
        
        // Check if territory is owned by different gang
        int currentOwner = territory.GetOwnerGangId();
        int capturingGang = captureData.GetCapturingGangId();
        
        float currentProgress = territory.GetCaptureProgress();
        
        if (currentOwner != -1 && currentOwner != capturingGang && currentProgress > 0)
        {
            // Uncapture first
            captureRate = m_UncaptureRate - (playerCount * 0.000375);
        }
        
        // Update progress
        float newProgress = currentProgress + captureRate;
        newProgress = Math.Clamp(newProgress, 0.0, 1.0);
        
        territory.SetCaptureProgress(newProgress);
        
        // Update capture UI
        UpdateCaptureUI(territoryName, newProgress, captureRate > 0);
        
        // Check for completion
        if (newProgress >= 1.0)
        {
            CompleteTerritoryCapture(territoryName, capturingGang);
            return false; // Remove from active captures
        }
        else if (newProgress <= 0.0 && currentOwner != capturingGang)
        {
            // Reset to neutral
            territory.SetOwnerGangId(-1);
            territory.SetOwnerGangName("");
            territory.SetCaptureProgress(0.5);
        }
        
        return true; // Continue capture
    }
    
    //! Complete territory capture
    protected void CompleteTerritoryCapture(string territoryName, int gangId)
    {
        EdenTerritoryData territory = m_Territories.Get(territoryName);
        if (!territory)
            return;
            
        // Get gang name
        string gangName = GetGangName(gangId);
        
        // Set territory ownership
        territory.SetOwnerGangId(gangId);
        territory.SetOwnerGangName(gangName);
        territory.SetCaptureProgress(1.0);
        territory.SetBeingCaptured(false);
        
        // Update territory flag
        IEntity flagEntity = m_TerritoryFlags.Get(territoryName);
        if (flagEntity)
        {
            flagEntity.SetVariableInt("owner_gang_id", gangId);
            flagEntity.SetVariableString("owner_gang_name", gangName);
        }
        
        // Update territory marker
        UpdateTerritoryMarker(territoryName, gangName);
        
        // Notify gang members
        NotifyGangMembers(gangId, string.Format("Your gang has captured the %1 territory!", GetTerritoryDisplayName(territoryName)));
        
        // Save to database
        SaveTerritoryToDatabase(territory);
        
        Print(string.Format("[EdenGangTerritorySystem] Gang %1 (%2) captured territory %3", gangName, gangId, territoryName));
    }
    
    //! Get players in territory
    protected array<IEntity> GetPlayersInTerritory(string territoryName)
    {
        array<IEntity> playersInTerritory = {};
        
        EdenTerritoryData territory = m_Territories.Get(territoryName);
        if (!territory)
            return playersInTerritory;
            
        vector territoryPos = territory.GetPosition();
        float radius = territory.GetRadius();
        
        // Get all players and check distance
        array<IEntity> allPlayers = GetAllPlayers();
        foreach (IEntity player : allPlayers)
        {
            if (!player)
                continue;
                
            vector playerPos = player.GetOrigin();
            float distance = vector.Distance(playerPos, territoryPos);
            
            if (distance <= radius)
            {
                playersInTerritory.Insert(player);
            }
        }
        
        return playersInTerritory;
    }
    
    //! Filter gang members from player list
    protected array<IEntity> FilterGangMembers(array<IEntity> players, int gangId)
    {
        array<IEntity> gangMembers = {};
        
        foreach (IEntity player : players)
        {
            if (!player)
                continue;
                
            EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(player.FindComponent(EdenPlayerComponent));
            if (!playerComp)
                continue;
                
            string playerId = playerComp.GetPlayerId();
            if (GetPlayerGangId(playerId) == gangId)
            {
                gangMembers.Insert(player);
            }
        }
        
        return gangMembers;
    }
    
    //! Territory garage access
    bool CanAccessTerritoryGarage(IEntity playerEntity, string territoryName)
    {
        if (!playerEntity || territoryName == "")
            return false;
            
        if (!m_Territories.Contains(territoryName))
            return false;
            
        EdenPlayerComponent playerComp = EdenPlayerComponent.Cast(playerEntity.FindComponent(EdenPlayerComponent));
        if (!playerComp)
            return false;
            
        string playerId = playerComp.GetPlayerId();
        int playerGangId = GetPlayerGangId(playerId);
        
        EdenTerritoryData territory = m_Territories.Get(territoryName);
        return territory.GetOwnerGangId() == playerGangId;
    }
    
    //! Get territory spawn points
    array<vector> GetTerritorySpawns(string territoryName)
    {
        array<vector> spawns = {};
        
        foreach (string spawnName, vector spawnPos : m_TerritorySpawns)
        {
            if (spawnName.Contains(territoryName))
            {
                spawns.Insert(spawnPos);
            }
        }
        
        return spawns;
    }
    
    //! Helper methods
    protected int GetTerritoryId(string territoryName)
    {
        switch (territoryName)
        {
            case "Meth": return 1;
            case "Moonshine": return 2;
            case "Mushroom": return 3;
            case "Arms": return 4;
            default: return 0;
        }
    }
    
    protected string GetTerritoryDisplayName(string territoryName)
    {
        switch (territoryName)
        {
            case "Meth": return "Meth and Weed";
            case "Moonshine": return "Moonshine and Heroin";
            case "Mushroom": return "Mushroom and Cocaine";
            case "Arms": return "Arms Dealer";
            default: return territoryName;
        }
    }
    
    protected void UpdateTerritoryMarker(string territoryName, string gangName)
    {
        // Update map marker with gang name
        string markerName = string.Format("%1_cartel", territoryName);
        string displayName = GetTerritoryDisplayName(territoryName);
        // Implementation would update actual map marker
    }
    
    //! Placeholder methods for actual implementation
    protected IEntity CreateTerritoryFlag(vector position, string territoryName) { return null; }
    protected bool HasIllegalWeapons(IEntity playerEntity) { return false; }
    protected bool IsPlayerInGang(string playerId) { return false; }
    protected int GetPlayerGangId(string playerId) { return -1; }
    protected string GetGangName(int gangId) { return ""; }
    protected array<IEntity> GetAllPlayers() { return {}; }
    protected void ShowCaptureUI(IEntity playerEntity, string territoryName) { }
    protected void UpdateCaptureUI(string territoryName, float progress, bool capturing) { }
    protected void HideCaptureUI(string territoryName) { }
    protected void NotifyGangMembers(int gangId, string message) { }
    protected void LoadTerritoryData() { }
    protected void SaveTerritoryToDatabase(EdenTerritoryData territory) { }
    
    //! Public query methods
    EdenTerritoryData GetTerritoryData(string territoryName)
    {
        if (m_Territories.Contains(territoryName))
            return m_Territories.Get(territoryName);
        return null;
    }
    
    array<string> GetAvailableTerritories()
    {
        array<string> territories = {};
        foreach (string territoryName, EdenTerritoryData territory : m_Territories)
        {
            territories.Insert(territoryName);
        }
        return territories;
    }
    
    bool IsTerritoryBeingCaptured(string territoryName)
    {
        return m_ActiveCaptures.Contains(territoryName);
    }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenGangTerritorySystem] Cleaning up territory system...");
        
        m_Territories.Clear();
        m_ActiveCaptures.Clear();
        m_TerritoryFlags.Clear();
        m_TerritorySpawns.Clear();
    }
}

//! Territory capture data
class EdenTerritoryCapture
{
    protected string m_TerritoryName;
    protected int m_CapturingGangId;
    protected string m_PlayerId;
    protected int m_StartTime;
    protected float m_Progress;
    
    void SetTerritoryName(string territoryName) { m_TerritoryName = territoryName; }
    void SetCapturingGangId(int capturingGangId) { m_CapturingGangId = capturingGangId; }
    void SetPlayerId(string playerId) { m_PlayerId = playerId; }
    void SetStartTime(int startTime) { m_StartTime = startTime; }
    void SetProgress(float progress) { m_Progress = progress; }
    
    string GetTerritoryName() { return m_TerritoryName; }
    int GetCapturingGangId() { return m_CapturingGangId; }
    string GetPlayerId() { return m_PlayerId; }
    int GetStartTime() { return m_StartTime; }
    float GetProgress() { return m_Progress; }
}
