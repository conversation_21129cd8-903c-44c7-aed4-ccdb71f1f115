//	File: fn_betMoney.sqf
//	Author: Fusah
//	Description: <PERSON><PERSON> betting money against another player.
#include "..\..\macro.h"
params [
	["_type",""],
	["_val",0],
	["_player",objNull]
];

switch (_type) do {
	case "amount": {
		if (oev_betCooldown) exitWith {hint "You have already bet money recently. Please try again later."};
		if (__GETC__(life_adminlevel) > 2) exitWith {hint "Staff can not bet!";};
		if (playerSide isEqualTo independent) exitWith {hint "Medics cannot bet!"};
		if (playerSide isEqualTo west) exitWith {hint "Cops cannot bet!"};
		if (oev_inBet) exitWith {hint "You are already in a bet!"};
		if (life_bettingVer) exitWith {hint "You currently have betting disabled!"};
		if (O_stats_playtime_civ < 1800) exitWith {hint "You must have at least 30 hours on the server to bet!"};
		["life_bet_money"] call OEC_fnc_createDialog;
	};
	case "bet": {
		_val = ctrlText 3652;
		closeDialog 0;
		if !(isNil _val) exitWith {};
		if (!([_val] call OEC_fnc_isNumeric)) exitWith {hint "Please enter a valid number!"};
		_val = parseNumber _val;
		if (O_stats_playtime_civ < 1800) exitWith {hint "You must have at least 30 hours on the server to bet!"};
		if ((_val) < 1000) exitWith {hint "You have to bet at least $1000."};
		if ((_val) > 5000000) exitWith {hint "You may not bet more than $5,000,000 at a time."};
		if ((_val) <= 0) exitWith {}; // Why bother
		if (oev_cash < (_val) && oev_atmcash < (_val)) exitWith {hint "You do not have that much money to bet!"};
		private _unit = life_pInact_curTarget;
		if(isNull _unit) exitWith {};
		if(!isPlayer _unit) exitWith {};
		if (_unit getVariable ["restrictions", false]) exitWith {hint "The other player is on restrictions and cannot be bet against.";};
		if (side _unit isEqualTo independent) exitWith {hint "You cannot bet medics!";};
		if (__GETC__(oev_restrictions)) exitWith {hint "You are on restrictions and cannot bet.";};
		if ((_unit distance player) > 20) exitWith {hint "The other player is too far away! Try again."};
		if (life_bettingVer) exitWith {hint "You currently have betting disabled!"};
		_exit = false;
		if (_val > 5000000) then {
			private _action = [
				format ["Are you sure you want to bet $%1",[_val] call OEC_fnc_numberText],
				"Confirmation",
				"Yes",
				"No"
			] call BIS_fnc_guiMessage;
			if !(_action) then {_exit = true;};
		};
		if !(_exit) then {
			uiSleep floor random 5;
			oev_inBet = true;
			oev_use_atm = false;
			oev_action_inUse = true;
			["confirm",_val,player] remoteExec ["OEC_fnc_betMoney",_unit,false];
			oev_betCooldown = true;
			[] spawn{
				uiSleep 180;
				oev_betCooldown = false;
				oev_inBet = false; //failsafe
				oev_action_inUse = false;
				oev_use_atm = true;
			};
		};
	};
	case "confirm": {
		if(!isNull (findDisplay 2700) || !isNull (findDisplay 3200)) then {
			closeDialog 0;
		};
		if (oev_action_inUse || oev_inCasino) exitWith {
			["failU"] remoteExec ["OEC_fnc_betMoney",_player,false];
		};
		if (oev_cash < (_val) && oev_atmcash < (_val)) exitWith {
			["failM"] remoteExec ["OEC_fnc_betMoney",_player,false];
		};
		if (oev_inBet) exitWith {
			["failB"] remoteExec ["OEC_fnc_betMoney",_player,false];
		};
		if (oev_betCooldown) exitWith {
			["failC"] remoteExec ["OEC_fnc_betMoney",_player,false];
		};
		if (life_bettingVer || (__GETC__(life_adminlevel) > 2)) exitWith {
			["failE"] remoteExec ["OEC_fnc_betMoney",_player,false];
		};
		if (O_stats_playtime_civ < 1800) exitWith {
			["failT"] remoteExec ["OEC_fnc_betMoney",_player,false];
		};
		oev_inBet = true;
		oev_use_atm = false;
		oev_action_inUse = true;
		private _action = [
			format ["Would you like to bet $%1 with %2?",[_val] call OEC_fnc_numberText,name _player],
			"Confirmation",
			"Yes",
			"No"
		] call BIS_fnc_guiMessage;
		if (_action) then {
			private _winNum = floor random 2;
			hint "Flipping coin...";
			uiSleep 3;
			oev_betCooldown = true;
			[] spawn{
				uiSleep 180;
				oev_betCooldown = false;
				oev_inBet = false; //failsafe
				oev_use_atm = true;
				oev_action_inUse = false;
			};
			if (_winNum isEqualTo 1) then {
				if !([getPlayerUID _player] call OEC_fnc_isUIDActive) exitWith {hint "The person who bet against you left the game... what a loser.."};
				_winNum = _val;
				["lose", _winNum, player] remoteExec ["OEC_fnc_betMoney",_player,false];
			} else {
				_winNum = _val;
				["lose", _winNum, _player] remoteExec ["OEC_fnc_betMoney", player];
			};
		} else {
			["no"] remoteExec ["OEC_fnc_betMoney",_player,false];
			oev_inBet = false;
			oev_use_atm = true;
			oev_action_inUse = false;
		};
	};
	case "win": {
		private _taxedVal = round(_val * 0.99);
		titleText [format["Congrats you won the bet worth $%1!",[_val] call OEC_fnc_numberText],"PLAIN DOWN"];
		oev_atmcash = oev_atmcash + _taxedVal;
		oev_cache_atmcash = oev_cache_atmcash + _taxedVal;
		["bets_won_value",_taxedVal] call OEC_fnc_statArrUp;
		["bets_won",1] call OEC_fnc_statArrUp;
		[1] call OEC_fnc_ClupdatePartial;
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
	};
	case "failM": {
		hint "The other player does not have that much money to bet!";
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
	};
	case "no": {
		hint "The other player has declined your bet.";
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
	};
	case "failB": {
		hint "The other player is already in bet!";
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
	};
	case "failC": {
		hint "The other player is in a cooldown!";
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
	};
	case "failD": {
		hint "The person you bet against is now too broke, so the bet was cancelled.";
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
	};
	case "failE": {
		hint "The person you bet against currently has betting disabled, so the bet was cancelled.";
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
	};
	case "failT": {
		hint "The person you are trying to bet does not have 30 or more hours on the server!";
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
	};
	case "failU": {
		hint "The person you are trying to bet is currently busy.";
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
	};
	case "lose": {
		if !([getPlayerUID _player] call OEC_fnc_isUIDActive) exitWith {
			hint "The person who bet against you left the game... what a loser..";
			oev_inBet = false;
			oev_use_atm = true;
			oev_action_inUse = false;
		};
		if (oev_cash < _val && oev_atmcash < _val) exitWith {
			["failD", _val, player] remoteExecCall ["OEC_fnc_betMoney", _player];
			[
				["event","Exploit Negative Money"],
				["player",name player],
				["player_id",getPlayerUID player],
				["target",name _player],
				["target_id",getPlayerUID _player],
				["value",[_val] call OEC_fnc_numberText],
				["location",getPosATL player]
			] call OEC_fnc_logIt;
			oev_inBet = false;
			oev_use_atm = true;
			oev_action_inUse = false;
		};
		if !(oev_cash < _val) then {
			oev_cash = oev_cash - _val;
			oev_cache_cash = oev_cache_cash - _val;
			[0] call OEC_fnc_ClupdatePartial;
		} else {
			oev_atmcash = oev_atmcash - _val;
			oev_cache_atmcash = oev_cache_atmcash - _val;
			[1] call OEC_fnc_ClupdatePartial;
		};
		hint "You lost the bet. Better luck next time!";
		["bets_lost_value",_val] call OEC_fnc_statArrUp;
		["bets_lost",1] call OEC_fnc_statArrUp;
		oev_inBet = false;
		oev_use_atm = true;
		oev_action_inUse = false;
		["win", _val, player] remoteExec ["OEC_fnc_betMoney", _player];
		if (remoteExecutedOwner isEqualTo (owner player)) then {
			[0, format ["%1 lost a bet worth $%2 against %3!", name player, [_val] call OEC_fnc_numberText, name _player]] remoteExecCall ["OEC_fnc_broadcast", -2];
		} else {
			[0, format ["%1 won a bet worth $%2 against %3!", name _player, [_val] call OEC_fnc_numberText, name player]] remoteExecCall ["OEC_fnc_broadcast", -2];
		};
		[
			["event","Player Lost Bet"],
			["player",name player],
			["player_id",getPlayerUID player],
			["target",name _player],
			["target_id",getPlayerUID _player],
			["value",[_val] call OEC_fnc_numberText],
			["location",getPosATL player]
		] call OEC_fnc_logIt;
	};
};
