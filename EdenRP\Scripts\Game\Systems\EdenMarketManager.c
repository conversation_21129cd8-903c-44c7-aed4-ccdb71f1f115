//! Eden Market Manager - Handles dynamic market pricing and supply/demand economics
//! Converted from original market systems

class EdenMarketManager
{
    protected ref map<string, ref EdenMarketItem> m_MarketItems;
    protected ref map<string, int> m_MarketPrices;
    protected ref map<string, int> m_StartingPrices;
    protected ref array<string> m_MarketVariableNames;
    protected ref map<string, ref EdenMarketConfig> m_MarketConfigs;
    
    // Market configuration
    protected int m_MarketUpdateInterval;
    protected bool m_MarketResetEnabled;
    protected int m_LastUpdateTime;
    protected int m_ServerId;
    
    void EdenMarketManager()
    {
        m_MarketItems = new map<string, ref EdenMarketItem>();
        m_MarketPrices = new map<string, int>();
        m_StartingPrices = new map<string, int>();
        m_MarketVariableNames = new array<string>();
        m_MarketConfigs = new map<string, ref EdenMarketConfig>();
        
        m_MarketUpdateInterval = 300000; // 5 minutes
        m_MarketResetEnabled = false;
        m_LastUpdateTime = 0;
        m_ServerId = 1; // Default server ID
        
        InitializeMarketSystem();
    }
    
    //! Initialize market system
    void Initialize()
    {
        Print("[EdenMarketManager] Initializing market system...");
        
        SetupMarketItems();
        LoadMarketData();
        
        // Start market update loop
        GetGame().GetCallqueue().CallLater(UpdateMarketPrices, m_MarketUpdateInterval, true);
        
        Print("[EdenMarketManager] Market system initialized");
    }
    
    //! Initialize market system configuration
    protected void InitializeMarketSystem()
    {
        // Set up market variable names
        m_MarketVariableNames.Insert("foodDiv");
        m_MarketVariableNames.Insert("apple");
        m_MarketVariableNames.Insert("peach");
        m_MarketVariableNames.Insert("salema");
        m_MarketVariableNames.Insert("ornate");
        m_MarketVariableNames.Insert("mackerel");
        m_MarketVariableNames.Insert("mullet");
        m_MarketVariableNames.Insert("catshark");
        m_MarketVariableNames.Insert("tuna");
        m_MarketVariableNames.Insert("legalDiv");
        m_MarketVariableNames.Insert("saltr");
        m_MarketVariableNames.Insert("cement");
        m_MarketVariableNames.Insert("glass");
        m_MarketVariableNames.Insert("ironr");
        m_MarketVariableNames.Insert("copperr");
        m_MarketVariableNames.Insert("silverr");
        m_MarketVariableNames.Insert("platinumr");
        m_MarketVariableNames.Insert("oilp");
        m_MarketVariableNames.Insert("diamondc");
        m_MarketVariableNames.Insert("illegalDiv");
        m_MarketVariableNames.Insert("marijuana");
        m_MarketVariableNames.Insert("frogp");
        m_MarketVariableNames.Insert("mmushroom");
        m_MarketVariableNames.Insert("heroinp");
        m_MarketVariableNames.Insert("cocainep");
        m_MarketVariableNames.Insert("turtle");
        m_MarketVariableNames.Insert("moonshine");
        m_MarketVariableNames.Insert("crystalmeth");
        m_MarketVariableNames.Insert("moneybag");
        m_MarketVariableNames.Insert("goldbar");
        
        Print(string.Format("[EdenMarketManager] Initialized %1 market variables", m_MarketVariableNames.Count()));
    }
    
    //! Setup market items with configurations
    protected void SetupMarketItems()
    {
        // Food items
        SetupMarketItem("apple", 88, 160, 0, 0.003, 0.001, 124);
        SetupMarketItem("peach", 128, 248, 0, 0.003, 0.001, 188);
        SetupMarketItem("salema", 96, 183, 0, 0.003, 0.001, 141);
        SetupMarketItem("ornate", 96, 183, 0, 0.003, 0.001, 141);
        SetupMarketItem("mackerel", 282, 525, 0, 0.003, 0.001, 435);
        SetupMarketItem("mullet", 312, 580, 0, 0.003, 0.001, 480);
        SetupMarketItem("catshark", 900, 1674, 0, 0.003, 0.001, 1392);
        SetupMarketItem("tuna", 600, 1116, 0, 0.003, 0.001, 930);
        
        // Legal resources
        SetupMarketItem("saltr", 795, 1479, 0, 0.003, 0.001, 1229);
        SetupMarketItem("cement", 1115, 2075, 0, 0.003, 0.001, 1725);
        SetupMarketItem("glass", 828, 1540, 0, 0.003, 0.001, 1280);
        SetupMarketItem("ironr", 873, 1623, 0, 0.003, 0.001, 1349);
        SetupMarketItem("copperr", 828, 1540, 0, 0.003, 0.001, 1280);
        SetupMarketItem("silverr", 849, 1579, 0, 0.003, 0.001, 1313);
        SetupMarketItem("platinumr", 932, 1734, 0, 0.003, 0.001, 1441);
        SetupMarketItem("oilp", 1467, 2728, 0, 0.003, 0.001, 2268);
        SetupMarketItem("diamondc", 1054, 1961, 0, 0.003, 0.001, 1630);
        
        // Illegal items
        SetupMarketItem("marijuana", 1278, 2378, 1, 0.003, 0.001, 1976);
        SetupMarketItem("frogp", 1188, 2209, 1, 0.003, 0.001, 1837);
        SetupMarketItem("mmushroom", 1669, 3105, 1, 0.003, 0.001, 2581);
        SetupMarketItem("heroinp", 1368, 2544, 1, 0.003, 0.001, 2115);
        SetupMarketItem("cocainep", 1459, 2713, 1, 0.003, 0.001, 2255);
        SetupMarketItem("turtle", 5163, 9605, 1, 0.003, 0.001, 7980);
        SetupMarketItem("moonshine", 5438, 10116, 1, 0.003, 0.001, 8406);
        SetupMarketItem("crystalmeth", 5706, 10614, 1, 0.003, 0.001, 8820);
        SetupMarketItem("moneybag", 19410, 36105, 1, 0.003, 0.001, 30000);
        SetupMarketItem("goldbar", 44668, 83093, 1, 0.003, 0.001, 69063);
        
        Print(string.Format("[EdenMarketManager] Set up %1 market items", m_MarketItems.Count()));
    }
    
    //! Setup individual market item
    protected void SetupMarketItem(string itemName, int minPrice, int maxPrice, int legality, float decreaseRate, float increaseRate, int startingPrice)
    {
        // Create market configuration
        EdenMarketConfig config = new EdenMarketConfig();
        config.SetItemName(itemName);
        config.SetMinPrice(minPrice);
        config.SetMaxPrice(maxPrice);
        config.SetLegality(legality);
        config.SetDecreaseRate(decreaseRate);
        config.SetIncreaseRate(increaseRate);
        
        m_MarketConfigs.Set(itemName, config);
        
        // Create market item
        EdenMarketItem item = new EdenMarketItem();
        item.SetItemName(itemName);
        item.SetCurrentPrice(startingPrice);
        item.SetStartingPrice(startingPrice);
        item.SetMinPrice(minPrice);
        item.SetMaxPrice(maxPrice);
        item.SetSalesCount(0);
        item.SetLastUpdateTime(GetGame().GetWorld().GetWorldTime());
        
        m_MarketItems.Set(itemName, item);
        m_MarketPrices.Set(itemName, startingPrice);
        m_StartingPrices.Set(itemName, startingPrice);
    }
    
    //! Get current market price for item
    int GetMarketPrice(string itemName)
    {
        if (m_MarketPrices.Contains(itemName))
            return m_MarketPrices.Get(itemName);
        return -1;
    }
    
    //! Update market price when item is sold
    void ProcessItemSale(string itemName, int quantity)
    {
        if (!m_MarketItems.Contains(itemName) || !m_MarketConfigs.Contains(itemName))
            return;
            
        EdenMarketItem item = m_MarketItems.Get(itemName);
        EdenMarketConfig config = m_MarketConfigs.Get(itemName);
        
        // Calculate price change
        float priceChange = quantity * config.GetDecreaseRate();
        int currentPrice = item.GetCurrentPrice();
        int newPrice = Math.Floor(currentPrice * (1.0 - priceChange));
        
        // Clamp to min/max prices
        newPrice = Math.Clamp(newPrice, config.GetMinPrice(), config.GetMaxPrice());
        
        // Update item data
        item.SetCurrentPrice(newPrice);
        item.AddSalesCount(quantity);
        item.SetLastUpdateTime(GetGame().GetWorld().GetWorldTime());
        
        // Update price map
        m_MarketPrices.Set(itemName, newPrice);
        
        // Save to database
        UpdateMarketItemInDatabase(item);
        
        Print(string.Format("[EdenMarketManager] %1 sold %2 units, price: $%3 -> $%4", itemName, quantity, currentPrice, newPrice));
    }
    
    //! Update market prices (periodic recovery)
    protected void UpdateMarketPrices()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        
        foreach (string itemName, EdenMarketItem item : m_MarketItems)
        {
            if (!m_MarketConfigs.Contains(itemName))
                continue;
                
            EdenMarketConfig config = m_MarketConfigs.Get(itemName);
            
            // Calculate price recovery
            int currentPrice = item.GetCurrentPrice();
            int startingPrice = item.GetStartingPrice();
            
            if (currentPrice < startingPrice)
            {
                // Price recovery towards starting price
                float recoveryRate = config.GetIncreaseRate();
                int newPrice = Math.Floor(currentPrice * (1.0 + recoveryRate));
                
                // Don't exceed starting price during recovery
                newPrice = Math.Min(newPrice, startingPrice);
                newPrice = Math.Clamp(newPrice, config.GetMinPrice(), config.GetMaxPrice());
                
                if (newPrice != currentPrice)
                {
                    item.SetCurrentPrice(newPrice);
                    item.SetLastUpdateTime(currentTime);
                    m_MarketPrices.Set(itemName, newPrice);
                    
                    UpdateMarketItemInDatabase(item);
                }
            }
        }
        
        m_LastUpdateTime = currentTime;
        
        // Broadcast market update to all clients
        BroadcastMarketUpdate();
        
        Print("[EdenMarketManager] Market prices updated");
    }
    
    //! Reset market prices to starting values
    void ResetMarketPrices()
    {
        if (!m_MarketResetEnabled)
            return;
            
        foreach (string itemName, EdenMarketItem item : m_MarketItems)
        {
            int startingPrice = item.GetStartingPrice();
            item.SetCurrentPrice(startingPrice);
            item.SetSalesCount(0);
            item.SetLastUpdateTime(GetGame().GetWorld().GetWorldTime());
            
            m_MarketPrices.Set(itemName, startingPrice);
            
            UpdateMarketItemInDatabase(item);
        }
        
        BroadcastMarketUpdate();
        
        Print("[EdenMarketManager] Market prices reset to starting values");
    }
    
    //! Get market item data
    EdenMarketItem GetMarketItem(string itemName)
    {
        if (m_MarketItems.Contains(itemName))
            return m_MarketItems.Get(itemName);
        return null;
    }
    
    //! Get all market prices
    map<string, int> GetAllMarketPrices()
    {
        return m_MarketPrices;
    }
    
    //! Get market configuration
    EdenMarketConfig GetMarketConfig(string itemName)
    {
        if (m_MarketConfigs.Contains(itemName))
            return m_MarketConfigs.Get(itemName);
        return null;
    }
    
    //! Check if item is legal
    bool IsItemLegal(string itemName)
    {
        if (m_MarketConfigs.Contains(itemName))
        {
            EdenMarketConfig config = m_MarketConfigs.Get(itemName);
            return config.GetLegality() == 0;
        }
        return true; // Default to legal
    }
    
    //! Get market statistics
    EdenMarketStats GetMarketStatistics()
    {
        EdenMarketStats stats = new EdenMarketStats();
        
        int totalItems = 0;
        int totalSales = 0;
        int legalItems = 0;
        int illegalItems = 0;
        
        foreach (string itemName, EdenMarketItem item : m_MarketItems)
        {
            totalItems++;
            totalSales += item.GetSalesCount();
            
            if (m_MarketConfigs.Contains(itemName))
            {
                EdenMarketConfig config = m_MarketConfigs.Get(itemName);
                if (config.GetLegality() == 0)
                    legalItems++;
                else
                    illegalItems++;
            }
        }
        
        stats.SetTotalItems(totalItems);
        stats.SetTotalSales(totalSales);
        stats.SetLegalItems(legalItems);
        stats.SetIllegalItems(illegalItems);
        stats.SetLastUpdateTime(m_LastUpdateTime);
        
        return stats;
    }
    
    //! Calculate territory tax for gang-controlled areas
    float CalculateTerritoryTax(string itemName, vector saleLocation)
    {
        // Check if sale location is in gang territory
        // This would integrate with the gang territory system
        
        // For now, return base tax rate
        return 0.0;
    }
    
    //! Apply market manipulation (admin function)
    bool ManipulateMarketPrice(string itemName, int newPrice, string adminId)
    {
        if (!m_MarketItems.Contains(itemName))
            return false;
            
        if (!m_MarketConfigs.Contains(itemName))
            return false;
            
        EdenMarketItem item = m_MarketItems.Get(itemName);
        EdenMarketConfig config = m_MarketConfigs.Get(itemName);
        
        // Clamp to valid range
        newPrice = Math.Clamp(newPrice, config.GetMinPrice(), config.GetMaxPrice());
        
        int oldPrice = item.GetCurrentPrice();
        item.SetCurrentPrice(newPrice);
        item.SetLastUpdateTime(GetGame().GetWorld().GetWorldTime());
        
        m_MarketPrices.Set(itemName, newPrice);
        
        // Update database
        UpdateMarketItemInDatabase(item);
        
        // Log manipulation
        LogMarketManipulation(adminId, itemName, oldPrice, newPrice);
        
        // Broadcast update
        BroadcastMarketUpdate();
        
        Print(string.Format("[EdenMarketManager] Admin %1 manipulated %2 price: $%3 -> $%4", adminId, itemName, oldPrice, newPrice));
        return true;
    }
    
    //! Broadcast market update to clients
    protected void BroadcastMarketUpdate()
    {
        // Implementation would send market data to all connected clients
        // This would use the network replication system
    }
    
    //! Helper methods
    protected void LogMarketManipulation(string adminId, string itemName, int oldPrice, int newPrice)
    {
        // Implementation would log to database or file
        Print(string.Format("[EdenMarketManager] Market Manipulation: Admin %1, Item %2, Price %3 -> %4", adminId, itemName, oldPrice, newPrice));
    }
    
    //! Placeholder methods for actual implementation
    protected void LoadMarketData() { }
    protected void UpdateMarketItemInDatabase(EdenMarketItem item) { }
    
    //! Public query methods
    array<string> GetMarketVariableNames() { return m_MarketVariableNames; }
    
    int GetLastUpdateTime() { return m_LastUpdateTime; }
    
    bool IsMarketResetEnabled() { return m_MarketResetEnabled; }
    
    void SetMarketResetEnabled(bool enabled) { m_MarketResetEnabled = enabled; }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenMarketManager] Cleaning up market system...");
        
        m_MarketItems.Clear();
        m_MarketPrices.Clear();
        m_StartingPrices.Clear();
        m_MarketVariableNames.Clear();
        m_MarketConfigs.Clear();
    }
}

//! Market statistics data
class EdenMarketStats
{
    protected int m_TotalItems;
    protected int m_TotalSales;
    protected int m_LegalItems;
    protected int m_IllegalItems;
    protected int m_LastUpdateTime;
    
    void SetTotalItems(int totalItems) { m_TotalItems = totalItems; }
    void SetTotalSales(int totalSales) { m_TotalSales = totalSales; }
    void SetLegalItems(int legalItems) { m_LegalItems = legalItems; }
    void SetIllegalItems(int illegalItems) { m_IllegalItems = illegalItems; }
    void SetLastUpdateTime(int lastUpdateTime) { m_LastUpdateTime = lastUpdateTime; }
    
    int GetTotalItems() { return m_TotalItems; }
    int GetTotalSales() { return m_TotalSales; }
    int GetLegalItems() { return m_LegalItems; }
    int GetIllegalItems() { return m_IllegalItems; }
    int GetLastUpdateTime() { return m_LastUpdateTime; }
}
