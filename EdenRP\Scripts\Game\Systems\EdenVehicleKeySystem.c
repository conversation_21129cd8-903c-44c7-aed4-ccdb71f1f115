//! Eden Vehicle Key System - Handles vehicle keys, access control, and gang vehicle sharing
//! Converted from original key systems

class EdenVehicleKeySystem
{
    protected ref map<string, ref array<string>> m_PlayerKeys; // Player ID -> Vehicle IDs
    protected ref map<string, ref array<string>> m_VehicleKeys; // Vehicle ID -> Player IDs with keys
    protected ref map<string, ref EdenVehicleKeyData> m_KeyDatabase; // Key ID -> Key Data
    protected ref map<int, ref array<string>> m_GangVehicleKeys; // Gang ID -> Vehicle IDs
    
    // Key system configuration
    protected bool m_KeySystemEnabled;
    protected int m_MaxKeysPerPlayer;
    protected int m_KeyCopyPrice;
    protected float m_KeyDistributionRange;
    protected bool m_GangKeySharing;
    
    void EdenVehicleKeySystem()
    {
        m_PlayerKeys = new map<string, ref array<string>>();
        m_VehicleKeys = new map<string, ref array<string>>();
        m_KeyDatabase = new map<string, ref EdenVehicleKeyData>();
        m_GangVehicleKeys = new map<int, ref array<string>>();
        
        m_KeySystemEnabled = true;
        m_MaxKeysPerPlayer = 50;
        m_KeyCopyPrice = 500;
        m_KeyDistributionRange = 10.0;
        m_GangKeySharing = true;
        
        InitializeKeySystem();
    }
    
    //! Initialize key system
    void Initialize()
    {
        Print("[EdenVehicleKeySystem] Initializing vehicle key system...");
        
        LoadKeyDatabase();
        SetupKeyDistribution();
        
        // Set up periodic key cleanup
        GetGame().GetCallqueue().CallLater(CleanupExpiredKeys, 600000, true); // 10 minutes
        
        Print("[EdenVehicleKeySystem] Vehicle key system initialized");
    }
    
    //! Initialize key system configuration
    protected void InitializeKeySystem()
    {
        Print("[EdenVehicleKeySystem] Key system configuration initialized");
    }
    
    //! Load key database
    protected void LoadKeyDatabase()
    {
        // Implementation would load keys from database
        Print("[EdenVehicleKeySystem] Key database loaded");
    }
    
    //! Setup key distribution system
    protected void SetupKeyDistribution()
    {
        Print("[EdenVehicleKeySystem] Key distribution system configured");
    }
    
    //! Give vehicle keys to player
    bool GiveVehicleKeys(string playerId, string vehicleId, bool isPermanent = true)
    {
        if (playerId == "" || vehicleId == "")
            return false;
            
        if (!m_KeySystemEnabled)
            return false;
            
        // Check if player already has keys
        if (PlayerHasKeys(playerId, vehicleId))
            return true;
            
        // Check key limit
        if (GetPlayerKeyCount(playerId) >= m_MaxKeysPerPlayer)
            return false;
            
        // Add keys to player
        if (!m_PlayerKeys.Contains(playerId))
        {
            m_PlayerKeys.Set(playerId, new array<string>());
        }
        
        array<string> playerKeys = m_PlayerKeys.Get(playerId);
        playerKeys.Insert(vehicleId);
        
        // Add player to vehicle key holders
        if (!m_VehicleKeys.Contains(vehicleId))
        {
            m_VehicleKeys.Set(vehicleId, new array<string>());
        }
        
        array<string> vehicleKeyHolders = m_VehicleKeys.Get(vehicleId);
        vehicleKeyHolders.Insert(playerId);
        
        // Create key data
        string keyId = GenerateKeyId();
        EdenVehicleKeyData keyData = new EdenVehicleKeyData();
        keyData.SetKeyId(keyId);
        keyData.SetPlayerId(playerId);
        keyData.SetVehicleId(vehicleId);
        keyData.SetIsPermanent(isPermanent);
        keyData.SetCreationTime(GetGame().GetWorld().GetWorldTime());
        keyData.SetIsActive(true);
        
        m_KeyDatabase.Set(keyId, keyData);
        
        // Save to database
        SaveKeyToDatabase(keyData);
        
        Print(string.Format("[EdenVehicleKeySystem] Gave keys for vehicle %1 to player %2", vehicleId, playerId));
        return true;
    }
    
    //! Remove vehicle keys from player
    bool RemoveVehicleKeys(string playerId, string vehicleId)
    {
        if (playerId == "" || vehicleId == "")
            return false;
            
        // Remove from player keys
        if (m_PlayerKeys.Contains(playerId))
        {
            array<string> playerKeys = m_PlayerKeys.Get(playerId);
            int keyIndex = playerKeys.Find(vehicleId);
            if (keyIndex != -1)
            {
                playerKeys.Remove(keyIndex);
            }
        }
        
        // Remove from vehicle key holders
        if (m_VehicleKeys.Contains(vehicleId))
        {
            array<string> vehicleKeyHolders = m_VehicleKeys.Get(vehicleId);
            int holderIndex = vehicleKeyHolders.Find(playerId);
            if (holderIndex != -1)
            {
                vehicleKeyHolders.Remove(holderIndex);
            }
        }
        
        // Remove from key database
        RemoveKeyFromDatabase(playerId, vehicleId);
        
        Print(string.Format("[EdenVehicleKeySystem] Removed keys for vehicle %1 from player %2", vehicleId, playerId));
        return true;
    }
    
    //! Check if player has keys for vehicle
    bool PlayerHasKeys(string playerId, string vehicleId)
    {
        if (playerId == "" || vehicleId == "")
            return false;
            
        if (!m_PlayerKeys.Contains(playerId))
            return false;
            
        array<string> playerKeys = m_PlayerKeys.Get(playerId);
        return playerKeys.Find(vehicleId) != -1;
    }
    
    //! Copy vehicle keys
    bool CopyVehicleKeys(string fromPlayerId, string toPlayerId, string vehicleId)
    {
        if (fromPlayerId == "" || toPlayerId == "" || vehicleId == "")
            return false;
            
        // Check if source player has keys
        if (!PlayerHasKeys(fromPlayerId, vehicleId))
            return false;
            
        // Check if target player already has keys
        if (PlayerHasKeys(toPlayerId, vehicleId))
            return false;
            
        // Get player entities
        IEntity fromPlayerEntity = GetPlayerEntity(fromPlayerId);
        IEntity toPlayerEntity = GetPlayerEntity(toPlayerId);
        
        if (!fromPlayerEntity || !toPlayerEntity)
            return false;
            
        // Check range
        if (vector.Distance(fromPlayerEntity.GetOrigin(), toPlayerEntity.GetOrigin()) > m_KeyDistributionRange)
            return false;
            
        // Get player components
        EdenPlayerComponent fromPlayerComp = EdenPlayerComponent.Cast(fromPlayerEntity.FindComponent(EdenPlayerComponent));
        EdenPlayerComponent toPlayerComp = EdenPlayerComponent.Cast(toPlayerEntity.FindComponent(EdenPlayerComponent));
        
        if (!fromPlayerComp || !toPlayerComp)
            return false;
            
        // Check if source player has enough money
        if (fromPlayerComp.GetCash() < m_KeyCopyPrice)
            return false;
            
        // Process payment
        fromPlayerComp.RemoveCash(m_KeyCopyPrice);
        
        // Give keys to target player
        GiveVehicleKeys(toPlayerId, vehicleId, false); // Temporary keys
        
        Print(string.Format("[EdenVehicleKeySystem] %1 copied keys for vehicle %2 to %3", fromPlayerId, vehicleId, toPlayerId));
        return true;
    }
    
    //! Distribute gang vehicle keys
    bool DistributeGangVehicleKeys(int gangId, string vehicleId)
    {
        if (gangId <= 0 || vehicleId == "")
            return false;
            
        if (!m_GangKeySharing)
            return false;
            
        // Get gang members
        array<string> gangMembers = GetGangMembers(gangId);
        if (gangMembers.Count() == 0)
            return false;
            
        // Give keys to all gang members
        foreach (string memberId : gangMembers)
        {
            GiveVehicleKeys(memberId, vehicleId, true);
        }
        
        // Add to gang vehicle keys
        if (!m_GangVehicleKeys.Contains(gangId))
        {
            m_GangVehicleKeys.Set(gangId, new array<string>());
        }
        
        array<string> gangVehicles = m_GangVehicleKeys.Get(gangId);
        if (gangVehicles.Find(vehicleId) == -1)
        {
            gangVehicles.Insert(vehicleId);
        }
        
        Print(string.Format("[EdenVehicleKeySystem] Distributed keys for gang vehicle %1 to gang %2", vehicleId, gangId));
        return true;
    }
    
    //! Remove gang vehicle keys
    bool RemoveGangVehicleKeys(int gangId, string vehicleId)
    {
        if (gangId <= 0 || vehicleId == "")
            return false;
            
        // Get gang members
        array<string> gangMembers = GetGangMembers(gangId);
        
        // Remove keys from all gang members
        foreach (string memberId : gangMembers)
        {
            RemoveVehicleKeys(memberId, vehicleId);
        }
        
        // Remove from gang vehicle keys
        if (m_GangVehicleKeys.Contains(gangId))
        {
            array<string> gangVehicles = m_GangVehicleKeys.Get(gangId);
            int vehicleIndex = gangVehicles.Find(vehicleId);
            if (vehicleIndex != -1)
            {
                gangVehicles.Remove(vehicleIndex);
            }
        }
        
        Print(string.Format("[EdenVehicleKeySystem] Removed keys for gang vehicle %1 from gang %2", vehicleId, gangId));
        return true;
    }
    
    //! Get player vehicle keys
    array<string> GetPlayerVehicleKeys(string playerId)
    {
        if (playerId == "" || !m_PlayerKeys.Contains(playerId))
            return {};
            
        return m_PlayerKeys.Get(playerId);
    }
    
    //! Get vehicle key holders
    array<string> GetVehicleKeyHolders(string vehicleId)
    {
        if (vehicleId == "" || !m_VehicleKeys.Contains(vehicleId))
            return {};
            
        return m_VehicleKeys.Get(vehicleId);
    }
    
    //! Get gang vehicle keys
    array<string> GetGangVehicleKeys(int gangId)
    {
        if (gangId <= 0 || !m_GangVehicleKeys.Contains(gangId))
            return {};
            
        return m_GangVehicleKeys.Get(gangId);
    }
    
    //! Check if vehicle can be accessed
    bool CanAccessVehicle(string playerId, string vehicleId)
    {
        if (playerId == "" || vehicleId == "")
            return false;
            
        // Check if player has direct keys
        if (PlayerHasKeys(playerId, vehicleId))
            return true;
            
        // Check gang access if enabled
        if (m_GangKeySharing)
        {
            // Get player's gang
            int playerGangId = GetPlayerGangId(playerId);
            if (playerGangId > 0)
            {
                // Check if vehicle belongs to player's gang
                if (m_GangVehicleKeys.Contains(playerGangId))
                {
                    array<string> gangVehicles = m_GangVehicleKeys.Get(playerGangId);
                    if (gangVehicles.Find(vehicleId) != -1)
                        return true;
                }
            }
        }
        
        return false;
    }
    
    //! Lock vehicle
    bool LockVehicle(string playerId, string vehicleId)
    {
        if (!CanAccessVehicle(playerId, vehicleId))
            return false;
            
        // Implementation would lock the vehicle
        // This would involve setting vehicle lock state
        
        Print(string.Format("[EdenVehicleKeySystem] Vehicle %1 locked by %2", vehicleId, playerId));
        return true;
    }
    
    //! Unlock vehicle
    bool UnlockVehicle(string playerId, string vehicleId)
    {
        if (!CanAccessVehicle(playerId, vehicleId))
            return false;
            
        // Implementation would unlock the vehicle
        // This would involve setting vehicle lock state
        
        Print(string.Format("[EdenVehicleKeySystem] Vehicle %1 unlocked by %2", vehicleId, playerId));
        return true;
    }
    
    //! Helper methods
    protected int GetPlayerKeyCount(string playerId)
    {
        if (!m_PlayerKeys.Contains(playerId))
            return 0;
            
        return m_PlayerKeys.Get(playerId).Count();
    }
    
    protected string GenerateKeyId()
    {
        return string.Format("KEY_%1_%2", GetGame().GetWorld().GetWorldTime(), Math.RandomInt(1000, 9999));
    }
    
    protected void RemoveKeyFromDatabase(string playerId, string vehicleId)
    {
        // Find and remove key from database
        foreach (string keyId, EdenVehicleKeyData keyData : m_KeyDatabase)
        {
            if (keyData.GetPlayerId() == playerId && keyData.GetVehicleId() == vehicleId)
            {
                keyData.SetIsActive(false);
                UpdateKeyInDatabase(keyData);
                break;
            }
        }
    }
    
    protected void CleanupExpiredKeys()
    {
        int currentTime = GetGame().GetWorld().GetWorldTime();
        array<string> expiredKeys = {};
        
        // Find expired temporary keys (24 hours)
        foreach (string keyId, EdenVehicleKeyData keyData : m_KeyDatabase)
        {
            if (!keyData.IsPermanent() && keyData.IsActive())
            {
                int keyAge = currentTime - keyData.GetCreationTime();
                if (keyAge > 86400) // 24 hours
                {
                    expiredKeys.Insert(keyId);
                }
            }
        }
        
        // Remove expired keys
        foreach (string keyId : expiredKeys)
        {
            EdenVehicleKeyData keyData = m_KeyDatabase.Get(keyId);
            RemoveVehicleKeys(keyData.GetPlayerId(), keyData.GetVehicleId());
        }
        
        if (expiredKeys.Count() > 0)
        {
            Print(string.Format("[EdenVehicleKeySystem] Cleaned up %1 expired keys", expiredKeys.Count()));
        }
    }
    
    //! Configuration methods
    void SetKeySystemEnabled(bool enabled) { m_KeySystemEnabled = enabled; }
    bool IsKeySystemEnabled() { return m_KeySystemEnabled; }
    
    void SetMaxKeysPerPlayer(int maxKeys) { m_MaxKeysPerPlayer = maxKeys; }
    int GetMaxKeysPerPlayer() { return m_MaxKeysPerPlayer; }
    
    void SetKeyCopyPrice(int price) { m_KeyCopyPrice = price; }
    int GetKeyCopyPrice() { return m_KeyCopyPrice; }
    
    void SetKeyDistributionRange(float range) { m_KeyDistributionRange = range; }
    float GetKeyDistributionRange() { return m_KeyDistributionRange; }
    
    void SetGangKeySharing(bool enabled) { m_GangKeySharing = enabled; }
    bool IsGangKeySharingEnabled() { return m_GangKeySharing; }
    
    //! Placeholder methods for actual implementation
    protected IEntity GetPlayerEntity(string playerId) { return null; }
    protected array<string> GetGangMembers(int gangId) { return {}; }
    protected int GetPlayerGangId(string playerId) { return 0; }
    protected void SaveKeyToDatabase(EdenVehicleKeyData keyData) { }
    protected void UpdateKeyInDatabase(EdenVehicleKeyData keyData) { }
    
    //! Cleanup
    void Cleanup()
    {
        Print("[EdenVehicleKeySystem] Cleaning up vehicle key system...");
        
        m_PlayerKeys.Clear();
        m_VehicleKeys.Clear();
        m_KeyDatabase.Clear();
        m_GangVehicleKeys.Clear();
    }
}
